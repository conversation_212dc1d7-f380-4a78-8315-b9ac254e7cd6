import axios from 'axios'
import nookies from 'nookies'
import { useState } from 'react'
import { serializeJson } from '../helpers/serializeData'
import Moment from 'react-moment'
import Link from 'next/link'
import Layout from '../components/Layout'
import style from '../styles/Notifications.module.scss'
import liststyle from '../styles/Lists.module.scss'
import Card from '../components/Card'
import poststyle from '../styles/Posts.module.scss'
import { Row, Col } from 'react-grid-system'
import Button from '../components/Button'
import { UilApps, UilListUl } from '@iconscout/react-unicons'
import { useRouter } from 'next/router'

const Tutorial = (props) => {
  const { knowledgeItems } = props
  const [listViewStyle, setListViewStyle] = useState('grid')
  const router = useRouter()

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Knowledge Base</h1>

      <div className={liststyle.viewSwitcher}>
        <Button color="lined" sizing="small" icon={<UilApps />} action={() => setListViewStyle('grid')} />
        <Button color="lined" sizing="small" icon={<UilListUl />} action={() => setListViewStyle('list')} />
      </div>

      {listViewStyle === 'list' ? (
        <section className={liststyle.list}>
          {knowledgeItems.length > 0 ? (
            knowledgeItems.map((item) => (
              <Link href={`/tutorials/${item.slug}`} legacyBehavior>
                <a className={liststyle.listItem} key={item.id}>
                  <div className={liststyle.description}>
                    <h2>{item.title}</h2>
                    <h3>
                      <span>Category: </span>
                      <small className={style.badge}>{item.category}</small> | <span>Last update:</span>{' '}
                      <Moment format="MMMM DD, YYYY">{item.createdAt}</Moment>
                    </h3>
                  </div>
                </a>
              </Link>
            ))
          ) : (
            <div className={style.listItem}>
              <div className={style.description}>
                <h2>There are no items.</h2>
              </div>
            </div>
          )}
        </section>
      ) : (
        <section className={poststyle.list}>
          <Row style={{ width: '100%' }}>
            {knowledgeItems.length > 0 ? (
              knowledgeItems.map((item) => {
                return (
                  <Col sm={12} md={4} style={{ marginBottom: '32px' }}>
                    <Card
                      key={item.id}
                      title={item.title}
                      hasButton
                      linkUrl={`/tutorials/${item.slug}`}
                      image={item.thumbnail.url}
                      buttonLabel="View"
                      description={`Category: ${item.category} `}
                      clickEvent={() => router.push(`/tutorials/${item.slug}`)}
                    />
                  </Col>
                )
              })
            ) : (
              <div className={poststyle.listItem}>
                <div className={poststyle.description}>
                  <h2>There are no items.</h2>
                </div>
              </div>
            )}
          </Row>
        </section>
      )}
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const knowledgeItems = await axios
        .get(`${apiURL}/tutorials?_sort=createdAt:DESC`, config)
        .then((res) => {
          const { data } = res
          return data
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          knowledgeItems,
          user: userData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Tutorial
