import Markdown from '../components/Markdown'
import style from '../styles/ContentPage.module.scss'
import tablestyle from '../styles/Table.module.scss'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Layout from '../components/Layout'
import ScrollContainer from 'react-indiana-drag-scroll'
import { UilTimes, UilCheck } from '@iconscout/react-unicons'
import { Row, Col, Container } from 'react-grid-system'
import Button from '../components/Button'

const IndiApp = (props) => {
  const { data } = props

  const headers = ['Features', 'Replaces', 'Indi App', 'Indi App (Premium)']

  const comparisson = [
    {
      feature: 'Optional Database Clean-Up',
      replaces: ['/images/table/Cleaning-Up/database-clean-up.jpg'],
      app: true,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Monthly Newsletter',
      replaces: [
        '/images/table/Newsletter-Apps/activecampaign.png',
        '/images/table/Newsletter-Apps/brevo.jpeg',
        '/images/table/Newsletter-Apps/mailchimp.jpeg',
        '/images/table/Newsletter-Apps/ominisend.webp',
        '/images/table/Newsletter-Apps/sendpulse.png'
      ],
      app: true,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Monthly Newsletter + Rewards',
      replaces: [
        '/images/table/Newsletter-Apps/activecampaign.png',
        '/images/table/Newsletter-Apps/brevo.jpeg',
        '/images/table/Newsletter-Apps/mailchimp.jpeg',
        '/images/table/Newsletter-Apps/ominisend.webp',
        '/images/table/Newsletter-Apps/sendpulse.png',
        '/images/table/Newsletter-Apps/vip.png'
      ],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Testimonials Pushed To Website',
      replaces: ['/images/table/Testimonials/testimonials.png'],
      app: true,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Live Chat (Pushed To Mobile Device)',
      replaces: ['/images/table/Live-Chat/live-chat.png'],
      app: true,
      premium: true,
      isFooter: false
    },
    {
      feature: '2-Way SMS Marketing',
      replaces: [
        '/images/table/2-Way-SMS/clicksend.jpg',
        '/images/table/2-Way-SMS/simple-texting.jpeg',
        '/images/table/2-Way-SMS/telesign.png'
      ],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Email Marketing',
      replaces: [
        '/images/table/Email-Marketing/activecampaign.png',
        '/images/table/Email-Marketing/constant-contact.png',
        '/images/table/Email-Marketing/hubspot.jpeg',
        '/images/table/Email-Marketing/mailchimp.jpeg'
      ],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Booking & Appointments',
      replaces: ['/images/table/Booking-Appointment/calendly.png'],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Social Media Planner',
      replaces: [
        '/images/table/Social-Media-Planer/buffer.png',
        '/images/table/Social-Media-Planer/hootsuite.jpeg',
        '/images/table/Social-Media-Planer/tailwindapp.jpeg'
      ],
      app: true,
      premium: true,
      isFooter: false
    },

    {
      feature: 'Workflow Automations',
      replaces: [
        '/images/table/Workflow-Automation/activecampaign.png',
        '/images/table/Workflow-Automation/hubspot.jpeg',
        '/images/table/Workflow-Automation/Keap.png'
      ],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Call Tracking',
      replaces: ['/images/table/Call-Tracking/call-rail.jpg', '/images/table/Call-Tracking/Call-Tracking-Metrics.jpg'],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: 'Reputation Management',
      replaces: [
        '/images/table/Reputation-Monitor/birdeye.png',
        '/images/table/Reputation-Monitor/reputation.png',
        '/images/table/Reputation-Monitor/reviews-io.jpeg'
      ],
      app: false,
      premium: true,
      isFooter: false
    },
    {
      feature: '',
      replaces: ['Overall Price'],
      app: 'Free',
      premium: '$199/mo',
      isFooter: true
    }
  ]

  return (
    <Layout>
      <Container>
        <Row>
          <Col>
            <h1 className={style.ax_page_title}>{data.title}</h1>
            <section className={style.pageBanner}>
              <img src={data.banner.url} alt="Banner Image" />
              <div className={style.pageThumb}>
                <img src={data.thumbnail.url} alt={data.title} />
              </div>
            </section>
            <section className={style.pageContent}>
              <Row justify="center">
                <Col sm={12} lg={9}>
                  <Markdown>{data.description}</Markdown>
                </Col>
              </Row>

              <Row justify="center" style={{ marginTop: '80px' }}>
                <Col sm={12} lg={6}>
                  <Button
                    color="highlight"
                    align="right"
                    isLink
                    blank
                    linkPath="https://go.indimortgage.ca/onboarding"
                    label="Indi App Onboarding"
                  />
                </Col>
                <Col sm={12} lg={6}>
                  <Button
                    color="highlight"
                    align="left"
                    isLink
                    blank
                    linkPath="https://go.indimortgage.ca/demo"
                    label="Book A Demo Call"
                  />
                </Col>
              </Row>

              <Row justify="center">
                <Col sm={12} lg={9}>
                  <div style={{ marginTop: '80px' }}>
                    <h3 className={style.ax_page_subtitle} style={{ textAlign: 'center' }}>
                      The Indi App value proposition is simple. Do less and make more! <br />
                      Cut your costs and start growing your business on autopilot!
                    </h3>

                    <ScrollContainer horizontal vertical={false} className={tablestyle.table}>
                      <table cellPadding="0" cellSpacing="0">
                        <thead>
                          <tr>
                            {headers.map((item) => (
                              <th key={item.name}>
                                <h3>{item}</h3>
                              </th>
                            ))}
                          </tr>
                        </thead>
                        {comparisson.map((item) => {
                          if (!item.isFooter) {
                            return (
                              <tbody>
                                <tr key={item.web}>
                                  <td>{item.feature}</td>
                                  <td>
                                    <div className={tablestyle.icon}>
                                      {item.replaces.map((i) => (
                                        <img src={i} key={i} />
                                      ))}
                                    </div>
                                  </td>
                                  <td>{item.app ? <UilCheck fill="green" /> : <UilTimes fill="red" />}</td>
                                  <td>{item.premium ? <UilCheck fill="green" /> : <UilTimes fill="red" />}</td>
                                </tr>
                              </tbody>
                            )
                          } else {
                            return (
                              <tfoot key={item.web} className={tablestyle.footer}>
                                <tr>
                                  <td>
                                    <h3>{item.feature}</h3>
                                  </td>
                                  <td></td>
                                  <td>
                                    <h3>{item.app}</h3>
                                  </td>
                                  <td>
                                    <h3>{item.premium}</h3>
                                  </td>
                                </tr>
                              </tfoot>
                            )
                          }
                        })}
                      </table>
                    </ScrollContainer>
                  </div>
                </Col>
              </Row>
            </section>
          </Col>
        </Row>
      </Container>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const data = await axios
        .get(`${apiURL}/indi-app`, config)
        .then((res) => {
          return res.data
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          data
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default IndiApp
