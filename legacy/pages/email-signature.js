// pages/email-signature.js

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import axios from 'axios'
import nookies from 'nookies'
import SignatureContext from '../context/signatureContext'
import { serializeJson } from '../helpers/serializeData'
import formatUrl from '../helpers/formatUrl'
import { Container, Row, Col } from 'react-grid-system'
import style from '../styles/SignaturePage.module.scss'
import alerts from '../styles/ToastsAlerts.module.scss'
import Form from '../components/Signature/Form'
import Switcher from '../components/Switcher'
import Signature from '../components/Signature/Signature'
import SignatureWithPhoto from '../components/Signature/SignatureWithPhoto'
import SignatureOutlook from '../components/Signature/SignatureOutlook'
import SignatureWithPhotoOutlook from '../components/Signature/SignatureWithPhotoOutlook'
import Layout from '../components/Layout'
import Button from '../components/Button'

const EmailSignature = (props) => {
  const { user } = props

  const ctxVal = {
    username: '',
    email: '',
    titles: '',
    address: '',
    license: '',
    phone: '',
    photo: '',
    applicationLink: '',
    firstname: '',
    lastname: '',
    position: '',
    facebook: '',
    instagram: '',
    linkedin: '',
    twitter: '',
    whatsapp: '',
    fax: '',
    team: {
      logo: {
        url: ''
      }
    },
    website: ''
  }

  const [context, setContext] = useState(ctxVal)
  const [photo, setPhoto] = useState(false)
  const [webClient, setWebClient] = useState(true)
  const [outlookClient, setOutlookClient] = useState(false)
  const [applemailClient, setApplemailClient] = useState(false)
  const [logo, setLogo] = useState(false)
  const [dark, setDark] = useState(false)
  const [extraButton, setExtraButton] = useState({ enabled: false, label: null, link: null })
  const [secondContact, setSecondContact] = useState({ enabled: false, name: '', position: '', phone: '', email: '' })
  const [customStyle, setCustomStyle] = useState({ styleEnabled: false, styles: user.team.customStyle })

  const handlePhoto = () => {
    setPhoto(!photo)
  }
  const handleEmailClient = (e) => {
    if (e.target.id === 'outlook' && outlookClient) {
      setWebClient(true)
    }
    if (e.target.id === 'applemail' && applemailClient) {
      setWebClient(true)
    }
    if (e.target.id === 'outlook') {
      setOutlookClient(!outlookClient)
      if (!outlookClient) {
        setApplemailClient(false)
      }
      if (!outlookClient && !applemailClient) {
        setWebClient(false)
      }
    }
    if (e.target.id === 'applemail') {
      setApplemailClient(!applemailClient)
      if (!applemailClient) {
        setOutlookClient(false)
      }
      if (!outlookClient && !applemailClient) {
        setWebClient(false)
      }
    }
  }

  const showSignature = () => {
    if (webClient && photo) {
      return (
        <SignatureWithPhoto
          user={user}
          logo={logo}
          extraButton={extraButton}
          secondContact={secondContact}
          customStyle={customStyle && customStyle.styleEnabled ? customStyle : null}
        />
      )
    }
    if (webClient && !photo) {
      return (
        <Signature
          user={user}
          logo={logo}
          extraButton={extraButton}
          secondContact={secondContact}
          customStyle={customStyle && customStyle.styleEnabled ? customStyle : null}
        />
      )
    }
    if (outlookClient && photo) {
      return (
        <SignatureWithPhotoOutlook
          user={user}
          logo={logo}
          photo={photo}
          extraButton={extraButton}
          secondContact={secondContact}
          customStyle={customStyle && customStyle.styleEnabled ? customStyle : null}
        />
      )
    }
    if (outlookClient && !photo) {
      return (
        <SignatureOutlook
          user={user}
          logo={logo}
          extraButton={extraButton}
          secondContact={secondContact}
          customStyle={customStyle && customStyle.styleEnabled ? customStyle : null}
        />
      )
    }
  }

  const handleLogo = () => {
    setLogo(!logo)
  }

  const handleDark = () => {
    setDark(!dark)
  }

  const handleCustomStyle = () => {
    setCustomStyle({ ...customStyle, styleEnabled: !customStyle.styleEnabled, styles: user.team.customStyle })
  }

  const updateExtraButton = (e) => {
    let value = e.target.value
    const name = e.target.name

    if (name === 'label') {
      setExtraButton({ ...extraButton, label: value })
    }
    if (name === 'link') {
      value = formatUrl(value)
      setExtraButton({ ...extraButton, link: value })
    }
  }

  const handleExtraButton = (e) => {
    e.preventDefault()
    setExtraButton({ ...extraButton, enabled: !extraButton.enabled })
  }

  useEffect(() => {
    setSecondContact(context)
  }, [ctxVal])

  return (
    <SignatureContext.Provider value={[context, setContext]}>
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
        <Layout>
          <div className={style.ax_signature}>
            <h1 className={style.ax_page_title}>Indi Signature Generator</h1>
            <p>
              The Indi Email Signature Generator allows you to create your branded email signature in a HTML format. It
              means that you can have your website address and social media links clickables.
            </p>
            <p>
              <strong>
                Warning: Any information changed on your Email Signature will be replaced on your Profile and update it
                as well.
              </strong>
            </p>
            <h3 className={alerts.ax_tip}>
              Please make sure to include the <span>https://</span> prefix at the begining of any link.
            </h3>

            <section className={style.columns}>
              <div className={style.left_column}>
                <Form isAdmin={false} user={user} />
              </div>
              <div className={style.right_column}>
                <Container>
                  <Row>
                    <Col>{showSignature()}</Col>

                    <Col>
                      <div className={style.actions} style={{ marginTop: '32px' }}>
                        <div className={style.contentBox}>
                          <Row>
                            <Col>
                              {' '}
                              <h4 style={{ marginBottom: '24px' }}>Signature Options</h4>
                            </Col>
                          </Row>
                          <Row style={{ width: '100%' }}>
                            <Col sm={12} md={6}>
                              <Switcher
                                labelPos="top"
                                label="Show Photo"
                                name="photo"
                                checked={photo}
                                action={(e) => handlePhoto(e)}
                              />
                            </Col>
                            {user.team.cobranded ? (
                              <Col sm={12} md={6}>
                                <Switcher
                                  labelPos="top"
                                  label="Show Logo"
                                  name="logo"
                                  checked={logo}
                                  action={(e) => handleLogo(e)}
                                />
                              </Col>
                            ) : (
                              ''
                            )}
                          </Row>

                          <Row style={{ width: '100%' }}>
                            <Col sm={12} md={12}>
                              <Switcher
                                labelPos="top"
                                label="Using Outlook (Desktop App Only)"
                                id="outlook"
                                name="outlook"
                                checked={outlookClient}
                                action={(e) => handleEmailClient(e)}
                                hasIcon
                                iconPos="left"
                                icon="./images/outlook-logo.png"
                              />
                            </Col>

                            {user.team && user.team.customStyle && user.team.customStyle.enabled ? (
                              <Col sm={12} md={4}>
                                <Switcher
                                  labelPos="top"
                                  label="Apply Brand Style"
                                  name="brandStyle"
                                  checked={customStyle.styleEnabled}
                                  action={(e) => handleCustomStyle(e)}
                                />
                              </Col>
                            ) : (
                              ''
                            )}
                          </Row>

                          <form className={style.ax_form}>
                            <Row style={{ width: '100%' }}>
                              <Col sm={12} lg={12}>
                                <h4 style={{ marginTop: '24px' }}>Extra Button</h4>
                              </Col>
                              <Col sm={12} lg={5}>
                                <div className={style.ax_field}>
                                  <label>Label</label>
                                  <input type="text" name="label" onChange={(e) => updateExtraButton(e)} />
                                </div>
                              </Col>
                              <Col sm={12} lg={7}>
                                <div className={style.ax_field}>
                                  <label>Link</label>
                                  <input type="text" name="link" onChange={(e) => updateExtraButton(e)} />
                                </div>
                              </Col>
                              <Col>
                                <Button
                                  label="Add Button"
                                  color="highlight"
                                  size="medium"
                                  action={(e) => handleExtraButton(e)}
                                />
                              </Col>
                            </Row>
                          </form>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Container>
              </div>
            </section>
          </div>
        </Layout>
      </motion.div>
    </SignatureContext.Provider>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default EmailSignature
