import { useState, useEffect } from 'react'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Card from '../components/Card'
import Layout from '../components/Layout'
import Processing from '../components/Processing'
import style from '../styles/ContentPage.module.scss'

const Resources = (props) => {
  const { docs } = props
  const [processing, setProcessing] = useState(true)
  const [provinceDocs, setProvinceDocs] = useState({ allProvinces: null, provinceSpecific: null })

  const docsToArray = () => {
    const docsArr = []
    for (let d in docs) {
      if (docs[d].isHidden !== true) {
        docsArr.push(docs[d])
      }
    }
    return docsArr
  }

  const resourceDocs = docsToArray()

  const docsAllProvinces = (p) => {
    const filtered = []
    resourceDocs.map((c) => {
      c.provinceFile.forEach((f) => {
        if (f.province === 'allProvinces' && f.province === p) {
          filtered.push({ file: f, title: c.title })
        }
      })
    })

    return filtered
  }

  const docsProvinceSpecific = (p) => {
    const filtered = []

    resourceDocs.map((c) => {
      c.provinceFile.forEach((f) => {
        if (f.province !== 'allProvinces' && f.province === p) {
          filtered.push({ file: f, title: c.title })
        }
      })
    })

    return filtered
  }

  const documentsData = () => {
    let groupedDocsAll = []
    let groupedDocsSpecific = []

    if (resourceDocs) {
      const provinces = [
        'allProvinces',
        'alberta',
        'britishColumbia',
        'manitoba',
        'newBrunswick',
        'newFoundlandAndLabrador',
        'northwestTerritories',
        'novaScotia',
        'nunavut',
        'ontario',
        'princeEdwardIsland',
        'quebec',
        'saskatchewan',
        'yukon'
      ]

      provinces.forEach((p) => {
        if (p === 'allProvinces') {
          groupedDocsAll.push(docsAllProvinces(p))
        } else {
          groupedDocsSpecific.push({ [p]: docsProvinceSpecific(p) })
        }
      })
    }
    setProvinceDocs({ allProvinces: groupedDocsAll, provinceSpecific: groupedDocsSpecific })
  }

  const title = (str) => {
    if (str !== undefined && str !== '' && str !== null) {
      const newTitle = []
      for (let i = 0; i < str.length; i++) {
        newTitle.push(/[A-Z]/g.test(str[i]) ? ` ${str[i]}` : str[i])
      }
      const clearTitle = newTitle.join('')
      return clearTitle
    }

    return ''
  }

  const allProvincesfolder = () => {
    const foldersArr = []
    if (provinceDocs.allProvinces && provinceDocs.allProvinces[0] && provinceDocs.allProvinces[0].length) {
      provinceDocs.allProvinces.forEach((g) => {
        foldersArr.push(
          <Card
            key={title('allProvinces')}
            title={title('allProvinces')}
            hasButton
            linkUrl={`/resources/${'allProvinces'}?category=${'allProvinces'}`}
            iconSquared="./images/ico-folder.svg"
            buttonLabel="View Documents"
            size="vertical"
          />
        )
      })
    }

    return foldersArr
  }

  const specificProvincesfolders = () => {
    const foldersArr = []
    if (provinceDocs && provinceDocs.provinceSpecific !== null) {
      const folders = provinceDocs.provinceSpecific.filter((g) => {
        const val = g[Object.keys(g)]
        if (val.length > 0) {
          return g
        }
      })

      if (folders.length > 0) {
        folders.forEach((g) => {
          const itemName = Object.keys(g)[0]
          const itemValue = Object.values(g)[0]
          foldersArr.push(
            <Card
              key={title(itemName)}
              title={title(itemName)}
              hasButton
              linkUrl={`/resources/${itemName}?category=${itemName}`}
              iconSquared="./images/ico-folder.svg"
              buttonLabel="View Documents"
              size="vertical"
            />
          )
        })
      }
    }

    return foldersArr
  }

  useEffect(() => {
    documentsData()
  }, [])

  useEffect(() => {
    if (provinceDocs && provinceDocs !== null) {
      setProcessing(false)
    }
  }, [provinceDocs])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Resources</h1>
      {allProvincesfolder().length > 0 ? (
        <>
          <h2 className={style.alertNote}>
            Resource documents applicable for <strong>all provinces:</strong>
          </h2>
          <div className={style.ax_card_list}>
            {provinceDocs.allProvinces === null ? <Processing processing={processing} /> : allProvincesfolder()}
          </div>
        </>
      ) : (
        ''
      )}

      {specificProvincesfolders().length > 0 ? (
        <>
          <h2 className={style.alertNote}>
            Resource documents applicable for <strong>specific provinces</strong>:
          </h2>
          <div className={style.ax_card_list}>
            {provinceDocs.provinceSpecific === null ? (
              <Processing processing={processing} />
            ) : (
              specificProvincesfolders()
            )}
          </div>
        </>
      ) : (
        ''
      )}
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    }

    const docs = await axios
      .get(`${apiURL}/docs?documentType=resource`, config)
      .then((res) => {
        const shopData = serializeJson(res.data)
        return shopData
      })
      .catch((error) => {
        throw error
      })

    return {
      props: {
        docs
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Resources
