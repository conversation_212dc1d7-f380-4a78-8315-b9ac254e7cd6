// pages/api/proxy.js
import axios from 'axios'
import { parse } from 'cookie'

export default async function handler(req, res) {
  const { url, mode = 'stream' } = req.query

  console.log('S3 Proxy fetching:', url)

  if (!url) {
    return res.status(400).json({ error: 'URL parameter is required' })
  }

  // Decode the URL if it's encoded
  let decodedUrl = decodeURIComponent(url)

  // Add region to S3 URL if missing
  if (decodedUrl.includes('s3.amazonaws.com') && !decodedUrl.includes('s3.us-east-1.amazonaws.com')) {
    decodedUrl = decodedUrl.replace('s3.amazonaws.com', 's3.us-east-1.amazonaws.com')
  }

  console.log('S3 Proxy fetching (modified URL):', decodedUrl)

  try {
    // If mode is 'check', just do a HEAD request to verify the file exists
    if (mode === 'check') {
      await axios.head(decodedUrl)
      return res.status(200).json({ success: true })
    }

    // Otherwise, stream the file - NO AUTH HEADER FOR S3
    const response = await axios({
      method: 'get',
      url: decodedUrl,
      responseType: 'arraybuffer',
      headers: {
        Accept: '*/*',
        'User-Agent': 'Mozilla/5.0 NextJS Proxy'
      },
      // Add timeout and max content length for safety
      timeout: 30000,
      maxContentLength: 10 * 1024 * 1024 // 10MB max
    })

    // Set appropriate content type and other headers
    const contentType = response.headers['content-type'] || 'application/octet-stream'
    res.setHeader('Content-Type', contentType)

    // Set CORS headers to allow access
    res.setHeader('Access-Control-Allow-Origin', '*')
    res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Range')

    // Set cache control headers to prevent all caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    res.setHeader('Pragma', 'no-cache')
    res.setHeader('Expires', '0')

    // Send the file data
    res.status(200).send(response.data)
  } catch (error) {
    console.error('S3 Proxy Error:', error.message)

    // Try to parse XML error from S3 if available
    let errorDetails = { info: 'No response details available' }
    if (error.response) {
      console.error('Error response status:', error.response.status)
      console.error('Error response headers:', error.response.headers)

      // If we have XML response data, try to extract the error message
      if (error.response.data) {
        try {
          const dataString = error.response.data.toString('utf8')
          console.error('S3 Error Response:', dataString)

          // Simple XML parsing to extract error message
          const messageMatch = /<Message>(.*?)<\/Message>/i.exec(dataString)
          if (messageMatch && messageMatch[1]) {
            errorDetails = { s3Message: messageMatch[1] }
          }
        } catch (parseError) {
          console.error('Error parsing S3 error response:', parseError)
        }
      }
    } else if (error.request) {
      console.error('No response received:', error.request)
    }

    // Return a more detailed error response
    return res.status(500).json({
      error: 'Failed to fetch file',
      message: error.message,
      url: decodedUrl,
      details: error.response
        ? {
            status: error.response.status,
            statusText: error.response.statusText,
            ...errorDetails
          }
        : errorDetails
    })
  }
}
