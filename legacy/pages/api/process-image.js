// pages/api/process-image.js
import sharp from 'sharp'
import axios from 'axios'

export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const { imageUrl } = req.body

      // Fetch the image
      const response = await axios.get(imageUrl, { responseType: 'arraybuffer' })
      const buffer = Buffer.from(response.data, 'binary')

      // Process the image
      const processedImage = await sharp(buffer)
        .resize(130, 130, { fit: 'cover', position: 'center' })
        .toFormat('jpeg')
        .toBuffer()

      // Convert to base64
      const base64Image = `data:image/jpeg;base64,${processedImage.toString('base64')}`

      res.status(200).json({ processedImageUrl: base64Image })
    } catch (error) {
      console.error('Error processing image:', error)
      res.status(500).json({ error: 'Error processing image' })
    }
  } else {
    res.setHeader('Allow', ['POST'])
    res.status(405).end(`Method ${req.method} Not Allowed`)
  }
}
