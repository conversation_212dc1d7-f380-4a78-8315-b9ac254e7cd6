import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import Card from '../../components/Card'
import Processing from '../../components/Processing'
import style from '../../styles/ContentPage.module.scss'

const fintracDocuments = (props) => {
  const { docs } = props
  const [processing, setProcessing] = useState(true)
  const [documents, setDocuments] = useState(null)
  const router = useRouter()

  const docsToArray = () => {
    const docsArr = []
    for (let d in docs) {
      if (docs[d].isHidden !== true) {
        docsArr.push(docs[d])
      }
    }
    return docsArr
  }

  const fintracDocs = docsToArray()

  const allDocs = (p) => {
    const filtered = []
    fintracDocs.map((c) => {
      c.provinceFile.forEach((f) => {
        if (f.province === router.query.category) {
          filtered.push({ file: f, title: c.title, thumbnail: f.thumbnail })
        }
      })
    })

    setDocuments(filtered)
    setProcessing(false)
    return filtered
  }

  const camelCaseToTitleCase = (str) => {
    return str.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
      return str.toUpperCase()
    })
  }

  useEffect(() => {
    allDocs()
  }, [])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>
        <span>{router.query.category ? `${camelCaseToTitleCase(router.query.category)} ` : ''}</span> fintrac Documents{' '}
      </h1>
      <section className={style.ax_card_list}>
        {documents === null ? (
          <Processing processing={processing} />
        ) : (
          documents.map((doc) => {
            const slug = doc.title.replace(/\s+/g, '-').toLowerCase()

            return (
              <>
                {' '}
                <Card
                  key={doc.title}
                  title={doc.title}
                  hasButton
                  linkUrl={`/fintrac/document/${slug}?title=${slug}&file=${
                    doc.file.file.url
                  }&ext=${doc.file.file.ext.replace('.', '')}`}
                  image={
                    doc?.thumbnail
                      ? doc?.thumbnail?.formats?.thumbnail
                        ? doc.thumbnail.formats.thumbnail.url
                        : doc.thumbnail.url
                      : '/images/ico-pdf.svg'
                  }
                  buttonLabel="View Document"
                  size="vertical"
                />
              </>
            )
          })
        )}
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    }
    const docs = await axios
      .get(`${apiURL}/docs?documentType=fintrac`, config)
      .then((res) => {
        const shopData = serializeJson(res.data)
        return shopData
      })
      .catch((error) => {
        throw error
      })

    return { props: { docs } }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default fintracDocuments
