import React from 'react'

export default function Custom500() {
  const handleCacheBustingReload = () => {
    // Generate a unique timestamp for cache busting
    const timestamp = Date.now()
    // Force a full page reload with a cache-busting query parameter
    window.location.href = `${window.location.pathname}?reload=${timestamp}`
  }

  const handleClearSiteData = () => {
    if ('caches' in window) {
      caches.keys().then((keyList) => {
        return Promise.all(
          keyList.map((key) => {
            return caches.delete(key)
          })
        )
      })
    }

    // Clear localStorage
    localStorage.clear()

    // Clear sessionStorage
    sessionStorage.clear()

    // Force reload after clearing data
    setTimeout(() => {
      const timestamp = Date.now()
      window.location.href = `${window.location.pathname}?reload=${timestamp}`
    }, 500)
  }

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        textAlign: 'center',
        padding: '20px',
        fontFamily: 'Arial, sans-serif'
      }}
    >
      <img
        src="/images/indi-central-logo.svg"
        alt="Indi Central Logo"
        style={{ marginBottom: '30px', maxWidth: '200px' }}
      />

      <div
        style={{
          backgroundColor: 'white',
          padding: '30px',
          borderRadius: '10px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          maxWidth: '500px'
        }}
      >
        <h1 style={{ color: '#333', marginTop: 0 }}>Server Error</h1>

        <p style={{ color: '#666', lineHeight: '1.5' }}>
          We've encountered a server error. This might be due to outdated cached data.
        </p>

        <p style={{ color: '#666', lineHeight: '1.5' }}>Please try these solutions:</p>

        <div style={{ marginTop: '20px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
          <button
            onClick={handleCacheBustingReload}
            style={{
              backgroundColor: '#0070f3',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Reload Application
          </button>

          <button
            onClick={handleClearSiteData}
            style={{
              backgroundColor: '#ff4b4b',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Clear Site Data & Reload
          </button>

          <p style={{ fontSize: '14px', color: '#888', marginTop: '15px' }}>
            If the problem persists, please try opening the site in an incognito window or contact support.
          </p>
        </div>
      </div>
    </div>
  )
}
