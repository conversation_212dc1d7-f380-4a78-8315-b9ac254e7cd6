import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Moment from 'react-moment'
import Layout from '../components/Layout'
import Markdown from '../components/Markdown'
import style from '../styles/Notifications.module.scss'

const Notifications = (props) => {
  const { pageData, user } = props

  const handleNotifications = () => {
    const filteredNotes = []

    pageData.forEach((note) => {
      if (note.showToAll) {
        filteredNotes.push(note)
      }

      note.teams.forEach((t) => {
        if (t.name === user.team.name) {
          filteredNotes.push(note)
        }
      })

      note.users.forEach((u) => {
        if (u.id === user.id) {
          filteredNotes.push(note)
        }
      })
    })

    return filteredNotes
  }

  const allNotes = handleNotifications()

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Notifications</h1>

      <section className={style.list}>
        {allNotes.length > 0 ? (
          allNotes.map((note) => (
            <div className={style.listItem} key={note.id}>
              <div className={style.description}>
                <h2>{note.title}</h2>
                <h3>
                  <span>Published at:</span> <Moment format="MMMM DD, YYYY">{note.createdAt}</Moment>
                </h3>
                <Markdown>{note.content}</Markdown>
              </div>
            </div>
          ))
        ) : (
          <div className={style.listItem}>
            <div className={style.description}>
              <h2>There are no new notifications.</h2>
            </div>
          </div>
        )}
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const pageData = await axios
        .get(`${apiURL}/notifications`, config)
        .then((res) => {
          const notifications = res.data
          return notifications
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          pageData,
          user: userData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Notifications
