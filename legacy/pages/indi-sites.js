import { useState, useRef, useEffect, useContext } from 'react'
import { motion } from 'framer-motion'
import axios from 'axios'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import { serializeJson } from '../helpers/serializeData'
import { Validation } from '../helpers/validateFields'
import { UilInfoCircle } from '@iconscout/react-unicons'
import AuthContext from '../context/authContext'
import Layout from '../components/Layout'
import Toast from '../components/Toast'
import { Container, Row, Col } from 'react-grid-system'
import style from '../styles/Profile.module.scss'
import contentStyle from '../styles/ContentPage.module.scss'

const IndiSites = (props) => {
  const { user, website } = props
  const photoName = typeof user.photo !== 'undefined' ? `${user.photo.hash}${user.photo.ext}` : ''
  // const photoUrl = `https://res.cloudinary.com/axiom-mortgage/image/upload/w_250,h_250,q_100,c_fill,g_face/${photoName}`
  const photoUrl = `${process.env.AWS_BUCKET_URL}/images/small/${photoName}`

  const apiUrl = process.env.API_URL

  const form = useRef(null)
  const [fieldsValidation, setFieldsValidation] = useState([])
  const [formInfo, setFormInfo] = useState(null)
  const [websiteInfo, setWebsiteInfo] = useState(website || null)
  const [spinner, setSpinner] = useState(false)
  const [message, setMessage] = useState('')
  const [isVisible, setIsVisible] = useState(false)
  const { userAuth, setUserAuth } = useContext(AuthContext)
  const userInfo = userAuth.userInfo

  //Required Fields and and Empty Values Checker
  const requiredFields = ['firstname', 'lastname', 'phone']

  //Form Status and Fields Validation
  const validate = async () => {
    const validation = await Validation(formInfo, requiredFields)

    if (validation.length > 0) {
      setFieldsValidation(validation)
    } else {
      setFieldsValidation(validation)
    }
    return validation
  }

  const checkValues = () => {
    const inputs = Array.from(form.current.querySelectorAll('input'))
    const selects = Array.from(form.current.querySelectorAll('select'))
    const textareas = Array.from(form.current.querySelectorAll('textarea'))

    const allInputs = inputs.concat(selects, textareas)
    const empty = []

    const mappedFormInfo = allInputs.map((i) => {
      if (i.value && i.value !== '' && i.value !== null && i.value !== undefined) {
        return { [i.name]: i.value }
      }

      requiredFields.forEach((r) => {
        if (i.name === r && i.value === '') {
          empty.push(i.name)
        }
        return { [i.name]: '' }
      })

      if (i.type === 'checkbox') {
        return { [i.name]: i.checked }
      }

      return { [i.name]: i.value }
    })

    let data = {}

    mappedFormInfo.forEach((f) => {
      data = { ...data, ...f }
    })

    //clear empty items from finalData
    const clearFinalData = Object.fromEntries(Object.entries(data).filter(([_, v]) => v != ''))

    let finalData = {}

    for (const d in clearFinalData) {
      if (clearFinalData[d] === 'true' || clearFinalData[d] === true) {
        finalData = { ...finalData, [d]: true }
      } else if (clearFinalData[d] === 'false' || clearFinalData[d] === false) {
        finalData = { ...finalData, [d]: false }
      } else {
        finalData = { ...finalData, [d]: clearFinalData[d] }
      }
    }

    setFormInfo({ ...formInfo, ...clearFinalData })
  }

  const updateFormInfo = (e) => {
    e.preventDefault()
    const { name } = e.target
    let { value } = e.target

    if (
      name === 'phone' ||
      name === 'workPhone' ||
      name === 'cellPhone' ||
      name === 'homePhone' ||
      name === 'emergencyPhone'
    ) {
      value = value.replace(/[^\d]/g, '')
    }

    setFormInfo({ ...formInfo, [name]: value })
  }

  const updateUser = async (e) => {
    e.preventDefault()
    const token = Cookies.get('jwt')

    const isValidated = await validate()
    if (isValidated.length > 0) {
      window.scroll({ top: 0, left: 0, behavior: 'smooth' })
      setSpinner(false)
      setMessage('error')
      return
    } else {
      if (e.target.id === 'form') {
        setSpinner(true)

        const newFormInfo = {
          firstname: formInfo.firstname,
          middlename: formInfo.middlename,
          lastname: formInfo.lastname,
          titles: formInfo.titles,
          position: formInfo.position,
          license: formInfo.license,
          workEmail: formInfo.workEmail,
          phone: formInfo.phone,
          instagram: formInfo.instagram,
          facebook: formInfo.facebook,
          linkedin: formInfo.linkedin,
          twitter: formInfo.twitter,
          youtube: formInfo.youtube,
          applicationLink: formInfo.applicationLink,
          appointmentScheduleLink: formInfo.appointmentScheduleLink,
          chatWidgetCode: formInfo.chatWidgetCode,
          reviewWidgetCode: formInfo.reviewWidgetCode,
          googleTag: formInfo.googleTag,
          googleTagManagerInHead: formInfo.googleTagManagerInHead,
          googleTagManagerInBody: formInfo.googleTagManagerInBody,
          googleWebsiteVerification: formInfo.googleWebsiteVerification,
          facebookPixelTag: formInfo.facebookPixelTag,
          thirdPartyScriptTag: formInfo.thirdPartyScriptTag
        }

        try {
          // Save user data
          await axios.put(`${apiUrl}/users/${user.id}`, newFormInfo, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          })

          // Extract only HTML Tags fields for website endpoint
          const htmlTagsFields = {
            chatWidgetCode: formInfo.chatWidgetCode,
            reviewWidgetCode: formInfo.reviewWidgetCode,
            googleTag: formInfo.googleTag,
            googleTagManagerInHead: formInfo.googleTagManagerInHead,
            googleTagManagerInBody: formInfo.googleTagManagerInBody,
            googleWebsiteVerification: formInfo.googleWebsiteVerification,
            facebookPixelTag: formInfo.facebookPixelTag,
            thirdPartyScriptTag: formInfo.thirdPartyScriptTag,
            userId: user.id
          }

          // Save website data if website exists
          if (websiteInfo && websiteInfo.id) {
            await axios.put(`${apiUrl}/websites/${websiteInfo.id}`, htmlTagsFields, {
              headers: {
                Authorization: `Bearer ${token}`
              }
            })
          } else if (user.id) {
            // Create website if it doesn't exist
            const newWebsite = await axios.post(`${apiUrl}/websites`, htmlTagsFields, {
              headers: {
                Authorization: `Bearer ${token}`
              }
            })
            setWebsiteInfo(newWebsite.data)
          }

          setSpinner(false)
          setMessage('success')
          setIsVisible(true)
          setTimeout(() => {
            setIsVisible(false)
          }, 3000)
        } catch (err) {
          setSpinner(false)
          setMessage('error')
          setIsVisible(true)
          setTimeout(() => {
            setIsVisible(false)
          }, 3000)
          console.error(err)
        }
      }
    }
  }

  useEffect(() => {
    if (form && form.current !== null && form.current !== undefined) {
      checkValues()
    }
  }, [userInfo])

  useEffect(() => {
    // Initialize form data with combined user and website data
    if (user && website) {
      // Merge user and website data, with website data taking precedence
      const combinedData = { ...user, ...website }
      setFormInfo(combinedData)
    } else {
      setFormInfo(user)
    }
  }, [user, website])

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <Layout>
        <Toast
          showToast={isVisible}
          toastType={message}
          message={
            message === 'success'
              ? 'Your website info has been updated successfuly!'
              : 'Ooops. Something went wrong. Please try again.'
          }
        />
        <form className={style.ax_form} ref={form}>
          <h1 className={style.ax_page_title}>My Indi Site</h1>
          <section className={contentStyle.pageContent}>
            <Container>
              <h2>Website Info</h2>

              <p className={`${style.alertNote} ${style.alertInfo}`}>
                <UilInfoCircle size={16} />
                <strong>Warning: You must use the name you have registered with the provincial regulator.</strong>
                <br />
                Any data modified in this section will be updated on your Website and on your Indi Central Profile as
                well.
              </p>

              {
                /*show validation only if First Save is Complete*/
                fieldsValidation && fieldsValidation.length > 0 ? (
                  <section className={style.validation}>
                    <h3>The following fields are Required:</h3>
                    <ul>
                      {fieldsValidation.map((f) => (
                        <li key={f}>{f}</li>
                      ))}
                    </ul>
                  </section>
                ) : (
                  ''
                )
              }
              <Row>
                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">
                      First Name<span>*</span>
                    </label>
                    <input
                      type="text"
                      name="firstname"
                      id="firstname"
                      placeholder="Name"
                      defaultValue={user.firstname}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="middlename">Middle Name</label>
                    <input
                      type="text"
                      name="middlename"
                      id="middlename"
                      placeholder="Name"
                      defaultValue={user.middlename}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">
                      Last Name<span>*</span>
                    </label>
                    <input
                      type="text"
                      name="lastname"
                      id="lastname"
                      placeholder="Last Name"
                      defaultValue={user.lastname}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="titles">Title After Name (e.g. AMP, BCC)</label>
                    <input
                      type="text"
                      name="titles"
                      id="titles"
                      placeholder="AMP, BCC, BCO"
                      defaultValue={user.titles}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="position">Position</label>
                    <input
                      type="text"
                      name="position"
                      id="position"
                      placeholder="I.E: Mortgage Broker, BCS"
                      defaultValue={user.position}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="license">License Number (Optional)</label>
                    <input
                      type="text"
                      name="license"
                      id="license"
                      placeholder="I.E: #AXM003333"
                      defaultValue={user.license ? user.license : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="workEmail">Preferred Email Address</label>
                    <input
                      type="email"
                      name="workEmail"
                      id="workEmail"
                      placeholder="<EMAIL>"
                      defaultValue={user.workEmail}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <div className={style.phoneExt}>
                      <div>
                        <label htmlFor="phone">
                          Preferred Phone Number<span>*</span>
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          id="phone"
                          placeholder="************"
                          defaultValue={user && user.phone ? user.phone : ''}
                          onChange={(e) => updateFormInfo(e)}
                        />
                      </div>
                    </div>
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="instagram">Instagram Page</label>
                    <input
                      type="text"
                      name="instagram"
                      id="instagram"
                      placeholder="I.E: https://instagram.com/jane-doe"
                      defaultValue={user.instagram ? user.instagram : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="facebook">Facebook Page</label>
                    <input
                      type="text"
                      name="facebook"
                      id="facebook"
                      placeholder="I.E: https://facebook.com/jane-doe"
                      defaultValue={user.facebook ? user.facebook : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="linkedin">Linkedin Page</label>
                    <input
                      type="text"
                      name="linkedin"
                      id="linkedin"
                      placeholder="I.E: https://linkedin.com/in/jane-doe"
                      defaultValue={user.linkedin ? user.linkedin : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="twitter">Twitter Page</label>
                    <input
                      type="text"
                      name="twitter"
                      id="twitter"
                      placeholder="I.E: https://twitter.com/jane-doe"
                      defaultValue={user.twitter ? user.twitter : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="youtube">Youtube Channel</label>
                    <input
                      type="text"
                      name="youtube"
                      id="youtube"
                      placeholder="I.E: https://youtube.com/c/jane-doe"
                      defaultValue={user.youtube ? user.youtube : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="applicationLink">Mortgage Application Link</label>
                    <input
                      type="text"
                      name="applicationLink"
                      id="applicationLink"
                      placeholder="I.E: https://mtgapp.scarlettnetwork.com/broker-name/home"
                      defaultValue={user.applicationLink ? user.applicationLink : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>

                <Col sm={12} md={6} lg={4}>
                  <div className={style.ax_field}>
                    <label htmlFor="appointmentScheduleLink">Appointment Schedule Link (I.e. Calendly)</label>
                    <input
                      type="text"
                      name="appointmentScheduleLink"
                      id="appointmentScheduleLink"
                      placeholder="Calendly or Other"
                      defaultValue={user.appointmentScheduleLink ? user.appointmentScheduleLink : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </Col>
              </Row>
            </Container>
          </section>

          <section className={contentStyle.pageContent}>
            <Container>
              <h2>HTML Tags</h2>
              <p>
                Insert below your website HTML tags, for example: Google Tag, Google Tracking, Googe Analytics, Facebook
                Pixel.
              </p>
              <ul>
                <li>Insert only the tag, no additional info.</li>
                <li>
                  If you need to add any HTML tag from a different provider than the ones listed below, please get in
                  touch with support.
                </li>
              </ul>
              <Row>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="chatWidgetCode">Chat Widget Code (Indi App)</label>
                    <textarea
                      name="chatWidgetCode"
                      id="chatWidgetCode"
                      defaultValue={websiteInfo?.chatWidgetCode}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="reviewWidgetCode">Reviews Widget Code (Indi App)</label>
                    <textarea
                      name="reviewWidgetCode"
                      id="reviewWidgetCode"
                      defaultValue={websiteInfo?.reviewWidgetCode}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Google Tag</label>
                    <textarea
                      name="googleTag"
                      id="googleTag"
                      defaultValue={websiteInfo?.googleTag}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Google Tag Manager in &lt;head&gt;</label>
                    <textarea
                      name="googleTagManagerInHead"
                      id="googleTagManagerInHead"
                      defaultValue={websiteInfo?.googleTagManagerInHead}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Google Tag Manager in &lt;body&gt;</label>
                    <textarea
                      name="googleTagManagerInBody"
                      id="googleTagManagerInBody"
                      defaultValue={websiteInfo?.googleTagManagerInBody}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Google Website Verification &lt;meta&gt;</label>
                    <textarea
                      name="googleWebsiteVerification"
                      id="googleWebsiteVerification"
                      defaultValue={websiteInfo?.googleWebsiteVerification}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Facebook Pixel</label>
                    <textarea
                      name="facebookPixelTag"
                      id="facebookPixelTag"
                      defaultValue={websiteInfo?.facebookPixelTag}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
                <Col sm={12} md={6} lg={6}>
                  <div className={style.ax_field}>
                    <label htmlFor="name">Other Provider Tag</label>
                    <textarea
                      name="thirdPartyScriptTag"
                      id="thirdPartyScriptTag"
                      defaultValue={websiteInfo?.thirdPartyScriptTag}
                      onChange={(e) => updateFormInfo(e)}
                      style={{ minHeight: '220px' }}
                      className={style.code}
                    />
                  </div>
                </Col>
              </Row>

              <Row justify="end">
                <Col sm={12} md={2} lg={2}>
                  <div className={style.ax_field}>
                    <button
                      id="form"
                      className={style.ax_btn_submit}
                      name="generate"
                      type="submit"
                      onClick={(e) => updateUser(e)}
                      style={{ width: 'auto', display: 'table' }}
                    >
                      {spinner ? (
                        <>
                          <img src="/images/spinner-white.svg" alt="spinner" />
                          Saving
                        </>
                      ) : (
                        'Save Info'
                      )}
                    </button>
                  </div>
                </Col>
              </Row>
            </Container>
          </section>
        </form>
      </Layout>
    </motion.div>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = `${process.env.API_URL}`
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  let websiteData
  if (userId) {
    // Fetch user data
    userData = await axios
      .get(`${apiURL}/users-permissions/users-light/${userId}`, config)
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })

    // Fetch website data
    websiteData = await axios
      .get(`${apiURL}/websites/user/${userId}`, config)
      .then((res) => {
        const website = res.data
        const serializedData = serializeJson(website)
        return serializedData
      })
      .catch((err) => {
        console.error('Error fetching website data:', err)
        return null
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData,
          website: websiteData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default IndiSites
