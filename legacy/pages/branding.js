import { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import AuthContext from '../context/authContext'
import { serializeJson } from '../helpers/serializeData'
import nookies from 'nookies'
import axios from 'axios'
import Layout from '../components/Layout'
import CardTile from '../components/CardTile'
import Processing from '../components/Processing'
import style from '../styles/Marketing.module.scss'
import { Container, Row, Col } from 'react-grid-system'

const Branding = () => {
  const { userAuth } = useContext(AuthContext)
  const [authenticated] = useState(userAuth.isAuth)
  const [pageLoading, setPageLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!authenticated) {
      router.push('https://brand.axiomsites.ca/')
      return <></>
    }
  }, [])

  useEffect(() => {
    if (!authenticated) {
      setPageLoading(true)
      return <></>
    }
    setPageLoading(false)
  }, [authenticated])

  const content = () => {
    if (pageLoading) {
      return <Processing message="Please wait..." processing={pageLoading} />
    }
    return (
      <Layout>
        <h1 className={style.ax_page_title}>Branding</h1>
        <Container>
          <Row>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/icon-brand-material-3d.png"
                title="Logos, Fonts & Guidelines"
                color="gradientgrey"
                clickEvent={() => router.push('/logos-fonts')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/icon-brand-material-3d.png"
                title="Brand Materials"
                color="gradientgrey"
                clickEvent={() => router.push('/brand-materials')}
              />
            </Col>
          </Row>
        </Container>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          userData
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Branding
