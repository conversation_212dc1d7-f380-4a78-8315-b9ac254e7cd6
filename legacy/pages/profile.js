import { useState, useRef, useEffect, useContext } from 'react'
import { motion } from 'framer-motion'
import axios from 'axios'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import { UilTimes } from '@iconscout/react-unicons'
import heicConvert from 'heic-convert'
import { serializeJson, serializeArray } from '../helpers/serializeData'
import { Validation } from '../helpers/validateFields'
import AuthContext from '../context/authContext'
import Layout from '../components/Layout'
import Toast from '../components/Toast'
import Card from '../components/Card'
import Switcher from '../components/Switcher'
import formatUrl from '../helpers/formatUrl'
import style from '../styles/Profile.module.scss'
import AddressSelect from '../components/AddressSelect'
import ImageCropper from '../components/ImageCropper'
import Processing from '../components/Processing'
import { Col, Container, Row } from 'react-grid-system'

const Profile = (props) => {
  const { user, branches } = props
  const apiUrl = process.env.API_URL

  const form = useRef(null)
  const [fieldsValidation, setFieldsValidation] = useState([])
  const [formInfo, setFormInfo] = useState(null)
  const [badges, setBadges] = useState(user.badges)
  const [showBadges, setShowBadges] = useState(user.showBadges)
  const [spinner, setSpinner] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [spinnerBadges, setSpinnerBadges] = useState(false)
  const [message, setMessage] = useState('')
  const [isVisible, setIsVisible] = useState(false)
  const [charCount, setCharCount] = useState({ bio: 0, note: 0 })
  const [userData, setUserData] = useState(null)
  const [currentBranchId, setCurrentBranchId] = useState(null)
  const { userAuth, setUserAuth } = useContext(AuthContext)
  const userPhoto = useRef()
  const userInfo = userAuth.userInfo
  const addressRef = useRef()
  const cityRef = useRef()
  const provinceRef = useRef()
  const postalCodeRef = useRef()
  const provinceLicenseNumberRef = useRef()
  const personalAddressRef = useRef()
  const personalCityRef = useRef()
  const personalProvinceRef = useRef()
  const personalPostalCodeRef = useRef()

  const [showCropper, setShowCropper] = useState(false)
  const [selectedImage, setSelectedImage] = useState(null)
  const [originalFileName, setOriginalFileName] = useState('')
  const [imageError, setImageError] = useState('')

  //Required Fields and and Empty Values Checker
  const requiredFields = ['firstname', 'lastname', 'phone']

  //Form Status and Fields Validation
  const validate = async () => {
    const validation = await Validation(formInfo, requiredFields)

    if (validation.length > 0) {
      setFieldsValidation(validation)
    } else {
      setFieldsValidation(validation)
    }
    return validation
  }

  const checkValues = () => {
    const inputs = Array.from(form.current.querySelectorAll('input'))
    const selects = Array.from(form.current.querySelectorAll('select'))
    const textareas = Array.from(form.current.querySelectorAll('textarea'))

    const allInputs = inputs.concat(selects, textareas)
    const empty = []

    const mappedFormInfo = allInputs.map((i) => {
      if (i.value && i.value !== '' && i.value !== null && i.value !== undefined) {
        return { [i.name]: i.value }
      }

      requiredFields.forEach((r) => {
        if (i.name === r && i.value === '') {
          empty.push(i.name)
        }
        return { [i.name]: '' }
      })

      if (i.type === 'checkbox') {
        return { [i.name]: i.checked }
      }

      return { [i.name]: i.value }
    })

    let data = {}

    mappedFormInfo.forEach((f) => {
      data = { ...data, ...f }
    })

    //clear empty items from finalData
    const clearFinalData = Object.fromEntries(Object.entries(data).filter(([_, v]) => v != ''))

    let finalData = {}

    for (const d in clearFinalData) {
      if (clearFinalData[d] === 'true' || clearFinalData[d] === true) {
        finalData = { ...finalData, [d]: true }
      } else if (clearFinalData[d] === 'false' || clearFinalData[d] === false) {
        finalData = { ...finalData, [d]: false }
      } else {
        finalData = { ...finalData, [d]: clearFinalData[d] }
      }
    }

    setFormInfo({ ...formInfo, ...clearFinalData })
  }

  const updateFormInfo = (e, type) => {
    e.preventDefault()
    let { name, value } = e.target

    if (e.target.name === 'bio') {
      setCharCount({ ...charCount, bio: e.target.value.length })
    }

    if (e.target.name === 'additionalNotes') {
      setCharCount({ ...charCount, note: e.target.value.length })
    }

    if (
      name === 'website' ||
      name === 'secondaryWebsite' ||
      name === 'appointmentScheduleLink' ||
      name === 'googleReviewsLink' ||
      name === 'applicationLink' ||
      name === 'facebook' ||
      name === 'instagram' ||
      name === 'linkedin' ||
      name === 'twitter' ||
      name === 'youtube'
    ) {
      value = formatUrl(e.target.value)
    }

    if (
      name === 'phone' ||
      name === 'workPhone' ||
      name === 'cellPhone' ||
      name === 'homePhone' ||
      name === 'emergencyPhone'
    ) {
      value = value.replace(/[^\d]/g, '')
    }

    setFormInfo({ ...formInfo, [name]: value })
  }

  const updateUser = async (e) => {
    e.preventDefault()
    const token = Cookies.get('jwt')
    const isValidated = await validate()

    if (isValidated.length > 0) {
      window.scroll({ top: 0, left: 0, behavior: 'smooth' })
      setSpinnerBadges(false)
      setSpinner(false)
      setMessage('error')
      return
    } else {
      if (e.target.id === 'badges') {
        setSpinnerBadges(true)
        await axios
          .put(
            `${apiUrl}/users/${user.id}`,
            {
              showBadges: {
                website: showBadges.website,
                emailSignature: showBadges.emailSignature
              },
              badges
            },
            {
              headers: {
                Authorization: `Bearer ${token}`
              }
            }
          )
          .then(() => {
            setSpinnerBadges(false)
            setMessage('success')
            setIsVisible(true)
            setTimeout(() => {
              setIsVisible(false)
            }, 3000)
          })
          .catch((err) => {
            setSpinnerBadges(false)
            setMessage('error')
            setIsVisible(true)
            setTimeout(() => {
              setIsVisible(false)
            }, 3000)
            throw err
          })
      }

      if (e.target.id === 'form') {
        setSpinner(true)
        await axios
          .put(
            `${apiUrl}/users/${user.id}`,
            { ...formInfo, profileUpdate: true },
            {
              headers: {
                Authorization: `Bearer ${token}`
              }
            }
          )
          .then(() => {
            setSpinner(false)
            setMessage('success')
            setIsVisible(true)
            setTimeout(() => {
              setIsVisible(false)
            }, 3000)
          })
          .catch((err) => {
            setSpinner(false)
            setMessage('error')
            setIsVisible(true)
            setTimeout(() => {
              setIsVisible(false)
            }, 3000)
            // eslint-disable-next-line no-console
            console.log(err)
            throw err
          })
      }
    }
  }

  const checkImageDimensions = (file) => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => {
        URL.revokeObjectURL(img.src)
        if (img.width < 500 || img.height < 500) {
          reject('Image must be at least 500x500 pixels')
        } else {
          resolve()
        }
      }
      img.onerror = () => {
        URL.revokeObjectURL(img.src)
        reject('Failed to load image')
      }
      img.src = URL.createObjectURL(file)
    })
  }

  const checkImageResolution = (file) => {
    return new Promise((resolve) => {
      // JavaScript cannot reliably detect DPI, but we'll make a best effort
      // Most modern devices save images with at least 72dpi, so we'll focus on dimensions
      // This is a placeholder that always resolves for now
      resolve()
    })
  }

  const handleImageSelect = async (e) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      setOriginalFileName(file.name)
      setImageError('')

      let imageFile = file

      if (file.name.toLowerCase().endsWith('.heic')) {
        try {
          const buffer = await file.arrayBuffer()
          const convertedBuffer = await heicConvert({
            buffer: Buffer.from(buffer),
            format: 'JPEG',
            quality: 0.8
          })
          imageFile = new File([convertedBuffer], file.name.replace('.heic', '.jpg'), { type: 'image/jpeg' })
        } catch (error) {
          console.error('Error converting HEIC to JPEG:', error)
          setImageError('Failed to convert HEIC image. Please try another image format.')
          return
        }
      }

      try {
        await checkImageDimensions(imageFile)
        await checkImageResolution(imageFile)

        const reader = new FileReader()
        reader.addEventListener('load', () => {
          setSelectedImage(reader.result)
          setShowCropper(true)
        })
        reader.readAsDataURL(imageFile)
      } catch (error) {
        setImageError(error)
        // Reset the input file
        e.target.value = ''
      }
    }
  }

  const handleCropComplete = async (croppedImage) => {
    setShowCropper(false)
    setIsProcessing(true)

    try {
      await uploadCroppedImage(croppedImage)
      setMessage('success')
    } catch (err) {
      console.error(err)
      setMessage('error')
    } finally {
      setIsProcessing(false)
      setIsVisible(true)
      setTimeout(() => {
        setIsVisible(false)
      }, 3000)
    }
  }

  const uploadCroppedImage = async (croppedImage) => {
    const token = await Cookies.get('jwt')

    try {
      // Convert base64 to blob
      const response = await fetch(croppedImage)
      const blob = await response.blob()

      // Create a new File object with the original file name
      const file = new File([blob], originalFileName, { type: blob.type })

      const formData = new FormData()
      formData.append('files', file)

      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      }

      // Upload the file
      const uploadResponse = await axios.post(`${apiUrl}/upload`, formData, config)

      if (!uploadResponse.data || uploadResponse.data.length === 0) {
        throw new Error('File upload failed')
      }

      const profilePhoto = uploadResponse.data[0]

      // Update user profile with new photo
      const updateUserResponse = await axios.put(
        `${apiUrl}/users/${user.id}`,
        { photo: profilePhoto },
        { headers: { Authorization: `Bearer ${token}` } }
      )

      if (!updateUserResponse.data) {
        throw new Error('User profile update failed')
      }

      setUserData(updateUserResponse.data)
      setMessage('success')
    } catch (error) {
      console.error('Error in uploadCroppedImage:', error)
      if (error.response) {
        console.error('Response data:', error.response.data)
        console.error('Response status:', error.response.status)
      }
      throw error // Re-throw the error to be caught in handleCropComplete
    }
  }

  const closeModal = () => {
    setShowCropper(false)
    setSelectedImage(null)
  }

  const handleEnableBadge = (e) => {
    const newArr = badges.map((b) => {
      if (b.id === e.target.id) {
        return { ...b, enabled: !b.enabled }
      }
      return b
    })

    setBadges(newArr)
  }

  const updateBadgesOptions = (e) => {
    e.preventDefault()

    if (e.target.name === 'website') {
      setShowBadges({ ...showBadges, website: !showBadges.website })
    }

    if (e.target.name === 'emailSignature') {
      setShowBadges({ ...showBadges, emailSignature: !showBadges.emailSignature })
    }
  }

  const triggerInput = (inputRef, enteredValue, id) => {
    const input = inputRef.current
    if (input.type === 'select-one') {
      switch (enteredValue.toLowerCase()) {
        case 'alberta':
          enteredValue = 'Alberta'
          break
        case 'britishcolumbia':
          enteredValue = 'British Columbia'
          break
        case 'manitoba':
          enteredValue = 'Manitoba'
          break
        case 'newbrunswick':
          enteredValue = 'New Brunswick'
          break
        case 'newfoundlandandlabrador':
          enteredValue = 'Newfoundland And Labrador'
          break
        case 'northwestterritories':
          enteredValue = 'Northwest Territories'
          break
        case 'novascotia':
          enteredValue = 'Nova Scotia'
          break
        case 'nunavut':
          enteredValue = 'Nunavut'
          break
        case 'ontario':
          enteredValue = 'Ontario'
          break
        case 'princeedwardisland':
          enteredValue = 'Prince Edward Island'
          break
        case 'quebec':
          enteredValue = 'Quebec'
          break
        case 'saskatchewan':
          enteredValue = 'Saskatchewan'
          break
        case 'yukon':
          enteredValue = 'Yukon'
          break
        default:
          enteredValue = 'Select'
      }
      input.value = enteredValue
      const event = new Event('change', { bubbles: true })
      input.dispatchEvent(event)
    } else {
      input.value = enteredValue
      const event = new Event('input', { bubbles: true })
      input.dispatchEvent(event)
    }
  }

  const setAddress = (selectedAddress) => {
    for (const a in selectedAddress) {
      if (a === 'address') {
        triggerInput(addressRef, selectedAddress.address)
      }
      if (a === 'city') {
        triggerInput(cityRef, selectedAddress.city)
      }
      if (a === 'province') {
        triggerInput(provinceRef, selectedAddress.province)
      }
      if (a === 'postalCode') {
        triggerInput(postalCodeRef, selectedAddress.postalCode)
      }
      if (a === 'suiteUnit') {
        triggerInput(postalCodeRef, selectedAddress.suiteUnit)
      }
      if (a === 'provinceLisenceNumber') {
        triggerInput(provinceLicenseNumberRef, selectedAddress.provinceLicenseNumber)
      }
    }

    const { _id, __v, ...newBranch } = selectedAddress
    const newAddress = {
      city: newBranch.city,
      province: newBranch.province,
      address: newBranch.address,
      postalCode: newBranch.postalCode,
      suiteUnit: newBranch.suiteUnit,
      provinceLicenseNumber: newBranch.provinceLicenseNumber
    }

    setFormInfo({ ...formInfo, ...newAddress, branch: selectedAddress })
  }

  const showUserPhoto = () => {
    if (user && userData) {
      if (userData.photo && userData.photo.formats && userData.photo.formats.squared) {
        return userData.photo.formats.squared.url
      }

      if (userData.photo && userData.photo.formats && userData.photo.formats.medium) {
        return userData.photo.formats.medium.url
      }
      return userData.photo.url
    }
  }

  useEffect(() => {
    if (user) {
      setUserData(user)
    }
  }, [])

  useEffect(() => {
    if (form && form.current !== null && form.current !== undefined) {
      checkValues()
    }
  }, [userInfo])

  useEffect(() => {
    const allBadges = user.badges.map((b) => b)
    setBadges(allBadges)
  }, [user])

  useEffect(() => {
    setFormInfo({ ...formInfo, badges })
  }, [badges])

  useEffect(() => {
    if (userData) {
      setUserAuth((prevState) => ({
        ...prevState,
        userInfo: userData
      }))
    }
  }, [userData])

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <Layout>
        <Toast
          showToast={isVisible}
          toastType={message}
          message={
            message === 'success'
              ? 'Your profile has been updated successfuly!'
              : 'Ooops. Something went wrong. Please try again.'
          }
        />

        {showCropper && (
          <div className={style.cropperModal}>
            <div className={style.cropperContent}>
              <button onClick={closeModal} className={style.closeButton}>
                <UilTimes />
              </button>
              <ImageCropper image={selectedImage} onCropComplete={handleCropComplete} />
            </div>
          </div>
        )}
        <h1 className={style.ax_page_title}>Your Profile</h1>

        <section className={style.ax_content}>
          <div className={style.ax_left_column}>
            <Processing processing={isProcessing} />

            <div className={style.photo}>
              <img src={showUserPhoto()} alt={user.firstname} ref={userPhoto} />
            </div>
            <div className={style.ax_image_options}>
              <h4>Change Photo</h4>
              <form className={style.photoForm}>
                <input type="file" name="files" onChange={handleImageSelect} accept="image/*,.heic" />
                {imageError && <p className={style.errorText}>{imageError}</p>}
                <p style={{ fontSize: '12px', marginTop: '4px' }}>Image must be at least 500x500 pixels and 72dpi</p>
              </form>
            </div>
          </div>

          <div className={style.ax_right_column}>
            <div className={style.content_left}>
              <h3>Edit Your Info</h3>
              <p>All the data changed will reflect automatically on your Indi Website.</p>
              {
                /*show validation only if First Save is Complete*/
                fieldsValidation && fieldsValidation.length > 0 ? (
                  <section className={style.validation}>
                    <h3>The following fields are Required:</h3>
                    <ul>
                      {fieldsValidation.map((f) => (
                        <li key={f}>{f}</li>
                      ))}
                    </ul>
                  </section>
                ) : (
                  ''
                )
              }
              <form className={style.ax_form} ref={form}>
                <div className={style.ax_field}>
                  <label htmlFor="name">
                    First Name<span>*</span>
                  </label>
                  <input
                    type="text"
                    name="firstname"
                    id="firstname"
                    placeholder="Name"
                    defaultValue={user.firstname}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="middlename">Middle Name</label>
                  <input
                    type="text"
                    name="middlename"
                    id="middlename"
                    placeholder="Name"
                    defaultValue={user.middlename}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="name">
                    Last Name<span>*</span>
                  </label>
                  <input
                    type="text"
                    name="lastname"
                    id="lastname"
                    placeholder="Last Name"
                    defaultValue={user.lastname}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="name">
                    Legal Name<span>*</span>
                  </label>
                  <input
                    type="text"
                    name="legal"
                    id="legalName"
                    placeholder="Legal Name"
                    defaultValue={user.legalName}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="name">
                    Preferred Name<span>*</span>
                  </label>
                  <input
                    type="text"
                    name="legal"
                    id="preferredName"
                    placeholder="Legal Name"
                    defaultValue={user.preferredName}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="titles">Title After Name (e.g. AMP, BCC)</label>
                  <input
                    type="text"
                    name="titles"
                    id="titles"
                    placeholder="AMP, BCC, BCO"
                    defaultValue={user.titles}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="position">Position</label>
                  <input
                    type="text"
                    name="position"
                    id="position"
                    placeholder="I.E: Mortgage Broker, BCS"
                    defaultValue={user.position}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="license">License Number</label>
                  <input
                    type="text"
                    name="license"
                    id="license"
                    placeholder="I.E: #AXM003333"
                    defaultValue={user.license ? user.license : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="tshirtSize">T-Shirt Size</label>
                  <select
                    id="tshirtSize"
                    name="tshirtSize"
                    onChange={(e) => updateFormInfo(e)}
                    defaultValue={user.tshirtSize ? user.tshirtSize : ''}
                  >
                    <option value="XS">XS</option>
                    <option value="S">S</option>
                    <option value="M">M</option>
                    <option value="L">L</option>
                    <option value="XL">XL</option>
                    <option value="XXL">XXL</option>
                  </select>
                </div>
                <div className={style.ax_field}></div>

                <div className={style.ax_field}>
                  <label htmlFor="bio">
                    More About Me (Bio) <span>*</span>
                  </label>
                  <textarea
                    rows={6}
                    name="bio"
                    maxLength={800}
                    id="bio"
                    defaultValue={user.bio ? user.bio : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                  <div className={style.counter}>
                    <span>{`${charCount.bio}`} </span>
                    <span>/ 800</span>
                  </div>
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="additionalNotes">Additional Notes</label>
                  <textarea
                    rows={6}
                    name="additionalNotes"
                    id="additionalNotes"
                    maxLength={800}
                    defaultValue={user.additionalNotes ? user.additionalNotes : ''}
                    onChange={(e) => updateFormInfo(e, null)}
                  />
                  <div className={style.counter}>
                    <span>{`${charCount.note}`} </span>
                    <span>/ 800</span>
                  </div>
                </div>

                <div className={style.sepparator} />
                <h3 style={{ width: '100%', display: 'block' }}>Contact</h3>

                <div className={style.ax_field}>
                  <label htmlFor="email">
                    Login Email{' '}
                    <span className={style.popup}>
                      ?
                      <span className={style.popupContent}>
                        The login email is immutable. If you must change it, please open a support ticket explaining why
                        you'd like to do it and what is the new login email address.
                      </span>
                    </span>
                  </label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    placeholder="<EMAIL>"
                    defaultValue={user.email}
                    onChange={(e) => updateFormInfo(e)}
                    disabled
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="workEmail">Preferred Email Address</label>
                  <input
                    type="email"
                    name="workEmail"
                    id="workEmail"
                    placeholder="<EMAIL>"
                    defaultValue={user.workEmail}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <div className={style.phoneExt}>
                    <div>
                      <label htmlFor="phone">
                        Preferred Phone Number<span>*</span>
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        id="phone"
                        placeholder="************"
                        defaultValue={user && user.phone ? user.phone : ''}
                        onChange={(e) => updateFormInfo(e)}
                      />
                    </div>
                    <div>
                      <label htmlFor="ext">Ext.</label>
                      <input
                        type="tel"
                        name="ext"
                        id="ext"
                        placeholder="123"
                        defaultValue={user.ext}
                        onChange={(e) => updateFormInfo(e)}
                      />
                    </div>
                  </div>
                </div>

                <div className={style.ax_field}>
                  <div>
                    <label htmlFor="homePhone">Home Phone</label>
                    <input
                      type="tel"
                      name="homePhone"
                      id="homePhone"
                      placeholder="************"
                      defaultValue={user && user.homePhone ? user.homePhone : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </div>

                <div className={style.ax_field}>
                  <div>
                    <label htmlFor="cellPhone">Cell Phone</label>
                    <input
                      type="tel"
                      name="cellPhone"
                      id="cellPhone"
                      placeholder="************"
                      defaultValue={user && user.cellPhone ? user.cellPhone : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </div>

                <div className={style.ax_field}>
                  <div>
                    <label htmlFor="emergencyContact">Emergency Contact</label>
                    <input
                      type="text"
                      name="emergencyContact"
                      id="emergencyContact"
                      placeholder="John Doe"
                      defaultValue={user.emergencyContact}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </div>

                <div className={style.ax_field}>
                  <div>
                    <label htmlFor="emergencyPhone">Emergency Phone</label>
                    <input
                      type="tel"
                      name="emergencyPhone"
                      id="emergencyPhone"
                      placeholder="************"
                      defaultValue={user && user.emergencyPhone ? user.emergencyPhone : ''}
                      onChange={(e) => updateFormInfo(e)}
                    />
                  </div>
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="dietRestriction">Do you have any diet restriction?</label>
                  <input
                    type="text"
                    name="dietRestriction"
                    id="dietRestriction"
                    placeholder="E.g.: Celiac disease"
                    defaultValue={user.dietRestriction ? user.dietRestriction : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.sepparator} />

                <h3 style={{ width: '100%', display: 'block' }}>Websites, Social Media & Links</h3>

                <div className={style.ax_field}>
                  <label htmlFor="website">Website</label>
                  <input
                    type="text"
                    name="website"
                    id="website"
                    placeholder="I.E: https://indimortgage.ca"
                    defaultValue={user.website ? user.website : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="secondaryWebsite">Secondary Website</label>
                  <input
                    type="text"
                    name="secondaryWebsite"
                    id="secondaryWebsite"
                    placeholder="I.E: https://axiommortgage.ca"
                    defaultValue={user.secondaryWebsite ? user.secondaryWebsite : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="instagram">Instagram Page</label>
                  <input
                    type="text"
                    name="instagram"
                    id="instagram"
                    placeholder="I.E: https://instagram.com/jane-doe"
                    defaultValue={user.instagram ? user.instagram : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="facebook">Facebook Page</label>
                  <input
                    type="text"
                    name="facebook"
                    id="facebook"
                    placeholder="I.E: https://facebook.com/jane-doe"
                    defaultValue={user.facebook ? user.facebook : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="linkedin">Linkedin Page</label>
                  <input
                    type="text"
                    name="linkedin"
                    id="linkedin"
                    placeholder="I.E: https://linkedin.com/in/jane-doe"
                    defaultValue={user.linkedin ? user.linkedin : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="twitter">Twitter Page</label>
                  <input
                    type="text"
                    name="twitter"
                    id="twitter"
                    placeholder="I.E: https://twitter.com/jane-doe"
                    defaultValue={user.twitter ? user.twitter : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="youtube">Youtube Channel</label>
                  <input
                    type="text"
                    name="youtube"
                    id="youtube"
                    placeholder="I.E: https://youtube.com/c/jane-doe"
                    defaultValue={user.youtube ? user.youtube : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="applicationLink">Mortgage Application Link</label>
                  <input
                    type="text"
                    name="applicationLink"
                    id="applicationLink"
                    placeholder="I.E: https://mtgapp.scarlettnetwork.com/broker-name/home"
                    defaultValue={user.applicationLink ? user.applicationLink : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>
                <div className={style.ax_field}>
                  <label htmlFor="appointmentScheduleLink">Appointment Schedule Link (I.e. Calendly)</label>
                  <input
                    type="text"
                    name="appointmentScheduleLink"
                    id="appointmentScheduleLink"
                    placeholder="Calendly or Other"
                    defaultValue={user.appointmentScheduleLink ? user.appointmentScheduleLink : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="googleReviewsLink">Google Reviews Link</label>
                  <input
                    type="text"
                    name="googleReviewsLink"
                    id="googleReviewsLink"
                    placeholder="Link to your Google Reviews page"
                    defaultValue={user.googleReviewsLink ? user.googleReviewsLink : ''}
                    onChange={(e) => updateFormInfo(e)}
                  />
                </div>

                <div className={style.sepparator} />

                <h3 style={{ width: '100%', display: 'block' }}>Office Address</h3>

                <div style={{ width: '100%', display: 'block' }}>
                  <div className={style.ax_field}>
                    <AddressSelect branches={branches} action={(e) => setAddress(e)} />
                  </div>
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="address">
                    Office Address (Street) <span>*</span>
                  </label>
                  <input
                    ref={addressRef}
                    type="text"
                    name="address"
                    id="address"
                    placeholder="223 14 Street NW"
                    defaultValue={user.address ? user.address : ''}
                    onChange={(e) => updateFormInfo(e, 'officeAddress')}
                    disabled
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="suiteUnit">Suite/Unit</label>
                  <input
                    type="text"
                    name="suiteUnit"
                    id="suiteUnit"
                    placeholder="suite/unit"
                    defaultValue={user.suiteUnit ? user.suiteUnit : ''}
                    onChange={(e) => updateFormInfo(e, 'officeAddress')}
                    disabled
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="city">
                    City <span>*</span>
                  </label>
                  <input
                    ref={cityRef}
                    type="text"
                    name="city"
                    id="city"
                    placeholder="Calgary"
                    defaultValue={user.city ? user.city : ''}
                    onChange={(e) => updateFormInfo(e, 'officeAddress')}
                    disabled
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="province">
                    Province <span>*</span>
                  </label>
                  <select
                    ref={provinceRef}
                    name="province"
                    id="province"
                    placeholder="Alberta"
                    defaultValue={user.province ? user.province : ''}
                    onChange={(e) => updateFormInfo(e, 'officeAddress')}
                    disabled
                  >
                    <option value="">Select a Province</option>
                    <option value="Alberta">Alberta</option>
                    <option value="British Columbia">British Columbia</option>
                    <option value="Manitoba">Manitoba</option>
                    <option value="New Brunswick">New Brunswick</option>
                    <option value="Newfoundland And Labrador">New Foundland And Labrador</option>
                    <option value="Northwest Territories">Northwest Territories</option>
                    <option value="Nova Scotia">Nova Scotia</option>
                    <option value="Nunavut">Nunavut</option>
                    <option value="Ontario">Ontario</option>
                    <option value="Prince Edward Island">Prince Edward Island</option>
                    <option value="Quebec">Quebec</option>
                    <option value="Saskatchewan">Saskatchewan</option>
                    <option value="Yukon">Yukon</option>
                  </select>
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="postalCode">
                    Postal Code <span>*</span>
                  </label>
                  <input
                    ref={postalCodeRef}
                    type="text"
                    name="postalCode"
                    id="postalCode"
                    placeholder="T2N 1Z6"
                    defaultValue={user.postalCode ? user.postalCode : ''}
                    onChange={(e) => updateFormInfo(e, 'officeAddress')}
                    disabled
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="brokerageLicense">
                    Province License Number <span>*</span>
                  </label>
                  <input
                    ref={provinceLicenseNumberRef}
                    type="text"
                    name="provinceLicenseNumberRef"
                    id="provinceLicenseNumberRef"
                    placeholder=""
                    defaultValue={
                      user && user.branch && user.branch.provinceLicenseNumber ? user.branch.provinceLicenseNumber : ''
                    }
                    onChange={(e) => updateFormInfo(e, provinceLicenseNumberRef)}
                    disabled
                  />
                </div>

                <div className={style.sepparator} />

                <h3 style={{ width: '100%', display: 'block' }}>Personal Address</h3>

                <div className={style.ax_field}>
                  <label htmlFor="personalAddress">
                    Personal Address (Street) <span>*</span>
                  </label>
                  <input
                    ref={personalAddressRef}
                    type="text"
                    name="personalAddress"
                    id="personalAddress"
                    placeholder="223 14 Street NW"
                    defaultValue={user.personalAddress ? user.personalAddress : ''}
                    onChange={(e) => updateFormInfo(e, 'personalAddress')}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="personalSuiteUnit">Suite/Unit</label>
                  <input
                    type="text"
                    name="personalSuiteUnit"
                    id="personalSuiteUnit"
                    placeholder="suite/unit"
                    defaultValue={user.personalSuiteUnit ? user.personalSuiteUnit : ''}
                    onChange={(e) => updateFormInfo(e, 'personalAddress')}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="personalCity">
                    City <span>*</span>
                  </label>
                  <input
                    ref={personalCityRef}
                    type="text"
                    name="personalCity"
                    id="personalCity"
                    placeholder="Calgary"
                    defaultValue={user.personalCity ? user.personalCity : ''}
                    onChange={(e) => updateFormInfo(e, 'personalAddress')}
                  />
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="personalProvince">
                    Province <span>*</span>
                  </label>
                  <select
                    ref={personalProvinceRef}
                    name="personalProvince"
                    id="personalProvince"
                    placeholder="Alberta"
                    defaultValue={user.personalProvince ? user.personalProvince : ''}
                    onChange={(e) => updateFormInfo(e, 'personalAddress')}
                  >
                    <option value="">Select a Province</option>
                    <option value="Alberta">Alberta</option>
                    <option value="British Columbia">British Columbia</option>
                    <option value="Manitoba">Manitoba</option>
                    <option value="New Brunswick">New Brunswick</option>
                    <option value="Newfoundland And Labrador">New Foundland And Labrador</option>
                    <option value="Northwest Territories">Northwest Territories</option>
                    <option value="Nova Scotia">Nova Scotia</option>
                    <option value="Nunavut">Nunavut</option>
                    <option value="Ontario">Ontario</option>
                    <option value="Prince Edward Island">Prince Edward Island</option>
                    <option value="Quebec">Quebec</option>
                    <option value="Saskatchewan">Saskatchewan</option>
                    <option value="Yukon">Yukon</option>
                  </select>
                </div>

                <div className={style.ax_field}>
                  <label htmlFor="personalPostalCode">
                    Postal Code <span>*</span>
                  </label>
                  <input
                    ref={personalPostalCodeRef}
                    type="text"
                    name="personalPostalCode"
                    id="personalPostalCode"
                    placeholder="T2N 1Z6"
                    defaultValue={user.personalPostalCode ? user.personalPostalCode : ''}
                    onChange={(e) => updateFormInfo(e, 'personalAddress')}
                  />
                </div>
              </form>
              <div className={style.ax_field}>
                <button
                  id="form"
                  className={style.ax_btn_submit}
                  name="generate"
                  type="submit"
                  onClick={(e) => updateUser(e)}
                >
                  {spinner ? (
                    <>
                      <img src="/images/spinner-white.svg" alt="spinner" />
                      Saving
                    </>
                  ) : (
                    'Save Info'
                  )}
                </button>
              </div>
            </div>

            {/* <div className={style.qrCodes}>
              <h3>Languages</h3>

              {user && user.languages && user.languages.length > 0
                ? user.languages.map((l) => (
                    <ul>
                      <li key={l.language}>{l.language}</li>
                    </ul>
                  ))
                : ''}
            </div> */}

            {user.showBadges ? (
              <div className={style.badges}>
                <h3>Your Award Badges</h3>
                <Container>
                  <Row>
                    {badges.map((b) => (
                      <Col xs={12} sm={6} md={4} lg={3}>
                        <Card
                          key={b.id}
                          id={b.id}
                          color="lightgray"
                          size="vertical"
                          description={b.title}
                          openInBlank
                          hasButton
                          icon={b?.image?.url}
                          linkUrl={b?.image?.url}
                          buttonLabel="Download"
                          hasSwitcher
                          switcherValue={b.enabled}
                          switcherLabel="Enabled:"
                          switcherAction={(e) => handleEnableBadge(e)}
                          isLink
                          isDownload
                          fullWidth
                        />
                      </Col>
                    ))}
                  </Row>
                </Container>

                <div className={style.sepparator} />
                <div className={style.ax_badge_options}>
                  <h3>Show Award Badges on:</h3>
                  <form>
                    <Switcher
                      labelPos="top"
                      label="Your Indi Website"
                      name="website"
                      checked={showBadges.website}
                      action={(e) => updateBadgesOptions(e)}
                    />
                    <Switcher
                      labelPos="top"
                      label="Email Signature"
                      name="emailSignature"
                      checked={showBadges.emailSignature}
                      action={(e) => updateBadgesOptions(e)}
                    />
                  </form>
                </div>
                <div className={style.sepparator} />
                <button
                  id="badges"
                  className={style.ax_btn_submit}
                  name="generate"
                  type="submit"
                  onClick={(e) => updateUser(e)}
                >
                  {spinnerBadges ? (
                    <>
                      <img src="/images/spinner-white.svg" alt="spinner" />
                      Saving
                    </>
                  ) : (
                    'Save Badges Options'
                  )}
                </button>
              </div>
            ) : (
              ''
            )}

            {user && user.qrCodes ? (
              <div className={style.qrCodes}>
                <h3>Your QrCodes</h3>

                <Container>
                  <Row>
                    {user.qrCodes.map((qr) => (
                      <Col xs={12} sm={6} md={4} lg={3}>
                        <Card
                          key={qr.id}
                          color="lightgray"
                          size="vertical"
                          openInBlank
                          hasButton
                          icon={qr.qrImage}
                          description={qr.url}
                          linkUrl={qr.qrImage}
                          buttonLabel="Download"
                          isLink
                          isDownload
                          fullWidth
                        />
                      </Col>
                    ))}
                  </Row>
                </Container>
              </div>
            ) : (
              ''
            )}
          </div>
        </section>
      </Layout>
    </motion.div>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  ctx.res.setHeader('Cache-Control', 'no-store')
  const apiURL = `${process.env.API_URL}`
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(`${apiURL}/users/${userId}`, config)
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    const branchesData = await axios
      .get(`${apiURL}/branches`, config)
      .then((res) => {
        const brch = res.data
        const serializedData = serializeArray(brch)
        return serializedData
      })
      .catch((err) => {
        throw err
      })

    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData,
          branches: branchesData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Profile
