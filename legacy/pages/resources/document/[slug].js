import { useRef, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import axios from 'axios'
import nookies from 'nookies'
import { saveAs } from 'file-saver'
import dynamic from 'next/dynamic'
import { serializeJson } from '../../../helpers/serializeData'
import Button from '../../../components/Button'
import Layout from '../../../components/Layout'
import style from '../../../styles/Printables.module.scss'
import Head from 'next/head'

// Import DocViewer with no SSR to prevent window not defined errors
const DocViewer = dynamic(
  () =>
    import('react-doc-viewer').then((mod) => {
      const { DocViewerRenderers } = mod
      return {
        default: (props) => <mod.default {...props} pluginRenderers={DocViewerRenderers} />
      }
    }),
  { ssr: false }
)

const Document = () => {
  const viewer = useRef(null)
  const router = useRouter()
  const [iframeHeight, setIframeHeight] = useState('2000')
  const [docInfo, setDocInfo] = useState({})
  const [isClient, setIsClient] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const downloadDocument = () => {
    if (docInfo.title && docInfo.ext) {
      saveAs(router.query.file, `${docInfo.title}${docInfo.ext}`)
    }
  }

  const calcIframeHeight = () => {
    if (typeof window !== 'undefined') {
      return Math.max((window.innerHeight - 70) * 1.41, 1200)
    }
    return 2000
  }

  const showDocument = () => {
    if (!isClient || !docInfo.uri) return null
    if (isLoading) return <div>Loading document...</div>

    const officeExtensions = ['docx', 'xlsx', 'xls', 'doc', 'ppt', 'pptx']

    if (officeExtensions.includes(docInfo.ext)) {
      return (
        <div style={{ width: '100%', maxWidth: '1000px', height: `${iframeHeight}px` }}>
          <DocViewer
            documents={[{ uri: docInfo.uri, fileName: `${docInfo.title}${docInfo.ext}` }]}
            style={{ height: '100%', width: '100%' }}
            config={{
              header: {
                disableHeader: false,
                disableFileName: false,
                retainURLParams: true
              }
            }}
          />
        </div>
      )
    } else {
      return (
        <iframe
          width="100%"
          height={`${iframeHeight}px`}
          title={docInfo.title}
          src={docInfo.uri}
          ref={viewer}
          style={{ maxWidth: '1000px' }}
          type="application/pdf"
        />
      )
    }
  }

  useEffect(() => {
    setIsClient(true)
    if (router.query.title && router.query.file) {
      const fileInfo = {
        title: router.query.title.replace(/\-/g, ' ').toLowerCase(),
        ext: router.query.ext ? router.query.ext.toLowerCase() : '',
        uri: router.query.file
      }
      setDocInfo(fileInfo)
      setIsLoading(false)
    }

    const handleResize = () => {
      setIframeHeight(calcIframeHeight())
    }

    setIframeHeight(calcIframeHeight())
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [router.query])

  return (
    <Layout>
      <Head>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
      </Head>
      <h1 className={style.ax_page_title}>{docInfo.title}</h1>
      {showDocument()}
      <div style={{ marginTop: '32px' }} />
      <Button action={downloadDocument} label="Download" color="highlight" />
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  ctx.res.setHeader('Pragma', 'no-cache')
  ctx.res.setHeader('Expires', '0')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  if (!ctx.req.headers.cookie || !jwt) {
    return {
      redirect: {
        destination: '/',
        permanent: false
      }
    }
  }

  let userData
  if (userId) {
    try {
      const res = await axios.get(
        `${apiURL}/users/${userId}`,

        config
      )
      userData = serializeJson(res.data)
    } catch (err) {
      console.error('Error fetching user data:', err)
      return {
        redirect: {
          destination: '/error',
          permanent: false
        }
      }
    }
  }

  if (userData.isOnboarding) {
    return {
      redirect: {
        destination: 'https://welcome.indimortgage.ca',
        permanent: false
      }
    }
  }

  return {
    props: {
      user: userData
    }
  }
}

export default Document
