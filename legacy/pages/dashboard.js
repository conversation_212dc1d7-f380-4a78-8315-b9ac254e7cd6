import nookies from 'nookies'
import axios from 'axios'
import { useRouter } from 'next/router'
import { useContext, useState, useEffect, useCallback } from 'react'
import { serializeJson } from '../helpers/serializeData'
import Layout from '../components/Layout'
import style from '../styles/Dashboard.module.scss'
import CardTile from '../components/CardTile'
import AuthContext from '../context/authContext'
import ProcessingBranded from '../components/ProcessingBranded'
import withAuth from '../auth/withAuth'
import { Container, Row, Col } from 'react-grid-system'

const Dashboard = () => {
  const router = useRouter()
  const { userAuth } = useContext(AuthContext)
  const [isLoading, setIsLoading] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [userInfo, setUserInfo] = useState(() => {
    return userAuth?.userInfo || null
  })

  useEffect(() => {
    if (userAuth?.userInfo) {
      setUserInfo(userAuth.userInfo)
    }
  }, [userAuth?.userInfo])

  useEffect(() => {
    setIsMounted(true)
    return () => setIsMounted(false)
  }, [])

  useEffect(() => {
    if (!isMounted) return

    const handleStart = () => {
      setIsLoading(true)
    }

    const handleComplete = () => {
      if (isMounted) {
        setIsLoading(false)
      }
    }

    const handleError = () => {
      if (isMounted) {
        setIsLoading(false)
      }
    }

    router.events.on('routeChangeStart', handleStart)
    router.events.on('routeChangeComplete', handleComplete)
    router.events.on('routeChangeError', handleError)

    return () => {
      router.events.off('routeChangeStart', handleStart)
      router.events.off('routeChangeComplete', handleComplete)
      router.events.off('routeChangeError', handleError)
      setIsLoading(false)
    }
  }, [router, isMounted])

  const handleNavigation = useCallback(
    async (path, isExternal = false) => {
      if (isLoading) return

      try {
        if (isExternal) {
          window.open(path, '_blank', 'noopener,noreferrer')
        } else {
          setIsLoading(true)
          await router.push(path)
        }
      } catch (error) {
        console.error('Navigation error:', error)
        setIsLoading(false)
      }
    },
    [isLoading, router]
  )

  if (!isMounted) {
    return null
  }

  if (!userInfo && !isLoading) {
    return <ProcessingBranded processing message="Loading user information..." />
  }

  return (
    <Layout>
      <Container fluid style={{ paddingLeft: '0px', paddingRight: '0px' }}>
        {isLoading ? (
          <ProcessingBranded processing message="Loading..." />
        ) : (
          <div className={style.dashboard_grid}>
            <h2 className={style.titleSection}>Dashboard</h2>
            <Container fluid>
              <Row>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-calendar-3d.png"
                    title="Calendar"
                    color="gradientred"
                    clickEvent={() => handleNavigation('/company-calendar')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-events-3d.png"
                    title="Events"
                    color="gradientred"
                    clickEvent={() => handleNavigation('https://indievents.ca', true)}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-notifications-3d.png"
                    title="Notifications"
                    color="gradientred"
                    clickEvent={() => handleNavigation('/notifications')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-indi-app-3d.png"
                    title="Indi App"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/indi-app')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-marketing-3d.png"
                    title="Marketing"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/marketing')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-socialmedia-3d.png"
                    title="Social Media"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/social-media')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-custom-shop-3d.png"
                    title="Custom Shop"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/custom-shop')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-gift-3d.png"
                    title="Operation Impact"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/client-gift')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-qr-code-3d.png"
                    title="QR Codes"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/qr-codes')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-printables-3d.png"
                    title="Printables"
                    color="gradientyellow"
                    clickEvent={() => handleNavigation('/printables')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-payroll-3d.png"
                    title="Payroll"
                    color="gradientgreen"
                    clickEvent={() => handleNavigation('https://portal.scarlettnetwork.com/portal/dashboard', true)}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-branding-3d.png"
                    title="Branding"
                    color="gradientgreen"
                    clickEvent={() => handleNavigation('/branding')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-resources-3d.png"
                    title="Resources"
                    color="gradientgreen"
                    clickEvent={() => handleNavigation('/resources')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-company-directory-3d.png"
                    title="Company Directory"
                    color="gradientgreen"
                    clickEvent={() => handleNavigation('/company-directory')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-lenders-3d.png"
                    title="Lenders"
                    color="gradientteal"
                    clickEvent={() => handleNavigation('/lenders')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-realtors-3d.png"
                    title="Realtors"
                    color="gradientteal"
                    clickEvent={() => handleNavigation('/realtors')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-listingsheet-3d.png"
                    title="Listing Sheet"
                    color="gradientblue"
                    clickEvent={() => handleNavigation('/listing-sheet')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-indisites-3d.png"
                    title="My Indi Site"
                    color="gradientblue"
                    clickEvent={() => handleNavigation('/indi-sites')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-technology-3d.png"
                    title="Technology"
                    color="gradientblue"
                    clickEvent={() => handleNavigation('/technology')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-compliance-3d.png"
                    title="Compliance"
                    color="gradientblue"
                    clickEvent={() => handleNavigation('/compliance')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-fintrac-3d.png"
                    title="FINTRAC"
                    color="gradientblue"
                    clickEvent={() => handleNavigation('/fintrac')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-academy-3d.png"
                    title="Indi Academy"
                    color="gradientdark"
                    clickEvent={() => handleNavigation('https://academy.indimortgage.ca', true)}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-email-signature-3d.png"
                    title="Email Signature"
                    color="gradientdark"
                    clickEvent={() => handleNavigation('/email-signature')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-newsletter-3d.png"
                    title="Newsletter"
                    color="gradientdark"
                    clickEvent={() => handleNavigation('/newsletter-archive')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-tutorials-3d.png"
                    title="Tutorials & Videos"
                    color="gradientdark"
                    clickEvent={() => handleNavigation('/tutorials')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-group-benefits-3d.png"
                    title="Group Benefits"
                    color="gradientgrey"
                    clickEvent={() => handleNavigation('/group-benefits')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-indi-cares-3d.png"
                    title="indi Cares"
                    color="gradientgrey"
                    clickEvent={() => handleNavigation('/indi-cares')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-fitclub-3d.png"
                    title="Fit Club"
                    color="gradientgrey"
                    clickEvent={() => handleNavigation('/indi-fit-club')}
                  />
                </Col>
                <Col xs={3} md={2} lg={1}>
                  <CardTile
                    icon="./images/tile-icon-awards-3d.png"
                    title="Awards"
                    color="gradientgrey"
                    clickEvent={() => handleNavigation('/awards')}
                  />
                </Col>
              </Row>
            </Container>
          </div>
        )}
      </Container>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  try {
    ctx.res.setHeader('Cache-Control', 'no-store')

    const tokens = nookies.get(ctx)
    const { jwt, userId } = tokens

    // Check for required tokens
    if (!jwt || !userId) {
      return {
        redirect: {
          destination: '/',
          permanent: false
        }
      }
    }

    return {
      props: {
        isAuthenticated: true
      }
    }
  } catch (error) {
    console.error('Server Side Props Error:', error)
    return {
      redirect: {
        destination: '/',
        permanent: false
      }
    }
  }
}

export default withAuth(Dashboard)
