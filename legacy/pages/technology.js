import nookies from 'nookies'
import axios from 'axios'
import Layout from '../components/Layout'
import { serializeArray, serializeJson } from '../helpers/serializeData'
import style from '../styles/Technology.module.scss'
import Card from '../components/Card'
import formatUrl from '../helpers/formatUrl'

const Dashboard = (props) => {
  const { techs } = props

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Technology</h1>
      <section className={style.techList}>
        <div className={style.ax_card_list}>
          {techs.map((tech) => (
            <Card
              key={tech.title}
              hasButton
              buttonLabel="Go To"
              icon={tech.logo.url}
              title={tech.title}
              description={tech.description}
              isLink
              linkUrl={formatUrl(tech.link)}
              openInBlank
            />
          ))}
        </div>
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const techData = await axios
        .get(`${apiURL}/technologies`, config)
        .then((res) => {
          const { data } = res
          const serializedData = serializeArray(data)
          return serializedData
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          techs: techData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Dashboard
