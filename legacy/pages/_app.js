import Head from 'next/head'
import '../styles/globals.css'
import { useState, useEffect } from 'react'
import { AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/router'
import AuthContext from '../context/authContext'
import { authStatus } from '../auth/auth'
import Script from 'next/script'
import ModalInfoContext from '../context/modalInfoContext'
import App from 'next/app'
import ProcessingBranded from '../components/ProcessingBranded'
import axios from 'axios'
import Cookies from 'js-cookie'
import MetaHeaders from '../components/MetaHeaders'

// Add this function after the imports, before the MyApp component
function injectErrorHandler() {
  // Only run on client side
  if (typeof window !== 'undefined') {
    // Save the original error handler
    const originalErrorHandler = window.onerror

    // Global error handler for uncaught exceptions
    window.onerror = function (message, source, lineno, colno, error) {
      console.error('Global error caught:', { message, source, lineno, colno, error })

      // Check if it matches the auth error pattern
      if (message && message.includes('cannot destructure property') && message.includes("'auth'")) {
        // Create recovery UI
        const errorContainer = document.createElement('div')
        errorContainer.id = 'error-recovery-container'
        errorContainer.style.position = 'fixed'
        errorContainer.style.top = '0'
        errorContainer.style.left = '0'
        errorContainer.style.width = '100%'
        errorContainer.style.height = '100%'
        errorContainer.style.backgroundColor = '#f5f5f5'
        errorContainer.style.zIndex = '9999'
        errorContainer.style.display = 'flex'
        errorContainer.style.flexDirection = 'column'
        errorContainer.style.alignItems = 'center'
        errorContainer.style.justifyContent = 'center'
        errorContainer.style.padding = '20px'
        errorContainer.style.textAlign = 'center'
        errorContainer.style.fontFamily = 'Arial, sans-serif'

        // Add logo
        const logo = document.createElement('img')
        logo.src = '/images/indi-central-logo.svg'
        logo.alt = 'Indi Central Logo'
        logo.style.marginBottom = '30px'
        logo.style.maxWidth = '200px'

        // Add content container
        const contentContainer = document.createElement('div')
        contentContainer.style.backgroundColor = 'white'
        contentContainer.style.padding = '30px'
        contentContainer.style.borderRadius = '10px'
        contentContainer.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)'
        contentContainer.style.maxWidth = '500px'

        // Add heading
        const heading = document.createElement('h1')
        heading.textContent = 'Application Error'
        heading.style.color = '#333'
        heading.style.marginTop = '0'

        // Add description
        const description = document.createElement('p')
        description.textContent =
          "We've detected an error with the application that might be caused by outdated cached files."
        description.style.color = '#666'
        description.style.lineHeight = '1.5'

        const instruction = document.createElement('p')
        instruction.textContent = 'Please try these solutions:'
        instruction.style.color = '#666'
        instruction.style.lineHeight = '1.5'

        // Add buttons container
        const buttonsContainer = document.createElement('div')
        buttonsContainer.style.marginTop = '20px'
        buttonsContainer.style.display = 'flex'
        buttonsContainer.style.flexDirection = 'column'
        buttonsContainer.style.gap = '15px'

        // Add reload button
        const reloadButton = document.createElement('button')
        reloadButton.textContent = 'Reload Application'
        reloadButton.style.backgroundColor = '#0070f3'
        reloadButton.style.color = 'white'
        reloadButton.style.border = 'none'
        reloadButton.style.padding = '10px 20px'
        reloadButton.style.borderRadius = '5px'
        reloadButton.style.cursor = 'pointer'
        reloadButton.style.fontSize = '16px'
        reloadButton.onclick = function () {
          const timestamp = Date.now()
          window.location.href = `${window.location.pathname}?reload=${timestamp}`
        }

        // Add clear data button
        const clearButton = document.createElement('button')
        clearButton.textContent = 'Clear Site Data & Reload'
        clearButton.style.backgroundColor = '#ff4b4b'
        clearButton.style.color = 'white'
        clearButton.style.border = 'none'
        clearButton.style.padding = '10px 20px'
        clearButton.style.borderRadius = '5px'
        clearButton.style.cursor = 'pointer'
        clearButton.style.fontSize = '16px'
        clearButton.onclick = function () {
          if ('caches' in window) {
            caches.keys().then(function (keyList) {
              return Promise.all(
                keyList.map(function (key) {
                  return caches.delete(key)
                })
              )
            })
          }
          localStorage.clear()
          sessionStorage.clear()
          setTimeout(function () {
            const timestamp = Date.now()
            window.location.href = `${window.location.pathname}?reload=${timestamp}`
          }, 500)
        }

        // Add note
        const note = document.createElement('p')
        note.textContent =
          'If the problem persists, please try opening the site in an incognito window or contact support.'
        note.style.fontSize = '14px'
        note.style.color = '#888'
        note.style.marginTop = '15px'

        // Assemble the UI
        buttonsContainer.appendChild(reloadButton)
        buttonsContainer.appendChild(clearButton)
        buttonsContainer.appendChild(note)

        contentContainer.appendChild(heading)
        contentContainer.appendChild(description)
        contentContainer.appendChild(instruction)
        contentContainer.appendChild(buttonsContainer)

        errorContainer.appendChild(logo)
        errorContainer.appendChild(contentContainer)

        // Add to document
        document.body.appendChild(errorContainer)

        // Prevent further error handling
        return true
      }

      // Call the original error handler if it existed
      if (typeof originalErrorHandler === 'function') {
        return originalErrorHandler(message, source, lineno, colno, error)
      }

      // Return false to indicate we want the browser to handle the error
      return false
    }

    // Also handle unhandled promise rejections
    window.addEventListener('unhandledrejection', function (event) {
      console.error('Unhandled promise rejection:', event.reason)
      // We don't inject UI here as the onerror handler should catch any errors that bubble up
    })
  }
}

const MyApp = ({ Component, pageProps }) => {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [userAuth, setUserAuth] = useState({
    isAuth: false,
    userInfo: null,
    notifications: [],
    onePages: [],
    initialized: false,
    error: null
  })
  const [modalInfo, setModalInfo] = useState({
    showModal: false,
    content: null,
    userVCard: null
  })

  // Initialize auth state and fetch user data
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we're on the client side
        if (typeof window === 'undefined') return

        // First set a loading state to prevent premature renders
        setUserAuth((prev) => ({
          ...prev,
          initialized: false
        }))

        const isAuthenticated = authStatus()
        const token = Cookies.get('jwt')
        const userId = Cookies.get('userId')

        if (isAuthenticated && token && userId) {
          const config = {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }

          try {
            // Fetch all required data in parallel
            const [userResponse, notificationsResponse, onePagesResponse] = await Promise.all([
              axios.get(`${process.env.NEXT_PUBLIC_API_URL}/users-permissions/users-light/${userId}`, config),
              axios.get(`${process.env.NEXT_PUBLIC_API_URL}/notifications`, config),
              axios.get(`${process.env.NEXT_PUBLIC_API_URL}/one-pages`, config)
            ])

            // Only set initialized to true after all data is fetched
            setUserAuth((prev) => ({
              ...prev,
              isAuth: isAuthenticated,
              userInfo: userResponse.data,
              notifications: notificationsResponse.data || [],
              onePages: onePagesResponse.data || [],
              initialized: true
            }))
          } catch (error) {
            console.error('Error fetching data:', error)
            setUserAuth((prev) => ({
              ...prev,
              error: 'Failed to fetch data',
              isAuth: false, // Set to false on error to prevent redirect loops
              initialized: true
            }))
          }
        } else {
          // Set a consistent state for unauthenticated users
          setUserAuth((prev) => ({
            ...prev,
            isAuth: false,
            userInfo: null,
            notifications: [],
            onePages: [],
            initialized: true
          }))
        }
      } catch (error) {
        setUserAuth((prev) => ({
          ...prev,
          error: 'Failed to initialize authentication',
          isAuth: false,
          initialized: true
        }))
      }
    }

    initializeAuth()
  }, [])

  // Version control to ensure clients get the latest app version
  useEffect(() => {
    // App version - change this with every significant update
    const APP_VERSION = '1.0.1'

    // Check if we're on the client side
    if (typeof window === 'undefined') return

    // Check stored version
    const storedVersion = localStorage.getItem('app_version')

    if (storedVersion !== APP_VERSION) {
      console.log(`New version detected: ${APP_VERSION} (was: ${storedVersion || 'none'})`)

      // Clear local storage
      localStorage.clear()

      // Store new version
      localStorage.setItem('app_version', APP_VERSION)

      // Clear any caches through the service worker
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then((registration) => {
          registration.active.postMessage({ type: 'SKIP_WAITING' })
        })

        // Force reload the page to ensure we get fresh content
        // Use a small timeout to allow the service worker to activate
        setTimeout(() => {
          console.log('Reloading page to apply new version')
          window.location.reload(true)
        }, 1000)
      }
    }
  }, [])

  // Handle route changes and ensure authentication state is preserved during navigation
  useEffect(() => {
    const handleStart = () => {
      setIsLoading(true)
    }

    const handleComplete = () => {
      setIsLoading(false)

      // Re-check authentication on route change completion
      // This helps prevent auth state issues when using browser back/forward buttons
      const isAuthenticated = authStatus()
      if (isAuthenticated) {
        // Update auth state minimally to ensure it's never undefined during navigation
        setUserAuth((prev) => {
          // Only update if not already initialized to avoid unnecessary re-renders
          if (!prev.initialized || !prev.isAuth) {
            return {
              ...prev,
              isAuth: true
              // Don't change initialized status here as it may trigger additional data fetching
            }
          }
          return prev
        })
      }
    }

    const handleError = () => {
      setIsLoading(false)
    }

    router.events.on('routeChangeStart', handleStart)
    router.events.on('routeChangeComplete', handleComplete)
    router.events.on('routeChangeError', handleError)

    return () => {
      router.events.off('routeChangeStart', handleStart)
      router.events.off('routeChangeComplete', handleComplete)
      router.events.off('routeChangeError', handleError)
      setIsLoading(false)
    }
  }, [router])

  // Register service worker
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js').then(
        function (registration) {
          console.log('Service Worker registration successful with scope: ', registration.scope)
        },
        function (err) {
          console.log('Service Worker registration failed: ', err)
        }
      )
    }
  }, [])

  // Initialize global error handler
  useEffect(() => {
    injectErrorHandler()
  }, [])

  // Error boundary for auth initialization
  if (userAuth.error) {
    return <ProcessingBranded processing message={userAuth.error} />
  }

  // Wait for auth to initialize
  if (!userAuth.initialized) {
    return <ProcessingBranded processing message="Initializing..." />
  }

  return (
    <>
      <MetaHeaders title="Indi Central" />

      <AuthContext.Provider value={{ userAuth, setUserAuth }}>
        <AnimatePresence mode="wait">
          <ModalInfoContext.Provider value={{ modalInfo, setModalInfo }}>
            {isLoading && <ProcessingBranded processing message="Loading..." />}
            <Component {...pageProps} key={router.asPath} />
          </ModalInfoContext.Provider>
        </AnimatePresence>
      </AuthContext.Provider>

      <Script
        key="script"
        id="ze-snippet"
        src="https://static.zdassets.com/ekr/snippet.js?key=06a13d7a-7677-49ec-a72b-8dea13a22624"
        strategy="lazyOnload"
      />
    </>
  )
}

MyApp.getInitialProps = async (appContext) => {
  const appProps = await App.getInitialProps(appContext)

  if (appContext.ctx.res) {
    appContext.ctx.res.setHeader('Cache-Control', 'public, s-maxage=10, stale-while-revalidate=59')
  }

  return { ...appProps }
}

export default MyApp
