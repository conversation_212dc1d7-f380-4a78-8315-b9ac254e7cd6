import { motion } from 'framer-motion'
import { useRouter } from 'next/router'
import { useContext, useEffect, useState } from 'react'
import Login from '../components/Login'
import AuthContext from '../context/authContext'

const Index = () => {
  const { userAuth } = useContext(AuthContext)
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  const [redirectAttempted, setRedirectAttempted] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  useEffect(() => {
    // Only redirect if auth is fully initialized and we haven't tried redirecting already
    // This prevents redirect loops
    if (userAuth?.initialized && userAuth.isAuth && !redirectAttempted) {
      setRedirectAttempted(true)
      router.push('/dashboard')
    }
  }, [userAuth, router, redirectAttempted])

  // Don't render anything on the server
  if (!isClient) {
    return null
  }

  // If we're authenticated and already trying to redirect, show a loading state
  if (userAuth?.isAuth && redirectAttempted) {
    return (
      <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
          <p></p>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <main>
        <Login />
      </main>
    </motion.div>
  )
}

export default Index
