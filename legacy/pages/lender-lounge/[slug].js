import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import Button from '../../components/Button'
import { UilDownloadAlt } from '@iconscout/react-unicons'
import { Container, Row, Col } from 'react-grid-system'
import style from '../../styles/LenderLounge.module.scss'

const Lender = (props) => {
  // eslint-disable-next-line react/destructuring-assignment
  const lender = props.data[0]

  return (
    <Layout>
      <Container>
        <Row>
          <Col>
            <section className={style.ax_post_title}>
              <h1 className={style.ax_page_title}>{lender.name}</h1>
            </section>
          </Col>
        </Row>
        <Row className={style.ax_wrapper}>
          <Col sm={12} md={3}>
            <img src={lender.logo.url} className={style.logo} alt="logo" />
            <Button label="Download" download isWide isLink linkPath={lender.logo.url} color="highlight" wide />
          </Col>
          <Col sm={12} md={9}>
            <section className={style.ax_post_info}>
              <div className={style.ax_post_details}>
                <p>
                  <strong>Name: </strong>
                  {lender.name}
                </p>
                <p>
                  <strong>Submission Agent: </strong>
                  {lender.submissionAgent}
                </p>
                <p>
                  <strong>Setup Requirements: </strong>
                  {lender.setupRequirements}
                </p>
                <p>
                  <strong>BDM Name: </strong>
                  {lender.bdmName}
                </p>
                <p>
                  <strong>BDM Email: </strong>
                  {lender.bdmEmail}
                </p>
                <p>
                  <strong>BDM Phone: </strong>
                  {lender.bdmPhone}
                </p>
                <p>
                  <strong>Underwriter Name: </strong>
                  {lender.underwriterName}
                </p>
                <p>
                  <strong>Underwriter Email: </strong>
                  {lender.underwriterEmail}
                </p>
                <p>
                  <strong>Underwriter Phone: </strong>
                  {lender.underwriterPhone}
                </p>
                <p>
                  <strong>Portal Website: </strong>
                  {lender.portalWebsite}
                </p>
                <p>
                  <strong>User ID: </strong>
                  {lender.userId}
                </p>
                <p>
                  <strong>Password: </strong>
                  {lender.password}
                </p>

                <p>
                  <strong>Email Notification: </strong>
                  {lender.emailNotification}
                </p>
                <p>
                  <strong>Documents: </strong>
                  {lender.documents}
                </p>
                <p>
                  <strong>Notes: </strong>
                  {lender.notes}
                </p>
              </div>
            </section>
          </Col>
        </Row>
      </Container>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const data = await axios
        .get(`${apiURL}/lenders?slug_eq=${ctx.params.slug}`, {
          headers: {
            Authorization: `Bearer ${jwt}`
          }
        })
        .then((res) => {
          const lenderData = serializeJson(res.data)
          return lenderData
        })
        .catch((error) => {
          throw error
        })

      return { props: { data } }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Lender
