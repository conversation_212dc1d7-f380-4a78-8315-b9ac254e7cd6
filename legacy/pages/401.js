import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Layout from '../components/Layout'
import Button from '../components/Button'
import { UilArrowLeft, UilLockAccess, UilSignin } from '@iconscout/react-unicons'
import styles from '../styles/ErrorPage.module.scss'

export default function UnauthorizedError() {
  const router = useRouter()
  const [errorDetails, setErrorDetails] = useState('')

  useEffect(() => {
    // Extract error details from URL query parameters
    const { message, details } = router.query
    if (message) {
      setErrorDetails(message)
    } else if (details) {
      try {
        const parsedDetails = JSON.parse(details)
        setErrorDetails(parsedDetails.message || 'Authentication required')
      } catch (e) {
        setErrorDetails(details)
      }
    }
  }, [router.query])

  return (
    <Layout fullpage>
      <Head>
        <title>401: Unauthorized</title>
      </Head>
      <div className={styles.errorContainer}>
        <div className={styles.errorBox}>
          <div className={styles.errorIcon}>
            <UilLockAccess size="64" />
          </div>
          <h1 className={styles.errorCode}>401</h1>
          <h2 className={styles.errorTitle}>Unauthorized</h2>

          <p className={styles.errorDescription}>
            You need to be logged in to access this page. Your session may have expired.
          </p>

          {errorDetails && (
            <div className={styles.errorDetails}>
              <p>{errorDetails}</p>
            </div>
          )}

          <div className={styles.actions}>
            <Button
              sizing="medium"
              color="highlight"
              label="Log In"
              action={() => router.push('/')}
              icon={<UilSignin />}
              iconPosition="left"
            />
            <Button
              sizing="medium"
              color="dark"
              label="Go Back"
              action={() => router.back()}
              icon={<UilArrowLeft />}
              iconPosition="left"
            />
          </div>
        </div>
      </div>
    </Layout>
  )
}
