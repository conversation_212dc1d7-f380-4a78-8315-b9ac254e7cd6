import { useState } from 'react'
import { useRouter } from 'next/router'
import axios from 'axios'
import { UilEyeSlash, UilEye } from '@iconscout/react-unicons'
import style from '../styles/Password.module.scss'

const NewPassword = () => {
  const router = useRouter()
  const { code } = router.query

  const [newPassword, setNewPassword] = useState('')
  const [confirmNewPassword, setConfirmNewPassword] = useState('')
  const [message, setMessage] = useState({ type: 'neutral', text: '' })
  const [seeNew, setSeeNew] = useState(false)
  const [seeConfirm, setSeeConfirm] = useState(false)
  const [spinner, setSpinner] = useState(false)

  const ApiUrl = process.env.NEXT_PUBLIC_API_URL

  const passValidation = () => {
    // Check minimum length for new password
    if (newPassword.length < 8) {
      setMessage({
        type: 'error',
        text: 'New password must be at least 8 characters long'
      })
      setSpinner(false)
      return false
    }

    // Check minimum length for confirmation
    if (confirmNewPassword.length < 8) {
      setMessage({
        type: 'error',
        text: 'Confirmation password must be at least 8 characters long'
      })
      setSpinner(false)
      return false
    }

    // Check for spaces
    if (newPassword.includes(' ') || confirmNewPassword.includes(' ')) {
      setMessage({
        type: 'error',
        text: 'Passwords cannot contain spaces'
      })
      setSpinner(false)
      return false
    }

    // Check if passwords match
    if (newPassword !== confirmNewPassword) {
      setMessage({
        type: 'error',
        text: 'Passwords do not match. Please make sure both entries are identical'
      })
      setSpinner(false)
      return false
    }

    return true
  }

  const handlePasswordChange = (e, setPassword) => {
    setPassword(e.target.value)
    setMessage({ type: 'neutral', text: '' }) // Clear error message on input change
  }

  const resetPassword = async (e) => {
    e.preventDefault()
    setSpinner(true)

    const validation = passValidation()

    if (validation) {
      try {
        await axios.post(`${ApiUrl}/auth/reset-password`, {
          code,
          password: newPassword,
          passwordConfirmation: confirmNewPassword
        })
        setMessage({
          type: 'success',
          text: 'Password reset successful! You will be redirected to login...'
        })
        setSpinner(false)
        setTimeout(() => {
          router.push('/')
        }, 3000)
      } catch (error) {
        setMessage({
          type: 'error',
          text: 'Failed to reset password. Please try again or contact support if the issue persists.'
        })
        setSpinner(false)
      }
    }
  }

  const seePassword = (e, field) => {
    e.preventDefault()
    if (field === 'new') setSeeNew(!seeNew)
    if (field === 'confirm') setSeeConfirm(!seeConfirm)
  }

  const getMessageStyles = () => {
    switch (message.type) {
      case 'error':
        return style.error_message
      case 'success':
        return style.success_message
      default:
        return ''
    }
  }

  return (
    <section className={`${style.ax_section} ${style.ax_form_container}`}>
      <img
        src="/images/indi-central-logo.svg"
        alt="indi central logo"
        style={{ width: '180px', maxWidth: '180px', height: 'auto' }}
      />
      <h1 className={style.ax_page_title}>Reset Password</h1>
      <form className={style.ax_form}>
        <p>Insert and confirm your new password.</p>
        <p>
          <strong>Required:</strong>
        </p>
        <ul>
          <li>Be at least 8 characters long</li>
          <li>No spaces</li>
        </ul>
        <p>
          <strong>Consider including at least one of the following (optional):</strong>
        </p>
        <ul>
          <li>Lowercase letter</li>
          <li>Uppercase letter</li>
          <li>Number</li>
          <li>Symbol</li>
        </ul>
        <div className={style.ax_field}>
          <label htmlFor="password">New Password</label>
          {seeNew ? (
            <input
              type="text"
              name="password"
              value={newPassword}
              onChange={(e) => handlePasswordChange(e, setNewPassword)}
            />
          ) : (
            <input
              type="password"
              name="password"
              value={newPassword}
              onChange={(e) => handlePasswordChange(e, setNewPassword)}
            />
          )}
          <button
            type="button"
            className={style.see}
            onClick={(e) => seePassword(e, 'new')}
            aria-label={seeNew ? 'Hide password' : 'Show password'}
          >
            {seeNew ? <UilEye /> : <UilEyeSlash />}
          </button>
        </div>
        <div className={style.ax_field}>
          <label htmlFor="passwordConfirmation">Confirm New Password</label>
          {seeConfirm ? (
            <input
              type="text"
              name="passwordConfirmation"
              value={confirmNewPassword}
              onChange={(e) => handlePasswordChange(e, setConfirmNewPassword)}
            />
          ) : (
            <input
              type="password"
              name="passwordConfirmation"
              value={confirmNewPassword}
              onChange={(e) => handlePasswordChange(e, setConfirmNewPassword)}
            />
          )}
          <button
            type="button"
            className={style.see}
            onClick={(e) => seePassword(e, 'confirm')}
            aria-label={seeConfirm ? 'Hide password confirmation' : 'Show password confirmation'}
          >
            {seeConfirm ? <UilEye /> : <UilEyeSlash />}
          </button>
        </div>
        {message.text && <div className={getMessageStyles()}>{message.text}</div>}
        <div className={style.ax_field}>
          <button className={style.ax_btn_submit} name="reset" type="submit" onClick={resetPassword}>
            {spinner ? <img src="/images/spinner-white.svg" alt="Loading..." /> : 'Reset Password'}
          </button>
        </div>
      </form>
    </section>
  )
}

export default NewPassword
