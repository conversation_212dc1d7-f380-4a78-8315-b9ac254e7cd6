import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import MediaList from '../../components/MediaList'
import { useRouter } from 'next/router'
import style from '../../styles/ContentPage.module.scss'

const BrandMaterialMedia = (props) => {
  const { media, title } = props
  const router = useRouter()

  return (
    <Layout>
      <h1 className={style.ax_page_title}>{title}</h1>
      <div className={style.ax_card_list} />
      <section className={style.mediaList}>
        <MediaList media={media} downloadItem source={router.asPath} />
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const media = await axios
        .get(`${apiURL}/brand-materials?slug_eq=${ctx.params.slug}`, config)
        .then((res) => {
          const mediaData = serializeJson(res.data[0])
          return mediaData
        })
        .catch((error) => {
          throw error
        })

      return { props: media }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default BrandMaterialMedia
