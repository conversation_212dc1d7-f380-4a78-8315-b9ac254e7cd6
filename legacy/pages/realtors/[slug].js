import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/router'
import axios from 'axios'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import { UilSave, UilTimes } from '@iconscout/react-unicons'
import { serializeJson } from '../../helpers/serializeData'
import { getRawPhone } from '../../helpers/formatPhone'
import Layout from '../../components/Layout'
import { PhoneInput } from '../../components/MaskedInputs'
import Processing from '../../components/Processing'
import Button from '../../components/Button'
import { Row, Col } from 'react-grid-system'
import style from '../../styles/Realtors.module.scss'
import formatUrl from '../../helpers/formatUrl'
import ImageCropper from '../../components/ImageCropper'

const Realtors = (props) => {
  const { user } = props
  const [formInfo, setFormInfo] = useState({ useDefaultPhoto: false })
  const [realtorInfo, setRealtorInfo] = useState(null)
  const [files, setFiles] = useState({})
  const [fileSizeMessage, setFileSizeMessage] = useState({ isVisible: false, message: '' })
  const [processingStatus, setProcessingStatus] = useState({ visible: false, status: '', message: '' })
  const [uploadedPhotos, setUploadedPhotos] = useState({})
  const [showCropper, setShowCropper] = useState(false)
  const [selectedImage, setSelectedImage] = useState(null)
  const [originalFileName, setOriginalFileName] = useState('')

  const digitalPhotoForm = useRef()
  const digitalPhoto = useRef()
  const selectPhotoFile = useRef()
  const digitalPhotoUrl = () => {
    if (realtorInfo && realtorInfo.photo) {
      if (realtorInfo?.photo?.formats?.squared?.url) {
        return realtorInfo?.photo?.formats?.squared?.url
      } else if (realtorInfo?.photo?.formats?.small?.url) {
        return realtorInfo?.photo?.formats?.small?.url
      } else {
        return realtorInfo.photo.url
      }
    }
    return ''
  }

  const defaultPhoto = `${process.env.BASE_URL}/documents/listing-sheet/full/realtor-image-default.jpg`
  const apiUrl = process.env.API_URL
  const router = useRouter()

  const fetchRealtor = async () => {
    let realtor
    const jwt = Cookies.get('jwt')
    const config = {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    }

    if (router && router.query && router.query.slug) {
      realtor = await axios
        .get(`${apiUrl}/realtors/${router.query.slug}`, config)
        .then((res) => res.data)
        .catch((error) => console.log(error))
    }

    setRealtorInfo(realtor)
  }

  const updateFormInfo = (e, type) => {
    let { name, value } = e.target

    if (e.target.type === 'checkbox') {
      if (e.target.checked === 'true' || e.target.checked === true) {
        value = true
      }
      if (e.target.checked === 'false' || e.target.checked === false) {
        value = false
      }
    }

    if (e.target.type === 'tel') {
      value = { masked: value, raw: value.replace(/\D/g, '') }
    }

    if (e.target.name === 'website') {
      value = formatUrl(value)
    }

    setFormInfo({ ...formInfo, [name]: value })
  }

  const handleSelectFile = (e) => {
    e.preventDefault()
    updateFormInfo(e)

    if (e.target.id === 'digital') {
      selectPhotoFile.current.click()
    }
  }

  const handleSelected = async (e) => {
    e.preventDefault()

    if (e.target.id === 'digital') {
      if (e.target.files && e.target.files.length > 0) {
        const file = e.target.files[0]
        setOriginalFileName(file.name)

        // Check file size before proceeding
        let fileSize = Math.round(file.size / 1024)
        if (fileSize >= 15 * 1024) {
          setFileSizeMessage({
            type: e.target.id,
            isVisible: true,
            message: 'The image file exceeds the maximum size of 15MB.'
          })
          return
        } else {
          setFileSizeMessage({ isVisible: false })
        }

        const reader = new FileReader()
        reader.addEventListener('load', () => {
          setSelectedImage(reader.result)
          setShowCropper(true)
        })
        reader.readAsDataURL(file)
      }
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    updateFormInfo(e)
    if (e.dataTransfer.items) {
      ;[...e.dataTransfer.items].forEach((item, i) => {
        // If dropped items aren't files, reject them
        if (item.kind === 'file') {
          const file = item.getAsFile()
          if (e.target.id === 'digital') {
            // Check file size before proceeding
            let fileSize = Math.round(file.size / 1024)
            if (fileSize >= 15 * 1024) {
              setFileSizeMessage({
                type: e.target.id,
                isVisible: true,
                message: 'The image file exceeds the maximum size of 15MB.'
              })
              return
            } else {
              setFileSizeMessage({ isVisible: false })
            }

            setOriginalFileName(file.name)
            const reader = new FileReader()
            reader.addEventListener('load', () => {
              setSelectedImage(reader.result)
              setShowCropper(true)
            })
            reader.readAsDataURL(file)
          }
        }
      })
    }
  }

  const handleCropComplete = async (croppedImage) => {
    setShowCropper(false)
    setProcessingStatus({ status: 'success', visible: false, message: 'Image cropped successfully!' })

    try {
      // Convert base64 to blob
      const response = await fetch(croppedImage)
      const blob = await response.blob()

      // Create a new File object with the original file name
      const file = new File([blob], originalFileName, { type: blob.type })

      // Set the cropped file to be used for upload
      setFiles({ ...files, digital: file })

      // Show preview of cropped image
      setUploadedPhotos({ ...uploadedPhotos, digital: croppedImage })

      setProcessingStatus({ status: 'success', visible: false, message: 'Image cropped successfully!' })
    } catch (err) {
      console.error(err)
      setProcessingStatus({ status: 'error', visible: false, message: 'Error cropping image' })
    }
  }

  const closeModal = () => {
    setShowCropper(false)
    setSelectedImage(null)
  }

  const showDragzone = () => {
    if (formInfo.useDefaultPhoto) {
      return ''
    } else {
      return (
        <div className={style.dragzone}>
          <input
            ref={selectPhotoFile}
            type="file"
            name="files"
            id="digital"
            hidden
            onChange={(e) => handleSelected(e)}
          />
          <div
            className={style.dragarea}
            type="file"
            name="files"
            id="digital"
            onDrop={(e) => handleDrop(e)}
            onClick={(e) => handleSelectFile(e)}
            onDragOver={(e) => handleDragOver(e)}
          >
            {files && files.digital && files.digital.name && files.digital.name.length > 0 ? (
              <span>{files.digital.name}</span>
            ) : (
              <span>
                {formInfo && formInfo.photo && formInfo.photo.name
                  ? formInfo.photo.name
                  : 'Drag/drop your image file here or click to choose it.'}
              </span>
            )}
          </div>
        </div>
      )
    }
  }

  const handleDragOver = (e) => {
    e.preventDefault()
  }

  const showPhoto = (type) => {
    if (formInfo && formInfo.useDefaultPhoto) {
      return (
        <Row>
          <Col sm={6} md={3}>
            <img
              src={defaultPhoto}
              alt={user.firstname}
              ref={digitalPhoto}
              className={style.img_responsive}
              style={{ borderRadius: '8px' }}
            />
          </Col>
        </Row>
      )
    } else if (uploadedPhotos.digital) {
      return (
        <Row>
          <Col sm={6} md={3}>
            <img src={uploadedPhotos.digital} alt={user.firstname} ref={digitalPhoto} style={{ borderRadius: '8px' }} />
          </Col>
        </Row>
      )
    } else {
      if (formInfo.photo) {
        return (
          <Row>
            <Col sm={6} md={3}>
              <img src={digitalPhotoUrl()} alt={user.firstname} ref={digitalPhoto} style={{ borderRadius: '8px' }} />
            </Col>
          </Row>
        )
      } else {
        return ''
      }
    }
  }

  const handleUploadPhoto = async (e) => {
    e.preventDefault()
    setProcessingStatus({ ...processingStatus, visible: true, message: 'Uploading Photo...' })

    const token = await Cookies.get('jwt')
    const userId = await Cookies.get('userId')
    const photoData = await new FormData()
    let photoFile
    let ext
    let uploadedPhoto
    const config = {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }

    if (files && files.digital && files.digital !== null && files.digital !== undefined) {
      if (formInfo.useDefaultPhoto) {
        const buffer = await fetch(defaultPhoto).then((res) => res.arrayBuffer())
        const bytes = await new Uint8Array(buffer)
        photoFile = await new Blob([bytes], { type: 'image/jpg' })
      } else {
        photoFile = await files.digital
        ext = photoFile.name.split('.').pop()
      }

      await photoData.append(
        'files',
        photoFile,
        `${formInfo && formInfo.useDefaultPhoto ? 'A-logo.jpg' : user.firstname + ' ' + user.lastname + '.' + ext}`
      )
      await photoData.append('field', 'photo')

      // File size check is now handled in handleSelected and handleDrop

      uploadedPhoto = await axios
        .post(`${apiUrl}/upload`, photoData, config)
        .then((res) => {
          console.log('')
          const photoUrl = res.data[0].url

          if (res.data[0].url.length > 0) {
            setUploadedPhotos({ ...uploadedPhotos, digital: photoUrl })
          }
          return res.data[0]
        })
        .catch((err) => {
          setProcessingStatus({ visible: false, status: 'error', message: `Error: ${err}` })
          console.log(err)
          throw err
        })

      console.log('UPDPHT', uploadedPhoto)
    }

    setProcessingStatus({ ...processingStatus, visible: true, message: 'Saving realtor info...' })

    const rawPhones = await getRawPhone(formInfo)

    const phones = rawPhones === undefined || rawPhones === null ? '' : rawPhones

    const formObj = () => {
      if (uploadedPhoto && uploadedPhoto.url) {
        return { ...formInfo, ...phones, photo: uploadedPhoto }
      }
      return { ...formInfo, ...phones }
    }

    const savedRealtor = await axios
      .put(`${apiUrl}/realtors/${realtorInfo.id}`, formObj(), {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      .then((res) => {
        setProcessingStatus({ ...processingStatus, visible: true, message: 'Saving realtor info...' })
        return res.data
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        setProcessingStatus({ status: 'error', visible: false, message: `Error: ${err}` })
        console.log(err)
        throw err
      })

    setRealtorInfo(savedRealtor)

    console.log('SVG', savedRealtor)

    setProcessingStatus({ ...processingStatus, visible: false, message: 'Realtor added!' })
  }

  const getDefaultVal = (name) => {
    if (realtorInfo && realtorInfo[name]) {
      return realtorInfo[name]
    }
    return ''
  }

  useEffect(() => {
    fetchRealtor()
  }, [])

  useEffect(() => {
    setFormInfo({ ...formInfo, ...realtorInfo })
  }, [realtorInfo])

  return (
    <Layout toast={processingStatus}>
      {processingStatus.visible ? (
        <Processing
          processing={processingStatus.visible}
          positionRef={(l) => loaderPosition(l)}
          message={processingStatus.message}
        />
      ) : (
        ''
      )}

      {showCropper && (
        <div className={style.cropperModal}>
          <div className={style.cropperContent}>
            <button onClick={closeModal} className={style.closeButton}>
              <UilTimes />
            </button>
            <ImageCropper image={selectedImage} onCropComplete={handleCropComplete} />
          </div>
        </div>
      )}

      <h1 className={style.ax_page_title}>
        Edit Realtor{''}
        {realtorInfo && realtorInfo.firstname
          ? `: ${realtorInfo.firstname} ${realtorInfo.lastname ? realtorInfo.lastname : ''}`
          : ''}
      </h1>
      <section className={style.contentBox}>
        <form className={style.ax_form}>
          <Row>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="name">First Name</label>
                <input
                  type="text"
                  name="firstname"
                  id="firstname"
                  placeholder="Name"
                  defaultValue={getDefaultVal('firstname')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="name">Middle Name</label>
                <input
                  type="text"
                  name="middlename"
                  id="middlename"
                  placeholder="Name"
                  defaultValue={getDefaultVal('middlename')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="name">Last Name</label>
                <input
                  type="text"
                  name="lastname"
                  id="lastname"
                  placeholder="Last Name"
                  defaultValue={getDefaultVal('lastname')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="position">Position</label>
                <input
                  type="text"
                  name="position"
                  id="position"
                  placeholder="I.E: Mortgage Broker"
                  defaultValue={getDefaultVal('position')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                  defaultValue={getDefaultVal('email')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <div>
                  <label htmlFor="phone">Phone</label>
                  <PhoneInput
                    type="tel"
                    name="phone"
                    id="phone"
                    defaultValue={getDefaultVal('phone')}
                    placeholder="************"
                    onChange={(e) => updateFormInfo(e, null)}
                  />
                </div>
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="company">Company</label>
                <input
                  type="text"
                  name="company"
                  id="company"
                  placeholder="Company Name"
                  defaultValue={getDefaultVal('company')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={4}>
              <div className={style.ax_field}>
                <label htmlFor="website">Website</label>
                <input
                  type="text"
                  name="website"
                  id="website"
                  placeholder="johnmortgage.ca"
                  defaultValue={getDefaultVal('website')}
                  onChange={(e) => updateFormInfo(e, null)}
                />
              </div>
            </Col>
            <Col sm={12} md={12}>
              <form className={style.ax_form} ref={digitalPhotoForm}>
                <div className={`${style.ax_field} ${style.photoUploadField}`}>
                  {showPhoto()}
                  <div className={style.ax_image_options}>
                    <label>Realtor Photo</label>
                    <p>
                      <strong>JPG or PNG files only.</strong>
                    </p>
                    {showDragzone()}
                  </div>
                  <p style={{ marginTop: '16px', lineHeight: '16px' }}>
                    <span>
                      <input
                        checked={!!(formInfo && formInfo.useDefaultPhoto)}
                        id="useDefaultPhoto"
                        name="useDefaultPhoto"
                        value={!!(formInfo && formInfo.useDefaultPhoto)}
                        type="checkbox"
                        onChange={(e) => updateFormInfo(e)}
                        style={{ marginBottom: '4px' }}
                      />
                    </span>{' '}
                    I don't have a photo right now. Please use the default image.
                  </p>

                  {fileSizeMessage.isVisible && fileSizeMessage.type === 'digital' ? (
                    <h3 style={{ color: 'red' }}>{fileSizeMessage.message}</h3>
                  ) : (
                    ''
                  )}
                </div>
              </form>
            </Col>
          </Row>
          <Row justify="end">
            <Col sm={12} md={3}>
              <Button
                id="digital"
                disable={fileSizeMessage.isVisible ? true : false}
                type="submit"
                color="highlight"
                action={(e) => handleUploadPhoto(e)}
                icon={<UilSave size={16} />}
                label="Save"
                isWide
              />
            </Col>
          </Row>
        </form>
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const API_URL = process.env.API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  if (ctx.req.headers.cookie && jwt) {
    const userData = await axios
      .get(
        `${API_URL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })

    return {
      props: {
        user: userData
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Realtors
