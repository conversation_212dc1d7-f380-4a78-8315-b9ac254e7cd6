import { useContext, useState } from 'react'
import <PERSON>ram<PERSON> from 'react-iframe'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import AuthContext from '../context/authContext'
import Layout from '../components/Layout'
import style from '../styles/Lenders.module.scss'

const Lenders = () => {
  const { userAuth } = useContext(AuthContext)
  const [pageLoading, setPageLoading] = useState(true)

  const content = () => {
    return (
      <Layout>
        <h1 className={style.ax_page_title}>Lender Lounge</h1>
        <section className={style.table}>
          <Iframe
            url="https://docs.google.com/spreadsheets/d/e/2PACX-1vR1p286mIvUno4_6j1ulz_tN0-oM7bWPEieTrEjb4b3kk7O7lB2x5H4RJDYLSSjU0d6UaGKsZDqKD9d/pubhtml?widget=true&amp;headers=false"
            width="100%"
            height="800px"
            id="myId"
            className="myClassname"
            display="initial"
            position="relative"
            frameBorder="0"
            scrolling="no"
          />
        </section>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = `${process.env.API_URL}`
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Lenders
