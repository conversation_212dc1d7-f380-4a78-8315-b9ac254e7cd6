import { useRouter } from 'next/router'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import style from '../../styles/ContentPage.module.scss'
import MediaList from '../../components/MediaList'

const CustomShopCategory = (props) => {
  const { pageData } = props
  const router = useRouter()

  const currentCategory = router.query.category
  const categoryItems = pageData.mediaItems.filter((m) => m.category === currentCategory)

  const title = (str) => {
    if (str !== undefined && str !== '' && str !== null) {
      const newTitle = []
      for (let i = 0; i < str.length; i++) {
        newTitle.push(/[A-Z]/g.test(str[i]) ? ` ${str[i]}` : str[i])
      }
      const clearTitle = newTitle.join('')
      return clearTitle
    }

    return ''
  }

  return (
    <Layout>
      <h1 className={style.ax_page_title}>The Custom Shop: {title(currentCategory)}</h1>
      <section className={style.mediaList}>
        <MediaList media={categoryItems} isDownload />
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const pageData = await axios
        .get(`${apiURL}/custom-shop`, config)
        .then((res) => {
          const shopData = serializeJson(res.data)
          return shopData
        })
        .catch((error) => {
          throw error
        })

      return { props: { pageData } }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default CustomShopCategory
