import { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import AuthContext from '../context/authContext'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Processing from '../components/Processing'
import Layout from '../components/Layout'
import { Container, Row, Col } from 'react-grid-system'
import CardTile from '../components/CardTile'
import style from '../styles/Marketing.module.scss'

const Marketing = () => {
  const { userAuth } = useContext(AuthContext)
  const [authenticated] = useState(userAuth.isAuth)
  const [pageLoading, setPageLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    if (!authenticated) {
      router.push('/')
      return <></>
    }
  }, [])

  useEffect(() => {
    if (!authenticated) {
      setPageLoading(true)
      return <></>
    }
    setPageLoading(false)
  }, [authenticated])

  const content = () => {
    if (pageLoading) {
      return <Processing message="Please wait..." processing={pageLoading} />
    }

    return (
      <Layout>
        <Container>
          <Row>
            <Col sm={12}>
              <h1 className={style.ax_page_title}>Marketing</h1>
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-socialmedia-3d.png"
                title="Social Media"
                color="gradientyellow"
                clickEvent={() => router.push('/social-media')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-custom-shop-3d.png"
                title="The Custom Shop"
                color="gradientyellow"
                clickEvent={() => router.push('/custom-shop')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-qr-code-3d.png"
                title="QR Codes"
                color="gradientyellow"
                clickEvent={() => router.push('/qr-codes')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-printables-3d.png"
                title="Printables"
                color="gradientyellow"
                clickEvent={() => router.push('/printables')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-email-signature-3d.png"
                title="Email Signature"
                color="gradientdark"
                clickEvent={() => router.push('/email-signature')}
              />
            </Col>
            <Col sm={12} md={6} lg={1}>
              <CardTile
                icon="./images/tile-icon-listingsheet-3d.png"
                title="Listing Sheet"
                color="gradientblue"
                clickEvent={() => router.push('/listing-sheet')}
              />
            </Col>
          </Row>
        </Container>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = `${process.env.API_URL}`
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Marketing
