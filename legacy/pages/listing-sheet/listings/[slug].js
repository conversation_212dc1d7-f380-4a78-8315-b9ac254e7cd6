import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Cookies from 'js-cookie'
import axios from 'axios'
import nookies from 'nookies'
import { UilArrowLeft } from '@iconscout/react-unicons'
import MortgageCalculator from '../../../components/MortgageCalculator/MortgageCalculator'
import Layout from '../../../components/Layout'
import Processing from '../../../components/Processing'
import Button from '../../../components/Button'
import style from '../../../styles/ListingSheet.module.scss'
import alerts from '../../../styles/ToastsAlerts.module.scss'

const ExistingListingSheet = ({ user, initialData }) => {
  const [data, setData] = useState(initialData)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState(null)
  const router = useRouter()

  useEffect(() => {
    if (!user) {
      router.push('/')
    }
  }, [user])

  const saveSheet = async (message, sheet, calcInfo) => {
    setLoading(true)
    const apiURL = process.env.NEXT_PUBLIC_API_URL
    const jwt = await Cookies.get('jwt')
    const config = {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    }

    try {
      const res = await axios.put(
        `${apiURL}/listing-sheets/${data.id}`,
        {
          user: user.id,
          sheet: calcInfo,
          title: `${calcInfo.pdf.mlsCode ? 'MLS: ' + calcInfo.pdf.mlsCode : 'price-' + calcInfo.askingPrice}`,
          slug: `${
            calcInfo.pdf.mlsCode
              ? 'mls-' + calcInfo.pdf.mlsCode.replace(' ', '-').replace('#', '').replace('/', '-')
              : 'price-' +
                calcInfo.askingPrice
                  .replace(' ', '-')
                  .replace('#', '')
                  .replace('/', '-')
                  .replace('$', '')
                  .replace(',', '')
          }`
        },
        config
      )
      setData(res.data)
      setMessage({ type: 'success', text: 'Listing sheet updated successfully!' })
    } catch (err) {
      console.error(err)
      setMessage({ type: 'error', text: 'Error updating listing sheet. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleBack = () => {
    router.push('/listing-sheet')
  }

  if (loading) {
    return <Processing message="Please wait..." processing={loading} />
  }

  return (
    <Layout>
      <div className={style.contentBox}>
        <section className={style.heading}>
          <h1 className={style.ax_page_title}>Edit Listing Sheet</h1>

          <Button
            label="Back to Listing Sheets"
            action={handleBack}
            color="highlight"
            icon={<UilArrowLeft />}
            iconPos="left"
          />
        </section>
        {message && (
          <div className={`${alerts.alert} ${alerts[message.type]}`}>
            <h4 style={{ marginBottom: '0' }}>{message.text}</h4>
          </div>
        )}
        <MortgageCalculator user={user} save={saveSheet} data={data.sheet} />
      </div>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const { slug } = ctx.params

  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  try {
    const [userRes, sheetRes] = await Promise.all([
      axios.get(`${apiURL}/users/${userId}`, config),
      axios.get(`${apiURL}/listing-sheets?slug=${slug}`, config)
    ])

    const user = userRes.data
    const initialData = sheetRes.data[0]

    if (!initialData) {
      return {
        notFound: true
      }
    }

    return {
      props: {
        user,
        initialData
      }
    }
  } catch (error) {
    console.error('Error fetching data:', error)
    return {
      redirect: {
        destination: '/',
        permanent: false
      }
    }
  }
}

export default ExistingListingSheet
