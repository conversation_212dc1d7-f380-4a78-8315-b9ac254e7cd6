import Cookies from 'js-cookie'
import nookies from 'nookies'
import { useRouter } from 'next/router'
import axios from 'axios'
import { UilArrowLeft } from '@iconscout/react-unicons'
import { serializeJson } from '../../../helpers/serializeData'
import { guidGenerator } from '../../../helpers/guidGenerator'
import MortgageCalculator from '../../../components/MortgageCalculator/MortgageCalculator'
import Layout from '../../../components/Layout'
import style from '../../../styles/ListingSheet.module.scss'
import Button from '../../../components/Button'
import { useState } from 'react'

const NewListingSheet = (props) => {
  // eslint-disable-next-line react/destructuring-assignment
  const { user, data } = props
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saveError, setSaveError] = useState(null)

  const saveSheet = async (message, sheet, calcInfo) => {
    try {
      setSaving(true)
      setSaveError(null)

      const apiURL = process.env.NEXT_PUBLIC_API_URL
      const jwt = await Cookies.get('jwt')
      const config = {
        headers: {
          Authorization: `Bearer ${jwt}`
        }
      }

      const pdfObj = calcInfo.pdf
      const { ...cleanPdf } = pdfObj
      const cleanSheet = { ...calcInfo, pdf: { ...cleanPdf } }

      const mlsCode = calcInfo.pdf.mlsCode || ''
      const askingPrice = calcInfo.askingPrice || 0

      const title = mlsCode ? `MLS: ${mlsCode}` : `price-${askingPrice}`
      const slug = `${mlsCode ? 'mls-' + mlsCode.replace(/\s+/g, '-') : 'price-' + askingPrice}-${guidGenerator()}`

      const response = await axios.post(
        `${apiURL}/listing-sheets`,
        {
          user: user,
          sheet: cleanSheet,
          title: title,
          slug: slug
        },
        config
      )

      // On successful save, redirect to the listing sheets page
      router.push({
        pathname: '/listing-sheet',
        query: { saveSuccess: true }
      })

      return response.data
    } catch (err) {
      console.error('Error saving listing sheet:', err)
      setSaveError('Failed to save listing sheet. Please try again.')
      setSaving(false)
    }
  }

  const handleBack = () => {
    router.push('/listing-sheet')
  }

  return (
    <Layout>
      <section className={style.heading}>
        <h1 className={style.ax_page_title}>New Listing Sheet</h1>

        <Button
          label="Back to Listing Sheets"
          action={handleBack}
          color="highlight"
          icon={<UilArrowLeft />}
          iconPos="left"
        />
      </section>

      {saveError && <div className={style.errorMessage}>{saveError}</div>}

      <section className={style.content}>
        <MortgageCalculator user={user} save={saveSheet} data={data} saving={saving} />
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    },
    params: {
      isListingSheet: 1
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(`${apiURL}/users-permissions/listings?id=${userId}&isListingSheet=1`, config)
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          user: userData
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default NewListingSheet
