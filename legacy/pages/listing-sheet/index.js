import { useEffect, useState } from 'react'
import { serializeJson } from '../../helpers/serializeData'
import nookies from 'nookies'
import axios from 'axios'
import Processing from '../../components/Processing'
import { Row, Col } from 'react-grid-system'
import { UilEditAlt, UilTrashAlt } from '@iconscout/react-unicons'
import Layout from '../../components/Layout'
import Button from '../../components/Button'
import style from '../../styles/ListingSheet.module.scss'
import Table from '../../components/Table'
import { useRouter } from 'next/router'

const ListingSheet = (props) => {
  const { userData } = props
  const [pageLoading, setPageLoading] = useState(true)
  const [existingSheets, setExistingSheets] = useState(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const router = useRouter()

  const columns = ['Image', '#MLS Code', 'Price', 'Address', 'Action']
  const rows = (existingSheets || []).map((item) => {
    return {
      image: <img src={item.sheet.pdf.propertyPhoto.url} alt="Property" width="50" height="50" />,
      '#mls code': item.sheet.pdf.mlsCode ? item.sheet.pdf.mlsCode : '',
      price: `$${item.sheet.askingPrice.toLocaleString()}`,
      address: item.sheet.pdf.address ? item.sheet.pdf.address : '',
      action: (
        <>
          <Button
            label="Edit"
            sizing="small"
            color="highlight"
            isLink
            linkPath={`/listing-sheet/listings/${item.slug}`}
            align="right"
            icon={<UilEditAlt size={10} />}
            iconPos="right"
          />
          <Button
            label=""
            action={() => handleDelete(item.id)}
            color="danger"
            icon={<UilTrashAlt size={10} />}
            disabled={isDeleting}
            className={style.delete_button}
          />
        </>
      )
    }
  })

  useEffect(() => {
    if (userData) {
      setExistingSheets(userData.listing_sheets.reverse())
    }
    setPageLoading(false)
  }, [userData])

  const handleDelete = async (id) => {
    if (confirm('Are you sure you want to delete this listing sheet?')) {
      setIsDeleting(true)
      const apiURL = process.env.NEXT_PUBLIC_API_URL
      const jwt = nookies.get(null).jwt
      const config = {
        headers: {
          Authorization: `Bearer ${jwt}`
        }
      }

      try {
        await axios.delete(`${apiURL}/listing-sheets/${id}`, config)
        router.reload()
      } catch (err) {
        console.error('Error deleting listing sheet:', err)
        alert('Failed to delete listing sheet. Please try again.')
      } finally {
        setIsDeleting(false)
      }
    }
  }

  const content = () => {
    if (pageLoading) {
      return <Processing message="Please wait..." processing={pageLoading} />
    }

    return (
      <Layout>
        <h1 className={style.ax_page_title}>Listing Sheet</h1>
        <div className={style.contentBox} style={{ display: 'block' }}>
          <Row>
            <Col sm={12} md={12}>
              <h3 className={style.ax_page_subtitle}>What do you want to do?</h3>
              <div className={`${style.alertNote} ${style.alertInfo}`}>
                <p>
                  The Listing Sheet feature keeps records of your generated sheets from the last{' '}
                  <strong>90 days</strong>.
                </p>
                <p>
                  Feel free to load an existing sheet and edit it as you wish, or create a new one from scratch by
                  selecting one of the below options.
                </p>
              </div>
            </Col>
          </Row>
          <Row style={{ marginTop: '32px' }}>
            <Col sm={12} md={4}>
              <p>
                <strong>Create New</strong>
              </p>
              <Button label="Create New" sizing="xlarge" isLink color="highlight" linkPath="/listing-sheet/new" />
            </Col>
          </Row>
        </div>

        <div className={style.contentBox} style={{ display: 'block' }}>
          <Row>
            <Col sm={12} md={12}>
              <h3 className={style.ax_page_subtitle}>Load Existing Listing</h3>
              {existingSheets && existingSheets.length > 0 ? (
                <>
                  <p>
                    <strong>Click on the Edit button to modify an existing sheet</strong>
                  </p>

                  {rows && rows.length > 0 ? <Table rows={rows} headers={columns} /> : ''}
                </>
              ) : (
                <p>
                  <strong>No sheets found</strong>
                </p>
              )}
            </Col>
          </Row>
        </div>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    },
    params: {
      isListingSheet: 1
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(`${apiURL}/users-permissions/listings?id=${userId}&isListingSheet=1`, config)
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          userData
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default ListingSheet
