import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Moment from 'react-moment'
import Layout from '../../components/Layout'
import Markdown from '../../components/Markdown'
import ReactPlayer from 'react-player'
import { Container, Row, Col } from 'react-grid-system'
import style from '../../styles/Posts.module.scss'

const Tutorial = (props) => {
  const { pageData } = props
  const tutorial = pageData[0]

  const refineVideoUrl = () => {
    const newVideoUrl = tutorial.videoUrl.replace('vimeo.com', 'player.vimeo.com/video')
    const videoUrl = newVideoUrl + '?h=0f0034f3c7&amp;badge=0&amp;autopause=0&amp;player_id=0&amp;app_id=58479'
    return videoUrl
  }

  return (
    <>
      <Layout>
        <article className={style.article}>
          <div className={style.contentBody}>
            <Container>
              <Row>
                <Col sm={12}>
                  <header className={style.heading}>
                    <h2>{tutorial.title}</h2>
                    <h3>
                      <span>Last update:</span>{' '}
                      <Moment format="MMMM DD, YYYY">
                        {tutorial.updatedAt ? tutorial.updatedAt : tutorial.published_at}
                      </Moment>
                    </h3>
                  </header>
                </Col>
              </Row>
              <Row>
                <Col sm={12}>
                  {tutorial && tutorial.videoUrl ? (
                    <div className={style.videoEmbedContainer} style={{ margin: '32px 0' }}>
                      <ReactPlayer url={refineVideoUrl()} controls />
                    </div>
                  ) : (
                    ''
                  )}

                  {tutorial.content && tutorial.content.length > 0 ? <Markdown>{tutorial.content}</Markdown> : ''}
                </Col>
              </Row>
            </Container>
          </div>
        </article>
      </Layout>
    </>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const { slug } = ctx.params

      const pageData = await axios
        .get(`${apiURL}/tutorials?slug_eq=${slug}`, config)
        .then((res) => {
          const tutorials = res.data
          return tutorials
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          pageData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Tutorial
