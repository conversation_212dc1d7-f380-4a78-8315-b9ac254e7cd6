/* eslint-disable prefer-const */

import { useState, useEffect } from 'react'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import mmt from 'moment'
import Layout from '../components/Layout'
import NewsletterList from '../components/Newsletters/NewsletterList'
import style from '../styles/Newsletters.module.scss'

const Newsletters = (props) => {
  // eslint-disable-next-line react/destructuring-assignment
  const { data } = props
  // eslint-disable-next-line no-unused-vars
  const [months, setMonths] = useState(null)

  const handleMonths = () => {
    let monthsList = []
    // eslint-disable-next-line array-callback-return
    data.map((d) => {
      const month = mmt(d.date).format('MMMM YYYY')
      monthsList.push(month)
    })

    const cleanArr = [...new Set(monthsList)]
    setMonths(cleanArr)
  }

  useEffect(() => {
    handleMonths()
  }, [])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Newsletters</h1>

      <div className={style.contentContainer}>{data ? <NewsletterList data={data} /> : ''}</div>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL

  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const allNewsletters = await axios
        .get(`${apiURL}/newsletter-archives?_sort=date:DESC`, config)
        .then((res) => {
          const { data } = res
          return data
        })
        .catch((err) => {
          throw err
        })
      return {
        props: { data: allNewsletters }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Newsletters
