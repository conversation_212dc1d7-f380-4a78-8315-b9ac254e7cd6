import { useEffect, useState, useRef } from 'react'
import { motion } from 'framer-motion'
import axios from 'axios'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import { useRouter } from 'next/router'
import { serializeJson } from '../helpers/serializeData'
import { fetchDirectoryData, fetchDirectoryDataWithConfig } from '../helpers/fetchers'
import CardUser from '../components/CardUser'
import Processing from '../components/Processing'
import Layout from '../components/Layout'
import Button from '../components/Button'
import ListFilter from '../components/ListFilter'
import SearchByName from '../components/SearchByName'
import BrokersFilterContext from '../context/brokersFilterContext'
import style from '../styles/AddBroker.module.scss'
import commonstyles from '../styles/Common.module.scss'
import filterStyle from '../styles/Filter.module.scss'
import { Col, Container, Row } from 'react-grid-system'
import Switcher from '../components/Switcher'

const AllBrokers = (props) => {
  const { users: initialUsers, page: initialPage, count: initialCount } = props
  const router = useRouter()
  const [brokers, setBrokers] = useState(initialUsers)
  const [isProcessing, setIsProcessing] = useState(false)
  const [count, setCount] = useState(initialCount)
  const [currPage, setCurrPage] = useState(initialPage)
  const [error, setError] = useState(null)

  const [activeFilter, setActiveFilter] = useState({
    province: router.query.province || 'all',
    isStaffMember: false
  })

  const currPageRef = useRef(initialPage)
  const [filteredPages, setFilteredPages] = useState([])
  const filteredPagesSet = useRef(false)

  useEffect(() => {
    if (count !== null && !filteredPagesSet.current) {
      const totalPages = Math.ceil(count / 12)
      setFilteredPages(Array.from({ length: totalPages }, (_, i) => i + 1))
      filteredPagesSet.current = true
    }
  }, [count])

  // Main data fetching effect for filters only
  useEffect(() => {
    const fetchData = async () => {
      setIsProcessing(true)

      try {
        const { users, count: totalCount } = await fetchDirectoryData({
          start: (currPageRef.current - 1) * 12,
          province: activeFilter.province,
          isStaffMember: activeFilter.isStaffMember
        })

        setBrokers(users)
        setCount(totalCount)
        setError(null)
      } catch (err) {
        console.error('🔴 Error in fetchData:', err)
        setError(err.message)
      } finally {
        setIsProcessing(false)
      }
    }

    fetchData()
  }, [activeFilter, currPageRef.current])

  const handleProvinceFilter = async (province) => {
    setCurrPage(1)
    currPageRef.current = 1
    setActiveFilter((prev) => ({ ...prev, province }))
    filteredPagesSet.current = false
  }

  const handleStaffFilter = (event) => {
    setCurrPage(1)
    currPageRef.current = 1
    setActiveFilter((prev) => ({ ...prev, isStaffMember: event.target.checked }))
    filteredPagesSet.current = false
  }

  const handleSearch = ({ resetPagination }) => {
    if (resetPagination) {
      setCurrPage(1)
      currPageRef.current = 1
      filteredPagesSet.current = false
    }
  }

  const handlePagination = async (newPage) => {
    if (!filteredPages.includes(newPage)) {
      return
    }

    setCurrPage(newPage)
    currPageRef.current = newPage
    setIsProcessing(true)

    try {
      const { users } = await fetchDirectoryData({
        start: (newPage - 1) * 12,
        province: activeFilter.province,
        isStaffMember: activeFilter.isStaffMember
      })
      setBrokers(users)
      setError(null)
    } catch (error) {
      console.error('Error in pagination:', error)
      setError(error.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const calculatePagination = () => {
    if (filteredPages.length <= 5) return filteredPages

    const pagesToShow = []
    const currentPageIndex = filteredPages.indexOf(currPage)

    if (currentPageIndex <= 2) {
      pagesToShow.push(...filteredPages.slice(0, 5))
    } else if (currentPageIndex >= filteredPages.length - 3) {
      pagesToShow.push(...filteredPages.slice(-5))
    } else {
      pagesToShow.push(...filteredPages.slice(currentPageIndex - 2, currentPageIndex + 3))
    }

    if (pagesToShow[0] > 2) pagesToShow.unshift('...')
    if (pagesToShow[0] !== 1) pagesToShow.unshift(1)
    if (pagesToShow[pagesToShow.length - 1] < filteredPages.length - 1) pagesToShow.push('...')
    if (pagesToShow[pagesToShow.length - 1] !== filteredPages.length) pagesToShow.push(filteredPages.length)

    return pagesToShow
  }

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <BrokersFilterContext.Provider value={{ brokers, setBrokers }}>
        <Layout>
          <Processing processing={isProcessing} message="Processing..." />

          <h1 className={style.ax_page_title}>Company Directory</h1>

          {error && <div className={style.error_message}>Error loading directory: {error}</div>}

          <Container fluid>
            <Row>
              <Col>
                <div className={filterStyle.filterContainer}>
                  <div>
                    <ListFilter filterAction={handleProvinceFilter} initialProvince={activeFilter.province} />
                  </div>
                  <div>
                    <SearchByName handleSearch={handleSearch} />
                  </div>
                  <div>
                    <Switcher
                      id="staff-filter"
                      name="staff-filter"
                      checked={activeFilter.isStaffMember}
                      action={handleStaffFilter}
                      label="Head Office Only"
                      labelPos="left"
                      style={{ marginBottom: '0px' }}
                    />
                  </div>
                </div>
              </Col>
            </Row>
          </Container>

          <Container fluid>
            <Row>
              {brokers && brokers.length > 0 ? (
                brokers.map((user) => (
                  <Col sm={12} md={6} lg={4} key={user.id} style={{ marginBottom: '32px' }}>
                    <CardUser
                      firstname={user.firstname}
                      lastname={user.lastname}
                      position={user.position}
                      email={user.email}
                      phone={user.phone}
                      photo={user.photo?.formats?.thumbnail?.url || user.photo?.url || './images/axiom-a-logo.svg'}
                      wide
                    />
                  </Col>
                ))
              ) : (
                <h2>No matches found.</h2>
              )}
            </Row>
          </Container>

          {brokers && brokers.length > 0 && (
            <section className={commonstyles.pagination}>
              <Button
                sizing="xsmall"
                color="highlight"
                disabled={currPage <= 1}
                label="Previous"
                action={() => handlePagination(currPage - 1)}
              />

              {calculatePagination().map((pageNum, index) => {
                if (pageNum === '...') {
                  return <span key={`ellipsis-${index}`}>...</span>
                }
                return (
                  <Button
                    key={pageNum}
                    sizing="xsmall"
                    color={currPage === pageNum ? 'dark' : 'highlight'}
                    disabled={!filteredPages.includes(pageNum)}
                    label={pageNum.toString()}
                    action={() => handlePagination(pageNum)}
                  />
                )
              })}

              <Button
                sizing="xsmall"
                color="highlight"
                disabled={currPage >= Math.ceil(count / 12)}
                label="Next"
                action={() => handlePagination(currPage + 1)}
              />
            </section>
          )}
        </Layout>
      </BrokersFilterContext.Provider>
    </motion.div>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const { jwt, userId } = nookies.get(ctx)
  const apiURL = process.env.NEXT_PUBLIC_API_URL

  if (!jwt) {
    return {
      redirect: {
        destination: '/',
        permanent: false
      }
    }
  }

  if (userId) {
    try {
      const userData = await axios
        .get(`${apiURL}/users/${userId}`, { headers: { Authorization: `Bearer ${jwt}` } })
        .then((res) => serializeJson(res.data))

      if (userData?.isOnboarding) {
        return {
          redirect: {
            destination: 'https://welcome.indimortgage.ca',
            permanent: false
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    }
  }

  try {
    const page = parseInt(ctx.query.page) || 1

    const { users, count, error } = await fetchDirectoryDataWithConfig(
      {
        start: (page - 1) * 12,
        province: ctx.query.province,
        search: ctx.query.search,
        isStaffMember: ctx.query.isStaffMember === 'true'
      },
      { headers: { Authorization: `Bearer ${jwt}` } }
    )

    if (error) throw new Error(error)

    return {
      props: {
        users,
        page,
        count
      }
    }
  } catch (error) {
    console.error('Error in getServerSideProps:', error)
    return {
      props: {
        users: [],
        page: 1,
        count: 0
      }
    }
  }
}

export default AllBrokers
