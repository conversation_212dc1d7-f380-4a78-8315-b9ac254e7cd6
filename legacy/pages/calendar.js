import { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import moment from 'moment'
import { Calendar, momentLocalizer } from 'react-big-calendar'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import { serializeJson } from '../helpers/serializeData'
import nookies from 'nookies'
import axios from 'axios'
import AuthContext from '../context/authContext'
import Processing from '../components/Processing'
import Layout from '../components/Layout'
import style from '../styles/CompanyCalendar.module.scss'

const BrookerageCalendar = (props) => {
  const { events, calendars, socials } = props
  const { userAuth } = useContext(AuthContext)
  const [authenticated] = useState(userAuth.isAuth)
  const [filterCalendar, setFilterCalendar] = useState({ type: 'all', isFiltered: false })
  const [pageLoading, setPageLoading] = useState(true)
  const [allEvents, setAllEvents] = useState(null)
  const [filteredEvents, setFilteredEvents] = useState(null)
  const router = useRouter()
  const localizer = momentLocalizer(moment)

  console.log('Events', events)

  const eventPropGetter = (evt) => {
    let evtStyles
    if (evt && evt.type) {
      if (evt.type.toLowerCase() === 'association') {
        evtStyles = { backgroundColor: evt.bgColorHex, color: '#000' }
      } else {
        evtStyles = { backgroundColor: evt.bgColorHex }
      }
    } else {
      evtStyles = { backgroundColor: evt.bgColorHex }
    }
    return { style: evtStyles }
  }

  const filterByType = (e) => {
    const type = e.target.id
    console.log('tupe', type)
    if (type !== 'all') {
      setFilterCalendar({ type: e.target.id, isFiltered: true })
    } else {
      setFilterCalendar({ type: 'all', isFiltered: false })
    }
    return
  }

  const populateFiltered = () => {
    const filtered = allEvents.filter((evt) => evt.type.toLowerCase() === filterCalendar.type.toLowerCase())
    setFilteredEvents(filtered)
  }

  const handleEvent = (e) => {
    e.preventDefault
    if (e.slug && e.slug !== null && e.slug !== undefined) {
      window.open(`https://indievents.ca/events/${e.slug}`)
    }
    if (e.socialSlug && e.socialSlug !== null && e.socialSlug !== undefined) {
      router.push(e.socialSlug)
    }
  }

  useEffect(() => {
    if (!authenticated) {
      router.push('/')
      return <></>
    }
  }, [])

  useEffect(() => {
    if (!authenticated) {
      setPageLoading(true)
      return <></>
    }
    setPageLoading(false)
  }, [authenticated])

  useEffect(() => {
    if (filterCalendar.type !== 'all' && filterCalendar.isFiltered) {
      populateFiltered()
    }
  }, [filterCalendar])

  useEffect(() => {
    let eventsArr = []

    if (events && events.length > 0) {
      const formattedEvents = events.map((e) => {
        return {
          start: e.startTime ? moment(`${e.startDate} ${e.startTime}`).toDate() : moment(`${e.startDate}`).toDate(),
          end: e.endTime ? moment(`${e.endDate} ${e.endTime}`).toDate() : moment(`${e.endDate}`).toDate(),
          title: e.title,
          slug: e.slug,
          bgColorHex: e.bgColorHex ? e.bgColorHex : '#2a7a94',
          type: 'indi'
        }
      })

      eventsArr = [...eventsArr, ...formattedEvents]
    }

    if (calendars && calendars.length > 0) {
      const setBgColor = (type) => {
        if (type && type !== null && type !== undefined) {
          switch (type.toLowerCase()) {
            case 'axiom':
              return '#2a7a94'
            case 'association':
              return '#e7ed2f'
            case 'lender':
              return '#f26518'
            case 'other':
              return '#212121'
            default:
              return '#212121'
          }
        }
        return '#212121'
      }

      let calendarItems = []
      calendars.forEach((cld) => {
        const items = cld.calendar.filter(
          (evt) => evt.startDate !== null && evt.startDate !== undefined && evt.startDate !== ''
        )
        calendarItems = [...calendarItems, ...items]
      })

      const formattedEvents = calendarItems.map((e) => {
        return {
          start: e.startTime ? moment(`${e.startDate} ${e.startTime}`).toDate() : moment(`${e.startDate}`).toDate(),
          end: e.endTime ? moment(`${e.endDate} ${e.endTime}`).toDate() : moment(`${e.endDate}`).toDate(),
          title: e.title,
          bgColorHex: e.bgColorHex ? e.bgColorHex : setBgColor(e.eventType),
          type: e.eventType
        }
      })

      eventsArr = [...eventsArr, ...formattedEvents]
    }

    if (socials && socials.length > 0) {
      let socialsItems = []
      socials.map((scl) => {
        const { captionTexts } = scl
        if (captionTexts && captionTexts.length > 0) {
          captionTexts.forEach((c) => {
            socialsItems.push({
              start: moment(`${c.postDate} 00:00:01 AM`).toDate(),
              end: moment(`${c.postDate} 00:59:59 PM`).toDate(),
              title: `Post: ${c.title}`,
              bgColorHex: '#0852c2',
              socialSlug: `${process.env.BASE_URL}/social-media?month=${scl.month}`,
              type: 'socialPost'
            })
          })
        }
      })

      eventsArr = [...eventsArr, ...socialsItems]
    }

    setAllEvents(eventsArr)
  }, [events, calendars, socials])

  const content = () => {
    if (pageLoading) {
      return <Processing message="Please wait..." processing={pageLoading} />
    }

    return (
      <Layout>
        <h1 className={style.ax_page_title}>Brokerage Calendar</h1>
        <section className={style.toolBar}>
          <h3>Filter:</h3>
          <ul className={style.colorFilterList}>
            <li className={style.colorFilter}>
              <button id="all" onClick={(e) => filterByType(e)} style={{ backgroundColor: '#787f82', color: '#fff' }}>
                All
              </button>
            </li>
            <li className={style.colorFilter}>
              <button id="indi" onClick={(e) => filterByType(e)} style={{ backgroundColor: '#2a7a94', color: '#fff' }}>
                Indi
              </button>
            </li>

            <li className={style.colorFilter}>
              <button
                id="association"
                onClick={(e) => filterByType(e)}
                style={{ backgroundColor: '#e7ed2f', color: '#000' }}
              >
                Association
              </button>
            </li>

            <li className={style.colorFilter}>
              <button
                id="lender"
                onClick={(e) => filterByType(e)}
                style={{ backgroundColor: '#f26518', color: '#fff' }}
              >
                Lender
              </button>
            </li>

            <li className={style.colorFilter}>
              <button
                id="socialPost"
                onClick={(e) => filterByType(e)}
                style={{ backgroundColor: '#0852c2', color: '#fff' }}
              >
                Social Media Post
              </button>
            </li>

            <li className={style.colorFilter}>
              <button id="other" onClick={(e) => filterByType(e)} style={{ backgroundColor: '#212121', color: '#fff' }}>
                Other
              </button>
            </li>
          </ul>
        </section>
        {(allEvents && allEvents !== null) || (filteredEvents && filteredEvents !== null) ? (
          <Calendar
            events={filterCalendar && filterCalendar.isFiltered ? filteredEvents : allEvents}
            localizer={localizer}
            onSelectEvent={(e) => handleEvent(e)}
            eventPropGetter={(e) => eventPropGetter(e)}
          />
        ) : (
          <h3>Loading...</h3>
        )}
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  const calendars = await axios
    .get(`${apiURL}/calendars`, config)
    .then((res) => res.data)
    .catch((err) => {
      console.log(err)
      throw err
    })

  const events = await axios
    .get(`${apiURL}/event-listing?showOnCalendar=true`, config)
    .then((res) => {
      const data = res.data
      return data
    })
    .catch((err) => {
      throw err
    })

  const socials = await axios
    .get(`${apiURL}/social-medias`, config)
    .then((res) => res.data)
    .catch((err) => {
      console.log(err)
      throw err
    })

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          userData,
          events,
          calendars,
          socials
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default BrookerageCalendar
