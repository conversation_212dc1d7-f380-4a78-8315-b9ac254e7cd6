import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Layout from '../components/Layout'
import Markdown from '../components/Markdown'
import style from '../styles/Awards.module.scss'

const Awards = (props) => {
  const { pageData } = props

  return (
    <Layout>
      <h1 className={style.ax_page_title}>{pageData.pageTitle}</h1>

      <section className={style.heading}>
        <Markdown>{pageData.description}</Markdown>
      </section>
      <section className={style.listWithThumb}>
        {pageData.prizesList.map((prize) => (
          <div className={style.listItem} key={prize.title}>
            <div className={style.tag}>{`$ ${prize.amount}`}</div>
            <div className={style.thumb}>
              <img src={prize.thumbnail.url} alt={prize.title} />
            </div>
            <div className={style.description}>
              <h2>{prize.title}</h2>
              <Markdown>{prize.description}</Markdown>
            </div>
          </div>
        ))}
      </section>

      <section className={style.list}>
        <h2>Additional Award Categories</h2>
        {pageData.additionalAwardsList.map((prize) => (
          <div className={style.listItem} key={prize.title}>
            <div className={style.description}>
              <h2>{prize.title}</h2>
              <Markdown>{prize.description}</Markdown>
            </div>
          </div>
        ))}
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const pageData = await axios
        .get(`${apiURL}/page-awards`, config)
        .then((res) => {
          const page = res.data
          return page
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          pageData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Awards
