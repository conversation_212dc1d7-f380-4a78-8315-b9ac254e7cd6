import nookies from 'nookies'
import axios from 'axios'
import Layout from '../components/Layout'
import Card from '../components/Card'
import { serializeJson } from '../helpers/serializeData'
import style from '../styles/Printables.module.scss'

const BrandMaterial = (props) => {
  const { data } = props

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Brand Materials</h1>
      <div className={style.ax_card_list}>
        {Object.keys(data).map((item) => (
          <Card
            key={data[item].id}
            title={data[item].title}
            hasButton
            linkUrl={`/brand-materials/${data[item].slug}`}
            image={data[item].thumbnail.url}
            buttonLabel="View Items"
          />
        ))}
      </div>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const allMaterials = await axios
        .get(`${apiURL}/brand-materials`, config)
        .then((res) => {
          const { data } = res
          const serializedData = serializeJson(data)
          return serializedData
        })
        .catch((err) => {
          throw err
        })
      return {
        props: { data: allMaterials }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default BrandMaterial
