import { useRef } from 'react'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Markdown from '../components/Markdown'
import Layout from '../components/Layout'
import Card from '../components/Card'
import style from '../styles/ContentPage.module.scss'

const CustomShop = (props) => {
  const { pageData } = props
  const contentPage = useRef()

  const title = (str) => {
    if (str !== undefined && str !== '' && str !== null) {
      const newTitle = []
      for (let i = 0; i < str.length; i++) {
        newTitle.push(/[A-Z]/g.test(str[i]) ? ` ${str[i]}` : str[i])
      }
      const clearTitle = newTitle.join('')
      return clearTitle
    }

    return ''
  }

  const categories = pageData.mediaItems.map((m) => m.category)
  const uniqueCategories = [...new Set(categories)]

  console.log(uniqueCategories)

  return (
    <Layout>
      <h1 className={style.ax_page_title}>{pageData.pageTitle}</h1>
      <div className={style.contentContainer} ref={contentPage}>
        <section className={style.mainContent}>
          <div className={style.pageContent}>
            <Markdown>{pageData.description}</Markdown>
          </div>
          <section className={style.mediaList}>
            {pageData ? (
              <div className={style.ax_card_list}>
                {uniqueCategories.map((m) => {
                  return (
                    <Card
                      key={title(m)}
                      title={title(m)}
                      hasButton
                      linkUrl={`/custom-shop/${m}?category=${m}`}
                      iconSquared="./images/ico-folder.svg"
                      buttonLabel="View Designs"
                      size="vertical"
                    />
                  )
                })}
              </div>
            ) : (
              'loading'
            )}
          </section>
        </section>
      </div>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const pageData = await axios
        .get(`${apiURL}/custom-shop`, config)
        .then((res) => {
          const { data } = res
          const serializedData = serializeJson(data)
          return serializedData
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          pageData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default CustomShop
