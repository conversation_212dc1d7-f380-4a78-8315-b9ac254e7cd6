import Iframe from 'react-iframe'
import { serializeJson } from '../helpers/serializeData'
import nookies from 'nookies'
import axios from 'axios'
import Layout from '../components/Layout'
import style from '../styles/CompanyCalendar.module.scss'

const CompanyCalendar = () => {
  const content = () => {
    return (
      <Layout>
        <h1 className={style.ax_page_title}>Company Calendar</h1>
        <Iframe
          url="https://calendar.google.com/calendar/embed?src=c_06llk171tl24o1ereeucn4tmd0%40group.calendar.google.com&ctz=America%2FEdmonton"
          width="100%"
          height="800px"
          id="myId"
          className="myClassname"
          display="initial"
          position="relative"
          frameBorder="0"
          scrolling="no"
        />
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          userData
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default CompanyCalendar
