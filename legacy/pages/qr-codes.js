import { useState, useEffect, useRef } from 'react'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import axios from 'axios'
import { serializeJson } from '../helpers/serializeData'
import { UilTrashAlt } from '@iconscout/react-unicons'
import QRCode from 'react-qr-code'
import formatUrl from '../helpers/formatUrl'
import * as htmlToImage from 'html-to-image'
import Layout from '../components/Layout'
import Button from '../components/Button'
import Processing from '../components/Processing'
import style from '../styles/QrCodes.module.scss'
import { Container, Row, Col } from 'react-grid-system'

const QrCodes = (props) => {
  // eslint-disable-next-line react/destructuring-assignment
  const { data } = props
  const [qrCodes, setQrCodes] = useState(data.qrCodes)
  const [QrUrl, setQrUrl] = useState({ url: null, isVisible: false })
  const [QrSvg, setQrSvg] = useState({ isVisible: false })
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const QRCodeContainer = useRef()
  const QRLink = useRef()
  const API_URL = process.env.NEXT_PUBLIC_API_URL

  const handleGenerateQr = () => {
    if (QRLink.current.value.length > 0) {
      setQrUrl({ url: formatUrl(QRLink.current.value), isVisible: true })
      setQrSvg({ isVisible: true })
    }
  }

  const handleSaveQr = async () => {
    setIsSaving(true)
    const HtmlNode = await QRCodeContainer.current.querySelector('svg')
    await htmlToImage
      .toBlob(HtmlNode)
      .then(async (dataUrl) => {
        const fileData = await new FormData()
        fileData.append('files', dataUrl, `qrcode[${QrUrl.url}].png`)

        setQrUrl({ ...QrUrl, jpgUrl: dataUrl })

        const userId = await Cookies.get('userId')
        const token = await Cookies.get('jwt')

        const config = {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }

        const uploadQR = await axios
          .post(`${API_URL}/upload`, fileData, config)
          .then((res) => {
            const qrImagePath = res.data[0].url
            const qrImageId = res.data[0]._id
            return { qrImagePath, qrLink: QrUrl.url, qrId: qrImageId }
          })
          .catch((err) => {
            // eslint-disable-next-line no-console
            console.log(err)
          })

        const userQrCodes = await qrCodes

        const newQrCodes = () => {
          if (userQrCodes === null || userQrCodes === undefined) {
            return [{ url: uploadQR.qrLink, qrImage: uploadQR.qrImagePath, id: uploadQR.qrId }]
          }
          return [...userQrCodes, { url: uploadQR.qrLink, qrImage: uploadQR.qrImagePath, id: uploadQR.qrId }]
        }

        const qrCodesArr = await newQrCodes()

        // eslint-disable-next-line no-unused-vars
        const addQRToUser = await axios
          .put(`${API_URL}/users/${userId}`, { qrCodes: qrCodesArr }, config)
          .then((res) => {
            setQrCodes(res.data.qrCodes)
            setIsSaving(false)
          })
          .catch((err) => {
            // eslint-disable-next-line no-console
            console.log(err)
          })
      })
      .catch((error) => {
        setIsSaving(false)
        // eslint-disable-next-line no-console
        console.error('oops, something went wrong!', error)
      })
  }

  const handleDelete = async (e) => {
    e.preventDefault()
    setIsDeleting(true)
    const newQrCodes = await qrCodes.filter((q) => q.id !== e.target.id)

    const userId = await Cookies.get('userId')
    const token = await Cookies.get('jwt')
    const config = {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }

    const qrCodesArr = await newQrCodes

    // eslint-disable-next-line no-unused-vars
    const addQRToUser = await axios
      .put(`${API_URL}/users/${userId}`, { qrCodes: qrCodesArr }, config)
      .then((res) => {
        setQrCodes(res.data.qrCodes)
        setIsDeleting(false)
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log(err)
        setIsDeleting(false)
      })
  }

  useEffect(() => {
    setQrCodes(qrCodes)
  }, [qrCodes])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>QR Codes</h1>

      <section className={style.heading}>
        <h2>Add QR Code</h2>

        <form className={style.qrForm}>
          <label htmlFor="link">
            Link (URL)
            <input type="text" name="link" ref={QRLink} />
          </label>
          <Button action={handleGenerateQr} color="highlight" sizing="medium" label="Generate QR Code" />
          <Button action={handleSaveQr} color="highlight" sizing="medium" label="Save" disabled={!QrSvg.isVisible} />
        </form>

        <div className={style.qrBlock}>
          <Processing processing={isSaving} message="Saving" />
          {QrSvg && QrSvg.isVisible ? (
            <div className={style.qrCodeContainer} ref={QRCodeContainer}>
              {QrUrl && QrUrl.isVisible ? (
                <QRCode value={QrUrl.url} size={300} bgColor="#FFFFFF" id="qr-code-svg" className={style.qrCodeSvg} />
              ) : (
                ''
              )}
            </div>
          ) : (
            ''
          )}

          <h3>{QrUrl.url}</h3>
        </div>
      </section>

      <div className={style.contentContainer}>
        <div className={style.qrCodes}>
          <h3>Your QR Codes</h3>
          <Container>
            <Row>
              <Processing processing={isDeleting} message="Deleting" />
              {qrCodes !== null && qrCodes !== undefined && qrCodes.length > 0 ? (
                qrCodes.map((qr) => (
                  <Col key={qr.id} sm={12} md={6} lg={3}>
                    <div className={style.cardItem}>
                      <img src={qr.qrImage} alt={`QR Code - ${qr.url}`} />
                      <p>{qr.url}</p>
                      <div className={style.cardItemFooter}>
                        <button id={qr.id} type="button" onClick={(e) => handleDelete(e)}>
                          <UilTrashAlt size={16} />
                        </button>
                        <a id={qr.id} href={qr.qrImage} target="_blank" download rel="noreferrer">
                          Download
                        </a>
                      </div>
                    </div>
                  </Col>
                ))
              ) : (
                <p className={style.noQrs}>You don&apos;t have any QR Code.</p>
              )}
            </Row>
          </Container>
        </div>
      </div>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const qrcodes = await axios
        .get(`${apiURL}/users?id_eq=${userId}`, config)
        .then((res) => {
          const { data } = res
          return data[0]
        })
        .catch((err) => {
          throw err
        })
      return {
        props: { data: qrcodes }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default QrCodes
