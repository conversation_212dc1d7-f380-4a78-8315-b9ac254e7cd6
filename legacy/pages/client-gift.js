import axios from 'axios'
import Cookies from 'js-cookie'
import nookies from 'nookies'
import { useEffect, useState, useRef } from 'react'
import Layout from '../components/Layout'
import EventBanner from '../components/EventBanner'
import Markdown from '../components/Markdown'
import Moment from 'react-moment'
import Button from '../components/Button'
import Switcher from '../components/Switcher'
import { checkValues } from '../helpers/savingForms'
import { PhoneInput } from '../components/MaskedInputs'
import { getRawPhone } from '../helpers/formatPhone'
import {
  UilCalendarAlt,
  UilCheckCircle,
  UilArrowRight,
  UilArrowLeft,
  UilPlusCircle,
  UilUserCircle,
  UilTruck,
  UilGift,
  UilWallet
} from '@iconscout/react-unicons'
import Processing from '../components/Processing'
import { Container, Row, Col } from 'react-grid-system'
import style from '../styles/Gift.module.scss'

const Gift = (props) => {
  const { page: gift, user } = props
  const [bannerHeight, setBannerHeight] = useState(0)
  const [currentStep, setCurrentStep] = useState(1)
  const [fieldsValidation, setFieldsValidation] = useState([])
  const [processingStatus, setProcessingStatus] = useState({ visible: false, status: '', message: '' })
  const form = useRef(null)
  const apiUrl = process.env.API_URL
  const [selectedItem, setSelectedItem] = useState(null)
  const [selectedItemImage, setSelectedItemImage] = useState(null)
  const [formInfo, setFormInfo] = useState({})
  const [requiredFields] = useState([
    'selectedGift',
    'clientFirstname',
    'clientLastname',
    'companyOrLender',
    'paymentMethod',
    'clientPhone'
  ])
  const [completionMessage, setCompletionMessage] = useState('')
  const [formSubmitted, setFormSubmitted] = useState(false)

  const calculatedHeight = (h) => {
    if (h) {
      setBannerHeight(h)
    }
  }

  console.log('formInfo', formInfo)

  useEffect(() => {
    Cookies.set('giftId', gift.id)
  }, [])

  const handleGiftSelection = (e, hasSubOptions) => {
    e.preventDefault()
    e.stopPropagation()
    const selectedGift = e.target.id || e.target.closest('[data-gift-id]').dataset.giftId
    const giftImage = e.target.closest('[data-gift-id]').querySelector('img').getAttribute('src')

    setSelectedItem(selectedGift)
    setSelectedItemImage(giftImage)
    const subOptions = e.target.closest('[data-gift-id]').querySelector('select').options
    const subOptionsArr = Array.from(subOptions)
    console.log('subOptionsArr', subOptionsArr)
    const options = subOptionsArr.map((o) => o.value)
    console.log('selectedGiftImage', selectedItemImage)
    console.log('OPTS', options)
    const filteredOption = options.filter((option) => option !== formInfo?.subOption)

    console.log('FTD', filteredOption)

    if (filteredOption.length >= 1) {
      setFormInfo({ ...formInfo, selectedGift, subOption: '' })
    } else {
      setFormInfo({ ...formInfo, selectedGift })
    }
  }

  const updateFormInfo = (e) => {
    if (e.target.type === 'checkbox') {
      let { name, value, id } = e.target

      if (id === 'paymentMethod') {
        name = 'paymentMethod'
        value = e.target.checked

        if (e.target.name === 'payrollDeduction') {
          setFormInfo({ ...formInfo, [name]: { payrollDeduction: value, creditCard: false } })
        }

        if (e.target.name === 'creditCard') {
          setFormInfo({ ...formInfo, [name]: { payrollDeduction: false, creditCard: value } })
        }

        return
      }

      value = e.target.checked
      setFormInfo({ ...formInfo, [name]: value })
      return
    }

    if (e.target && e.target.name) {
      let { name, value } = e.target
      if (e.target.type === 'tel') {
        value = { masked: value, raw: value.replace(/\D/g, '') }
      }
      setFormInfo({ ...formInfo, [name]: value })
    }
  }

  const validateForm = () => {
    const missingFields = requiredFields.filter((field) => {
      if (field === 'paymentMethod') {
        return (
          !formInfo.paymentMethod || (!formInfo.paymentMethod.payrollDeduction && !formInfo.paymentMethod.creditCard)
        )
      }
      if (field === 'clientPhone') {
        return !formInfo.clientPhone || !formInfo.clientPhone.raw || formInfo.clientPhone.raw.length < 10
      }
      return !formInfo[field]
    })

    const camelToCapital = (str) => {
      if (str) {
        return str
          .replace(/([A-Z])/g, ' $1')
          .replace(/^./, (str) => str.toUpperCase())
          .trim()
      }

      return
    }

    const invalidFields = missingFields.map((field) => camelToCapital(field))

    setFieldsValidation(invalidFields)
    return missingFields.length === 0
  }

  const submitRegistration = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      setProcessingStatus({ visible: false, status: 'error', message: 'Please fill in all required fields.' })
      if (typeof window !== undefined) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
      return
    }

    setProcessingStatus({ visible: true, message: 'Submitting...' })

    const rawPhones = await getRawPhone(formInfo)

    const userId = Cookies.get('userId')
    const jwt = Cookies.get('jwt')

    const config = {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    }

    const paymentMethod = () => {
      if (formInfo?.paymentMethod?.payrollDeduction) {
        return 'payrollDeduction'
      } else if (formInfo?.paymentMethod?.creditCard) {
        return 'creditCard'
      }
    }

    const formObj = {
      user: userId,
      client: `${formInfo.clientFirstname || ''} ${formInfo.clientMiddlename || ''} ${
        formInfo.clientLastname || ''
      }`.trim(),
      clientEmail: formInfo.clientEmail || '',
      clientFirstname: formInfo.clientFirstname || '',
      clientLastname: formInfo.clientLastname || '',
      clientMiddlename: formInfo.clientMiddlename || '',
      clientPhone: formInfo.clientPhone && formInfo.clientPhone.raw ? formInfo.clientPhone.raw : '',
      companyOrLender: formInfo.companyOrLender || '',
      desiredShippingDate: '',
      requester: user ? `${user.firstname || ''} ${user.lastname || ''}`.trim() : 'Unknown User',
      selectedGift: formInfo.selectedGift || '',
      shippingAddress: formInfo.shippingAddress || '',
      subOption:
        (formInfo?.subOption === 'choose'
          ? 'Choose Something For Me'
          : formInfo?.subOption === 'idea'
          ? 'I Have An Idea For A Gift'
          : '') || '',
      paymentMethod: paymentMethod(),
      giftIdeaDescription: formInfo.giftIdeaDescription || '',
      receiverDetails: formInfo.receiverDetails || '',
      giftCardMessage: formInfo.giftCardMessage || ''
    }

    if (userId && jwt) {
      try {
        await axios.post(`${apiUrl}/client-gifting-requests`, formObj, config)
        setProcessingStatus({ visible: false, status: 'success', message: 'Registration successful!' })
        setCompletionMessage('Thank you. Your gift request has been submitted successfully!')
        setFormSubmitted(true)
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      } catch (error) {
        console.error('Registration error:', error)
        setProcessingStatus({ visible: false, status: 'error', message: `Error: ${error.message}` })
      }
    }
  }

  const resetForm = () => {
    setFormInfo({})
    setSelectedItem(null)
    setCompletionMessage('')
    setFormSubmitted(false)
    setFieldsValidation([])
    setCurrentStep(1)
    if (form.current) {
      form.current.reset()
    }
  }

  const showSubOptions = (option) => {
    if (option && option.subOptions) {
      return option.subOptions.map((so) => (
        <option key={so.title} value={so.title}>
          {so.title}
        </option>
      ))
    }
    return null
  }

  useEffect(() => {
    if (user) {
      if (form && form.current !== null && form.current !== undefined) {
        const current = form.current
        const obj = checkValues(current, requiredFields)
        setFormInfo({ ...formInfo, ...obj })
      }
    }
  }, [user])

  useEffect(() => {
    if (formInfo?.selectedGift?.subOptions === 'idea') {
      setFieldsValidation([...fieldsValidation, 'giftIdeaDescription'])
    }
  }, [formInfo])

  const handleNextStep = () => {
    if (formInfo.selectedGift) {
      setCurrentStep(2)
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    }
  }

  const handlePreviousStep = () => {
    setCurrentStep(1)
  }

  const renderStepIndicator = () => (
    <Row>
      {currentStep === 2 ? (
        <Col sm={12} md={12}>
          <Button
            sizing="medium"
            color="highlight"
            label="Back to Gift Selection"
            action={handlePreviousStep}
            icon={<UilArrowLeft />}
            iconPosition="left"
          />
        </Col>
      ) : (
        ''
      )}

      <Col sm={12} md={12}>
        <div className={style.stepIndicator}>
          <div className={`${style.step} ${currentStep === 1 ? style.active : style.completed}`}>
            <div className={style.stepNumber}>1</div>
            Select Gift
          </div>
          <div className={`${style.stepDivider} ${currentStep === 2 ? style.completed : ''}`} />
          <div className={`${style.step} ${currentStep === 2 ? style.active : ''}`}>
            <div className={style.stepNumber}>2</div>
            Details
          </div>
        </div>
      </Col>
    </Row>
  )

  const renderGiftSelection = () => (
    <Row>
      <Col sm={12}>
        <h3>Select your item:</h3>
        <Row>
          {gift.giftOption.map((option) => (
            <Col sm={12} md={6} lg={4} xxl={4} key={option.title} className={style.giftItem}>
              <div
                data-gift-id={option.title}
                className={`${style.selectCard} ${selectedItem === option.title ? style.selectedItem : ''}`}
                style={{ marginBottom: '32px' }}
              >
                {selectedItem === option.title && (
                  <div className={style.checked}>
                    <UilCheckCircle size={32} />
                  </div>
                )}
                <div className={style.cardImage}>
                  <img src={option.image.url} alt={option.title} />
                </div>
                <div className={style.cardBody}>
                  <h4 style={{ marginTop: 0 }}>{option.title}</h4>
                  {option.description && (
                    <>
                      <Markdown>{option.description || ''}</Markdown>
                    </>
                  )}
                </div>
                <Col sm={12} style={{ padding: '0' }}>
                  <div className={style.ax_field} onClick={(e) => e.stopPropagation()}>
                    <label htmlFor="subOption">Gift Options:</label>
                    {formInfo.selectedGift !== option.title && (
                      <small style={{ marginBottom: '8px' }}>Select this item before selecting a gift option.</small>
                    )}
                    <select
                      disabled={formInfo.selectedGift !== option.title}
                      id="subOption"
                      name="subOption"
                      onChange={updateFormInfo}
                      value={formInfo.subOption || ''}
                    >
                      <option value="">Select an option</option>
                      <option value="idea">I have an idea for a gift</option>
                      <option value="choose">Choose something for me</option>
                      {showSubOptions(option)}
                    </select>
                  </div>
                </Col>
                {selectedItem === option.title ? (
                  <div className={style.cardFooter} onClick={(e) => e.stopPropagation()}>
                    <Button
                      sizing="medium"
                      color="dark"
                      label="Next Step"
                      disabled={!formInfo.selectedGift || !formInfo.subOption}
                      action={handleNextStep}
                      icon={<UilArrowRight />}
                      iconPosition="right"
                    />
                  </div>
                ) : (
                  <Button
                    label="Select This Item"
                    sizing="medium"
                    color="highlight"
                    icon={<UilArrowRight />}
                    iconPosition="right"
                    role="button"
                    data-gift-id={option.title}
                    action={(e) => handleGiftSelection(e, option.subOptions && option.subOptions.length > 0)}
                    isWide
                  />
                )}
              </div>
            </Col>
          ))}
        </Row>
      </Col>
    </Row>
  )

  const renderDetailsForm = () => (
    <>
      <Container fluid nogutter style={{ padding: '0' }}>
        <Row style={{ marginBottom: '32px' }}>
          <Col sm={12} md={9} lg={10}>
            {fieldsValidation.length > 0 && (
              <section className={style.validation} style={{ marginBottom: '32px' }}>
                <h4>The following fields are required:</h4>
                <ul>
                  {fieldsValidation.map((f) => (
                    <li key={f}>{f}</li>
                  ))}
                </ul>
              </section>
            )}
          </Col>

          <Col sm={12}>
            <Row gutterWidth={32}>
              <Col sm={12} md={3}>
                <Row
                  style={{
                    background: '#f2f5f9',
                    padding: '16px',
                    borderRadius: '12px'
                  }}
                  gutterWidth={16}
                >
                  <Col sm={12} md={12}>
                    <img
                      src={selectedItemImage}
                      alt={selectedItem?.title}
                      style={{ width: '100%', maxWidth: '100%', margin: '4px 0 16px 0' }}
                    />
                  </Col>
                  <Col sm={12}>
                    <h3 className={style.ax_page_subtitle}>Your Gift Option:</h3>
                    <p>
                      <strong>{selectedItem}</strong>
                    </p>
                    <p>
                      <strong>
                        {formInfo?.subOption === 'choose' ? 'Choose Something For Me' : 'I Have An Idea For A Gift'}
                      </strong>
                    </p>
                  </Col>
                </Row>
              </Col>
              <Col sm={12} md={9}>
                <Row>
                  {formInfo?.subOption === 'idea' && (
                    <>
                      <Col sm={12}>
                        <h3 className={style.ax_page_subtitle} style={{ marginTop: '32px' }}>
                          <span>
                            <UilGift />
                          </span>{' '}
                          Describe Your Gift Idea
                        </h3>
                      </Col>

                      <Col sm={12} md={12}>
                        <div className={style.ax_field}>
                          <label htmlFor="giftIdeaDescription">
                            Description <span>*</span>
                          </label>
                          <textarea
                            name="giftIdeaDescription"
                            id="giftIdeaDescription"
                            onChange={updateFormInfo}
                            value={formInfo.giftIdeaDescription || ''}
                            style={{ minHeight: '160px' }}
                          />
                        </div>
                      </Col>
                    </>
                  )}

                  {formInfo?.subOption === 'choose' && (
                    <Col sm={12} md={12} style={{ marginTop: '32px' }}>
                      <div className={`${style.alertNote} ${style.alertInfo}`}>
                        <Markdown>{gift?.chooseForMeDescription || ''}</Markdown>
                      </div>
                    </Col>
                  )}
                </Row>

                <Row style={{ marginBottom: '32px' }}>
                  <Col sm={12} style={{ marginTop: '32px' }}>
                    <h3 className={style.ax_page_subtitle}>
                      <span>
                        <UilWallet />
                      </span>{' '}
                      Select payment Method <span style={{ color: 'red' }}>*</span>
                    </h3>
                  </Col>
                  <Col sm={12} md={4}>
                    <Switcher
                      id="paymentMethod"
                      name="payrollDeduction"
                      checked={formInfo?.paymentMethod?.payrollDeduction}
                      action={updateFormInfo}
                      label="Deducted From Payroll"
                      labelPos="top"
                    />
                  </Col>
                  <Col sm={12} md={4}>
                    <Switcher
                      id="paymentMethod"
                      name="creditCard"
                      checked={formInfo?.paymentMethod?.creditCard}
                      action={updateFormInfo}
                      label="Paid By Credit Card With a 3.5% Admin Fee."
                      labelPos="top"
                    />
                  </Col>
                </Row>

                <Row style={{ marginBottom: '32px' }}>
                  <Col sm={12}>
                    <h3 className={style.ax_page_subtitle} style={{ marginTop: '12px' }}>
                      <span>
                        <UilUserCircle />
                      </span>{' '}
                      Gift Recipient Information
                    </h3>
                  </Col>

                  <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="clientFirstname">
                        First Name <span>*</span>
                      </label>
                      <input
                        type="text"
                        name="clientFirstname"
                        id="clientFirstname"
                        placeholder="First Name"
                        onChange={updateFormInfo}
                        value={formInfo.clientFirstname || ''}
                      />
                    </div>
                  </Col>

                  <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="clientLastname">
                        Last Name <span>*</span>
                      </label>
                      <input
                        type="text"
                        name="clientLastname"
                        id="clientLastname"
                        placeholder="Last Name"
                        onChange={updateFormInfo}
                        value={formInfo.clientLastname || ''}
                      />
                    </div>
                  </Col>
                  <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="companyOrLender">
                        Company/Lender <span>*</span>
                      </label>
                      <input
                        type="text"
                        name="companyOrLender"
                        id="companyOrLender"
                        placeholder="Company Name"
                        onChange={updateFormInfo}
                        value={formInfo.companyOrLender || ''}
                      />
                    </div>
                  </Col>
                  <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="clientEmail">Email</label>
                      <input
                        type="email"
                        name="clientEmail"
                        id="clientEmail"
                        placeholder="<EMAIL>"
                        onChange={updateFormInfo}
                        value={formInfo.clientEmail || ''}
                      />
                    </div>
                  </Col>
                  <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="clientPhone">
                        Phone Number <span>*</span>
                      </label>
                      <PhoneInput
                        type="tel"
                        name="clientPhone"
                        id="clientPhone"
                        placeholder="************"
                        onChange={updateFormInfo}
                        value={formInfo.clientPhone && formInfo.clientPhone.masked ? formInfo.clientPhone.masked : ''}
                      />
                    </div>
                  </Col>

                  <Col sm={12} md={12}>
                    <div className={style.ax_field}>
                      <label htmlFor="receiverDetails">More About Who Is Receiving The Gift</label>
                      <p>
                        Tell us a bit more about the receiver, for example if we know they love baseball or jazz music,
                        it can help with selecting better gifts.{' '}
                      </p>
                      <textarea
                        name="receiverDetails"
                        id="receiverDetails"
                        onChange={updateFormInfo}
                        value={formInfo.receiverDetails || ''}
                        style={{ minHeight: '160px' }}
                      />
                    </div>
                  </Col>

                  <Col sm={12} md={12}>
                    <div className={style.ax_field}>
                      <label htmlFor="giftCardMessage">Gift Card Message</label>
                      <p>If you'd like to include a message with your gift, please enter it here.</p>
                      <textarea
                        name="giftCardMessage"
                        id="giftCardMessage"
                        onChange={updateFormInfo}
                        value={formInfo.giftCardMessage || ''}
                        style={{ minHeight: '120px' }}
                      />
                    </div>
                  </Col>
                </Row>
                <Row>
                  <Col sm={12} md={12}>
                    <h3 className={style.ax_page_subtitle}>
                      <span>
                        <UilTruck />
                      </span>{' '}
                      Shipping Information
                    </h3>
                  </Col>
                  <Col sm={12} md={8}>
                    <div className={style.ax_field}>
                      <label htmlFor="shippingAddress">Shipping Address</label>
                      <textarea
                        name="shippingAddress"
                        id="shippingAddress"
                        onChange={updateFormInfo}
                        value={formInfo.shippingAddress || ''}
                        style={{ minHeight: '160px' }}
                      />
                    </div>
                  </Col>
                  {/* <Col sm={12} md={4}>
                    <div className={style.ax_field}>
                      <label htmlFor="desiredShippingDate">Desired Shipping Date</label>
                      <input
                        type="date"
                        name="desiredShippingDate"
                        id="desiredShippingDate"
                        onChange={updateFormInfo}
                        value={formInfo.desiredShippingDate || ''}
                      />
                    </div>
                  </Col> */}
                </Row>

                <Row justify="end" style={{ marginTop: '24px' }}>
                  <Col sm={12} md={12}>
                    <Button
                      sizing="large"
                      color="highlight"
                      label="Submit"
                      action={submitRegistration}
                      icon={<UilCheckCircle />}
                      iconPosition="right"
                    />
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>
        </Row>
      </Container>
    </>
  )

  return (
    <>
      <Layout fullpage toast={{ showToast: false, message: '' }}>
        <Processing processing={processingStatus?.visible} />
        <section className={style.eventPage}>
          <EventBanner
            eventStatus={gift.eventStatus}
            showTitle={gift && gift.eventStatus === 'registrationOpen' ? false : true}
            title={gift.title}
            imagePath={gift.banner.url}
            svg={gift && gift.svgBanner ? gift.svgBanner : false}
            calcBannerHeight={(e) => calculatedHeight(e)}
          />

          <div className={style.contentBox}>
            {bannerHeight && bannerHeight <= 0 ? (
              <Processing />
            ) : (
              <Container className={style.eventDetails} style={{ width: '100%' }}>
                <Row>
                  <Col sm={12} lg={12}>
                    <div className={`${style.dateAndPlace} ${style.dateAndPlaceWide}`}>
                      {gift.startDate && (
                        <div className={style.info}>
                          <div className={style.meta}>
                            <UilCalendarAlt size={24} />
                            <h4>
                              Requests up to:{' '}
                              <span>
                                {gift.endDate && gift.endDate !== gift.startDate && (
                                  <>
                                    <Moment format="MMM DD">{gift.endDate}</Moment>
                                    <span>, </span>
                                  </>
                                )}
                                <Moment format="YYYY">{gift.startDate}</Moment>
                              </span>
                            </h4>
                          </div>
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>
                <Row justify="between">
                  <Col sm={12} lg={12}>
                    <div className={style.sideText}>
                      {currentStep === 1 && (
                        <>
                          {/* <h2>{gift.title}</h2> */}
                          <Markdown>{gift.description}</Markdown>
                        </>
                      )}
                      {!formSubmitted ? (
                        <>
                          {renderStepIndicator()}
                          <form className={style.ax_form} ref={form} onSubmit={submitRegistration}>
                            {currentStep === 1 ? renderGiftSelection() : renderDetailsForm()}
                          </form>
                        </>
                      ) : (
                        <div className={style.successMessage}>
                          <h3 style={{ marginBottom: '24px' }}>
                            {' '}
                            <UilCheckCircle size={24} style={{ transform: 'translateY(4px)' }} /> {completionMessage}
                          </h3>
                          <Button
                            sizing="medium"
                            color="highlight"
                            label="Send Another Gift"
                            action={resetForm}
                            icon={<UilPlusCircle />}
                            iconPosition="right"
                          />
                        </div>
                      )}
                    </div>
                  </Col>
                </Row>
              </Container>
            )}
          </div>
        </section>
      </Layout>
    </>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL

  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens

  // Check if jwt exists before making API calls
  if (!jwt) {
    return {
      redirect: {
        destination: '/',
        permanent: false
      }
    }
  }

  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  try {
    const page = await axios
      .get(`${apiURL}/client-gift`, config)
      .then((res) => res.data)
      .catch((err) => {
        console.error('API Error:', err.message)
        throw err
      })

    let user = null

    if (ctx.req.headers.cookie && jwt) {
      user = await axios
        .get(`${apiURL}/users/${userId}`, config)
        .then((res) => {
          const data = res.data
          return data
        })
        .catch((err) => {
          console.error('User fetch error:', err.message)
          return null
        })
    }

    return {
      props: {
        page,
        user: user || null
      }
    }
  } catch (error) {
    console.error('getServerSideProps error:', error.message)

    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      return {
        redirect: {
          destination: '/',
          permanent: false
        }
      }
    }

    // For other errors, return an error page
    return {
      props: {
        error: 'Failed to load data. Please try again later.',
        page: null,
        user: null
      }
    }
  }
}

export default Gift
