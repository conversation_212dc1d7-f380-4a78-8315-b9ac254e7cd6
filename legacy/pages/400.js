import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'
import Layout from '../components/Layout'
import Button from '../components/Button'
import { UilArrowLeft, UilExclamationTriangle } from '@iconscout/react-unicons'
import styles from '../styles/ErrorPage.module.scss'

export default function BadRequestError() {
  const router = useRouter()
  const [errorDetails, setErrorDetails] = useState('')

  useEffect(() => {
    // Extract error details from URL query parameters
    const { message, details } = router.query
    if (message) {
      setErrorDetails(message)
    } else if (details) {
      try {
        const parsedDetails = JSON.parse(details)
        setErrorDetails(parsedDetails.message || 'Invalid request parameters')
      } catch (e) {
        setErrorDetails(details)
      }
    }
  }, [router.query])

  return (
    <Layout fullpage>
      <Head>
        <title>400: Bad Request</title>
      </Head>
      <div className={styles.errorContainer}>
        <div className={styles.errorBox}>
          <div className={styles.errorIcon}>
            <UilExclamationTriangle size="64" />
          </div>
          <h1 className={styles.errorCode}>400</h1>
          <h2 className={styles.errorTitle}>Bad Request</h2>

          <p className={styles.errorDescription}>
            The server cannot process the request due to invalid syntax or parameters.
          </p>

          {errorDetails && (
            <div className={styles.errorDetails}>
              <p>{errorDetails}</p>
            </div>
          )}

          <div className={styles.actions}>
            <Button
              sizing="medium"
              color="highlight"
              label="Go Back"
              action={() => router.back()}
              icon={<UilArrowLeft />}
              iconPosition="left"
            />
            <Button sizing="medium" color="dark" label="Go to Dashboard" action={() => router.push('/dashboard')} />
          </div>
        </div>
      </div>
    </Layout>
  )
}
