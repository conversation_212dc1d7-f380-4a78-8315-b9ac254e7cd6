import Layout from '../../components/Layout'
import axios from 'axios'
import nookies from 'nookies'
import { useState, useEffect } from 'react'
import { serializeArray } from '../../helpers/serializeData'
import ScrollContainer from 'react-indiana-drag-scroll'
import { brightTonesRows, warmTonesRows } from '../../helpers/colorpalettes'
import { Container, Row, Col } from 'react-grid-system'
import { UilInfoCircle } from '@iconscout/react-unicons'
import Card from '../../components/Card'
import style from '../../styles/Branding.module.scss'
import alerts from '../../styles/ToastsAlerts.module.scss'
import scrolltablestyle from '../../styles/ScrollTable.module.scss'

const LogosFonts = ({ logosFonts }) => {
  const headers = ['Color', 'Pantone', 'CMYK', 'RGB', 'WEB', 'Text', 'Headings', 'Fill', 'Accent']
  const [items, setItems] = useState({ logos: [], fonts: [] })

  useEffect(() => {
    if (logosFonts && logosFonts.length > 0) {
      const logos = logosFonts.filter((item) => item.category === 'logo')
      const fonts = logosFonts.filter((item) => item.category === 'font')

      setItems({ logos, fonts })
    }
  }, [])

  const content = () => {
    return (
      <Layout>
        <h1 className={style.ax_page_title}>Logos, Fonts & Guidelines</h1>
        <h2 className={alerts.ax_tip}>Logos</h2>

        <section className={`${style.brandGuidelines} ${style.presentation}`}>
          <Container fluid style={{ padding: '0' }}>
            <Row>
              <Col sm={12} md={7}>
                <h2>The Logo</h2>
                <p>
                  The indi logo is clean, minimal, modern, and speaks to our value proposition: the best way to obtain a
                  mortgage is to seek the expert/unbiased advice of an independent, licensed mortgage professional.
                </p>

                <p>
                  The two i’s represent the individual - the client and our agents/brokers; while at the same time
                  illustrate that the brokerage is here to care and support our people. While each indi agent is an
                  independant broker, we are a collection of the best and brightest in the industry - choosing to work
                  together; to collaborate, innovate, and evolve within the ever changing landscape that is the Canadian
                  mortgage industry.
                </p>
              </Col>
              <Col sm={12} md={3}>
                <img
                  src="images/indi-logo-standard.svg"
                  alt="the Indi logo"
                  style={{ padding: '56px', maxWidth: '360px', border: '1px solid #f0f0f0', borderRadius: '8px' }}
                  className={style.img_responsive}
                />
              </Col>
            </Row>

            <Row style={{ marginTop: '32px' }}>
              <Col sm={12} md={7}>
                <h2>The Logomark (Symbol)</h2>
                <p>The abstract, rounded shape of the i and m represent indi mortgage.</p>

                <p>
                  The bold logotype and logomark should be clearly displayed in its primary colours (black/white)
                  against black, white, and coloured backgrounds from our approved colour palettes.
                </p>
                <p>
                  The words “Indi Mortgage - The Independent Mortgage Company” must be clearly displayed within the
                  asset when using the logomark. Primary use case: swag and promotional items where the full logo may
                  not be a fit.
                </p>
              </Col>
              <Col sm={12} md={3}>
                <img
                  src="images/indi-symbol.svg"
                  alt="the Logomark (Symbol)"
                  style={{ padding: '56px', maxWidth: '260px', border: '1px solid #f0f0f0', borderRadius: '8px' }}
                  className={style.img_responsive}
                />
              </Col>
            </Row>
          </Container>
        </section>

        <section className={style.brandList}>
          <div className={style.ax_card_list}>
            {items && items.logos && items.logos.length > 0
              ? items.logos.map((logo) => {
                  return (
                    <Card
                      hasButton
                      buttonLabel="Download"
                      image={logo?.thumbnail?.url}
                      description={logo.title}
                      isLink
                      isDownload
                      linkUrl={logo?.files?.url}
                      openInBlank
                      size="vertical"
                      key={logo.title}
                    />
                  )
                })
              : ''}
          </div>

          <h2 className={alerts.ax_tip}>Fonts</h2>

          <div className={style.ax_card_list} style={{ width: '100%' }}>
            {items && items.fonts && items.fonts.length > 0
              ? items.fonts.map((logo) => {
                  return (
                    <Card
                      hasButton
                      buttonLabel="Download"
                      image={logo?.thumbnail?.url}
                      description={logo.title}
                      isLink
                      isDownload
                      linkUrl={logo?.files?.url}
                      openInBlank
                      size="vertical"
                      key={logo.title}
                    />
                  )
                })
              : ''}
          </div>
        </section>

        <section className={style.brandGuidelines}>
          <h2>Logo Format</h2>
          <p>
            The Indi Mortgage logo has been designed in a stacked version, where ‘mortgage’ must always appear below the
            Indi word.
          </p>

          <h3>Printing Standard Size</h3>
          <p>
            The standard size for the Indi Mortgage logo is 1.25” inches (31.75 mm) wide (proportionately). This size is
            recommended for business cards, letterhead, envelopes, etc.
          </p>
          <img src="./images/guidelines/Standard-Size.svg" alt="Indi Logo size limit" />
          <img src="./images/guidelines/Standard-Size-Horizontal.svg" alt="Indi Logo size limit" />
        </section>

        <section className={style.brandGuidelines}>
          <h2>Minumum Size</h2>
          <p>
            Minimum size refers to the smallest size at which the Indi Mortgage logo may be reproduced to ensure
            legibility. Do not reproduce the Indi Mortgage logo at any size less than 1” inch (25.4 mm) in width
            (proportionately).
          </p>
          <img src="./images/guidelines/Minimum-Size.svg" alt="Indi Logo minimum size" />
          <img src="./images/guidelines/Minimum-Size-Horizontal.svg" alt="Indi Logo minimum size" />
          <img src="./images/guidelines/Wrong-Size.svg" alt="Indi Logo wrong size" />
          <img src="./images/guidelines/Wrong-Size-Horizontal.svg" alt="Indi Logo wrong size" />{' '}
        </section>

        <section className={style.brandGuidelines}>
          <h2>Protective Space</h2>
          <p>
            Always maintain the minimum protective space around the Indi Mortgage logo to maintain clarity and impact of
            the logo. The minimum protective space is X, where X is equal to the half height of the letter ‘O’ in Indi.
            This space is required around all sides of the logo. This applies to positioning around other printed
            elements like type, other logos and photography, as well as from edges of the printed piece.
          </p>
          <img
            src="./images/guidelines/Protective-Area.svg"
            alt="Indi Logo protective space"
            style={{ minWidth: '300px', height: 'auto' }}
          />
          <img
            src="./images/guidelines/Protective-Area-Horizontal.svg"
            alt="Indi Logo protective space"
            style={{ minWidth: '300px', height: 'auto' }}
          />
        </section>

        <h2 className={alerts.ax_tip}>Official Supporting Colors</h2>

        <section className={style.brandGuidelines}>
          <h2>Use of Supporting Colors</h2>
          <p className={`${style.alertNote} ${style.alertWarning}`}>
            <UilInfoCircle size={16} />
            <strong>Warning: </strong> <b>The Indi Logo must be used in black and white colours only.</b>
          </p>
          <p>Supporting colours are to be used as accents and background to our primary logo colours.</p>
          <p>
            Given the array of options it is best to stick with one to two supporting colours. Please see following
            pages for colour blocking and use recommendations.
          </p>

          <h3>The Warm Tones Collection</h3>

          <ScrollContainer
            horizontal
            vertical={false}
            className={scrolltablestyle.table}
            style={{ marginBottom: '32px' }}
          >
            <table className={scrolltablestyle.table} cellPadding="0" cellSpacing="0">
              <thead>
                <tr>
                  {headers.map((item) => (
                    <th key={item.name}>{item}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {warmTonesRows.map((item) => (
                  <tr key={item.web}>
                    <td>
                      <div
                        style={{ width: '32px', height: '32px', borderRadius: '6px', backgroundColor: item.web }}
                      ></div>
                    </td>
                    <td>{item.pantone}</td>
                    <td>{item.cmyk}</td>
                    <td>{item.rgb}</td>
                    <td>{item.web}</td>
                    <td>{item.text}</td>
                    <td>{item.headings}</td>
                    <td>{item.fill}</td>
                    <td>{item.accent}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </ScrollContainer>
          <h3>The Brights Collection</h3>

          <ScrollContainer horizontal vertical={false} className={scrolltablestyle.table}>
            <table className={scrolltablestyle.table} cellPadding="0" cellSpacing="0">
              <thead>
                <tr>
                  {headers.map((item) => (
                    <th key={item.name}>{item}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {brightTonesRows.map((item) => (
                  <tr key={item.web}>
                    <td>
                      <div
                        style={{ width: '32px', height: '32px', borderRadius: '6px', backgroundColor: item.web }}
                      ></div>
                    </td>
                    <td>{item.pantone}</td>
                    <td>{item.cmyk}</td>
                    <td>{item.rgb}</td>
                    <td>{item.web}</td>
                    <td>{item.text}</td>
                    <td>{item.headings}</td>
                    <td>{item.fill}</td>
                    <td>{item.accent}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </ScrollContainer>

          <Container fluid style={{ padding: '0' }}>
            <Row style={{ marginBottom: '32px' }}>
              <Col sm={12} md={5}>
                <h2>Colour Blocking</h2>
                <p>
                  Our marketing department is happy to help with colour theory/use of the approved supporting colours.
                </p>

                <p>
                  (Note: All marketing created outside of the brokerage must be sent to our VP of Marketing prior to
                  use/distribution).
                </p>
              </Col>
              <Col sm={12} md={3}>
                <img src="images/colour-blocking.svg" alt="colour blocking" className={style.img_responsive} />
              </Col>
            </Row>
            <Row>
              <Col sm={12} md={3}>
                <img src="images/colour-blocking-schema.svg" alt="colour blocking" className={style.img_responsive} />
              </Col>
              <Col sm={12} md={5}>
                <img src="images/colour-blocking-examples.svg" alt="colour blocking" className={style.img_responsive} />
              </Col>
            </Row>
          </Container>
        </section>
        <section className={style.brandGuidelines}>
          <h2>The prohibited uses</h2>
          <p>
            Under no circumstances should the logo be redrawn, modified or altered in any way. The logo should never be
            displayed in any other orientation different from a horizontal format. Do not place the logo on its side. Do
            not stretch or skew the logo in any unproportional orientation.
          </p>
          <div className={style.imagesList}>
            <div className={style.image}>
              <h4>Do not rotate</h4>
              <img src="./images/logo-no-rotate.svg" alt="Logo rotated" />
            </div>

            <div className={style.image}>
              <h4>Do not add shadow</h4>
              <img src="./images/logo-no-shadow.png" alt="Logo with shadow" />
            </div>

            <div className={style.image}>
              <h4>Do not stretch</h4>
              <img src="./images/logo-no-stretch.svg" alt="Logo stretched" />
            </div>

            <div className={style.image}>
              <h4>Do not use in vertical</h4>
              <img src="./images/logo-no-vertical.svg" alt="Logo vertical" />
            </div>
            <div className={style.image}>
              <h4>Do not colorize (use only black and white)</h4>
              <img src="./images/logo-no-colorize.svg" alt="Logo colorized" />
            </div>
          </div>
        </section>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = `${process.env.API_URL}`
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => res.data)
      .catch((err) => {
        throw err
      })
  }

  const logosFonts = await axios
    .get(
      `${apiURL}/logos-fonts`,

      config
    )
    .then((res) => {
      return serializeArray(res.data)
    })
    .catch((err) => {
      throw err
    })

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          logosFonts
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default LogosFonts
