import { useState, useRef, useEffect, useMemo, useContext } from 'react'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import axios from 'axios'
import { serializeJson } from '../helpers/serializeData'
import Moment from 'react-moment'
import { UilFolder, UilAngleDown, UilAngleUp, UilStar } from '@iconscout/react-unicons'
import Layout from '../components/Layout'
import style from '../styles/SocialMedia.module.scss'
import Processing from '../components/Processing'
import SocialPostsList from '../components/SocialPosts/SocialPostsList'
import AuthContext from '../context/authContext'

const jwt = Cookies.get('jwt')

// Helper function to check if a post is available for a specific province
const isPostAvailableForProvince = (post, userProvince) => {
  if (!post.Province || !Array.isArray(post.Province) || post.Province.length === 0) {
    return false
  }

  const userProvinceLower = userProvince ? userProvince.toLowerCase() : ''

  // Check if any province in the post has 'all' set to true or matches the user's province
  return post.Province.some((prov) => {
    // Handle the case where province data is in a ref object
    const provinceData = prov.ref || prov

    // Check for 'all' flag first
    if (provinceData.all === true) return true

    // Check each property in the province object for a match
    return Object.keys(provinceData).some((key) => {
      // Skip non-boolean properties or special properties
      if (
        typeof provinceData[key] !== 'boolean' ||
        key === '__v' ||
        key === '_id' ||
        key === 'id' ||
        key === 'images' ||
        key === 'kind' ||
        key === '[[Prototype]]'
      ) {
        return false
      }

      // Compare lowercase key with user province
      return key.toLowerCase() === userProvinceLower && provinceData[key] === true
    })
  })
}

// Helper function to get province-specific content
const getProvinceContent = (post, userProvince) => {
  // Convert userProvince to lowercase for case-insensitive comparison
  const userProvinceLower = userProvince ? userProvince.toLowerCase() : ''

  // Find a province that matches either 'all' flag or the user's province (case-insensitive)
  const provinceData = post.Province.find((prov) => {
    // Handle the case where province data is in a ref object
    const provinceRef = prov.ref || prov

    // Check for 'all' flag first
    if (provinceRef.all === true) return true

    // Check each property in the province object for a match
    return Object.keys(provinceRef).some((key) => {
      // Skip non-boolean properties or special properties
      if (
        typeof provinceRef[key] !== 'boolean' ||
        key === '__v' ||
        key === '_id' ||
        key === 'id' ||
        key === 'images' ||
        key === 'kind' ||
        key === '[[Prototype]]'
      ) {
        return false
      }

      // Compare lowercase key with user province
      return key.toLowerCase() === userProvinceLower && provinceRef[key] === true
    })
  })

  if (!provinceData) return null

  // Images might be in the provinceData directly or in the ref object
  const images = provinceData.images || (provinceData.ref && provinceData.ref.images)

  if (!images) {
    console.error('No images found in province data:', provinceData)
    return null
  }

  return {
    images: images,
    captions: post.captionTexts || null,
    calendar: post.calendar?.url || null,
    month: post.month,
    isExtra: post.isExtra
  }
}

// Update the fetchSocialMedia function
const fetchSocialMedia = async (filters = {}, jwt) => {
  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const queryParams = new URLSearchParams()

  // Handle Strapi v3 filters
  if (filters.month) {
    queryParams.append('month', filters.month)
  }

  if (filters._sort) {
    queryParams.append('_sort', filters._sort)
  }

  if (filters._limit) {
    queryParams.append('_limit', filters._limit)
  }

  try {
    const response = await axios.get(`${apiURL}/social-medias?${queryParams.toString()}`, {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    })
    return response.data
  } catch (error) {
    console.error('Error fetching social media:', error)
    return [] // Return empty array instead of throwing to prevent redirect
  }
}

const SocialMedia = (props) => {
  const { availableMonths } = props
  const { userAuth } = useContext(AuthContext)
  const user = userAuth.userInfo
  const [content, setContent] = useState(null)
  const [showExtras, setShowExtras] = useState(false)
  const [isMonthsMobOpen, setIsMonthsMobOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const contentPage = useRef()

  // Memoize filtered months
  const { monthlyPosts, extraPosts } = useMemo(() => {
    if (!availableMonths?.data) {
      return { monthlyPosts: [], extraPosts: [] }
    }
    return {
      monthlyPosts: availableMonths.data
        .filter((p) => !p.isExtra)
        .filter((p) => isPostAvailableForProvince(p, user?.team?.province)),
      extraPosts: availableMonths.data
        .filter((p) => p.isExtra)
        .filter((p) => isPostAvailableForProvince(p, user?.team?.province))
    }
  }, [availableMonths, user?.team?.province])

  // Fetch initial content based on the latest available month
  useEffect(() => {
    // Only run if monthlyPosts has been populated and content hasn't been set yet
    if (monthlyPosts.length > 0 && !content) {
      // Sort monthlyPosts by month descending to find the latest
      // Ensure month format is sortable, e.g., 'YYYY-MM-DD'
      const sortedMonthlyPosts = [...monthlyPosts].sort((a, b) => {
        // Assuming p.month is in 'YYYY-MM-DD' or similar sortable format
        return new Date(b.month) - new Date(a.month)
      })

      if (sortedMonthlyPosts.length > 0) {
        const latestPost = sortedMonthlyPosts[0]
        // Pass the lightweight post object to handleSetContent
        handleSetContent(latestPost)
      } else {
        console.log("No monthly posts available for this user's province after filtering.")
        setIsLoading(false) // Stop loading if no posts found
      }
    } else if (monthlyPosts.length === 0 && availableMonths?.data) {
      // Handle case where availableMonths loaded but resulted in zero monthlyPosts
      console.log("No monthly posts available for this user's province.")
      setIsLoading(false) // Stop loading
    }
  }, [monthlyPosts, content, availableMonths]) // Rerun if monthlyPosts changes or content is set

  // Modified handleSetContent with scroll-to-top behavior
  const handleSetContent = async (post, isExtra = false) => {
    // Check if post object and id are valid
    if (!post || !post.id) {
      console.error('Invalid post object passed to handleSetContent:', post)
      setIsLoading(false) // Ensure loading stops if post is invalid
      alert('Failed to load content: Invalid post data.')
      return
    }

    setIsLoading(true)
    try {
      // Scroll to top of the content area
      if (contentPage.current) {
        contentPage.current.scrollIntoView({ behavior: 'smooth' })
      } else {
        // Fallback to scrolling to top of window
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }

      // Fetch post directly by ID
      const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/social-medias/${post.id}`, {
        headers: {
          Authorization: `Bearer ${jwt}`
        }
      })

      // Log response for debugging

      if (response.data) {
        const fullPost = response.data

        // Check if Province data exists and has the right structure
        if (!fullPost.Province || !Array.isArray(fullPost.Province) || fullPost.Province.length === 0) {
          throw new Error('Post data is missing province information')
        }

        const provinceContent = getProvinceContent(fullPost, user.team?.province)

        if (provinceContent) {
          setContent({
            ...provinceContent,
            isExtra: fullPost.isExtra || isExtra
          })
        } else {
          throw new Error(`No content found for your province (${user.team?.province})`)
        }
      } else {
        throw new Error('No content found for selected post')
      }
    } catch (error) {
      console.error('Error fetching post content:', error)

      // More detailed error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data)
        console.error('Error response status:', error.response.status)
        console.error('Error response headers:', error.response.headers)
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request)
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message)
      }

      alert(`Failed to load content: ${error.message}. Please try again.`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Layout>
      <Processing processing={isLoading} message="Loading content..." position="top" />
      <h1 className={style.ax_page_title}>Social media content</h1>
      <div className={style.contentContainer} ref={contentPage}>
        {monthlyPosts ? (
          <ul className={style.listColumn}>
            <li key="extras" className={style.extras}>
              <button type="button" onClick={() => setShowExtras((prev) => !prev)}>
                <UilStar /> Extra Posts {showExtras ? <UilAngleUp /> : <UilAngleDown />}
              </button>
              {showExtras && (
                <ul className={style.submenuVisible}>
                  {extraPosts.map((p) => (
                    <li key={p.id}>
                      <button type="button" onClick={() => handleSetContent(p, true)}>
                        <UilStar /> <small>{p.extraTitle}</small>
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </li>
            {monthlyPosts.map((p) => (
              <li key={p.id}>
                <button type="button" onClick={() => handleSetContent(p, false)}>
                  <UilFolder /> <Moment format="MMMM Y">{p.month}</Moment>
                </button>
              </li>
            ))}
          </ul>
        ) : (
          <Processing processing={true} message="Loading months..." position="top" />
        )}

        <button
          className={`${style.listColumnMob} ${isMonthsMobOpen ? style.listColumnMobOpen : ''}`}
          onClick={() => setIsMonthsMobOpen((prev) => !prev)}
          type="button"
        >
          Select a Month {isMonthsMobOpen ? <UilAngleUp /> : <UilAngleDown />}
          {monthlyPosts ? (
            <ul>
              {monthlyPosts.map((p) => (
                <li key={p.id}>
                  <button type="button" onClick={() => handleSetContent(p)}>
                    <UilFolder /> <Moment format="MMMM Y">{p.month}</Moment>
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <Processing processing={true} message="Loading months..." position="top" />
          )}
        </button>

        <section className={style.contentColumn}>
          {content && (
            <SocialPostsList
              images={content.images}
              captions={content.captions}
              calendar={content.calendar}
              month={content.month}
              isExtra={content.isExtra}
            />
          )}
        </section>
      </div>
    </Layout>
  )
}

// Update getServerSideProps to handle errors better
export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  try {
    const tokens = nookies.get(ctx)
    const { jwt, userId } = tokens

    if (!jwt || !userId) {
      return {
        redirect: {
          destination: '/',
          permanent: false
        }
      }
    }

    const apiURL = process.env.NEXT_PUBLIC_API_URL
    const config = {
      headers: {
        Authorization: `Bearer ${jwt}`
      }
    }

    // Get lightweight data for sidebar using the new endpoint
    const availableMonthsResponse = await axios.get(`${apiURL}/social-medias/lightweight`, config)

    return {
      props: {
        availableMonths: serializeJson(availableMonthsResponse.data) || { data: [] }
      }
    }
  } catch (error) {
    console.error('Error in getServerSideProps:', error)
    if (error.response?.status === 401) {
      return {
        redirect: {
          destination: '/',
          permanent: false
        }
      }
    }

    return {
      props: {
        initialPost: null,
        availableMonths: { data: [] }
      }
    }
  }
}

export default SocialMedia
