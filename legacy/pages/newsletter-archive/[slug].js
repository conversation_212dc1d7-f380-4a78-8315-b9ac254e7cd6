import { useRef, useEffect, useState } from 'react'
import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import { UilExternalLinkAlt } from '@iconscout/react-unicons'
import style from '../../styles/Printables.module.scss'
import Button from '../../components/Button'

const Newsletter = (props) => {
  const { newsletter } = props
  const viewer = useRef(null)
  const [isMobile, setIsMobile] = useState(true)

  useEffect(() => {
    if (window) {
      if (window.innerWidth <= 767) {
        setIsMobile(true)
      } else {
        setIsMobile(false)
      }
    }
  }, [])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>Newsletter</h1>
      {isMobile ? (
        <Button
          label="Open Newsletter"
          icon={<UilExternalLinkAlt />}
          iconPos="right"
          linkPath={newsletter[0].file.url}
          isDownload
          isLink
          blank
          color="highlight"
        />
      ) : (
        <div className={style.frame}>
          <iframe height="2800" title="test-frame" src={newsletter[0].file.url} ref={viewer} type="application/pdf" />
        </div>
      )}
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const newsletter = await axios
        .get(`${apiURL}/newsletter-archives?slug_eq=${ctx.params.slug}`, config)
        .then((res) => {
          const lenderData = serializeJson(res.data)
          return lenderData
        })
        .catch((error) => {
          throw error
        })

      return {
        props: { newsletter }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Newsletter
