import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Layout from '../components/Layout'
import Card from '../components/Card'
import Markdown from '../components/Markdown'
import style from '../styles/GroupBenefits.module.scss'

const GroupBenefits = (props) => {
  const { pageData } = props

  return (
    <Layout>
      <h1 className={style.ax_page_title}>{pageData.pageTitle}</h1>

      <section className={style.heading}>
        <Markdown>{pageData.description}</Markdown>
      </section>
      <section className={style.list}>
        <div className={style.ax_card_list}>
          {pageData.files.map((file) => (
            <Card
              key={file.id}
              title={file.title}
              hasButton
              linkUrl={file.file.url}
              iconSquared="./images/ico-pdf.svg"
              buttonLabel="Download"
              isDownload
              openInBlank
            />
          ))}
        </div>
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const pageData = await axios
        .get(`${apiURL}/page-group-benefits`, config)
        .then((res) => {
          const page = res.data
          return page
        })
        .catch((err) => {
          throw err
        })

      return {
        props: {
          pageData
        }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default GroupBenefits
