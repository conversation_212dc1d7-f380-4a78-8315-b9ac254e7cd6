import axios from 'axios'
import nookies from 'nookies'
import { serializeJson } from '../helpers/serializeData'
import Layout from '../components/Layout'
import ScrollContainer from 'react-indiana-drag-scroll'
import { brightTonesRows, warmTonesRows } from '../helpers/colorpalettes'
import style from '../styles/Branding.module.scss'
import scrolltablestyle from '../styles/ScrollTable.module.scss'

const BrandingMaterial = () => {
  const headers = ['Color', 'Pantone', 'CMYK', 'RGB', 'WEB', 'Text', 'Headings', 'Fill', 'Accent']

  const content = () => {
    return (
      <Layout>
        <h1 className={style.ax_page_title}>Brand Guidelines</h1>

        <section className={style.brandGuidelines}>
          <h2>Logo Format</h2>
          <p>
            The Indi Mortgage logo has been designed in a stacked version, where ‘mortgage’ must always appear below the
            Indi word.
          </p>

          <h3>Printing Standard Size</h3>
          <p>
            The standard size for the Indi Mortgage logo is 1.25” inches (31.75 mm) wide (proportionately). This size is
            recommended for business cards, letterhead, envelopes, etc.
          </p>
          <img src="./images/guidelines/Standard-Size.svg" alt="Indi Logo size limit" />
          <img src="./images/guidelines/Standard-Size-Horizontal.svg" alt="Indi Logo size limit" />
        </section>

        <section className={style.brandGuidelines}>
          <h2>Minumum Size</h2>
          <p>
            Minimum size refers to the smallest size at which the Indi Mortgage logo may be reproduced to ensure
            legibility. Do not reproduce the Indi Mortgage logo at any size less than 1” inch (25.4 mm) in width
            (proportionately).
          </p>
          <img src="./images/guidelines/Minimum-Size.svg" alt="Indi Logo minimum size" />
          <img src="./images/guidelines/Minimum-Size-Horizontal.svg" alt="Indi Logo minimum size" />
          <img src="./images/guidelines/Wrong-Size.svg" alt="Indi Logo wrong size" />
          <img src="./images/guidelines/Wrong-Size-Horizontal.svg" alt="Indi Logo wrong size" />{' '}
        </section>

        <section className={style.brandGuidelines}>
          <h2>Protective Space</h2>
          <p>
            Always maintain the minimum protective space around the Indi Mortgage logo to maintain clarity and impact of
            the logo. The minimum protective space is X, where X is equal to the half height of the letter ‘O’ in Indi.
            This space is required around all sides of the logo. This applies to positioning around other printed
            elements like type, other logos and photography, as well as from edges of the printed piece.
          </p>
          <img
            src="./images/guidelines/Protective-Area.svg"
            alt="Indi Logo protective space"
            style={{ minWidth: '300px', height: 'auto' }}
          />
          <img
            src="./images/guidelines/Protective-Area-Horizontal.svg"
            alt="Indi Logo protective space"
            style={{ minWidth: '300px', height: 'auto' }}
          />
        </section>

        <section className={style.brandGuidelines}>
          <h2>Use of Colors</h2>
          <p>
            The Indi Mortgage visual identity has a wide color pallete. See below all colors and their recommended use..
          </p>
          <ScrollContainer
            horizontal
            vertical={false}
            className={scrolltablestyle.table}
            style={{ marginBottom: '32px' }}
          >
            <table className={scrolltablestyle.table} cellPadding="0" cellSpacing="0">
              <thead>
                <h3>Warm Tones</h3>

                <tr>
                  {headers.map((item) => (
                    <th key={item.name}>{item}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {warmTonesRows.map((item) => (
                  <tr key={item.web}>
                    <td>
                      <div
                        style={{ width: '32px', height: '32px', borderRadius: '6px', backgroundColor: item.web }}
                      ></div>
                    </td>
                    <td>{item.pantone}</td>
                    <td>{item.cmyk}</td>
                    <td>{item.rgb}</td>
                    <td>{item.web}</td>
                    <td>{item.text}</td>
                    <td>{item.headings}</td>
                    <td>{item.fill}</td>
                    <td>{item.accent}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </ScrollContainer>

          <ScrollContainer horizontal vertical={false} className={scrolltablestyle.table}>
            <table className={scrolltablestyle.table} cellPadding="0" cellSpacing="0">
              <thead>
                <h3>Bright Tones</h3>

                <tr>
                  {headers.map((item) => (
                    <th key={item.name}>{item}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {brightTonesRows.map((item) => (
                  <tr key={item.web}>
                    <td>
                      <div
                        style={{ width: '32px', height: '32px', borderRadius: '6px', backgroundColor: item.web }}
                      ></div>
                    </td>
                    <td>{item.pantone}</td>
                    <td>{item.cmyk}</td>
                    <td>{item.rgb}</td>
                    <td>{item.web}</td>
                    <td>{item.text}</td>
                    <td>{item.headings}</td>
                    <td>{item.fill}</td>
                    <td>{item.accent}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </ScrollContainer>
        </section>

        <section className={style.brandGuidelines}>
          <h2>The prohibited uses</h2>
          <p>
            Under no circumstances should the logo be redrawn, modified or altered in any way. The logo should never be
            displayed in any other orientation different from a horizontal format. Do not place the logo on its side. Do
            not stretch or skew the logo in any unproportional orientation.
          </p>
          <div className={style.imagesList}>
            <div className={style.image}>
              <h4>Do not rotate</h4>
              <img src="./images/logo-no-rotate.svg" alt="Logo rotated" />
            </div>

            <div className={style.image}>
              <h4>Do not add shadow</h4>
              <img src="./images/logo-no-shadow.png" alt="Logo with shadow" />
            </div>

            <div className={style.image}>
              <h4>Do not stretch</h4>
              <img src="./images/logo-no-stretch.svg" alt="Logo stretched" />
            </div>

            <div className={style.image}>
              <h4>Do not use in vertical</h4>
              <img src="./images/logo-no-vertical.svg" alt="Logo vertical" />
            </div>
          </div>
        </section>
      </Layout>
    )
  }

  return content()
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.Indimortgage.ca',
          permanent: false
        }
      }
    } else {
      return {
        props: {
          userData
        }
      }
    }
  }

  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default BrandingMaterial
