import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import axios from 'axios'
import nookies from 'nookies'
import Cookies from 'js-cookie'
import { serializeJson } from '../../helpers/serializeData'
import Layout from '../../components/Layout'
import Card from '../../components/Card'
import Processing from '../../components/Processing'
import style from '../../styles/ContentPage.module.scss'

const PrintableDocuments = (props) => {
  const { docs, user } = props
  const [processing, setProcessing] = useState(true)
  const [documents, setDocuments] = useState(null)
  const [qrId, setQrId] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [enabled, setEnabled] = useState()
  const [showOnPrintable, setShowOnPrintable] = useState({
    photoOnPrintable: user.photoOnPrintable,
    qrCodeOnPrintable: user.qrCodeOnPrintable
  })
  const router = useRouter()
  const apiUrl = process.env.API_URL
  const userId = Cookies.get('userId')
  const token = Cookies.get('jwt')
  const config = {
    headers: {
      Authorization: `Bearer ${token}`
    }
  }

  const docsToArray = () => {
    const docsArr = []
    for (let d in docs) {
      if (docs[d].isHidden !== true) {
        docsArr.push(docs[d])
      }
    }
    return docsArr
  }

  const printableDocs = docsToArray()

  const allDocs = (p) => {
    const filtered = []
    printableDocs.map((c) => {
      c.provinceFile.forEach((f) => {
        if (f.province === router.query.category) {
          filtered.push({ file: f, title: c.title, contactPosition: c.contactPosition, thumbnail: f.thumbnail })
        }
      })
    })

    setDocuments(filtered)
    setProcessing(false)
    return filtered
  }

  const handleShowOnPrintable = async (e) => {
    setIsLoading(true)
    let whatToShow
    if (e.target.name === 'photo') {
      whatToShow = { photoOnPrintable: true, qrCodeOnPrintable: false, emptyPrintableFooter: false }
    }
    if (e.target.name === 'qrcode') {
      whatToShow = { photoOnPrintable: false, qrCodeOnPrintable: true, emptyPrintableFooter: false }
    }

    if (e.target.name === 'empty') {
      whatToShow = { photoOnPrintable: false, qrCodeOnPrintable: false, emptyPrintableFooter: true }
    }

    setShowOnPrintable(whatToShow)

    await axios
      .put(`${apiUrl}/users/${user.id}`, whatToShow, config)
      .then((res) => {
        setShowOnPrintable({
          photoOnPrintable: res.data.photoOnPrintable,
          qrCodeOnPrintable: res.data.qrCodeOnPrintable,
          emptyPrintableFooter: res.data.emptyPrintableFooter
        })
        setIsLoading(false)
        // eslint-disable-next-line no-console
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.log(err)
      })
  }

  const handleShowQrList = () => {
    if (user.qrCodes && user.qrCodes.length > 0) {
      return user.qrCodes.map((qr) => (
        <option id={qr.id} value={qr.qrImage}>
          {qr.url}
        </option>
      ))
    }

    return (
      <option id="noQR" value="noQR">
        No QR Codes found. Show your photo instead.
      </option>
    )
  }

  const handleQrId = async (e) => {
    if (user && user.qrCodes && user.qrCodes.length > 0) {
      const { qrCodes } = user

      const updatedQrCodes = qrCodes.map((qr) => {
        if (qr.id === e.target.options[e.target.selectedIndex].id) {
          return { ...qr, isLastUsed: true }
        } else {
          return { ...qr, isLastUsed: false }
        }
      })
      await axios.put(`${apiUrl}/users/${userId}`, { qrCodes: updatedQrCodes }, config)
    }

    setQrId(e.target.value)
  }

  const handleEnabled = () => {
    if (
      (qrId === null || qrId === 'null' || qrId === undefined) &&
      showOnPrintable.qrCodeOnPrintable === true &&
      showOnPrintable.photoOnPrintable === false &&
      showOnPrintable.emptyPrintableFooter === false
    ) {
      return true
    }

    if (
      (qrId === null || qrId === 'null' || qrId === undefined) &&
      showOnPrintable.qrCodeOnPrintable === false &&
      showOnPrintable.photoOnPrintable === false &&
      showOnPrintable.emptyPrintableFooter === false
    ) {
      return true
    }

    if (
      (showOnPrintable.qrCodeOnPrintable === false ||
        showOnPrintable.qrCodeOnPrintable === undefined ||
        showOnPrintable.qrCodeOnPrintable === null) &&
      (showOnPrintable.photoOnPrintable === false ||
        showOnPrintable.photoOnPrintable === undefined ||
        showOnPrintable.photoOnPrintable === null) &&
      (showOnPrintable.emptyPrintableFooter === false ||
        showOnPrintable.emptyPrintableFooter === undefined ||
        showOnPrintable.emptyPrintableFooter === null)
    ) {
      return true
    }

    if (showOnPrintable.qrCodeOnPrintable === true) {
      if (qrId === null || qrId === 'null') {
        return true
      } else {
        return false
      }
    }
    if (showOnPrintable.photoOnPrintable === true) {
      return false
    }
    if (showOnPrintable.emptyPrintableFooter === true) {
      return false
    }
  }

  const camelCaseToTitleCase = (str) => {
    return str.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
      return str.toUpperCase()
    })
  }
  useEffect(() => {
    setEnabled(handleEnabled())
  }, [showOnPrintable, qrId])

  useEffect(() => {
    allDocs()
    const { id, qrCodes, qrCodeOnPrintable, photoOnPrintable, emptyPrintableFooter } = user

    if (qrCodes.length === 0 && qrCodeOnPrintable && emptyPrintableFooter === false) {
      setIsLoading(true)
      const token = Cookies.get('jwt')
      axios
        .put(
          `${apiUrl}/users/${id}`,
          { photoOnPrintable: false, qrCodeOnPrintable: false, emptyPrintableFooter: true },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        )
        .then((res) => {
          setShowOnPrintable({
            photoOnPrintable: res.data.photoOnPrintable,
            qrCodeOnPrintable: res.data.qrCodeOnPrintable,
            emptyPrintableFooter: res.data.emptyPrintableFooter
          })
          setIsLoading(false)
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.log(err)
        })
    }

    if (photoOnPrintable === false && qrCodeOnPrintable === false && emptyPrintableFooter === false) {
      setIsLoading(true)
      const token = Cookies.get('jwt')
      axios
        .put(
          `${apiUrl}/users/${id}`,
          { photoOnPrintable: false, qrCodeOnPrintable: false, emptyPrintableFooter: true },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        )
        .then((res) => {
          setShowOnPrintable({
            photoOnPrintable: res.data.photoOnPrintable,
            qrCodeOnPrintable: res.data.qrCodeOnPrintable,
            emptyPrintableFooter: res.data.emptyPrintableFooter
          })
          setIsLoading(false)
        })
        .catch((err) => {
          // eslint-disable-next-line no-console
          console.log(err)
        })
    }
  }, [])

  return (
    <Layout>
      <h1 className={style.ax_page_title}>
        <span>{router.query.category ? `${camelCaseToTitleCase(router.query.category)} ` : ''}</span> printable
        Documents{' '}
      </h1>
      <div className={style.heading} style={isLoading ? { minHeight: '100px' } : {}}>
        <Processing processing={isLoading} />

        <div className={style.options}>
          <h4>
            Are you going to generate a Printable? What do you want to show on the printable <strong>Footer</strong>?
          </h4>
          <form style={{ display: 'flex', alignItems: 'flex-start' }}>
            <label>
              <input
                type="radio"
                name="empty"
                checked={!!showOnPrintable.emptyPrintableFooter}
                onChange={(e) => handleShowOnPrintable(e)}
              />
              No Photo or QR Code (Empty)
            </label>
            <label>
              <input
                type="radio"
                name="photo"
                checked={!!showOnPrintable.photoOnPrintable}
                onChange={(e) => handleShowOnPrintable(e)}
              />
              My Profile Photo
            </label>

            {user.qrCodes && user.qrCodes.length > 0 ? (
              <label>
                <input
                  type="radio"
                  name="qrcode"
                  checked={!!showOnPrintable.qrCodeOnPrintable}
                  onChange={(e) => handleShowOnPrintable(e)}
                />
                A QR Code
              </label>
            ) : (
              ''
            )}

            {showOnPrintable.qrCodeOnPrintable ? (
              <div style={{ maxWidth: '220px' }}>
                <label htmlFor="qrCodes">QR Codes:</label>
                <select name="qrCodes" onChange={(e) => handleQrId(e)} style={{ maxWidth: '220px' }}>
                  <option value="null">Select a QR Code</option>
                  {handleShowQrList()}
                </select>
              </div>
            ) : (
              ''
            )}
          </form>
          {enabled ? (
            showOnPrintable.qrCodeOnPrintable && (qrId === null || qrId === 'null') ? (
              <h3 className={style.error}>Please select a QR Code on the list.</h3>
            ) : (
              <h3 className={style.error}>Please select a footer style option above.</h3>
            )
          ) : (
            ''
          )}
        </div>
      </div>
      <section className={style.ax_card_list}>
        {documents === null ? (
          <Processing processing={processing} />
        ) : (
          documents.map((doc) => {
            const slug = doc.title.replace(/\s+/g, '-').toLowerCase()

            return (
              <Card
                key={doc.title}
                title={doc.title}
                hasButton
                linkUrl={`/printables/document/${slug}?title=${slug}&file=${
                  doc.file.file.url
                }&ext=${doc.file.file.ext.replace('.', '')}&filetype=${doc.file.file.ext}&position=${
                  doc.contactPosition
                }&qrId=${qrId}`}
                image={
                  doc?.thumbnail
                    ? doc?.thumbnail?.formats?.thumbnail
                      ? doc.thumbnail.formats.thumbnail.url
                      : doc.thumbnail.url
                    : '/images/ico-pdf.svg'
                }
                buttonLabel="View Printable"
                size="vertical"
                disabled={enabled}
              />
            )
          })
        )}
      </section>
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(
        `${apiURL}/users/${userId}`,

        config
      )
      .then((res) => {
        const me = res.data
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    }
    const docs = await axios
      .get(`${apiURL}/docs?documentType=printable`, config)
      .then((res) => {
        const shopData = serializeJson(res.data)
        return shopData
      })
      .catch((error) => {
        throw error
      })

    return { props: { docs, user: userData } }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default PrintableDocuments
