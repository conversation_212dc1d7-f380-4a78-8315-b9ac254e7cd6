import { useEffect, useState, useRef } from 'react'
import axios from 'axios'
import nookies from 'nookies'
import { saveAs } from 'file-saver'
import { serializeJson } from '../../../helpers/serializeData'
import { useRouter } from 'next/router'
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib'
import Layout from '../../../components/Layout'
import style from '../../../styles/Printables.module.scss'
import Button from '../../../components/Button'
import Processing from '../../../components/Processing'
import { fetchImage, getFileExtension } from '../../../helpers/fetchImage'
import heicConvert from 'heic-convert'
import Head from 'next/head'
import { fetchPdf } from '../../../helpers/fetchPdf'

const ensureUserPreferences = (user) => {
  const updatedUser = { ...user }

  const hasPhotoOption = typeof updatedUser.photoOnPrintable === 'boolean'
  const hasQROption = typeof updatedUser.qrCodeOnPrintable === 'boolean'

  if (!hasPhotoOption && !hasQROption) {
    updatedUser.photoOnPrintable = false
    updatedUser.qrCodeOnPrintable = false
  }

  return updatedUser
}

const Printable = ({ contactPosition, url, user, filetype }) => {
  const [pdfInfo, setPdfInfo] = useState(null)
  const [iframeHeight, setIframeHeight] = useState('100')
  const [processing, setProcessing] = useState(false)
  const [validationErrors, setValidationErrors] = useState([])
  const viewer = useRef(null)
  const router = useRouter()

  console.log('user', user)

  // Apply default preferences if needed
  const userWithDefaults = ensureUserPreferences(user)

  const convertHeicToJpeg = async (arrayBuffer) => {
    try {
      const jpegBuffer = await heicConvert({
        buffer: Buffer.from(arrayBuffer),
        format: 'JPEG',
        quality: 0.9
      })
      return jpegBuffer
    } catch (error) {
      console.error('Error converting HEIC to JPEG:', error)
      throw error
    }
  }

  const validateRequiredData = (user, contactPosition) => {
    const errors = []

    // Basic user info validation
    if (!user.firstname || !user.lastname) {
      errors.push('Your first and last name are required for the document footer')
    }

    // Photo validation if photo option is enabled
    if (user.photoOnPrintable && contactPosition !== 'noContactInfo') {
      if (!user.photo || (!user.photo.url && !user.photo.formats)) {
        errors.push("You've selected to show your photo, but no photo is available in your profile")
      }
    }

    // QR code validation if QR option is enabled
    if (user.qrCodeOnPrintable && contactPosition !== 'noContactInfo') {
      if (!router.query.qrId) {
        errors.push("You've selected to show your QR code, but no QR code is available")
      }
    }

    return errors
  }

  const createFooterPdf = async () => {
    console.log('createFooterPdf is deprecated and should not be called')
    return await PDFDocument.create()
  }

  const modifyPdf = async () => {
    setProcessing(true)

    // Validate required data
    const validationErrors = validateRequiredData(userWithDefaults, contactPosition)

    if (validationErrors.length > 0) {
      setProcessing(false)
      setValidationErrors(validationErrors)
      return null
    }

    try {
      // Fetch the PDF file
      const pdfResponse = await fetchPdf(url)
      if (!pdfResponse.arrayBuffer) {
        throw new Error('Failed to fetch PDF file')
      }

      if (!pdfResponse.contentType.includes('pdf')) {
        throw new Error('Fetched file is not a PDF')
      }

      // Load the original PDF
      const originalPdf = await PDFDocument.load(pdfResponse.arrayBuffer, {
        updateMetadata: false // Prevent metadata updates to preserve form fields
      })

      // For noContactInfo or if we encounter embedding issues, just display the original PDF
      if (contactPosition === 'noContactInfo') {
        const pdfBytes = await originalPdf.save({ updateMetadata: false })
        const blob = new Blob([pdfBytes], { type: 'application/pdf' })
        const docUrl = URL.createObjectURL(blob)

        setPdfInfo(docUrl)
        setProcessing(false)
        return pdfBytes
      }

      // Get the last page for footer placement
      const pages = originalPdf.getPages()
      const lastPage = pages[pages.length - 1]
      const { width } = lastPage.getSize()

      // Prepare image URLs
      const photoName =
        userWithDefaults.photo && userWithDefaults.photo.name
          ? userWithDefaults.photo.name
          : userWithDefaults.photo && userWithDefaults.photo.url
          ? userWithDefaults.photo.url.replace(/.*\/([^/]+)$/, '$1')
          : ''

      const photoUrl = userWithDefaults.photo && userWithDefaults.photo.url ? userWithDefaults.photo.url : ''

      const photoUrlSmall =
        userWithDefaults.photo && userWithDefaults.photo.formats && userWithDefaults.photo.formats.small
          ? userWithDefaults.photo.formats.small.url
          : ''

      const photoUrlSquared =
        userWithDefaults.photo && userWithDefaults.photo.formats && userWithDefaults.photo.formats.squared
          ? userWithDefaults.photo.formats.squared.url
          : ''

      const photoUrlCircular =
        userWithDefaults.circularPhoto && userWithDefaults.circularPhoto.url ? userWithDefaults.circularPhoto.url : ''

      const emptyPrintableFooterUrl = `${process.env.AWS_BUCKET_URL}/public/images/small/${photoName.replace(
        / /g,
        '_'
      )}`
      const qrUrlPath = router.query.qrId || null

      console.log('User photo data:', {
        photoUrl,
        photoUrlSmall,
        photoUrlSquared,
        photoUrlCircular,
        photoOnPrintable: userWithDefaults.photoOnPrintable,
        qrCodeOnPrintable: userWithDefaults.qrCodeOnPrintable
      })

      // Prepare image if needed
      const imageUrl = await getImageUrl(
        userWithDefaults,
        photoUrl,
        photoUrlSmall,
        qrUrlPath,
        emptyPrintableFooterUrl,
        photoUrlSquared,
        photoUrlCircular
      )

      console.log('Selected image URL:', imageUrl)

      let image
      if (imageUrl) {
        try {
          let { arrayBuffer: imageBytes, contentType } = await fetchImage(`${imageUrl}`)
          console.log('Fetched image content type:', contentType)
          console.log('Image bytes length:', imageBytes.byteLength)

          if (contentType.includes('heic')) {
            imageBytes = await convertHeicToJpeg(imageBytes)
            contentType = 'image/jpeg'
          }

          if (!imageBytes || imageBytes.byteLength === 0) {
            console.error('Empty image data received')
          } else {
            if (contentType.includes('png')) {
              image = await originalPdf.embedPng(imageBytes)
              console.log('PNG image embedded successfully, dimensions:', image.width, 'x', image.height)
            } else if (contentType.includes('jpeg') || contentType.includes('jpg')) {
              image = await originalPdf.embedJpg(imageBytes)
              console.log('JPEG image embedded successfully, dimensions:', image.width, 'x', image.height)
            } else {
              console.warn('Unsupported image format:', contentType)
            }
          }
        } catch (imageError) {
          console.error('Error processing image:', imageError)
          // Continue without the image
        }
      }

      // Create font
      const helveticaFont = await originalPdf.embedFont(StandardFonts.Helvetica)

      // Create footer PDF with try/catch to handle potential embedding errors
      try {
        // Get positions for drawing
        const itemPositions = getItemPositions(width, userWithDefaults.emptyPrintableFooter, helveticaFont)
        const pos = itemPositions[contactPosition]

        // Skip createFooterPdf and draw directly on the original page
        const originalLastPage = originalPdf.getPages()[originalPdf.getPageCount() - 1]

        // Draw name if available
        if (userWithDefaults.firstname && userWithDefaults.lastname) {
          originalLastPage.drawText(
            `${userWithDefaults.firstname} ${userWithDefaults.lastname}${
              userWithDefaults.titles && userWithDefaults.titles.length > 0 ? ', ' + userWithDefaults.titles : ''
            }`,
            pos.name
          )
        }

        if (userWithDefaults.position) {
          originalLastPage.drawText(userWithDefaults.position, pos.position)
        }

        if (userWithDefaults.workEmail) {
          originalLastPage.drawText(userWithDefaults.workEmail, pos.email)
        }

        const phone = formatPhone(userWithDefaults.workPhone || userWithDefaults.phone)
        if (phone) {
          originalLastPage.drawText(phone, pos.phone)
        }

        if (userWithDefaults.website) {
          originalLastPage.drawText(transformSiteURL(userWithDefaults.website), pos.website)
        }

        // Draw image directly on the original page
        let imageDrawn = false
        if ((userWithDefaults.photoOnPrintable || userWithDefaults.qrCodeOnPrintable) && image && !imageDrawn) {
          try {
            console.log('Drawing image directly on original page with dimensions:', {
              x: pos.photo.x,
              y: pos.photo.y,
              width: pos.photo.width,
              height: pos.photo.height
            })

            // Draw the image directly on the original page
            originalLastPage.drawImage(image, {
              x: pos.photo.x,
              y: pos.photo.y,
              width: pos.photo.width,
              height: pos.photo.height
            })
            console.log('Image drawn directly on original page')
            imageDrawn = true // Set flag to prevent duplicate drawing
          } catch (directDrawError) {
            console.error('Error drawing image directly:', directDrawError)
          }
        }
      } catch (embedError) {
        console.error('Error adding footer content:', embedError)
        // Continue without the footer rather than failing completely
      }

      // Save the modified PDF
      const pdfBytes = await originalPdf.save({ updateMetadata: false })
      const blob = new Blob([pdfBytes], { type: 'application/pdf' })
      const docUrl = URL.createObjectURL(blob)

      setPdfInfo(docUrl)
      setProcessing(false)
      return pdfBytes
    } catch (error) {
      console.error('Error modifying PDF:', error)

      // Try to display the original PDF without modifications if there was an error
      try {
        const pdfResponse = await fetchPdf(url)
        const blob = new Blob([pdfResponse.arrayBuffer], { type: 'application/pdf' })
        const docUrl = URL.createObjectURL(blob)
        setPdfInfo(docUrl)
      } catch (fallbackError) {
        console.error('Error displaying original PDF:', fallbackError)
      }

      setProcessing(false)
      setValidationErrors(['An error occurred while processing your document. Displaying original PDF.'])
      return null
    }
  }

  const getImageUrl = async (
    user,
    photoUrl,
    photoUrlSmall,
    qrUrlPath,
    emptyPrintableFooterUrl,
    photoUrlSquared,
    photoUrlCircular
  ) => {
    try {
      // Default to empty footer if no contact info is shown
      if (contactPosition === 'noContactInfo') {
        return emptyPrintableFooterUrl
      }

      // Check for photo preference and availability
      if (user.photoOnPrintable) {
        // First try squared photo (highest priority)
        if (photoUrlSquared) {
          console.log('Using squared photo:', photoUrlSquared)
          return photoUrlSquared
        }

        // Then try circular photo
        if (photoUrlCircular) {
          console.log('Using circular photo:', photoUrlCircular)
          return photoUrlCircular
        }

        // Then try small photo
        if (photoUrlSmall) {
          console.log('Using small photo:', photoUrlSmall)
          return photoUrlSmall
        }

        // Finally use original photo if nothing else is available
        if (photoUrl) {
          console.log('Using original photo:', photoUrl)
          return photoUrl
        }
      }

      // Check for QR code preference and availability
      if (user.qrCodeOnPrintable && qrUrlPath) {
        console.log('Using QR code:', qrUrlPath)
        return qrUrlPath
      }

      // Default fallback
      console.log('Using default empty footer:', emptyPrintableFooterUrl)
      return emptyPrintableFooterUrl
    } catch (error) {
      console.error('Error getting image URL:', error)
      return emptyPrintableFooterUrl
    }
  }

  const drawContactInfo = (page, user, font, width, position, image, ext) => {
    // Skip if no contact info should be shown
    if (position === 'noContactInfo') {
      return
    }

    const itemPositions = getItemPositions(width, user.emptyPrintableFooter, font)

    // Ensure the position exists in our configuration
    if (!itemPositions[position]) {
      console.error(`Invalid position: ${position}`)
      return
    }

    const pos = itemPositions[position]
    console.log('position', position)
    console.log('itemPositions', itemPositions)
    console.log('pos', pos)

    // Draw name if available
    if (user.firstname && user.lastname) {
      page.drawText(
        `${user.firstname} ${user.lastname}${user.titles && user.titles.length > 0 ? ', ' + user.titles : ''}`,
        pos.name
      )
    }

    if (user.position) {
      page.drawText(user.position, pos.position)
    }

    if (user.workEmail) {
      page.drawText(user.workEmail, pos.email)
    }

    const phone = formatPhone(user.workPhone || user.phone)
    if (phone) {
      page.drawText(phone, pos.phone)
    }

    if (user.website) {
      page.drawText(transformSiteURL(user.website), pos.website)
    }

    // Only draw image if we have one and the user has selected to show it
    if ((user.photoOnPrintable || user.qrCodeOnPrintable) && image) {
      try {
        page.drawImage(image, pos.photo)
      } catch (error) {
        console.error('Error drawing image:', error)
        // Continue without the image rather than failing completely
      }
    }
  }

  const getItemPositions = (width, emptyPrintableFooter, font) => {
    return {
      columnRight: {
        name: { x: width - 184, y: 75, size: 10, font: font, color: rgb(0.42, 0.61, 0.72) },
        position: { x: width - 184, y: 65, size: 8, font: font, color: rgb(0.42, 0.61, 0.72) },
        email: { x: width - 184, y: 51, size: 8, font: font, color: rgb(1, 1, 1) },
        phone: { x: width - 184, y: 39, size: 8, font: font, color: rgb(1, 1, 1) },
        website: { x: width - 184, y: 28, size: 8, font: font, color: rgb(1, 1, 1) },
        photo: { x: width - 184, y: 90, width: 40, height: 40 }
      },
      footerLeft: {
        name: { x: emptyPrintableFooter ? 24 : 94, y: 74, size: 12, font: font, color: rgb(0.42, 0.61, 0.72) },
        position: { x: emptyPrintableFooter ? 24 : 94, y: 60, size: 10, font: font, color: rgb(0.42, 0.61, 0.72) },
        email: { x: emptyPrintableFooter ? 24 : 94, y: 48, size: 10, font: font, color: rgb(1, 1, 1) },
        phone: { x: emptyPrintableFooter ? 24 : 94, y: 37, size: 10, font: font, color: rgb(1, 1, 1) },
        website: { x: emptyPrintableFooter ? 24 : 94, y: 25, size: 10, font: font, color: rgb(1, 1, 1) },
        photo: { x: 20, y: 26, width: 60, height: 60 }
      }
    }
  }

  const formatPhone = (phone) => {
    return phone ? `${phone.slice(0, 3)}.${phone.slice(3, 6)}.${phone.slice(6, 10)}` : ''
  }

  const transformSiteURL = (site) => {
    return site.replace(/^(https?:\/\/)?(www\.)?/, '')
  }

  const downloadPrintable = () => {
    const filename = `printable${filetype.toLowerCase()}`
    saveAs(filetype && filetype.toLowerCase() === '.pdf' ? pdfInfo : url, filename)
  }

  const calcIframeHeight = () => {
    return viewer.current ? (window.innerHeight - 70) * 1.41 : 0
  }

  useEffect(() => {
    if (filetype && filetype.toLowerCase() === '.pdf') {
      modifyPdf()
    }
  }, [])

  useEffect(() => {
    if (pdfInfo) {
      setIframeHeight(calcIframeHeight())
    }
  }, [pdfInfo, viewer.current])

  return (
    <Layout>
      <Head>
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
      </Head>
      <Processing processing={processing} message="Loading Printable..." />

      <h1 className={style.ax_page_title}>{router.query.title.replace(/-/g, ' ')}</h1>
      <div className={style.frame} style={{ marginBottom: '16px' }}>
        {validationErrors.length > 0 && (
          <div className={style.validationErrors}>
            <h3>Please fix the following issues:</h3>
            <ul>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
            <p>
              You can update your profile information in your account settings. If you need assistance, please contact
              your administrator.
            </p>
          </div>
        )}
        {filetype && filetype.toLowerCase() === '.pdf' ? (
          <iframe
            width="1000"
            height={iframeHeight || '2000'}
            title="printable"
            src={pdfInfo}
            ref={viewer}
            type="application/pdf"
          />
        ) : (
          <img src={url} style={{ width: '80%', maxWidth: '80%', height: 'auto' }} alt="axiom printable" />
        )}
      </div>
      <Button action={downloadPrintable} label="Download" color="highlight" />
    </Layout>
  )
}

export const getServerSideProps = async (ctx) => {
  ctx.res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  ctx.res.setHeader('Pragma', 'no-cache')
  ctx.res.setHeader('Expires', '0')

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const tokens = nookies.get(ctx)
  const { jwt, userId } = tokens
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  let userData
  if (userId) {
    userData = await axios
      .get(`${apiURL}/users?id=${userId}`, config)
      .then((res) => {
        const me = res.data[0]
        const serializedData = serializeJson(me)
        return serializedData
      })
      .catch((err) => {
        throw err
      })
  }

  if (ctx.req.headers.cookie && jwt) {
    if (userData.isOnboarding) {
      return {
        redirect: {
          destination: 'https://welcome.indimortgage.ca',
          permanent: false
        }
      }
    } else {
      const contactPosition = ctx.query.position
      const url = ctx.query.file
      const filetype = ctx.query.filetype

      return {
        props: { user: userData, contactPosition, url, filetype }
      }
    }
  }
  return {
    redirect: {
      destination: '/',
      permanent: false
    }
  }
}

export default Printable
