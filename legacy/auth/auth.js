import Cookies from 'js-cookie'
import Router from 'next/router'

export const authStatus = () => {
  try {
    // Make sure we're on the client side
    if (typeof window === 'undefined') {
      return false
    }

    const token = Cookies.get('jwt')
    if (token) {
      return true
    }
    return false
  } catch (error) {
    console.error('Error checking auth status:', error)
    return false
  }
}

export const logout = () => {
  try {
    Cookies.remove('jwt')
    Cookies.remove('userId')

    // Force a page reload to clear any in-memory state
    // This helps prevent auth state inconsistencies
    window.location.href = '/'
  } catch (error) {
    console.error('Error during logout:', error)
    Router.push('/')
  }
}
