import { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import AuthContext from '../context/authContext'
import ProcessingBranded from '../components/ProcessingBranded'

/**
 * Higher-order component to ensure pages have authentication
 * before rendering. Provides consistent auth checking and loading states.
 */
const withAuth = (WrappedComponent) => {
  const WithAuth = (props) => {
    const router = useRouter()
    const { userAuth } = useContext(AuthContext)
    const [redirectAttempted, setRedirectAttempted] = useState(false)

    useEffect(() => {
      // Only attempt redirect if we're sure auth is initialized and user is not authenticated
      if (userAuth?.initialized && !userAuth.isAuth && !redirectAttempted) {
        setRedirectAttempted(true)
        router.push('/')
      }
    }, [userAuth, router, redirectAttempted])

    // Show loading state if auth isn't initialized yet or we're in the middle of a redirection
    if (!userAuth || !userAuth.initialized) {
      return <ProcessingBranded processing message="Initializing authentication..." />
    }

    // If not authenticated and we've attempted to redirect, show loading until the redirect happens
    if (!userAuth.isAuth && redirectAttempted) {
      return <ProcessingBranded processing message="Not authenticated, redirecting..." />
    }

    // If we need user info and it's not loaded yet, show loading
    if (userAuth.isAuth && !userAuth.userInfo) {
      return <ProcessingBranded processing message="Loading user information..." />
    }

    // If everything is good, render the protected component
    return <WrappedComponent {...props} />
  }

  // Copy getInitialProps from the wrapped component if it exists
  if (WrappedComponent.getInitialProps) {
    WithAuth.getInitialProps = WrappedComponent.getInitialProps
  }

  return WithAuth
}

export default withAuth
