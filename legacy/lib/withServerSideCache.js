// utils/withServerSideCache.js
export function withServerSideCache(getServerSidePropsFunc) {
  console.log('Runs withServerSideCache')
  return async (context) => {
    // Set cache headers
    context.res.setHeader('Cache-Control', 'public, s-maxage=1, stale-while-revalidate=59')
    console.log('Cache Control Headers Set')

    // If no getServerSideProps provided, return empty props
    if (!getServerSidePropsFunc) {
      return {
        props: {}
      }
    }

    console.log('Runs GetServerSideProp then')

    // Execute the original getServerSideProps
    return await getServerSidePropsFunc(context)
  }
}
