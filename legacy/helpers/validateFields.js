export const Validation = (fields, requiredFields) => {
  const invalidFields = () => {
    let passedFieldsArr = []
    let invalid = []
    console.log('FDS', fields)

    for (const f in fields) {
      //pushing to invalid
      requiredFields.forEach((vf) => {
        if (
          (vf === f && fields[f] === null) ||
          (vf === f && fields[f] === '') ||
          (vf === f && fields[f] === undefined)
        ) {
          invalid.push(f)
        }
      })

      //transforming object in Array
      if (
        f !== '_id' &&
        f !== 'id' &&
        f !== '_v' &&
        f !== '__v' &&
        f !== 'createdAt' &&
        f !== 'updatedAt' &&
        f !== 'created_by' &&
        f !== 'updated_by' &&
        f !== 'published_at' &&
        f !== 'slug' &&
        f !== 'user'
      ) {
        passedFieldsArr = [...passedFieldsArr, f]
      }
    }

    let cleanPassedFields = [...new Set(passedFieldsArr)]

    //checking if required field wasn't passed
    const nonPassed = requiredFields.filter((rq) => {
      return cleanPassedFields.indexOf(rq) == -1
    })

    //Adding nonPassed fields to invalid
    const allInvalid = invalid.concat(nonPassed)

    return allInvalid
  }

  let invalidItems
  if (invalidFields().length > 0) {
    const validatedFields = invalidFields()
    invalidItems = validatedFields.map((f) => {
      switch (f) {
        case 'signature':
          return 'Signature'
        case 'initials':
          return 'Initials'
        case 'witnessName':
          return 'Witness Name'
        case 'firstname':
          return 'First Name'
        case 'lastname':
          return 'Last Name'
        case 'workEmail':
          return 'Preferred Email Address'
        case 'workPhone':
          return 'Preferred Phone Number'
        case 'position':
          return 'Position'
        case 'address':
          return 'Address'
        case 'city':
          return 'City'
        case 'province':
          return 'Province'
        case 'postalCode':
          return 'Postal Code'
        case 'phone':
          return 'Preferred Phone Number'
        case 'cellPhone':
          return 'Cell Phone'
        case 'emergencyContact':
          return 'Emergency Contact'
        case 'emergencyPhone':
          return 'Emergency Phone'
        case 'birthdate':
          return 'Birth Date'
        case 'sin':
          return 'SIN'
        case 'websiteOptIn':
          return 'Indi Website'
        case 'startDate':
          return 'Start Date'
        case 'brokerOfRecord':
          return 'Broker of Record'
        case 'brokerName':
          return 'Broker Name'
        case 'nameAndTitle':
          return 'Name And Title'
        case 'loginId':
          return 'Login ID'
        case 'firmCode':
          return 'Firm Code'
        case 'firmName':
          return 'Firm Name'
        case 'per':
          return 'Per'
        case 'brokerName':
          'Broker Name'
        case 'bankName':
          return 'Bank Name'
        case 'bankAddress':
          return 'Bank Address'
        case 'institutionNumber':
          return 'Institution Number'
        case 'transitNumber':
          return 'Transit Number'
        case 'accountNumber':
          return 'Account Number'
        case 'nameOnAccount':
          return 'Name On Account'
        case 'officeAddress':
          return 'Office Address'
        case 'officeCity':
          return 'Office City'
        case 'officePostalCode':
          return 'Office Postal Code'
        case 'officeProvince':
          return 'Office Province'
        default:
          return f
      }
    })
  } else {
    invalidItems = []
  }

  return invalidItems
}
