import PdfThumbnail from 'react-pdf-thumbnail'
const pdfThumbGenerator = async (url) => {
  const urlExtension = () => url.split(/[#?]/)[0].split('.').pop().trim()

  if (urlExtension() === 'pdf') {
    try {
      const thumb = await PdfThumbnail(url, {
        // thumb image config
        fileName: 'thumb.png', // thumb file name
        height: 200, // image height
        width: 200, // image width
        pageNo: 1 // pdf page number
      })
      console.log('IMB_URL', thumb)
      if (thumb) {
        return thumb.imageUrl
      }
    } catch (error) {
      console.log('PDF THUMB ERROR', error)
    }
  }
}

export default pdfThumbGenerator
