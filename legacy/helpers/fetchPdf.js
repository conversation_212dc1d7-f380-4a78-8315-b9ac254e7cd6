import axios from 'axios'

/**
 * Fetches a PDF file directly without using the proxy
 * @param {string} url - The URL of the PDF to fetch
 * @returns {Promise<{arrayBuffer: ArrayBuffer, contentType: string}>}
 */
export async function fetchPdf(url, maxRetries = 3) {
  let lastError = null

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`Fetching PDF (attempt ${attempt + 1}): ${url}`)

      // Fetch the PDF directly using axios
      const response = await axios.get(url, {
        responseType: 'arraybuffer',
        headers: {
          Accept: 'application/pdf',
          'Accept-Encoding': 'gzip, deflate, br'
        }
      })

      // Return the PDF data
      return {
        arrayBuffer: response.data,
        contentType: response.headers['content-type'] || 'application/pdf'
      }
    } catch (error) {
      console.error(`Attempt ${attempt + 1} failed to fetch PDF:`, error.message)
      lastError = error

      // Wait before retrying
      if (attempt < maxRetries - 1) {
        await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)))
      }
    }
  }

  // If all attempts fail, throw the last error
  throw lastError || new Error('Failed to fetch PDF after multiple attempts')
}
