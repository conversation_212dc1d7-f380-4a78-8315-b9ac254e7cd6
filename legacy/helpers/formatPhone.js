export const formatPhone = (phone) => {
  if (phone && phone.length > 0 && phone !== undefined && phone !== null) {
    return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6, 10)}`
  }

  return ''
}

export const getRawPhone = (formInfo) => {
  let phonesObj = null

  for (let item in formInfo) {
    if (formInfo && formInfo[item] && formInfo[item].raw) {
      const rawPhone = () => (formInfo[item].raw ? formInfo[item].raw : '')
      switch (item) {
        case 'phone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'preferredPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'secondPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'officePhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'companyPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'workPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'emergencyPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'cellPhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'homePhone':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'tollfree':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        case 'fax':
          phonesObj = { ...phonesObj, [item]: rawPhone() }
          break
        default:
          phonesObj = null
          break
      }
    }
  }

  console.log('FROM FORMAT PHONE', phonesObj)

  return phonesObj
}
