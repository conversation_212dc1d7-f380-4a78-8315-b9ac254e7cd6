import { formatPhone } from './formatPhone'

//Check Values does a pre validation and update the formInfo Object
export const checkValues = (currentForm, requiredFields) => {
  const inputs = Array.from(currentForm.querySelectorAll('input'))
  const selects = Array.from(currentForm.querySelectorAll('select'))
  const textareas = Array.from(currentForm.querySelectorAll('textarea'))
  const allInputs = inputs.concat(selects, textareas)
  // const allInputs = [...inputs, ...selects, ...textareas]
  console.log('CFRM', currentForm)
  console.log('ALL', allInputs)

  const empty = []

  const mappedFormInfo = allInputs.map((i) => {
    if (i.value && i.value !== '' && i.value !== null && i.value !== undefined) {
      if (i.type === 'tel') {
        return { [i.name]: { masked: i.value, raw: i.value.replace(/\D/g, '') } }
      }
      return { [i.name]: i.value }
    }

    requiredFields.forEach((r) => {
      if (i.name === r && i.value === '') {
        empty.push(i.name)
      }
      return { [i.name]: '' }
    })

    if (i.type === 'checkbox') {
      return { [i.name]: i.checked }
    }

    return { [i.name]: i.value }
  })

  let data = {}

  mappedFormInfo.forEach((f) => {
    data = { ...data, ...f }
  })

  if ((data && data !== null) || (data && data !== undefined)) {
    //clear empty items from finalData
    let clearFinalData = {}
    for (let i in data) {
      if (data[i] != '') {
        clearFinalData = { ...clearFinalData, [i]: data[i] }
      }
    }

    let finalData = {}
    for (const d in clearFinalData) {
      if (clearFinalData[d] === 'true' || clearFinalData[d] === true) {
        finalData = { ...finalData, [d]: true }
      } else if (clearFinalData[d] === 'false' || clearFinalData[d] === false) {
        finalData = { ...finalData, [d]: false }
      } else {
        finalData = { ...finalData, [d]: clearFinalData[d] }
      }
    }

    return finalData
  }

  return null
}

export const updatePercentage = (forms) => {
  let percentValues

  const filterFinishedForms = () => {
    let currentFinished = []
    let totalForms = []

    for (let f in forms) {
      if (
        f === 'brokerInfo' ||
        f === 'businessCardInfo' ||
        f === 'contractAndSchedule' ||
        f === 'letterOfDirection' ||
        f === 'mpcApplication' ||
        f === 'paymentAuthorization' ||
        f === 'photos' ||
        f === 'policiesAndProcedure' ||
        f === 'websiteInfo'
      ) {
        totalForms.push(f)
        if (forms[f] && forms[f].isFormComplete) {
          currentFinished.push(f)
        }
      }
    }

    percentValues = { total: totalForms.length, current: currentFinished.length }
    return percentValues
  }

  return Math.floor((filterFinishedForms().current / filterFinishedForms().total) * 100)
}

//Updates the formStatus in userAuth the Context
export const updateFormsInContext = (formName, formObj, allForms, userAuth, fromFinished) => {
  const pct = updatePercentage(allForms)

  let newCtxFormsObj

  if (fromFinished) {
    newCtxFormsObj = {
      ...userAuth,
      forms: allForms
    }
  } else {
    newCtxFormsObj = {
      ...userAuth,
      beforeLeave: { action: 'stay', showAlert: false, route: null, event: null },
      forms: { ...allForms, [formName]: formObj, isFormSaved: true }
    }
  }

  return newCtxFormsObj
}

//Check if there is any previous data for that field and return it
export const checkPreviousData = (formName, fieldName, user, onboarding) => {
  if (user) {
    if (fieldName === 'workPhone') {
      fieldName = 'phone'
    }

    if (
      user &&
      user[fieldName] &&
      (user[fieldName] !== null || user[fieldName] !== undefined || user[fieldName] !== '')
    ) {
      return user[fieldName]
    } else {
      return ''
    }
  }
}

//Check if there is a Signature
export const checkSignature = (signature, hasSignature) => {
  let status = () => {
    if (signature && signature !== null && signature.url && signature.url.length > 0) {
      return true
    } else {
      return false
    }
  }

  if (status()) {
    return { isSaved: true, data: signature }
  } else {
    return { isSaved: false }
  }
}

//Check if there is a Signature
export const checkInitials = (signature) => {
  let status = () => {
    if (signature && signature !== null && signature.url && signature.url.length > 0) {
      return true
    } else {
      return false
    }
  }

  if (status()) {
    return { isSaved: true, data: signature }
  } else {
    return { isSaved: false }
  }
}

export const isFormSaved = (event, isSaved) => {
  event.preventDefault()
  if (isSaved) {
    return false
  }
  return true
}

export const filterCompletedForms = (forms, curr, incomplete) => {
  let formsArr = []
  let currFormName

  for (let f in forms) {
    if (
      f === 'brokerInfo' ||
      f === 'businessCardInfo' ||
      f === 'contractAndSchedule' ||
      f === 'letterOfDirection' ||
      f === 'mpcApplication' ||
      f === 'paymentAuthorization' ||
      f === 'photos' ||
      f === 'policiesAndProcedure' ||
      f === 'websiteInfo'
    ) {
      if (f === curr) {
        currFormName = f
      }

      formsArr.push({ [f]: forms[f] })
    }
  }

  let filtered = []
  //Get All forms but current
  formsArr.forEach((f) => {
    const key = Object.keys(f)[0]
    console
    if (f[key].isFormComplete && key !== currFormName) {
      filtered.push(f)
    }
  })

  //If the current is complete, sum + 1 to filtered
  let completeNum = filtered.length
  if (incomplete === false) {
    completeNum = completeNum + 1
  }

  console.log('PCT', completeNum, formsArr.length)

  const percent = Math.floor((completeNum / formsArr.length) * 100)

  return percent
}
