export const warmTonesRows = [
  {
    pantone: 'poinciana 18-1564',
    cmyk: '0, 60, 65, 22',
    rgb: '199, 47, 32',
    web: '#C72F20',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'samba 19-1662',
    cmyk: '0, 49, 47, 38',
    rgb: '159, 35, 38',
    web: '#9F2326',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caut<PERSON>',
    accent: 'Yes'
  },
  {
    pantone: 'viva magenta 18-1750',
    cmyk: '0, 55, 41, 29',
    rgb: '180, 41, 75',
    web: '#B4294B',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'magenta 17-2036',
    cmyk: '0, 56, 35, 18',
    rgb: '209, 66, 120',
    web: '#D14278',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'natural 16-1310',
    cmyk: '0, 11, 17, 33',
    rgb: '170, 142, 127',
    web: '#AA8E7F',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'pheasant 16-1332',
    cmyk: '0, 29, 42, 20',
    rgb: '203, 128, 96',
    web: '#CB8060',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'burnt orange 16-1448',
    cmyk: '0, 38, 56, 24',
    rgb: '193, 96, 50',
    web: '#C16032',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'oil yellow 15-0743',
    cmyk: '0, 15, 51, 22',
    rgb: '198, 161, 67',
    web: '#C6A143',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'raven 19-0000',
    cmyk: '0, 2, 1, 74',
    rgb: '67, 62, 64',
    web: '#433E40',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'real teal 18-4018',
    cmyk: '19, 9, 0, 56',
    rgb: '65, 90, 113',
    web: '#415A71',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'stormy sea 18-4711',
    cmyk: '8, 2, 0, 49',
    rgb: '110, 127, 131',
    web: '#6E7F83',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'green heron 19-4727',
    cmyk: '22, 5, 0, 64',
    rgb: '37, 79, 92',
    web: '#254F5C',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'teal 17-4919',
    cmyk: '24, 0, 1, 48',
    rgb: '71, 132, 130',
    web: '#478482',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'lavender crystal 18-3530',
    cmyk: '0, 19, 5, 40',
    rgb: '154, 105, 142',
    web: '#9A698E',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'viola 16-3815',
    cmyk: '10, 16, 0, 24',
    rgb: '168, 152, 194',
    web: '#A898C2',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'nimbus cloud 13-4108',
    cmyk: '1, 2, 0, 15',
    rgb: '214, 211, 217',
    web: '#D6D3D9',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'cannoli cream 11-4302',
    cmyk: '0, 1, 7, 5',
    rgb: '241, 239, 224',
    web: '#F1EFE0',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'bottle green 13-0643',
    cmyk: '26, 0, 12, 45',
    rgb: '72, 139, 109',
    web: '#488B6D',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'blissful blue 16-4121',
    cmyk: '29, 11, 0, 28',
    rgb: '109, 157, 184',
    web: '#6D9DB8',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'dark cheddar 15-1150',
    cmyk: '0, 33, 72, 15',
    rgb: '216, 131, 32',
    web: '#D88320',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'yellow plum 13-0643',
    cmyk: '6, 0, 56, 14',
    rgb: '205, 220, 76',
    web: '#CDDC4C',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  }
]

export const brightTonesRows = [
  {
    pantone: 'orange.com 18-1561',
    cmyk: '0, 75, 83, 11',
    rgb: '228, 36, 17',
    web: '#E42411',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'goji berry 18-1659',
    cmyk: '0, 67, 66, 25',
    rgb: '191, 19, 23',
    web: '#BF1317',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'toasted almond 14-1213',
    cmyk: '0, 13, 20, 18',
    rgb: '180, 41, 75',
    web: '#D1B19F',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'super lemon 14-0754',
    cmyk: '0, 17, 60, 9',
    rgb: '232, 189, 78',
    web: '#E8BD4E',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'black beauty 19-3911',
    cmyk: '0, 0, 0, 100',
    rgb: '0, 0, 0     ',
    web: '#000000',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'campanula 18-4141',
    cmyk: '46, 22, 0, 30',
    rgb: '60, 121, 178',
    web: '#3C79B2',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'stratosphere 14-4508',
    cmyk: '15, 3, 0, 22',
    rgb: '61, 192, 200',
    web: '#A1C0C8',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'sea of belize 18-4037',
    cmyk: '42, 10, 0, 42',
    rgb: '42, 122, 148',
    web: '#2A7A94',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'waterfall 15-5516',
    cmyk: '44, 0, 2, 33',
    rgb: '60, 171, 167',
    web: '#3CABA7',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'clover 18-2320',
    cmyk: '0, 34, 8, 46',
    rgb: '137, 51, 116',
    web: '#893374',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'blue atoll 16-4535',
    cmyk: '100, 21, 0, 12',
    rgb: '0, 177, 224',
    web: '#00B1E0',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'lyons blue 19-4340',
    cmyk: '100, 26, 0, 56',
    rgb: '0, 84, 113',
    web: '#005471',
    text: 'Yes',
    headings: 'Yes',
    fill: 'Caution',
    accent: 'Yes'
  },
  {
    pantone: 'tangelo 15-1335',
    cmyk: '0, 48, 99, 0',
    rgb: '254, 131, 2',
    web: '#FE8302',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  },
  {
    pantone: 'sulphur spring 13-0650',
    cmyk: '10, 0, 93, 4',
    rgb: '221, 246, 8',
    web: '#DDF608',
    text: 'No',
    headings: 'No',
    fill: 'Yes',
    accent: 'Caution'
  }
]

export default { warmTonesRows, brightTonesRows }
