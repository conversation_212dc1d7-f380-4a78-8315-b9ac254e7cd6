import { PDFDocument, rgb } from 'pdf-lib'
import { saveAs } from 'file-saver'
import fontkit from '@pdf-lib/fontkit'
import { fetchImage, getFileExtension } from '../helpers/fetchImage'

const GenerateListingSheet = async (pdfData, isSafari) => {
  const { pdf, realtor } = pdfData
  const { user } = pdf
  const baseUrl = 'https://indicentral.ca'
  // const baseUrl = 'localhost:1337'

  let askingPrice = null
  let mlsCode = null
  let years = null
  let rate = null
  let customDown = null
  let frequencyTitle = null
  let middleTierOr10DownPayment = null
  let maxTierOr15DownPayment = null
  let periodicPay5 = null
  let periodicPay10 = null
  let periodicPay15 = null
  let periodicPay20 = null
  let periodicPayCustom = null
  let downPay5 = null
  let downPay10 = null
  let downPay15 = null
  let downPay20 = null
  let downPayCustom = null
  let customPercentage = null
  let insurance5 = null
  let insurance10 = null
  let insurance15 = null
  let insurance20 = null
  let insuranceCustom = null
  let rate5 = null
  let rate10 = null
  let rate15 = null
  let rate20 = null
  let rateCustom = null
  let brokerPhoto = null
  let realtorPhoto = null
  let propertyPhoto = null
  let brokerName = null
  let brokerPosition = null
  let email = null
  let phone = null
  let website = null
  let licensedWith = null
  let realtorName = null
  let realtorPosition = null
  let realtorEmail = null
  let realtorPhone = null
  let realtorWebsite = null
  let realtorLicensedWith = null
  let mortgagePayment = null
  let propertyTax = null
  let propertyInsurance = null
  let monthlyDebtPayment = null
  let utilities = null
  let condoFee = null
  let hoaFee = null
  let phoneExpense = null
  let cable = null
  let internet = null
  let totalExpenses = null
  let totalYearly = null
  let downPayment = null
  let downPaymentRate = null
  let lawyerFees = null
  let homeInspection = null
  let appraisal = null
  let titleInsurance = null
  let estoppelCertificate = null
  let totalCashNeeded = null
  let address = null

  let pdfDoc, form, arialBlackFont

  const realtorDefaultImage = `${baseUrl}/listing-models/full/2024/realtor-image-default.jpg`
  const propertyDefaultImage = `${baseUrl}/listing-models/full/2024/property-image-default.jpg`

  const brokerPhotoUrl = user?.photo?.url || realtorDefaultImage
  const realtorPhotoUrl = realtor?.photo?.url || realtorDefaultImage
  const propertyPhotoUrl = (pdf.propertyPhoto && pdf.propertyPhoto.url) || propertyDefaultImage

  let brokerPhotoFinal, realtorPhotoFinal, propertyPhotoFinal

  const toCurrencyString = (num, field) => {
    if (num === '0' || num === 0 || num === null || num === undefined || num === NaN) {
      return 'N/A'
    }
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currencyDisplay: 'symbol',
      currency: 'CAD'
    }).format(num)
  }

  const getPdfForm = async (url) => {
    const { arrayBuffer: sourceBuffer } = await fetchImage(url)
    pdfDoc = await PDFDocument.load(sourceBuffer)
    form = pdfDoc.getForm()

    pdfDoc.registerFontkit(fontkit)
    const { arrayBuffer: fontBytes } = await fetchImage(`${baseUrl}/fonts/ArialBlack.ttf`)
    arialBlackFont = await pdfDoc.embedFont(fontBytes)

    return form
  }

  const handleFrequency = (f) => {
    const frequencies = {
      monthly: 'Monthly',
      biweekly: 'Bi-Weekly',
      weekly: 'Weekly',
      accbiweekly: 'Accelerated Bi-Weekly',
      accweekly: 'Accelerated Weekly'
    }
    return frequencies[f] || 'Monthly'
  }

  const handleChosenRate = () => {
    const { chosenDownPay } = pdfData
    const rates = {
      rate5: pdfData.rate5,
      rate10: pdfData.rate10,
      rate15: pdfData.rate15,
      rate20: pdfData.rate20,
      rateRange: pdfData.rateRange,
      rateCustom: pdfData.rateCustom
    }
    return rates[chosenDownPay?.rate] || pdfData.rate5
  }

  // Helper to detect image type by magic number
  const detectImageType = (arrayBuffer) => {
    const bytes = new Uint8Array(arrayBuffer)
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    if (
      bytes[0] === 0x89 &&
      bytes[1] === 0x50 &&
      bytes[2] === 0x4e &&
      bytes[3] === 0x47 &&
      bytes[4] === 0x0d &&
      bytes[5] === 0x0a &&
      bytes[6] === 0x1a &&
      bytes[7] === 0x0a
    ) {
      return 'png'
    }
    // JPEG: FF D8 FF
    if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
      return 'jpg'
    }
    return 'unknown'
  }

  const fetchAndEmbedImages = async () => {
    try {
      const [
        { arrayBuffer: brokerPhotoBytes },
        { arrayBuffer: realtorPhotoBytes },
        { arrayBuffer: propertyPhotoBytes }
      ] = await Promise.all([
        fetchImage(brokerPhotoUrl),
        fetchImage(realtorPhotoUrl, { fallbackUrl: realtorDefaultImage }),
        fetchImage(propertyPhotoUrl, { fallbackUrl: propertyDefaultImage })
      ])

      const brokerExt = getFileExtension(brokerPhotoUrl)
      const realtorExt = getFileExtension(realtorPhotoUrl)
      const propertyExt = getFileExtension(propertyPhotoUrl)

      // Detect actual image type by magic number
      const brokerType = detectImageType(brokerPhotoBytes)
      const realtorType = detectImageType(realtorPhotoBytes)
      const propertyType = detectImageType(propertyPhotoBytes)

      brokerPhotoFinal =
        brokerType === 'png'
          ? await pdfDoc.embedPng(brokerPhotoBytes)
          : brokerType === 'jpg'
          ? await pdfDoc.embedJpg(brokerPhotoBytes)
          : null
      realtorPhotoFinal =
        realtorType === 'png'
          ? await pdfDoc.embedPng(realtorPhotoBytes)
          : realtorType === 'jpg'
          ? await pdfDoc.embedJpg(realtorPhotoBytes)
          : null
      propertyPhotoFinal =
        propertyType === 'png'
          ? await pdfDoc.embedPng(propertyPhotoBytes)
          : propertyType === 'jpg'
          ? await pdfDoc.embedJpg(propertyPhotoBytes)
          : null
    } catch (error) {
      console.error('Failed to fetch or embed one or more images:', error)
      // Handle errors, possibly by using placeholder images
    }
  }

  //Set Fields Short
  const setFields = () => {
    if (pdf.address && pdf.address !== null && pdf.address !== undefined) {
      address.setText(pdf.address)
    }
    if (pdfData.askingPrice && pdfData.askingPrice !== null && pdfData.askingPrice !== undefined) {
      askingPrice.setText(toCurrencyString(pdfData.askingPrice))
      askingPrice.updateAppearances(arialBlackFont)
    }
    if (pdf.mlsCode && pdf.mlsCode !== null && pdf.mlsCode !== undefined) {
      mlsCode.setText(`MLS ${pdf.mlsCode}`)
    }
    if (pdfData.years && pdfData.years !== null && pdfData.years !== undefined && pdfData.years !== 'custom') {
      years.setText(`${pdfData.years.toString()} years`)
      years.updateAppearances(arialBlackFont)
    }
    if (
      pdfData.customYears &&
      pdfData.customYears !== null &&
      pdfData.customYears !== undefined &&
      pdfData.years === 'custom'
    ) {
      years.setText(`${pdfData.customYears.toString()} years`)
      years.updateAppearances(arialBlackFont)
    }

    if (pdfData.frequency && pdfData.frequency !== null && pdfData.frequency !== undefined) {
      frequencyTitle.setText(`${handleFrequency(pdfData.frequency)} Payment`, {
        lineHeight: 1.2
      })
      frequencyTitle.updateAppearances(arialBlackFont)
    }

    if (pdfData.rangeRate && pdfData.rangeRate.type === 'normalTier') {
      middleTierOr10DownPayment.setText('10')
      maxTierOr15DownPayment.setText('15')
      middleTierOr10DownPayment.updateAppearances(arialBlackFont)
      maxTierOr15DownPayment.updateAppearances(arialBlackFont)
    }

    if (pdfData.rangeRate && pdfData.rangeRate.type === 'middleTier') {
      middleTierOr10DownPayment.setText((Math.round(pdfData.rangeRate.rate * 100) / 100).toString())
      maxTierOr15DownPayment.setText('15')
      middleTierOr10DownPayment.updateAppearances(arialBlackFont)
      maxTierOr15DownPayment.updateAppearances(arialBlackFont)
    }

    // if (pdfData.rangeRate && pdfData.rangeRate.type === 'maxTier') {
    //   middleTierOr10DownPayment.setText('10')
    //   maxTierOr15DownPayment.setText('15')
    //   middleTierOr10DownPayment.updateAppearances(arialBlackFont)
    //   maxTierOr15DownPayment.updateAppearances(arialBlackFont)
    // }

    if (
      pdfData.periodicPay5 &&
      pdfData.periodicPay5 !== null &&
      pdfData.periodicPay5 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      periodicPay5.setText(toCurrencyString(pdfData.periodicPay5))
    }

    if (
      pdfData.periodicPay10 &&
      pdfData.periodicPay10 !== null &&
      pdfData.periodicPay10 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      periodicPay10.setText(toCurrencyString(pdfData.periodicPay10))
    }

    if (
      pdfData.periodicPayRange &&
      pdfData.periodicPayRange !== null &&
      pdfData.periodicPayRange !== undefined &&
      pdfData.rangeRate.type === 'middleTier'
    ) {
      periodicPay10.setText(toCurrencyString(pdfData.periodicPayRange))
    }

    if (
      pdfData.periodicPay15 &&
      pdfData.periodicPay15 !== null &&
      pdfData.periodicPay15 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      periodicPay15.setText(toCurrencyString(pdfData.periodicPay15))
    }

    if (
      pdfData.periodicPayRange &&
      pdfData.periodicPayRange !== null &&
      pdfData.periodicPayRange !== undefined &&
      pdfData.rangeRate.type === 'middleTier'
    ) {
      periodicPay15.setText(toCurrencyString(pdfData.periodicPay15))
    }

    if (pdfData.periodicPay20 && pdfData.periodicPay20 !== null && pdfData.periodicPay20 !== undefined) {
      periodicPay20.setText(toCurrencyString(pdfData.periodicPay20))
    }

    if (pdfData.periodicPayCustom && pdfData.periodicPayCustom !== null && pdfData.periodicPayCustom !== undefined) {
      periodicPayCustom.setText(toCurrencyString(pdfData.periodicPayCustom))
    } else if (pdfData.periodicPayCustom === NaN) {
      periodicPayCustom.setText(toCurrencyString(pdfData.periodicPayCustom))
    } else {
      periodicPayCustom.setText('N/A')
    }

    if (
      pdfData.downPay5 &&
      pdfData.downPay5 !== null &&
      pdfData.downPay5 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      downPay5.setText(toCurrencyString(pdfData.downPay5))
    }

    if (
      pdfData.downPay10 &&
      pdfData.downPay10 !== null &&
      pdfData.downPay10 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      downPay10.setText(toCurrencyString(pdfData.downPay10))
    }

    if (
      pdfData.downPayRange &&
      pdfData.downPayRange !== null &&
      pdfData.downPayRange !== undefined &&
      pdfData.rangeRate.type === 'middleTier'
    ) {
      downPay10.setText(toCurrencyString(pdfData.downPayRange))
    }

    if (
      pdfData.downPay15 &&
      pdfData.downPay15 !== null &&
      pdfData.downPay15 !== undefined &&
      pdfData.rangeRate.type === 'normalTier'
    ) {
      downPay15.setText(toCurrencyString(pdfData.downPay15))
    }

    if (
      pdfData.downPayRange &&
      pdfData.downPayRange !== null &&
      pdfData.downPayRange !== undefined &&
      pdfData.rangeRate.type === 'middleTier'
    ) {
      downPay15.setText(toCurrencyString(pdfData.downPay15))
    }

    if (pdfData.downPay20 && pdfData.downPay20 !== null && pdfData.downPay20 !== undefined) {
      downPay20.setText(toCurrencyString(pdfData.downPay20))
    }
    if (pdfData.downPayCustom && pdfData.downPayCustom !== null && pdfData.downPayCustom !== undefined) {
      downPayCustom.setText(toCurrencyString(pdfData.downPayCustom, 'downPayCustom'))
    } else if (pdfData.downPayCustom === NaN) {
      downPayCustom.setText(toCurrencyString(pdfData.downPayCustom, 'downPayCustom'))
    } else {
      downPayCustom.setText('N/A')
    }

    if (
      pdfData.insurance5 &&
      pdfData.insurance5 !== null &&
      pdfData.insurance5 !== undefined &&
      pdfData.rangeRate.type !== 'maxTier' &&
      pdfData.rangeRate.type !== 'middleTier'
    ) {
      insurance5.setText(toCurrencyString(pdfData.insurance5))
    }

    if (
      pdfData.insurance10 &&
      pdfData.insurance10 !== null &&
      pdfData.insurance10 !== undefined &&
      pdfData.rangeRate.type !== 'maxTier'
    ) {
      insurance10.setText(toCurrencyString(pdfData.insurance10))
    }

    if (
      pdfData.insurance15 &&
      pdfData.insurance15 !== null &&
      pdfData.insurance15 !== undefined &&
      pdfData.rangeRate.type !== 'maxTier'
    ) {
      insurance15.setText(toCurrencyString(pdfData.insurance15))
    }

    insurance20.setText('N/A')

    if (pdfData.insuranceCustom !== null && pdfData.insuranceCustom !== undefined) {
      if (pdfData.rangeRate.type === 'maxTier') {
        if (parseInt(pdfData.customPercentage) >= 20) {
          insuranceCustom.setText('N/A')
        }
      } else {
        insuranceCustom.setText(toCurrencyString(pdfData.insuranceCustom, 'insuranceCustom'))
      }
    }

    if (
      pdfData.insuranceRange &&
      pdfData.insuranceRange !== null &&
      pdfData.insuranceRange !== undefined &&
      pdfData.rangeRate.type === 'middleTier'
    ) {
      if (parseInt(pdfData.customPercentage) >= 20) {
        insuranceCustom.setText('N/A')
      }
      insurance10.setText(toCurrencyString(pdfData.insuranceRange))
    }

    if (
      pdfData.rate5 &&
      pdfData.rate5 !== null &&
      pdfData.rate5 !== undefined &&
      pdfData.rangeRate.type !== 'middleTier' &&
      pdfData.rangeRate.type !== 'maxTier'
    ) {
      rate5.setText(`${pdfData.rate5}%`)
    }

    if (
      pdfData.rate10 &&
      pdfData.rate10 !== null &&
      pdfData.rate10 !== undefined &&
      pdfData.rangeRate.type !== 'maxTier'
    ) {
      rate10.setText(`${pdfData.rate10}%`)
    }

    if (
      pdfData.rate15 &&
      pdfData.rate15 !== null &&
      pdfData.rate15 !== undefined &&
      pdfData.rangeRate.type !== 'maxTier'
    ) {
      rate15.setText(`${pdfData.rate15}%`)
    }

    if (pdfData.rate20 && pdfData.rate20 !== null && pdfData.rate20 !== undefined) {
      rate20.setText(`${pdfData.rate20}%`)
    }

    if (pdfData.rateCustom) {
      if (pdfData.rateCustom === 0 || pdfData.rateCustom === NaN || pdfData.rateCustom === '0') {
        rateCustom.setText('N/A')
      } else {
        rateCustom.setText(`${pdfData.rateCustom}%`)
      }
    } else {
      rateCustom.setText('N/A')
    }

    if (
      pdfData.rateRange &&
      pdfData.rateRange !== null &&
      pdfData.rateRange !== undefined &&
      (pdfData.rangeRate.type === 'normalTier' || pdfData.rangeRate.type === 'middleTier')
    ) {
      rate10.setText(`${pdfData.rateRange}%`)
    }

    if (brokerPhotoFinal && brokerPhotoFinal !== null && brokerPhotoFinal !== undefined) {
      brokerPhoto.setImage(brokerPhotoFinal)
    }
    if (realtorPhotoFinal && realtorPhotoFinal !== null && realtorPhotoFinal !== undefined) {
      realtorPhoto.setImage(realtorPhotoFinal)
    } else {
      realtorPhoto.setImage(realtorDefaultImage)
    }
    if (propertyPhotoFinal && propertyPhotoFinal !== null && propertyPhotoFinal !== undefined) {
      propertyPhoto.setImage(propertyPhotoFinal)
    } else {
      propertyPhoto.setImage(propertyDefaultImage)
    }
    if (user && user.firstname && user.firstname !== null && user.firstname !== undefined) {
      brokerName.setText(`${user.firstname} ${user.lastname}`)
      brokerName.updateAppearances(arialBlackFont)
    }
    if (user && user.position && user.position !== null && user.position !== undefined) {
      brokerPosition.setText(user.position)
    }
    if (user && user.workEmail && user.workEmail !== null && user.workEmail !== undefined && user.workEmail !== '') {
      email.setText(user.workEmail)
    } else {
      if (user && user.email && user.email !== null && user.email !== undefined) {
        email.setText(user.email)
      } else {
        email.setText('--')
      }
    }

    if (user && user.workPhone && user.workPhone !== null && user.workPhone !== undefined) {
      phone.setText(user.workPhone)
    } else if (user && user.phone && user.phone !== null && user.phone !== undefined) {
      phone.setText(user.phone)
    } else if (user && user.cellPhone && user.cellPhone !== null && user.cellPhone !== undefined) {
      phone.setText(user.cellPhone)
    } else {
      phone.setText('--')
    }

    if (user && user.website && user.website !== null && user.website !== undefined) {
      website.setText(user.website)
    }
    if (user && user.brokerage && user.brokerage !== null && user.brokerage !== undefined) {
      licensedWith.setText(user.brokerage)
    }
    if (realtor && realtor.firstname && realtor.firstname !== null && realtor.firstname !== undefined) {
      realtorName.setText(`${realtor.firstname} ${realtor.lastname}`)
      realtorName.updateAppearances(arialBlackFont)
    }
    if (realtor && realtor.position && realtor.position !== null && realtor.position !== undefined) {
      realtorPosition.setText(realtor.position)
    }
    if (realtor && realtor.email && realtor.email !== null && realtor.email !== undefined) {
      realtorEmail.setText(realtor.email)
    }
    if (realtor && realtor.phone && realtor.phone !== null && realtor.phone !== undefined) {
      realtorPhone.setText(realtor.phone)
    }
    if (realtor && realtor.website && realtor.website !== null && realtor.website !== undefined) {
      realtorWebsite.setText(realtor.website)
    }
    if (realtor && realtor.company && realtor.company !== null && realtor.company !== undefined) {
      realtorLicensedWith.setText(realtor.company)
    }

    if (pdfData.customPercentage) {
      if (pdfData.customPercentage === 0 || pdfData.customPercentage === NaN || pdfData.customPercentage === '0') {
        customDown.setText('N/A')
      } else {
        customDown.setText(`${pdfData.customPercentage}`)
      }
      customDown.updateAppearances(arialBlackFont)
    } else {
      customDown.setText('N/A')
      customDown.updateAppearances(arialBlackFont)
    }

    if (pdf.monthlyExpenses || pdf.cashNeeded) {
      if (pdfData.chosenDownPay && pdfData.chosenDownPay.rate !== null && pdfData.chosenDownPay.rate !== undefined) {
        rate.setText(`${handleChosenRate().toString()}%`)
        rate.updateAppearances(arialBlackFont)
      }
      if (
        pdfData.chosenDownPay &&
        pdfData.chosenDownPay.percent !== null &&
        pdfData.chosenDownPay.percent !== undefined
      ) {
        downPaymentRate.setText(pdfData.chosenDownPay.percent)
        downPaymentRate.updateAppearances(arialBlackFont)
      }
    }
  }

  const getExpensesFields = (form) => {
    mortgagePayment = form.getTextField('mortgagePayment')
    propertyTax = form.getTextField('propertyTax')
    propertyInsurance = form.getTextField('propertyInsurance')
    monthlyDebtPayment = form.getTextField('monthlyDebtPayment')
    utilities = form.getTextField('utilities')
    condoFee = form.getTextField('condoFee')
    hoaFee = form.getTextField('hoaFee')
    phoneExpense = form.getTextField('phoneExpense')
    cable = form.getTextField('cable')
    internet = form.getTextField('internet')
    totalExpenses = form.getTextField('totalExpenses')
    totalYearly = form.getTextField('totalYearly')
    mortgagePayment.setText(toCurrencyString(pdfData.chosenPeriodicPay))
    propertyTax.setText(toCurrencyString(pdfData.propertyTax))
    propertyInsurance.setText(toCurrencyString(pdfData.propertyInsurance))
    monthlyDebtPayment.setText(toCurrencyString(pdfData.monthlyDebtPayments))
    utilities.setText(toCurrencyString(pdfData.utilities))
    condoFee.setText(toCurrencyString(pdfData.condoFees))
    hoaFee.setText(toCurrencyString(pdfData.hoaFees))
    phoneExpense.setText(toCurrencyString(pdfData.phone))
    cable.setText(toCurrencyString(pdfData.cable))
    internet.setText(toCurrencyString(pdfData.internet))
    totalExpenses.setText(toCurrencyString(pdfData.totalMonthlyPayments))
    totalExpenses.updateAppearances(arialBlackFont)
    totalYearly.setText(toCurrencyString(pdfData.totalYearlyPayments))
    totalYearly.updateAppearances(arialBlackFont)
  }

  const getCashFields = (form) => {
    downPayment = form.getTextField('downPayment')
    lawyerFees = form.getTextField('lawyerFees')
    homeInspection = form.getTextField('homeInspection')
    appraisal = form.getTextField('appraisal')
    titleInsurance = form.getTextField('titleInsurance')
    estoppelCertificate = form.getTextField('estoppelCertificate')
    totalCashNeeded = form.getTextField('totalCashNeeded')
    downPayment.setText(toCurrencyString(pdfData.chosenDownPay.amount))
    lawyerFees.setText(toCurrencyString(pdfData.lawyerFee))
    homeInspection.setText(toCurrencyString(pdfData.homeInspection))
    appraisal.setText(toCurrencyString(pdfData.appraisal))
    titleInsurance.setText(toCurrencyString(pdfData.titleInsurance))
    estoppelCertificate.setText(toCurrencyString(pdfData.estoppelFee))
    totalCashNeeded.setText(toCurrencyString(pdfData.totalCashNeeded))
    totalCashNeeded.updateAppearances(arialBlackFont)
  }

  const getCommonFields = async (form) => {
    if (pdf.monthlyExpenses || pdf.cashNeeded) {
      downPaymentRate = form.getTextField('downPaymentRate')
      rate = form.getTextField('rate')
    }
    address = form.getTextField('address')
    address.enableMultiline()
    mlsCode = form.getTextField('mlsCode')
    askingPrice = form.getTextField('askingPrice')
    years = form.getTextField('years')
    customDown = form.getTextField('customDownPayment')
    frequencyTitle = form.getTextField('frequencyTitle')
    frequencyTitle.enableMultiline()

    if (pdfData.rangeRate.type === 'normalTier') {
      middleTierOr10DownPayment = form.getTextField('middleTierOr10DownPayment')
      maxTierOr15DownPayment = form.getTextField('maxTierOr15DownPayment')
      periodicPay5 = form.getTextField('periodicPay5')
      periodicPay10 = form.getTextField('periodicPay10')
      periodicPay15 = form.getTextField('periodicPay15')
      periodicPay20 = form.getTextField('periodicPay20')
      downPay5 = form.getTextField('downPay5')
      downPay10 = form.getTextField('downPay10')
      downPay15 = form.getTextField('downPay15')
      downPay20 = form.getTextField('downPay20')
      downPayCustom = form.getTextField('downPayCustom')
      insurance5 = form.getTextField('insurance5')
      insurance10 = form.getTextField('insurance10')
      insurance15 = form.getTextField('insurance15')
      insurance20 = form.getTextField('insurance20')
      rate5 = form.getTextField('rate5')
      rate10 = form.getTextField('rate10')
      rate15 = form.getTextField('rate15')
      rate20 = form.getTextField('rate20')
    }

    if (pdfData.rangeRate.type === 'middleTier') {
      middleTierOr10DownPayment = form.getTextField('middleTierOr10DownPayment')
      maxTierOr15DownPayment = form.getTextField('maxTierOr15DownPayment')
      periodicPay10 = form.getTextField('periodicPay10')
      periodicPay15 = form.getTextField('periodicPay15')
      periodicPay20 = form.getTextField('periodicPay20')
      downPay10 = form.getTextField('downPay10')
      downPay15 = form.getTextField('downPay15')
      downPay20 = form.getTextField('downPay20')
      downPayCustom = form.getTextField('downPayCustom')
      insurance10 = form.getTextField('insurance10')
      insurance15 = form.getTextField('insurance15')
      insurance20 = form.getTextField('insurance20')
      rate10 = form.getTextField('rate10')
      rate15 = form.getTextField('rate15')
      rate20 = form.getTextField('rate20')
    }

    if (pdfData.rangeRate.type === 'maxTier') {
      periodicPay20 = form.getTextField('periodicPay20')
      downPay20 = form.getTextField('downPay20')
      downPayCustom = form.getTextField('downPayCustom')
      insurance20 = form.getTextField('insurance20')
      rate20 = form.getTextField('rate20')
    }

    periodicPayCustom = form.getTextField('periodicPayCustom')
    downPayCustom = form.getTextField('downPayCustom')
    insuranceCustom = form.getTextField('insuranceCustom')
    rateCustom = form.getTextField('rateCustom')
    brokerPhoto = form.getButton('brokerPhoto_af_image')
    realtorPhoto = form.getButton('realtorPhoto_af_image')
    propertyPhoto = form.getButton('banner_af_image')
    brokerName = form.getTextField('brokerName')
    brokerPosition = form.getTextField('brokerPosition')
    email = form.getTextField('email')
    phone = form.getTextField('phone')
    website = form.getTextField('website')
    licensedWith = form.getTextField('licensedWith')
    realtorName = form.getTextField('realtorName')
    realtorPosition = form.getTextField('realtorPosition')
    realtorEmail = form.getTextField('realtorEmail')
    realtorPhone = form.getTextField('realtorPhone')
    realtorWebsite = form.getTextField('realtorWebsite')
    realtorLicensedWith = form.getTextField('realtorLicensedWith')
  }

  const handleBrokerageLogo = async () => {
    let logo

    const pages = pdfDoc.getPages()
    const firstPage = pages[0]
    const lastPage = pages[pages.length - 1]

    if (user && user.team && user.team.id === '60c0aa8960fa67001794f578') {
      logo = `${baseUrl}/images/Ideal_Mortgage_Solutions_Logo.png`
      const { arrayBuffer: brokerageLogo } = await fetchImage(logo)
      const brokerageLogoFinal = await pdfDoc.embedPng(brokerageLogo)

      const company = `${baseUrl}/images/indi-logo-horizontal-tagline-fsra.png`
      const { arrayBuffer: companyLogo } = await fetchImage(company)
      const companyLogoFinal = await pdfDoc.embedPng(companyLogo)

      const { width, height } = firstPage.getSize()

      //Adding Logo
      firstPage.drawImage(brokerageLogoFinal, {
        width: 103,
        height: 45,
        x: 15,
        y: height - 58
      })

      lastPage.drawImage(brokerageLogoFinal, {
        width: !pdf.monthlyExpenses && !pdf.cashNeeded ? 80 : 103,
        height: !pdf.monthlyExpenses && !pdf.cashNeeded ? 35 : 45,
        x: !pdf.monthlyExpenses && !pdf.cashNeeded ? 20 : 24,
        y: !pdf.monthlyExpenses && !pdf.cashNeeded ? 8 : 12
      })

      firstPage.drawImage(companyLogoFinal, {
        width: 115,
        height: 31,
        x: width - 146,
        y: height - 50
      })
    } else {
      logo = `${baseUrl}/images/indi-logo-horizontal-tagline.png`
      const { arrayBuffer: brokerageLogo } = await fetchImage(logo)
      const brokerageLogoFinal = await pdfDoc.embedPng(brokerageLogo)

      const pages = pdfDoc.getPages()
      const firstPage = pages[0]
      const { width, height } = firstPage.getSize()

      //Adding Logo
      firstPage.drawImage(brokerageLogoFinal, {
        width: 111,
        height: 30,
        x: 26,
        y: height - 58
      })

      lastPage.drawImage(brokerageLogoFinal, {
        width: 111,
        height: 30,
        x: 26,
        y: 10
      })
    }
  }

  // Determine which PDF template to use
  let sourcePdfUrl
  if (pdf.monthlyExpenses && pdf.cashNeeded) {
    sourcePdfUrl = `${baseUrl}/listing-models/${
      pdfData.rangeRate && pdfData.rangeRate.type ? pdfData.rangeRate.type : 'full/2024'
    }/Listing_Sheets_Template_Payment_Cash_Expenses.pdf`
  } else if (pdf.monthlyExpenses && !pdf.cashNeeded) {
    sourcePdfUrl = `${baseUrl}/listing-models/${
      pdfData.rangeRate && pdfData.rangeRate.type ? pdfData.rangeRate.type : 'full/2024'
    }/Listing_Sheets_Template_Payment_Expenses.pdf`
  } else if (!pdf.monthlyExpenses && pdf.cashNeeded) {
    sourcePdfUrl = `${baseUrl}/listing-models/${
      pdfData.rangeRate && pdfData.rangeRate.type ? pdfData.rangeRate.type : 'full/2024'
    }/Listing_Sheets_Template_Payment_Cash.pdf`
  } else {
    sourcePdfUrl = `${baseUrl}/listing-models/${
      pdfData.rangeRate && pdfData.rangeRate.type ? pdfData.rangeRate.type : 'full/2024'
    }/Listing_Sheets_Template_Only_Payment.pdf`
  }

  // Main execution
  try {
    const pdfForm = await getPdfForm(sourcePdfUrl)
    await fetchAndEmbedImages()
    await getCommonFields(pdfForm)
    if (pdf.monthlyExpenses) getExpensesFields(pdfForm)
    if (pdf.cashNeeded) getCashFields(pdfForm)
    setFields()
    await handleBrokerageLogo()

    const pdfBytes = await pdfDoc.save()
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const pdfFile = new File([blob], 'listing-sheet.pdf')
    const docUrl = URL.createObjectURL(blob)

    if (isSafari) {
      saveAs(docUrl, 'listing-sheet.pdf')
      window.open(docUrl)
      return { pdfFile, docUrl, status: 'finished' }
    }
    window.open(docUrl)
    return { pdfFile, docUrl, status: 'finished' }
  } catch (error) {
    console.error('Error generating listing sheet:', error)
    return { status: 'error', message: error.message }
  }
}

export default GenerateListingSheet
