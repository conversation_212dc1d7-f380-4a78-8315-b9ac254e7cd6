import axios from 'axios'
import heicConvert from 'heic-convert'

/**
 * Checks if the given ArrayBuffer is likely to be a HEIC image.
 *
 * @param {ArrayBuffer} arrayBuffer - The buffer to check.
 * @returns {boolean} - True if the buffer is likely a HEIC image, false otherwise.
 */
function isLikelyHEIC(arrayBuffer) {
  const uint8Array = new Uint8Array(arrayBuffer)
  const heicSignature = [0x66, 0x74, 0x79, 0x70, 0x68, 0x65, 0x69, 0x63] // "ftypheic"

  for (let i = 4; i < uint8Array.length - 8; i++) {
    if (heicSignature.every((value, index) => uint8Array[i + index] === value)) {
      return true
    }
  }
  return false
}

/**
 * Converts HEIC image data to JPEG format.
 *
 * @param {ArrayBuffer} arrayBuffer - The HEIC image data.
 * @returns {Promise<ArrayBuffer>} - The converted JPEG data.
 */
async function convertHeicToJpeg(arrayBuffer) {
  try {
    if (!isLikelyHEIC(arrayBuffer)) {
      console.warn('File does not appear to be a valid HEIC image. Skipping conversion.')
      return arrayBuffer
    }

    const jpegBuffer = await heicConvert({
      buffer: Buffer.from(arrayBuffer),
      format: 'JPEG',
      quality: 0.9
    })
    return jpegBuffer.buffer
  } catch (error) {
    console.error('Error converting HEIC to JPEG:', error)
    return arrayBuffer
  }
}

/**
 * Fetches an image directly without using the proxy
 *
 * @param {string} url - The URL of the image to fetch
 * @param {Object} options - Additional options for the fetch operation
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {string} options.fallbackUrl - Fallback URL to use if the main URL fails
 * @returns {Promise<{arrayBuffer: ArrayBuffer, contentType: string}>}
 */
export async function fetchImage(url, options = {}) {
  const { maxRetries = 3, fallbackUrl = null } = options

  const fetchWithRetry = async (attemptUrl) => {
    let lastError = null

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        // Fetch the image via the s3proxy API route
        const proxyUrl = `/api/s3proxy?url=${encodeURIComponent(attemptUrl)}`

        const response = await axios.get(proxyUrl, {
          responseType: 'arraybuffer',
          // Headers like Accept and User-Agent are less critical here as the proxy handles the S3 request,
          // but keeping Accept might be good practice.
          headers: {
            Accept: 'image/*'
          }
        })

        let contentType = response.headers['content-type'] || 'image/jpeg'
        let arrayBuffer = response.data

        // Handle HEIC conversion if needed
        if (contentType.includes('heic') || attemptUrl.toLowerCase().endsWith('.heic')) {
          arrayBuffer = await convertHeicToJpeg(arrayBuffer)
          contentType = 'image/jpeg'
        }

        // Return the image data
        return { arrayBuffer, contentType }
      } catch (error) {
        console.error(`Attempt ${attempt + 1} failed to fetch image:`, error.message)
        lastError = error

        // Wait before retrying
        if (attempt < maxRetries - 1) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * (attempt + 1)))
        }
      }
    }

    // If all attempts fail and we have a fallback URL, try that
    if (fallbackUrl && attemptUrl !== fallbackUrl) {
      return fetchWithRetry(fallbackUrl)
    }

    // If everything fails, throw the error
    throw lastError || new Error('Failed to fetch image after multiple attempts')
  }

  try {
    return await fetchWithRetry(url)
  } catch (error) {
    console.error('All attempts to fetch image failed:', error)
    throw error
  }
}

/**
 * Determines the file extension from a URL or falls back to a default.
 *
 * @param {string} url - The URL of the file.
 * @param {string} defaultExt - The default extension to use if one can't be determined.
 * @returns {string} - The file extension (including the dot) or the default.
 */
export function getFileExtension(url, defaultExt = '.jpg') {
  if (!url) return defaultExt
  const match = url.match(/\.([a-zA-Z0-9]+)(?:[?#]|$)/i)
  return match ? `.${match[1].toLowerCase()}` : defaultExt
}
