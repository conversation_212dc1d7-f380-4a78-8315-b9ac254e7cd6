// helpers/fetchers.js
import axios from 'axios'
import Cookies from 'js-cookie'

const apiURL = process.env.NEXT_PUBLIC_API_URL
const config = {
  headers: {
    Authorization: `Bearer ${Cookies.get('jwt')}`
  }
}

export const getUserRealtors = async (userId) => {
  console.log('userId', userId)
  return axios
    .get(`${apiURL}/realtors?filters[user][$eq]=${userId}`, config)
    .then((res) => {
      console.log('REALTORS res.data', res.data)
      return res.data.data
    })
    .catch((err) => {
      throw err
    })
}

const buildDirectoryQueryParams = ({
  start = 0,
  limit = 12,
  sort = 'firstname:ASC',
  province,
  search,
  isStaffMember
} = {}) => {
  const queryParams = new URLSearchParams({
    _start: start.toString(),
    _limit: limit.toString(),
    _sort: sort
  })

  if (province && province !== 'all') {
    queryParams.append('province', province)
  }

  if (search) {
    queryParams.append('search', search)
  }

  // Add staffFilter parameter if isStaffMember is true
  if (isStaffMember === true) {
    queryParams.append('staffFilter', 'only')
  }

  return queryParams
}

export const fetchDirectoryData = async (
  { start = 0, limit = 12, sort = 'firstname:ASC', province, search, isStaffMember, customConfig = null },
  isFilteringRef = null
) => {
  const queryParams = buildDirectoryQueryParams({ start, limit, sort, province, search, isStaffMember })
  const requestConfig = customConfig || config

  try {
    const [usersResponse, countResponse] = await Promise.all([
      axios.get(`${apiURL}/users-permissions/directory?${queryParams}`, requestConfig),
      axios.get(`${apiURL}/users-permissions/directory/count?${queryParams}`, requestConfig)
    ])

    // Ensure we have valid data before returning
    if (!usersResponse.data) {
      throw new Error('Invalid response from directory endpoint')
    }

    return {
      users: usersResponse.data,
      count: countResponse.data || 0,
      error: null
    }
  } catch (error) {
    console.error('Error fetching directory data:', error)
    throw error // Let the component handle the error
  }
}

// This overload can be used in getServerSideProps when you need to pass a specific JWT
export const fetchDirectoryDataWithConfig = async (params = {}, customConfig) => {
  return fetchDirectoryData({ ...params, customConfig })
}
