{"name": "axiomcentral", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "rm -rf .next && next build", "start": "next start", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"@cloudinary/url-gen": "^1.19.0", "@iconscout/react-unicons": "^1.1.6", "@pdf-lib/fontkit": "^1.1.1", "axios": "^0.21.1", "dotenv": "^16.4.5", "file-saver": "^2.0.5", "framer-motion": "^10.12.16", "fslightbox-react": "^1.7.6", "heic-convert": "^2.1.0", "html-to-image": "^1.10.8", "html2canvas": "^1.0.0-rc.7", "js-cookie": "^2.2.1", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "lottie-react": "^2.4.0", "markdown-it": "^13.0.1", "moment": "^2.29.1", "next": "^13.4.5", "nookies": "^2.5.2", "nprogress": "^0.2.0", "pdf-lib": "^1.16.0", "pure-react-carousel": "^1.30.1", "react": "^18.2.0", "react-big-calendar": "^1.8.4", "react-currency-input-field": "^3.9.0", "react-doc-viewer": "^0.1.5", "react-dom": "^18.2.0", "react-draggable": "^4.4.4", "react-easy-crop": "^5.0.8", "react-grid-layout": "^1.3.4", "react-grid-system": "^8.1.6", "react-iframe": "^1.8.0", "react-indiana-drag-scroll": "^1.8.1", "react-moment": "^1.1.1", "react-pdf-thumbnail": "^0.1.0", "react-player": "^2.16.0", "react-qr-code": "^2.0.2", "react-text-mask": "^5.5.0", "sass": "^1.32.2", "sharp": "^0.28.1"}, "devDependencies": {"babel-core": "^6.26.3", "babel-eslint": "^10.1.0", "babel-preset-airbnb": "^5.0.0", "babel-preset-react-native": "^4.0.1", "cz-conventional-changelog": "3.3.0", "eslint": "^7.25.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.2.0", "eslint-watch": "^7.0.0", "pre-commit": "^1.2.2", "prettier": "^2.2.1", "prettier-eslint": "^12.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": "18"}}