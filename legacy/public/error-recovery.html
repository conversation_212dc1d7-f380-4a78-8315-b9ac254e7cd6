<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Indi Central - Error Recovery</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
        background-color: #f5f5f5;
      }
      .logo {
        margin-bottom: 30px;
        max-width: 200px;
      }
      .container {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 500px;
      }
      h1 {
        color: #333;
        margin-top: 0;
      }
      p {
        color: #666;
        line-height: 1.5;
      }
      .btn {
        background-color: #0070f3;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 10px;
        width: 100%;
      }
      .btn.red {
        background-color: #ff4b4b;
      }
      .btn:hover {
        opacity: 0.9;
      }
      .note {
        font-size: 14px;
        color: #888;
        margin-top: 15px;
      }
      .button-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <img src="/images/indi-central-logo.svg" alt="Indi Central Logo" class="logo" />
    <div class="container">
      <h1>Application Error Recovery</h1>
      <p>We've detected an error with the application that might be caused by outdated cached files.</p>
      <p>Please try one of these solutions:</p>

      <div class="button-container">
        <button id="reloadBtn" class="btn">Reload Application</button>
        <button id="clearDataBtn" class="btn red">Clear Site Data & Reload</button>
      </div>

      <p class="note">
        If the problem persists, please try opening the site in an incognito window or contact support.
      </p>
    </div>

    <script>
      // Get buttons
      const reloadBtn = document.getElementById('reloadBtn')
      const clearDataBtn = document.getElementById('clearDataBtn')

      // Cache-busting reload function
      function cacheBustingReload() {
        const timestamp = Date.now()
        // Get current path or default to root
        const currentPath = window.location.pathname || '/'
        window.location.href = `${currentPath}?reload=${timestamp}`
      }

      // Clear site data function
      function clearSiteDataAndReload() {
        // Clear caches if available
        if ('caches' in window) {
          caches.keys().then(function (keyList) {
            return Promise.all(
              keyList.map(function (key) {
                return caches.delete(key)
              })
            )
          })
        }

        // Clear localStorage
        localStorage.clear()

        // Clear sessionStorage
        sessionStorage.clear()

        // Delay reload slightly to ensure clearing completes
        setTimeout(function () {
          cacheBustingReload()
        }, 500)
      }

      // Add event listeners to buttons
      reloadBtn.addEventListener('click', cacheBustingReload)
      clearDataBtn.addEventListener('click', clearSiteDataAndReload)

      // Also check for online status changes
      window.addEventListener('online', function () {
        cacheBustingReload()
      })
    </script>
  </body>
</html>
