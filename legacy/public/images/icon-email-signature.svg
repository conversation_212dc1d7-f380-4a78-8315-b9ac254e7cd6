<svg width="133" height="130" viewBox="0 0 133 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_1051)">
<path d="M54.8396 103.05L56.6899 104.095L70.8136 78.7182C70.0118 77.7965 69.8268 76.3833 70.4436 75.2158C71.2454 73.7412 73.1573 73.1882 74.6375 73.9869C76.1177 74.7857 76.6728 76.6905 75.871 78.1652C75.2543 79.3327 73.9591 79.8857 72.7256 79.7013L58.6018 105.078L60.2671 106C60.2671 106 73.3423 85.7844 89.563 81.1146L91.4133 60.4076L84.2589 56.4751L77.1045 52.5426L60.4521 65.1389C65.1395 81.2989 54.8396 103.05 54.8396 103.05Z" fill="url(#paint0_linear_1_1051)"/>
<path d="M79.695 48.4873L93.5104 56.1065C94.6822 56.7209 95.1139 58.1956 94.4355 59.3631C93.8187 60.5305 92.3385 60.9606 91.1667 60.2847L77.3513 52.6655C76.1795 52.0511 75.7477 50.5764 76.4262 49.4089C77.0429 48.3029 78.5231 47.8728 79.695 48.4873Z" fill="url(#paint1_radial_1_1051)"/>
<path d="M93.4487 56.2294L79.6333 48.6102L85.1224 27.043C85.1224 27.043 86.4793 24.5852 98.321 31.0984C109.423 37.2429 108.806 40.0694 108.806 40.0694L93.4487 56.2294Z" fill="url(#paint2_linear_1_1051)"/>
</g>
<path d="M39.3119 89.86C38.6572 93.5559 38.6572 98.3389 30.9321 97.9475C27.7897 97.7736 24.4727 94.9038 24.4727 90.6427C24.4727 83.9465 29.099 78.9462 36.6495 78.9462C39.0064 78.9462 39.7483 79.4245 41.1449 80.0767L39.3119 89.86ZM39.3119 89.86C38.4826 93.9907 39.1373 95.8604 40.9267 95.9039C43.6763 95.9474 48.0844 92.295 48.0844 85.9902C48.0844 78.1635 43.3708 72.5109 34.9474 72.5109C26.0876 72.5544 18.668 79.5549 18.668 90.5122C18.668 99.5563 24.6036 105.122 32.6342 105.122C35.733 105.122 48.9573 105.122 54.8057 105.035" stroke="#CFCFCF" stroke-width="3" stroke-miterlimit="10"/>
<defs>
<filter id="filter0_d_1_1051" x="46.8396" y="18.5883" width="85.9724" height="111.412" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_1051"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_1051" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_1051" x1="85.4551" y1="88.5616" x2="56.4205" y2="72.424" gradientUnits="userSpaceOnUse">
<stop stop-color="#A9A9A9"/>
<stop offset="0.4794" stop-color="#EDEDED"/>
<stop offset="1" stop-color="#C2C2C2"/>
</linearGradient>
<radialGradient id="paint1_radial_1_1051" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(85.4215 54.4249) rotate(28.8836) scale(15.2974 5.05571)">
<stop offset="0.0107473" stop-color="#E2E2E2"/>
<stop offset="0.4814" stop-color="#BDBDBD"/>
<stop offset="1" stop-color="#A2A2A2"/>
</radialGradient>
<linearGradient id="paint2_linear_1_1051" x1="104.305" y1="48.3272" x2="80.6297" y2="35.1682" gradientUnits="userSpaceOnUse">
<stop stop-color="#541A01"/>
<stop offset="0.4794" stop-color="#99523C"/>
<stop offset="1" stop-color="#502208"/>
</linearGradient>
</defs>
</svg>
