<svg width="130" height="130" viewBox="0 0 130 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_1838)">
<path d="M65.5257 102.051C87.3551 102.051 105.051 84.3551 105.051 62.5257C105.051 40.6962 87.3551 23 65.5257 23C43.6962 23 26 40.6962 26 62.5257C26 84.3551 43.6962 102.051 65.5257 102.051Z" fill="url(#paint0_linear_1_1838)"/>
</g>
<path d="M65.5257 79.0033C74.626 79.0033 82.0033 71.626 82.0033 62.5257C82.0033 53.4254 74.626 46.0481 65.5257 46.0481C56.4254 46.0481 49.0481 53.4254 49.0481 62.5257C49.0481 71.626 56.4254 79.0033 65.5257 79.0033Z" fill="url(#paint1_linear_1_1838)"/>
<g filter="url(#filter1_d_1_1838)">
<path d="M96.2735 47.9474C95.9142 47.2287 95.0929 46.9207 94.3743 47.2801L67.8869 60.0104C67.3736 59.4971 66.655 59.1891 65.885 59.0864L57.9799 51.13C56.9532 50.1033 55.2593 50.1033 54.2326 51.13C53.206 52.1566 53.206 53.8506 54.2326 54.8772L62.0864 62.731C62.1891 64.5276 63.6777 65.9649 65.5257 65.9649C67.425 65.9649 68.9136 64.4763 68.9649 62.6283L95.6062 49.898C96.2736 49.5387 96.5815 48.666 96.2735 47.9474Z" fill="url(#paint2_linear_1_1838)"/>
</g>
<path d="M65.5256 64.887C66.8297 64.887 67.8869 63.8298 67.8869 62.5257C67.8869 61.2216 66.8297 60.1644 65.5256 60.1644C64.2215 60.1644 63.1643 61.2216 63.1643 62.5257C63.1643 63.8298 64.2215 64.887 65.5256 64.887Z" fill="url(#paint3_linear_1_1838)"/>
<g filter="url(#filter2_d_1_1838)">
<path d="M66.963 62.5256C66.963 61.807 66.4497 61.2423 65.7823 61.0883V56.2118C65.7823 56.0578 65.6797 55.9551 65.5257 55.9551C65.3717 55.9551 65.269 56.0578 65.269 56.2118V61.0883C64.6017 61.191 64.0884 61.807 64.0884 62.5256C64.0884 63.2443 64.6017 63.8089 65.269 63.9629V92.0416C65.269 92.1956 65.3717 92.2982 65.5257 92.2982C65.6797 92.2982 65.7823 92.1956 65.7823 92.0416V63.9116C66.4497 63.8089 66.963 63.193 66.963 62.5256ZM65.5257 63.4496C65.0124 63.4496 64.6017 63.039 64.6017 62.5256C64.6017 62.0123 65.0124 61.6017 65.5257 61.6017C66.039 61.6017 66.4497 62.0123 66.4497 62.5256C66.4497 63.039 66.039 63.4496 65.5257 63.4496Z" fill="#E2591B"/>
</g>
<path d="M65.5258 94.9675C65.2178 94.9675 64.9612 95.2242 64.9612 95.5322V99.9467C64.9612 100.255 65.2178 100.511 65.5258 100.511C65.8338 100.511 66.0905 100.255 66.0905 99.9467V95.5322C66.0392 95.2242 65.7825 94.9675 65.5258 94.9675Z" fill="#6A6A6A"/>
<path d="M65.5258 24.54C65.2178 24.54 64.9612 24.7966 64.9612 25.1046V29.5192C64.9612 29.8272 65.2178 30.0838 65.5258 30.0838C65.8338 30.0838 66.0905 29.8272 66.0905 29.5192V25.1046C66.0392 24.7966 65.7825 24.54 65.5258 24.54Z" fill="#6A6A6A"/>
<path d="M33.0326 62.5257C33.0326 62.2177 32.7759 61.9611 32.4679 61.9611H28.1047C27.7967 61.9611 27.54 62.2177 27.54 62.5257C27.54 62.7824 27.7967 63.039 28.1047 63.039H32.5192C32.7759 63.039 33.0326 62.7824 33.0326 62.5257Z" fill="#6A6A6A"/>
<path d="M103.46 62.5257C103.46 62.2177 103.203 61.9611 102.895 61.9611H98.4809C98.1729 61.9611 97.9163 62.2177 97.9163 62.5257C97.9163 62.8337 98.1729 63.0904 98.4809 63.0904H102.895C103.203 63.039 103.46 62.7824 103.46 62.5257Z" fill="#6A6A6A"/>
<defs>
<filter id="filter0_d_1_1838" x="18" y="15" width="111.051" height="111.051" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_1838"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_1838" result="shape"/>
</filter>
<filter id="filter1_d_1_1838" x="49.4626" y="47.1295" width="50.9307" height="26.8354" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_1838"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_1838" result="shape"/>
</filter>
<filter id="filter2_d_1_1838" x="60.0884" y="55.9551" width="10.8745" height="44.3431" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_1838"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_1838" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1_1838" x1="80.6931" y1="33.2569" x2="44.2703" y2="103.385" gradientUnits="userSpaceOnUse">
<stop stop-color="#CACACC"/>
<stop offset="1" stop-color="#FEFEFE"/>
</linearGradient>
<linearGradient id="paint1_linear_1_1838" x1="65.5033" y1="78.2443" x2="65.5033" y2="45.2986" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFBFBF"/>
</linearGradient>
<linearGradient id="paint2_linear_1_1838" x1="83.9605" y1="60.7045" x2="63.863" y2="40.6071" gradientUnits="userSpaceOnUse">
<stop stop-color="#747474"/>
<stop offset="1" stop-color="#4B4E4E"/>
</linearGradient>
<linearGradient id="paint3_linear_1_1838" x1="65.4782" y1="60.3216" x2="65.5326" y2="65.0695" gradientUnits="userSpaceOnUse">
<stop stop-color="#DCDCDC"/>
<stop offset="1" stop-color="#E9E9E9"/>
</linearGradient>
</defs>
</svg>
