<svg width="130" height="130" viewBox="0 0 130 130" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_201_410)">
<path d="M97.147 104.723H35.8531C32.2347 104.723 29.2742 101.731 29.2742 98.075V36.1381C29.2742 32.4818 32.2347 29.4902 35.8531 29.4902H97.147C100.765 29.4902 103.726 32.4818 103.726 36.1381V98.075C103.726 101.731 100.765 104.723 97.147 104.723Z" fill="url(#paint0_linear_201_410)" stroke="url(#paint1_linear_201_410)" stroke-miterlimit="10"/>
<path d="M97.147 103.892H35.8531C32.2347 103.892 29.2742 100.9 29.2742 97.2441V35.3626C29.2742 31.7062 32.2347 28.7146 35.8531 28.7146H97.147C100.765 28.7146 103.726 31.7062 103.726 35.3626V97.2994C103.726 100.9 100.765 103.892 97.147 103.892Z" fill="url(#paint2_linear_201_410)" stroke="url(#paint3_linear_201_410)" stroke-miterlimit="10"/>
<path d="M97.147 103.116H35.8531C32.2347 103.116 29.2742 100.125 29.2742 96.4684V34.5316C29.2742 30.8752 32.2347 27.8836 35.8531 27.8836H97.147C100.765 27.8836 103.726 30.8752 103.726 34.5316V96.4684C103.726 100.125 100.765 103.116 97.147 103.116Z" fill="url(#paint4_linear_201_410)" stroke="url(#paint5_linear_201_410)" stroke-miterlimit="10"/>
<path d="M97.147 102.285H35.8531C32.2347 102.285 29.2742 99.2938 29.2742 95.6374V33.7005C29.2742 30.0442 32.2347 27.0526 35.8531 27.0526H97.147C100.765 27.0526 103.726 30.0442 103.726 33.7005V95.6374C103.726 99.2938 100.765 102.285 97.147 102.285Z" fill="url(#paint6_linear_201_410)" stroke="url(#paint7_linear_201_410)" stroke-miterlimit="10"/>
<path d="M103.726 32.925V94.8618C103.726 98.5182 100.765 101.51 97.147 101.51H35.8531C32.2347 101.51 29.2742 98.5182 29.2742 94.8618V32.925C29.2742 29.2686 32.2347 26.277 35.8531 26.277H97.147C100.765 26.277 103.726 29.2686 103.726 32.925Z" fill="url(#paint8_linear_201_410)" stroke="url(#paint9_linear_201_410)" stroke-miterlimit="10"/>
<path d="M41.2259 36.3043V42.2321H39.8553L37.2785 39.0743V42.2321H35.6338V36.3043H37.0044L39.5812 39.4621V36.3043H41.2259Z" fill="#313F3F"/>
<path d="M47.0921 40.9025V42.1767H42.3772V36.3043H46.9825V37.5785H44.0219V38.5757H46.5987V39.8499H44.0219V40.9025H47.0921V40.9025Z" fill="#313F3F"/>
<path d="M57.0701 36.3043L55.1513 42.2321H53.3969L52.3004 38.6311L51.1491 42.2321H49.3947L47.4758 36.3043H49.1754L50.3267 40.0715L51.5877 36.3043H53.1228L54.2741 40.1269L55.4802 36.3043H57.0701Z" fill="#313F3F"/>
<path d="M57.2346 41.6781L57.7829 40.4593C58.2763 40.7917 59.0438 41.0687 59.7017 41.0687C60.3596 41.0687 60.6337 40.8471 60.6337 40.5701C60.6337 39.6283 57.2894 40.2931 57.2894 38.1325C57.2894 37.0799 58.1666 36.1935 59.8662 36.1935C60.6337 36.1935 61.4013 36.3597 62.0044 36.6921L61.5109 37.9109C60.9627 37.6339 60.4144 37.4677 59.8662 37.4677C59.1535 37.4677 58.9342 37.6893 58.9342 38.0217C58.9342 38.9081 62.2237 38.2433 62.2237 40.4039C62.2237 41.4565 61.3465 42.2875 59.6469 42.2875C58.7697 42.2875 57.7829 42.0659 57.2346 41.6781Z" fill="#313F3F"/>
<path d="M63.0461 36.3043H64.6909V40.9025H67.4869V42.2321H63.0461V36.3043V36.3043Z" fill="#313F3F"/>
<path d="M72.8597 40.9025V42.1767H68.1448V36.3043H72.7501V37.5785H69.7895V38.5757H72.3663V39.8499H69.7895V40.9025H72.8597V40.9025Z" fill="#313F3F"/>
<path d="M74.9979 37.5785H73.1887V36.2489H78.3971V37.5785H76.5879V42.1767H74.9431V37.5785H74.9979Z" fill="#313F3F"/>
<path d="M80.3707 37.5785H78.5615V36.2489H83.7699V37.5785H81.9606V42.1767H80.3159V37.5785H80.3707Z" fill="#313F3F"/>
<path d="M89.1427 40.9025V42.1767H84.4277V36.3043H89.033V37.5785H86.0725V38.5757H88.6492V39.8499H86.0725V40.9025H89.1427V40.9025Z" fill="#313F3F"/>
<path d="M92.5964 40.6255H91.7192V42.1767H90.0745V36.3043H92.7609C94.3508 36.3043 95.3376 37.1353 95.3376 38.4649C95.3376 39.3513 94.899 39.9607 94.1863 40.3485L95.4473 42.2321H93.6929L92.5964 40.6255ZM92.5964 37.5785H91.6644V39.2959H92.5964C93.2543 39.2959 93.6381 38.9635 93.6381 38.4095C93.6381 37.9109 93.3091 37.5785 92.5964 37.5785Z" fill="#313F3F"/>
<path d="M65.897 49.1017H36.2917V73.3667H65.897V49.1017Z" fill="#B0C6C6"/>
<path d="M95.0637 49.1017H68.9124V53.8106H95.0637V49.1017Z" fill="#B0C6C6"/>
<path d="M85.7983 55.3618H68.9124V60.0708H85.7983V55.3618Z" fill="#B0C6C6"/>
<path d="M95.0637 63.7826H68.9124V66.1648H95.0637V63.7826Z" fill="#B0C6C6"/>
<path d="M95.0637 67.439H68.9124V69.8212H95.0637V67.439Z" fill="#B0C6C6"/>
<path d="M87.772 71.1508H68.9124V73.5329H87.772V71.1508Z" fill="#B0C6C6"/>
<path d="M54.4935 76.4691H36.2917V78.8513H54.4935V76.4691Z" fill="#B0C6C6"/>
<path d="M54.4935 80.1809H36.2917V82.5631H54.4935V80.1809Z" fill="#B0C6C6"/>
<path d="M54.4935 83.8927H36.2917V86.2749H54.4935V83.8927Z" fill="#B0C6C6"/>
<path d="M54.4935 87.5491H36.2917V89.9313H54.4935V87.5491Z" fill="#B0C6C6"/>
<path d="M54.4935 91.2609H36.2917V93.6431H54.4935V91.2609Z" fill="#B0C6C6"/>
<path d="M74.8333 76.4691H56.6316V78.8513H74.8333V76.4691Z" fill="#B0C6C6"/>
<path d="M74.8333 80.1809H56.6316V82.5631H74.8333V80.1809Z" fill="#B0C6C6"/>
<path d="M74.8333 83.8927H56.6316V86.2749H74.8333V83.8927Z" fill="#B0C6C6"/>
<path d="M74.8333 87.5491H56.6316V89.9313H74.8333V87.5491Z" fill="#B0C6C6"/>
<path d="M74.8333 91.2609H56.6316V93.6431H74.8333V91.2609Z" fill="#B0C6C6"/>
<path d="M95.1185 76.4691H76.9167V78.8513H95.1185V76.4691Z" fill="#B0C6C6"/>
<path d="M95.1185 80.1809H76.9167V82.5631H95.1185V80.1809Z" fill="#B0C6C6"/>
<path d="M95.1185 83.8927H76.9167V86.2749H95.1185V83.8927Z" fill="#B0C6C6"/>
<path d="M95.1185 87.5491H76.9167V89.9313H95.1185V87.5491Z" fill="#B0C6C6"/>
<path d="M95.1185 91.2609H76.9167V93.6431H95.1185V91.2609Z" fill="#B0C6C6"/>
</g>
<defs>
<filter id="filter0_d_201_410" x="20.7742" y="17.777" width="107.452" height="111.446" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="8" dy="8"/>
<feGaussianBlur stdDeviation="8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_201_410"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_201_410" result="shape"/>
</filter>
<linearGradient id="paint0_linear_201_410" x1="66.4981" y1="104.728" x2="66.4981" y2="29.4993" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint1_linear_201_410" x1="66.4981" y1="29.2223" x2="66.4981" y2="105.005" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint2_linear_201_410" x1="66.4981" y1="103.917" x2="66.4981" y2="28.6879" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint3_linear_201_410" x1="66.4981" y1="28.4109" x2="66.4981" y2="104.194" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint4_linear_201_410" x1="66.4981" y1="103.118" x2="66.4981" y2="27.8889" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint5_linear_201_410" x1="66.4981" y1="27.6119" x2="66.4981" y2="103.395" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint6_linear_201_410" x1="66.4981" y1="102.279" x2="66.4981" y2="27.0501" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#BFD9D9"/>
</linearGradient>
<linearGradient id="paint7_linear_201_410" x1="66.4981" y1="26.7731" x2="66.4981" y2="102.556" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D7DBDB"/>
</linearGradient>
<linearGradient id="paint8_linear_201_410" x1="66.4981" y1="101.506" x2="66.4981" y2="26.277" gradientUnits="userSpaceOnUse">
<stop offset="0.5161" stop-color="white"/>
<stop offset="1" stop-color="#C1D6D6"/>
</linearGradient>
<linearGradient id="paint9_linear_201_410" x1="66.4981" y1="26" x2="66.4981" y2="101.783" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#E6EBEB"/>
</linearGradient>
</defs>
</svg>
