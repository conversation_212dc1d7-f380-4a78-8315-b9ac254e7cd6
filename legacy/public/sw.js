// Version control - change this when deploying new versions
const CACHE_VERSION = 'v1.0.1'
const CACHE_NAME = `indicentral-${CACHE_VERSION}`

// Set to true to force update for all users regardless of cache version
const FORCE_UPDATE = true

// Assets to cache - add critical assets here
const ASSETS_TO_CACHE = [
  '/',
  '/index.html',
  '/dashboard',
  '/offline.html', // Create this page for offline fallback
  '/images/indi-central-logo.svg',
  '/images/indi-symbol.svg'
]

// Install event - cache key assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('Service Worker: Caching files')
      return cache.addAll(ASSETS_TO_CACHE)
    })
  )

  // Force this service worker to become active right away
  self.skipWaiting()
})

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Delete any old caches
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: Clearing old cache', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )

  // Ensure the updated service worker takes control immediately
  return self.clients.claim()
})

// Fetch event - network first with cache fallback strategy
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return
  }

  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return
  }

  // For HTML and JS files, always try network first
  // This ensures users get the latest version of your app
  if (
    event.request.url.match(/\.(html|js)$/) ||
    event.request.url.includes('/api/') ||
    event.request.mode === 'navigate'
  ) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Cache the fresh response
          const responseClone = response.clone()
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseClone)
          })
          return response
        })
        .catch(() => {
          // Fallback to cache
          return caches.match(event.request).then((cachedResponse) => {
            if (cachedResponse) {
              return cachedResponse
            }
            // Fallback to offline page if nothing is in cache
            return caches.match('/offline.html')
          })
        })
    )
  } else {
    // For other assets, try cache first, then network
    event.respondWith(
      caches.match(event.request).then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse
        }
        return fetch(event.request).then((response) => {
          // Cache the response for future
          const responseClone = response.clone()
          caches.open(CACHE_NAME).then((cache) => {
            cache.put(event.request, responseClone)
          })
          return response
        })
      })
    )
  }
})

// Handle messages from clients
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})
