<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Indi Central - Offline</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
        background-color: #f5f5f5;
      }
      .logo {
        margin-bottom: 30px;
        max-width: 200px;
      }
      .container {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        max-width: 500px;
      }
      h1 {
        color: #333;
        margin-top: 0;
      }
      p {
        color: #666;
        line-height: 1.5;
      }
      .btn {
        background-color: #0070f3;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 20px;
      }
      .btn:hover {
        background-color: #0051b3;
      }
    </style>
  </head>
  <body>
    <img src="/images/indi-central-logo.svg" alt="Indi Central Logo" class="logo" />
    <div class="container">
      <h1>You're currently offline</h1>
      <p>It seems you're not connected to the internet or our servers are temporarily unavailable.</p>
      <p>Please check your connection and try again.</p>
      <button class="btn" onclick="window.location.reload()">Try Again</button>
    </div>

    <script>
      // Check for new version when online again
      window.addEventListener('online', () => {
        window.location.reload()
      })
    </script>
  </body>
</html>
