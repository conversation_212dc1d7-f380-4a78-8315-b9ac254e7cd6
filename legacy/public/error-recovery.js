// Error recovery script for Indi Central
;(function () {
  // Check if we're already injected
  if (window.indiCentralErrorRecoveryInjected) return
  window.indiCentralErrorRecoveryInjected = true

  console.log('Indi Central Error Recovery script loaded')

  // Function to create error recovery UI
  function createErrorRecoveryUI() {
    // Create error recovery container
    const errorContainer = document.createElement('div')
    errorContainer.id = 'indi-error-recovery-container'
    errorContainer.style.position = 'fixed'
    errorContainer.style.top = '0'
    errorContainer.style.left = '0'
    errorContainer.style.width = '100%'
    errorContainer.style.height = '100%'
    errorContainer.style.backgroundColor = '#f5f5f5'
    errorContainer.style.zIndex = '9999'
    errorContainer.style.display = 'flex'
    errorContainer.style.flexDirection = 'column'
    errorContainer.style.alignItems = 'center'
    errorContainer.style.justifyContent = 'center'
    errorContainer.style.padding = '20px'
    errorContainer.style.textAlign = 'center'
    errorContainer.style.fontFamily = 'Arial, sans-serif'

    // Create logo
    const logo = document.createElement('img')
    logo.src = '/images/indi-central-logo.svg'
    logo.alt = 'Indi Central Logo'
    logo.style.marginBottom = '30px'
    logo.style.maxWidth = '200px'

    // Create content container
    const contentContainer = document.createElement('div')
    contentContainer.style.backgroundColor = 'white'
    contentContainer.style.padding = '30px'
    contentContainer.style.borderRadius = '10px'
    contentContainer.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)'
    contentContainer.style.maxWidth = '500px'

    // Create heading
    const heading = document.createElement('h1')
    heading.textContent = 'Application Error Recovery'
    heading.style.color = '#333'
    heading.style.marginTop = '0'

    // Create description
    const description = document.createElement('p')
    description.textContent =
      "We've detected an error with the application that might be caused by outdated cached files."
    description.style.color = '#666'
    description.style.lineHeight = '1.5'

    const instruction = document.createElement('p')
    instruction.textContent = 'Please try these solutions:'
    instruction.style.color = '#666'
    instruction.style.lineHeight = '1.5'

    // Create buttons container
    const buttonsContainer = document.createElement('div')
    buttonsContainer.style.marginTop = '20px'
    buttonsContainer.style.display = 'flex'
    buttonsContainer.style.flexDirection = 'column'
    buttonsContainer.style.gap = '15px'

    // Create reload button
    const reloadButton = document.createElement('button')
    reloadButton.textContent = 'Reload Application'
    reloadButton.style.backgroundColor = '#0070f3'
    reloadButton.style.color = 'white'
    reloadButton.style.border = 'none'
    reloadButton.style.padding = '10px 20px'
    reloadButton.style.borderRadius = '5px'
    reloadButton.style.cursor = 'pointer'
    reloadButton.style.fontSize = '16px'
    reloadButton.onclick = function () {
      const timestamp = Date.now()
      window.location.href = `${window.location.pathname}?reload=${timestamp}`
    }

    // Create clear data button
    const clearButton = document.createElement('button')
    clearButton.textContent = 'Clear Site Data & Reload'
    clearButton.style.backgroundColor = '#ff4b4b'
    clearButton.style.color = 'white'
    clearButton.style.border = 'none'
    clearButton.style.padding = '10px 20px'
    clearButton.style.borderRadius = '5px'
    clearButton.style.cursor = 'pointer'
    clearButton.style.fontSize = '16px'
    clearButton.onclick = function () {
      if ('caches' in window) {
        caches.keys().then(function (keyList) {
          return Promise.all(
            keyList.map(function (key) {
              return caches.delete(key)
            })
          )
        })
      }
      localStorage.clear()
      sessionStorage.clear()
      setTimeout(function () {
        const timestamp = Date.now()
        window.location.href = `${window.location.pathname}?reload=${timestamp}`
      }, 500)
    }

    // Create note
    const note = document.createElement('p')
    note.textContent = 'If the problem persists, please try opening the site in an incognito window or contact support.'
    note.style.fontSize = '14px'
    note.style.color = '#888'
    note.style.marginTop = '15px'

    // Assemble the UI
    buttonsContainer.appendChild(reloadButton)
    buttonsContainer.appendChild(clearButton)
    buttonsContainer.appendChild(note)

    contentContainer.appendChild(heading)
    contentContainer.appendChild(description)
    contentContainer.appendChild(instruction)
    contentContainer.appendChild(buttonsContainer)

    errorContainer.appendChild(logo)
    errorContainer.appendChild(contentContainer)

    return errorContainer
  }

  // Function to check if the page contains an error message
  function checkForErrorMessage() {
    // Look for typical error messages
    const body = document.body
    const text = body.textContent || ''

    const errorKeywords = [
      'Application error',
      'client-side exception',
      'cannot destructure property',
      'TypeError',
      'is undefined'
    ]

    for (const keyword of errorKeywords) {
      if (text.includes(keyword)) {
        console.log(`Detected error keyword: ${keyword}`)
        return true
      }
    }

    // Also check for minimal page content which might indicate an error
    if (body.children.length < 3 && text.trim().length < 200) {
      const isErrorPage = document.title.includes('Error') || text.includes('error') || text.includes('exception')

      if (isErrorPage) {
        console.log('Detected minimal error page content')
        return true
      }
    }

    return false
  }

  // Inject the UI if error is detected
  function injectErrorRecoveryIfNeeded() {
    if (checkForErrorMessage()) {
      console.log('Injecting error recovery UI')
      // Remove existing content
      document.body.innerHTML = ''
      // Add our error recovery UI
      document.body.appendChild(createErrorRecoveryUI())
    }
  }

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', injectErrorRecoveryIfNeeded)
  } else {
    injectErrorRecoveryIfNeeded()
  }

  // Also try after a short delay in case the error occurs after initial load
  setTimeout(injectErrorRecoveryIfNeeded, 2000)
})()
