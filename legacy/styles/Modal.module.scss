@import 'theme';
@import 'mixins';

.modalOverlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.85);
  display: none;
  justify-content: center;
  align-items: flex-start;
  z-index: 1000;
  appearance: none;
  border: none;
}

.modal {
  width: 48rem;
  display: none;
  flex-direction: column;
  padding: 1.6rem;
  border-radius: 0.8rem;
  background: #fff;
  margin: 6.4rem auto;
  position: fixed;
  left: calc(50% - 20rem);
  top: 4rem;
  z-index: 2000;

  header {
    width: 100%;
    display: block;
    border-bottom: 0.1rem solid $mid-gray;
    padding: 0.8rem;
    min-height: 3.4rem;

    h2 {
      font-size: 2.4rem;
      color: #000;
      margin: 0;
      font-family: $display-font;
    }
  }

  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 1.6rem 0.8rem;

    .photo {
      width: 6rem;
      height: 6rem;
      border-radius: 3rem;
      display: block;
      overflow: hidden;
      border: 0.2rem solid $mid-gray;
      position: relative;
      background-position: center center;
      background-size: cover;

      img {
        width: 100%;
        max-width: 100%;
        height: 100%;
      }
    }

    .contentBody {
      width: calc(100% - 7.6rem);
      font-size: 1.6rem;
      font-weight: 400;
      h2,
      h3 {
        font-family: $body-font;
      }
      h2 {
        font-size: 1.8rem;
        color: #000;
        margin: 0 0 0.8rem 0;
      }
      h3 {
        font-size: 1.6rem;
        color: $highlight-color;
        margin: 0 0 0.8rem 0;
      }
      p {
        font-size: 1.6rem;
        font-family: $body-font;
      }
    }
  }

  footer {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.6rem 0.8rem 0 0.8rem;
    border-top: 0.1rem solid $mid-gray;
    gap: 1.6rem;
  }
}

.modalOpened {
  display: flex !important;
}

.isVisible {
  display: flex !important;
}
