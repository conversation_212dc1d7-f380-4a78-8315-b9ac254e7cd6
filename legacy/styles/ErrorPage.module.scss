.errorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 2rem;
}

.errorBox {
  max-width: 600px;
  text-align: center;
  padding: 3rem;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.errorIcon {
  margin-bottom: 1.5rem;
  color: #ff5757;
}

.errorCode {
  font-size: 5rem;
  font-weight: 700;
  margin: 0;
  line-height: 1;
  color: #333;
}

.errorTitle {
  font-size: 1.8rem;
  margin: 0.5rem 0 1.5rem;
  color: #333;
}

.errorDescription {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1.5rem;
}

.errorDetails {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: left;

  p {
    margin: 0;
    color: #555;
    font-size: 0.95rem;
  }
}

.actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.75rem;
  }
}
