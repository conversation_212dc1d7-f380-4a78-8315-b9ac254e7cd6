@import 'theme';
@import 'mixins';

.carousel {
  width: 100%;
  position: relative;
  margin: 3.2rem 0;
  overflow: hidden;
  padding: 3.2rem 3.2rem 4.8rem 3.2rem;

  @include responsive(mobile) {
    padding: 2.4rem 2.4rem 3.6rem 2.4rem;
  }
}

.carouselContainer {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.carouselTrack {
  display: flex;
  width: 100%;
  transition: transform 0.5s ease-in-out;
}

.carouselSlide {
  flex: 0 0 100%;
  width: 100%;
  padding: 0 1.6rem;
  box-sizing: border-box;

  @include responsive(desktop) {
    flex: 0 0 33.333%;
    width: 33.333%;
  }

  @include responsive(tablet) {
    flex: 0 0 50%;
    width: 50%;
  }

  @include responsive(mobile) {
    flex: 0 0 100%;
    width: 100%;
  }
}

.carouselCard {
  background: $bright-color;
  border-radius: 0.8rem;
  border: thin solid $mid-gray;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-0.4rem);
    box-shadow: $shadow-large;
  }
}

.cardImage {
  width: 100%;
  overflow: hidden;

  img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
  }
}

.cardContent {
  padding: 2.4rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.cardTitle {
  font-family: $display-font;
  font-size: 2.1rem;
  line-height: 2.8rem;
  color: $base-color;
  margin: 0 0 1.6rem 0;
  font-weight: 600;
}

.cardDescription {
  flex-grow: 1;
  margin-bottom: 2.4rem;

  p {
    font-family: $body-font;
    font-size: 1.6rem;
    line-height: 2.4rem;
    color: $base-color;
    margin: 0 0 1.6rem 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: $display-font;
    color: $base-color;
    margin: 1.6rem 0 0.8rem 0;

    &:first-child {
      margin-top: 0;
    }
  }

  ul,
  ol {
    font-family: $body-font;
    font-size: 1.6rem;
    line-height: 2.4rem;
    color: $base-color;
    margin: 0 0 1.6rem 0;
    padding-left: 2.4rem;
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  a {
    color: $highlight-color;
    text-decoration: underline;

    &:hover {
      color: $highlight-dark;
    }
  }
}

.cardButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1.2rem 2.4rem;
  background: $highlight-color;
  color: $bright-color !important;
  text-decoration: none;
  border-radius: 0.4rem;
  font-family: $display-font;
  font-size: 1.4rem;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: flex-start;
  text-decoration: none !important;

  &:hover {
    background: $highlight-dark;
    transform: translateY(-0.2rem);
  }

  &:active {
    transform: translateY(0);
  }
}

.navButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 4.8rem;
  height: 4.8rem;
  background-color: $bright-color;
  border: thin solid $mid-gray;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $base-color;
  transition: all 0.3s ease;

  &:hover {
    background-color: $highlight-color;
    color: $bright-color;
    transform: translateY(-50%) scale(1.1);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  svg {
    width: 2.4rem;
    height: 2.4rem;
  }

  @include responsive(mobile) {
    width: 4rem;
    height: 4rem;

    svg {
      width: 2rem;
      height: 2rem;
    }
  }
}

.prevButton {
  left: -2.4rem;

  @include responsive(mobile) {
    left: -2rem;
  }
}

.nextButton {
  right: -2.4rem;

  @include responsive(mobile) {
    right: -2rem;
  }
}
