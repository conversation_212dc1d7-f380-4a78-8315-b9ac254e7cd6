@import 'theme';
@import 'mixins';

.photoUploadField {
  width: 100%;
  display: flex;
}

.photo {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.6rem;

  svg {
    margin: 3rem auto;
    fill: $mid-gray;
  }

  img {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 0.8rem 0.8rem 0 0;
    background: $light-gray;
    display: block;
  }

  .photoLoading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    display: table;
    text-align: center;
    background: white;
    border-bottom: 0.1rem solid $light-gray;

    img {
      width: 80%;
      max-width: 80%;
      height: auto;
    }

    p {
      font-family: $display-font;
      font-size: 1.6rem;
      color: $highlight-color;
      margin: 1.6rem 0;
      width: 100%;
      display: block;
      text-align: center;
    }
  }
}

.photoForm {
  input {
    width: 100%;
    height: 4rem;
  }

  button {
    width: 100%;
    background: $highlight-color;
    color: #fff;
    font-family: $display-font;
    font-weight: 600;
    border: none;
    height: 3.6rem;
    border-radius: 0.4rem;
    padding: 0.8rem 2.4rem;
    clear: both;
  }

  small {
    width: 100%;
    display: block;
    margin-top: 1.6rem;
  }
}

.ax_image_options {
  font-family: $display-font;
  font-size: 1.4rem;
  padding: 0.8rem;

  p {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 1.6rem;
    color: $highlight-color;
  }

  input {
    width: 100%;
    height: 3.4rem;
    line-height: 3.4rem;
    margin: 1.6rem 0;
    position: relative;
    border-radius: 0.2rem;
    border: 0.4rem dashed $mid-gray;
    background-color: $light-gray;
  }

  label {
    width: 100%;
    display: block;
    font-size: 1.4rem;
    line-height: 1.4rem;
    color: $base-color;
    font-weight: 600;
  }

  button {
    height: 4rem;
    border-radius: 0.4rem;
    background: $highlight-color;
    color: $bright-color;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 600;
    border: none;
    padding: 0 2.4rem;
    width: 100%;
    margin-top: 0.8rem;
    > img,
    svg {
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.cropperModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.cropperContent {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  position: relative;
  max-width: 90%;
  max-height: 90%;
  overflow: auto;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 24px;
  color: #333;
}
