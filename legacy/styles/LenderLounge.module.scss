@import 'theme';
@import 'mixins';

.logo {
  width: 100%;
  max-width: 100%;
  height: auto;
  border: 0.2rem solid $light-gray;
  border-radius: 0.4rem;
}

.lendersTable {
  width: 100%;
  cursor: grab;

  table {
    display: table;
    font-family: $display-font;
    background: $bright-color;
    padding: 3.2rem;
    border-radius: 0.8rem;

    thead,
    tbody {
      width: 100%;

      tr {
        th {
          font-size: 1.2rem;
          border-bottom: 0.2rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0 0.8rem;
          text-align: left;
          color: $highlight-dark;
        }

        td {
          font-size: 1.2rem;
          border-bottom: 0.1rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0.4rem 0.8rem;
          transition: all 0.2s ease-out;

          &:nth-child(1) {
            min-width: 10rem;
            font-weight: 700;
          }

          &:nth-child(2),
          &:nth-child(4),
          &:nth-child(5),
          &:nth-child(6),
          &:nth-child(7),
          &:nth-child(8),
          &:nth-child(9),
          &:nth-child(11),
          &:nth-child(12) {
            min-width: 12rem;
          }

          &:nth-child(3),
          &:nth-child(15) {
            min-width: 20rem;
          }
        }

        &:hover {
          td {
            background-color: $light-gray;
          }
        }
      }
    }
  }
}

.ax_post_title {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-top: 1.6rem;
}

.ax_post_info {
  width: 100%;
  display: table;

  .ax_post_details {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    font-family: $display-font;
    background: $bright-color;
    padding: 0;
    border-radius: 0.8rem;

    .logo {
      width: 10rem;
      max-width: 10rem;
      height: 10rem;
      display: block;
    }

    p {
      width: 100%;
      font-size: 1.4rem;
      line-height: 1.8rem;
      display: block;
      transition: all 0.2s ease-out;
      padding: 0.4rem;
      border-radius: 0.3rem;
      font-family: $body-font;
      word-wrap: break-word;
      margin: 0 0 0.6rem 0;

      &:hover {
        background-color: $light-gray;
      }

      strong {
        color: $base-color;
      }
    }
  }
}

.ax_toggle_view {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  height: 4rem;

  button {
    width: auto;
    border: 0.1rem solid $mid-gray;
    appearance: none;
    background: transparent;
    color: $base-color;
    margin: 0 1.2rem 0 0;
    border-radius: 0.3rem;
    cursor: pointer;
    padding: 0.4rem 1rem;
    background: #fff;

    svg {
      width: 1.6rem;
      height: 1.6rem;
      display: inline-block;
      vertical-align: top;
      margin-right: 0.4rem;
    }
  }

  button.active {
    background: $highlight-color;
    color: $bright-color;
  }
}

.ax_card_view {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

@include responsive(desktop) {
  .ax_add_broker_form {
    width: 100rem;
  }
}

@include responsive(tablet) {
  .ax_add_broker_form {
    width: 70rem;

    .ax_field {
      width: 100%;
    }
  }
}

@include responsive(mobile) {
  .ax_add_broker_form {
    width: 100%;

    .ax_field {
      width: 100%;
    }
  }
}
