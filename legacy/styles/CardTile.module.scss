@import 'theme';
@import 'mixins';

.cardTile {
  width: 100%;
  padding: 0;
  display: block;
  text-align: center;
  transition: all 0.2s ease-out;
  position: relative;
  z-index: 50;

  // &::before {
  //   content: '';
  //   width: 75%;
  //   height: 65%;
  //   border-left: 0.1rem solid rgba(255, 255, 255, 0.75);
  //   border-top: 0.1rem solid rgba(248, 255, 255, 0.75);
  //   border-top-left-radius: 2.4rem;
  //   filter: blur(1px);
  //   position: absolute;
  //   left: 0.2rem;
  //   top: 0.2rem;
  //   z-index: 100;
  //   mix-blend-mode: screen;
  // }

  .cardBody {
    width: 100%;
    display: block;
    text-align: center;
    border-radius: 2.4rem;
    position: relative;

    &:before {
      content: '';
      width: 50%;
      height: 50%;
      display: block;
      background: #000;
      position: absolute;
      top: 33%;
      left: 33%;
      z-index: 10;
      pointer-events: none;
      border-radius: 50%;
      mix-blend-mode: overlay;
      opacity: 0.35;
      // box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.35);
      filter: blur(12px);
    }

    // &:hover {
    //   &:before {
    //     opacity: 1;
    //     mix-blend-mode: color-burn;
    //   }
    // }

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      padding: 32px;
      position: relative;
      z-index: 20;
    }
  }

  h3 {
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 600;
    color: $base-color;
  }

  &:hover {
    transform: scale(1.05);
    cursor: pointer;
  }
}

@include responsive(desktop) {
  .cardTile {
    width: 100%;

    .cardBody {
      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        padding: 16px;
        position: relative;
        z-index: 20;
      }
    }
  }
}

@include responsive(tablet) {
  .cardTile {
    width: 100%;

    &::before {
      height: 45%;
    }

    .cardBody {
      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        padding: 24px;
        position: relative;
        z-index: 20;
      }
    }
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .cardTile {
    width: 100%;

    &::before {
      height: 45%;
    }

    .cardBody {
      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        padding: 1rem;
        position: relative;
        z-index: 20;
      }
    }
  }
}

@media screen and (min-width: 1280px) and (max-width: 1540px) {
  .cardTile {
    h3 {
      font-size: 1rem !important;
    }
    .cardBody {
      border-radius: 1.2rem;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        padding: 0.7rem;
        position: relative;
        z-index: 20;
      }
    }
  }
}

@include responsive(mobile) {
  .cardTile {
    width: calc(33% - 3.2rem) !important;
    margin: 0 1.6rem 2.4rem;

    &::before {
      height: 45%;
    }

    .cardBody {
      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        padding: 1rem;
        position: relative;
        z-index: 20;
      }
    }
  }
}

.gradientGreen {
  background: $gradient-green;
  // background: #b4e690;
}
.gradientPurple {
  background: $gradient-purple;
  // background: #b188d7;
}
.gradientRed {
  background: $gradient-red;
  // background: #f96b6b;
}
.gradientTeal {
  background: $gradient-teal;
  // background: rgb(130, 206, 206);
}
.gradientBlue {
  background: $gradient-blue;
  // background: rgb(105, 153, 216);
}
.gradientBlueDark {
  background: $gradient-blue-dark;
  // background: rgb(112, 112, 195);
}
.gradientBronze {
  background: $gradient-bronze;
  // background: rgb(247, 213, 154);
}
.gradientLilac {
  background: $gradient-lilac;
  // background: rgb(194, 148, 193);
}
.gradientPink {
  background: $gradient-pink;
  // background: rgb(235, 152, 194);
}
.gradientYellow {
  background: $gradient-yellow;
  // background: rgb(246, 227, 141);
}
.gradientDark {
  background: $gradient-dark;
  // background: rgb(104, 104, 123);
}
.gradientGrey {
  background: $gradient-grey;
  // background: #fff;
}

.transparent {
  background: transparent !important;
  box-shadow: 0 0 0 transparent !important;
  box-shadow: 0 0 0 transparent, 0 0 0 transparent !important;
}

.cardShadow {
  .ax_card_icon {
    transform: translateX(6%);
  }
}
