@import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@300..700&display=swap');
@import '../node_modules/react-grid-layout/css/styles.css';
@import '../node_modules/react-resizable/css/styles.css';

@font-face {
  font-family: 'herobold';
  src: url('/fonts/hero-bold-webfont.woff2') format('woff2'), url('/fonts/hero-bold-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'herolight';
  src: url('/fonts/hero-light-webfont.woff2') format('woff2'), url('/fonts/hero-light-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'heroregular';
  src: url('/fonts/hero-regular-webfont.woff2') format('woff2'), url('/fonts/hero-regular-webfont.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

html,
body {
  padding: 0;
  margin: 0;
  background: #f2f5f9;
}

html {
  font-size: 10px;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

#nprogress {
  width: 100%;
  height: 0.8rem;
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
}

.rbc-calendar {
  font-family: 'Montserrat', sans-serif !important;
  background: #fff !important;
  padding: 1.6rem;
  border-radius: 0.8rem;
}

.rbc-event {
  border-radius: 0.2rem !important;
  border: none;
  font-size: 1.4rem;
  font-weight: 500;
}

.rbc-event-content {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 12px;
  font-weight: 600;
}

.rbc-month-view .rbc-row {
  height: 4rem;
}

.rbc-month-view .rbc-row .rbc-header {
  background-color: #000;
}

.rbc-month-view .rbc-row .rbc-header span {
  line-height: 4rem;
  color: #fff;
}

.rbc-agenda-table td {
  font-size: 1.4rem;
}

.rbc-agenda-table tr:hover td {
  background: #f7faff;
  color: #00a0a5;
  cursor: pointer;
}

.rbc-date-cell button {
  font-size: 1.7rem;
}

#react-doc-viewer #header-bar #file-name {
  font-family: 'heroregular', 'Quicksand', sans-serif !important;
}
