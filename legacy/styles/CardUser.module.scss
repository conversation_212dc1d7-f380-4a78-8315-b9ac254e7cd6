@import 'theme';
@import 'mixins';

.cardUser {
  width: calc(66% - 3.2rem);
  padding: 1.6rem;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  text-align: center;
  background: $bright-color;
  border-radius: 0.8rem;

  .photo {
    width: 7rem;
    height: 7rem;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 1.6rem 1.6rem 0;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top center;
    background-color: $mid-gray;
  }

  .cardBody {
    width: calc(100% - 11.6rem);
    max-width: calc(100% - 11.6rem);
    display: block;
    text-align: left;
    margin-top: 0.8rem;

    .editButton {
      color: $highlight-dark;
      font-family: $display-font;
      font-size: 1.4rem;
      text-decoration: none;
      height: 3.8rem;
      line-height: 3.8rem;
      font-weight: 800;

      svg {
        transform: translateY(2px);
      }
    }

    h3 {
      font-family: $display-font;
      font-size: 1.6rem;
      font-weight: 600;
      color: $base-color;
      margin: 0 !important;
    }

    h4 {
      font-family: $display-font;
      font-size: 1.3rem;
      line-height: 1.8rem;
      font-weight: 400;
      color: $highlight-color;
      margin: 0 0 0.8rem 0;
    }

    p {
      font-family: $display-font;
      font-size: 1.2rem;
      line-height: 1.8rem;
      font-weight: 400;
      color: $base-color;
      margin: 0;
      word-break: break-all;

      strong {
        font-weight: 600;
        color: $highlight-color;
      }
    }
  }
}

@media screen and (min-width: 320px) {
  .cardUser {
    width: calc(100% - 1.6rem);
  }
}

@media screen and (min-width: 768px) {
  .cardUser {
    width: calc(50% - 1.6rem);
  }
}

@media screen and (min-width: 1440px) {
  .cardUser {
    width: calc(33% - 1.6rem);
  }
}

@media screen and (min-width: 1640px) {
  .cardUser {
    width: calc(33% - 1.6rem);
  }
}

.cardWide {
  width: 100% !important;
}

.shadow {
  box-shadow: 6px 6px 10px rgba(0, 0, 0, 0.2);
}
