@import 'theme';
@import 'mixins';

.dragzone {
  width: 100%;
  min-height: 10rem;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: 1.6rem;
  padding: 1.6rem;
  border-radius: 0.8rem;
  background: white;
  border: 0.1rem solid $mid-gray;
  transition: all 0.2s ease-out;
  cursor: grab;
  position: relative;
  z-index: 1;

  span {
    width: 100%;
    line-height: 4.8rem;
    font-family: $display-font;
    font-weight: 700;
    pointer-events: none;
    position: absolute;
    top: 28%;
    text-align: center;
    z-index: 1;
    transition: all 0.2s ease-out;

    p {
      color: $dark-gray;
    }

    svg {
      width: 4.8rem;
      height: 4.8rem;
      color: $dark-gray;
    }
  }

  .dragarea {
    width: 100%;
    min-height: 20rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: $light-gray;
    border-radius: 0.8rem;
    border: 0.2rem dashed $mid-gray;
    z-index: 10;
    transition: all 0.2s ease-out;
    cursor: grab;

    &:hover {
      background: $dark-gray;
      cursor: grab;

      span {
        p {
          color: white;
        }
        svg {
          width: 4.8rem;
          height: 4.8rem;
          color: white;
        }
      }
    }
  }

  .previewOn {
    border: none !important;
    background: transparent;
  }

  .filePreview {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1.6rem;
    position: relative;
    z-index: 10;
    transition: all 0.2s ease-out;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
    }

    p {
      color: #000;
      padding: 1.6rem;
      text-align: center;
    }
    svg {
      width: 4.8rem;
      height: 4.8rem;
      color: #000;
    }
  }

  .footer {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 1.6rem;
  }

  @include responsive(mobile) {
    .footer {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1.6rem;

      button {
        width: 100%;
      }
    }
  }

  @include responsive(tablet) {
    .footer {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 1.6rem;

      button {
        width: 100%;
      }
    }
  }
}
