/* Filter.module.scss */

@import 'theme';
@import 'mixins';

.searchingIndicator,
.loadingIndicator {
  display: inline-flex;
  align-items: center;
  margin-left: 1.6rem;

  svg {
    animation: spin 1s linear infinite;
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.filterContainer {
  width: 100%;
  border-radius: 0.8rem;
  background: $bright-color;
  margin-bottom: 3.2rem;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-items: center;
  padding: 1.6rem;

  @include responsive(desktop) {
    > div {
      width: auto;
    }
  }

  @include responsive(mobile) {
    > div {
      width: 100%;
    }
  }

  .filterRow {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: flex-start;

    .active {
      background-color: $highlight-color;
      color: $bright-color;
    }

    p {
      font-size: 1.6rem;
      font-family: $display-font;
      line-height: 2rem;
      vertical-align: top;

      svg {
        fill: $gray;
        margin-right: 0.8rem;
        transform: translateY(0.5rem);
      }
    }
  }
}

.filter {
  width: 100%;
  padding: 1.6rem;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: nowrap;
  align-items: center;

  select {
    width: auto;
    height: 4rem;
    border-radius: 0.4rem;
    border: 0.1rem solid $mid-gray;
    background: $light-gray;
    padding: 0 4rem 0 1.6rem;
    color: $base-color;
    font-family: $display-font;
    font-size: 1.4rem;
    display: inline-block;
    margin-left: 0.8rem;
  }

  label {
    display: inline-block;
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 600;
    margin-left: 3.2rem;

    &:first-child {
      margin-left: 0;
    }

    input[type='text'] {
      width: auto;
      height: 4rem;
      border-radius: 0.4rem;
      border: 0.1rem solid $mid-gray;
      background: $light-gray;
      padding: 0 4rem 0 1.6rem;
      color: $base-color;
      font-family: $display-font;
      font-size: 1.4rem;
      display: inline-block;
      margin-left: 0.8rem;
    }
  }

  select,
  input[type='text'] {
    min-width: 20rem;
    transition: all 0.2s ease-in-out;

    &:focus {
      border-color: $highlight-color;
      box-shadow: 0 0 0 2px rgba($highlight-color, 0.1);
      outline: none;
    }
  }

  .error {
    width: 100%;
    min-height: 4.8rem;
    color: $red;
    font-family: $display-font;
    font-size: 1.6rem;
  }

  button {
    padding: 0 2.4rem;
    height: 4rem;
    border-radius: 0.4rem;
    background: $highlight-color;
    color: $bright-color;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 600;
    border: none;
    margin-left: 1.6rem;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: darken($highlight-color, 10%);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.groupField {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  width: 100%;
}

.searchFilter {
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 2.1rem;

  label {
    margin-left: 1.6rem;
  }
  button {
    margin-left: 1.6rem;
    border-radius: 0.4rem !important;
  }
}

.searchHint {
  width: 100%;
  font-size: 1.4rem;
  color: $red;
  margin-top: 0.4rem;
  font-family: $display-font;
  position: absolute;
  bottom: 0;
  left: 1.6rem;
}

.error {
  color: $red;
  font-size: 1.4rem;
  margin-top: 0.4rem;
  font-family: $display-font;
  position: absolute;
  bottom: 0;
  left: 1.6rem;
}
