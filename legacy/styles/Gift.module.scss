@import 'theme';
@import 'mixins';

.eventList {
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.event {
  width: 100%;
  height: auto;
  display: flex;
  transition: all 0.2s ease-out;
  border-radius: 0.8rem;
  margin-bottom: 3.2rem;
  overflow: hidden;
  position: relative;
  padding: 0.8rem;

  &:hover {
    background: $light-gray;
  }
}

.horizontal {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6.4rem;

  .thumb {
    width: 36rem;
    height: 22rem;
    display: block;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 0.8rem;
    border: thin solid $light-gray;
    background-color: #fff;
    position: relative;
    z-index: 10;

    .badge {
      position: absolute;
      height: 2rem;
      font-size: 1.2rem;
      font-weight: 600;
      display: flex;
      flex-direction: row;
      align-items: center;
      font-family: $display-font;
      top: 1.2rem;
      right: 0;
      background-color: white;
      border: thin solid $mid-gray;
      border-right: none;
      padding: 0.8rem 1.2rem;
      border-radius: 1.2rem 0 0 1.2rem;
      z-index: 20;

      svg {
        fill: $highlight-color;
        transform: translateX(0.5rem);
      }
    }
    .badgeSuccess {
      color: $highlight-dark;
    }

    .badgeNeutral {
      color: $highlight-dark;
    }

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: block;
      border-radius: 0.8rem;
    }
  }

  .dateAndPlace {
    width: 14.5rem;
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .info {
    width: 100%;
    min-height: 10rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 0.1rem solid $mid-gray;
    padding: 1.2rem;
    margin-bottom: 1.8rem;
    text-align: left;

    &:last-child {
      margin-bottom: 0;
    }

    .meta {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;

      svg {
        margin-right: 0.8rem;
      }

      h4 {
        color: $highlight-dark;
        font-family: $display-font;
        font-size: 1.4rem;
        font-weight: 600;
        margin: 0;

        span {
          padding-right: 0;
          border: none;
        }
      }
    }

    p {
      width: 100%;
      color: #000;
      font-family: $display-font;
      font-size: 1.6rem;
      font-weight: 600;
      margin: 0.8rem 0 0 0;
      text-align: left;
    }
  }

  .description {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: calc(100% - 58rem);

    h2 {
      font-family: $display-font;
      font-size: 2.8rem;
      font-weight: 700;
      color: #000;
      margin: 0 0 2.4rem 0;
    }

    p {
      color: #000;
      font-family: $display-font;
      font-size: 1.8rem;
      line-height: 2.4rem;
      font-weight: 400;
      margin: 0 0 2.4rem 0;
    }

    a {
      width: 24.7rem;
    }
  }
}

.vertical {
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5.6rem;
  margin-right: 3.2rem;

  .heading {
    .thumb {
      width: 100%;
      display: block;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 0.6rem;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        display: block;
        border-radius: 0.6rem;
      }
    }
    h2 {
      font-family: $display-font;
      font-size: 2.4rem;
      font-weight: 700;
      color: #000;
      margin: 1.2rem 0 1.6rem 0;
    }
  }

  .description {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    width: 100%;

    h2 {
      font-family: $display-font;
      font-size: 2.4rem;
      font-weight: 700;
      color: #000;
      margin: 1.2rem 0 1.6rem 0;
    }

    a {
      width: 100%;
      margin-top: 0;
    }
  }
}

@include responsive(mobile) {
  .vertical {
    width: 100%;
    text-align: center;
    margin-right: 0;
    margin-bottom: 5.2rem;

    .description {
      a {
        width: 100%;
      }
    }
  }

  .horizontal {
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 5.4rem;
    text-align: center;

    .dateAndPlace {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 1.2rem;
      margin-bottom: 3.2rem;
      text-align: left;

      .info {
        width: calc(50% - 1.6rem);
        justify-content: flex-start;
        align-self: stretch;
        height: 100%;
      }
    }

    .description {
      width: 100%;
      h2 {
        width: 100%;
      }

      a {
        width: 100%;
      }
    }
  }

  .featured {
    text-align: center;

    .featuredDescription {
      h2 {
        font-size: 2.8rem !important;
        margin: 1.6rem 0 !important;
      }

      a {
        width: 100% !important;
      }
    }
  }
}

.eventDetails {
  padding-top: 5.4rem;

  .dateAndPlace {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;

    .info {
      width: 24rem;
      min-height: 10rem;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      border: none;
      padding: 1.2rem;
      margin-bottom: 1.8rem;
      margin-right: 2.4rem;

      &:last-child {
        margin-bottom: 0;
      }

      .meta {
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;

        svg {
          margin-right: 0.8rem;
        }

        h4 {
          color: $highlight-dark;
          font-family: $display-font;
          font-size: 2.4rem;
          font-weight: 600;
          margin: 0;
        }
      }

      p {
        width: 100%;
        color: #000;
        font-family: $display-font;
        font-size: 2.4rem;
        font-weight: 600;
        margin: 0.8rem 0 0 0;
      }
    }
  }

  .dateAndPlaceWide {
    .info {
      width: auto !important;
      margin: 0 3.2rem 0 0;
      padding: 0;

      h4 {
        span {
          padding-right: 3.2rem;
          color: #000;
          border-right: 0.1rem solid $mid-gray;
        }
      }

      &:last-child {
        h4 {
          span {
            border: none;
          }
        }
      }
    }

    @include responsive(mobile) {
      .info {
        width: 100% !important;
        min-height: 0.1rem;
        margin: 1.6rem 0 !important;

        .meta {
          h4 {
            font-size: 1.8rem;
            width: calc(100% - 5.4rem);
            span {
              border: none;
            }
          }
        }

        &:last-child {
          margin-bottom: 4rem !important;
        }
      }
    }
  }

  .videoBox {
    width: 100%;
    display: block;
    margin-top: 5.4rem;

    h3 {
      font-size: 2.4rem;
      font-weight: 700;
      margin: 0 0 1.6rem 0;
      color: $highlight-dark;
      font-family: $display-font;
    }
  }

  .logos {
    width: 100%;
    display: block;
    margin: 12rem 0 5.4rem 0;

    h3 {
      font-family: $display-font;
      font-size: 2.1rem;
      font-weight: 600;
      color: $highlight-dark;
      text-align: center;
      margin: 0;
    }

    h4 {
      font-family: $display-font;
      font-size: 1.6rem;
      font-weight: 600;
      color: #000;
      text-align: center;
      margin: 0;
      text-align: center;

      span {
        color: $highlight-dark;
      }
    }

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 1.6rem;

      img {
        width: 94%;
        max-width: 94%;
        height: auto;
      }
    }
  }

  @include responsive(desktop) {
    .dateAndPlace {
      .info {
        width: calc(50% - 3.2rem);
      }
    }
  }

  @include responsive(mobile) {
    .dateAndPlace {
      width: 100%;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: space-between;

      .info {
        width: calc(50% - 1.6rem);
        margin: 3.2rem 0;

        p {
          font-size: 1.8rem;
        }
      }
    }

    .videoBox {
      margin-top: 1.6rem;
      h3 {
        font-size: 1.8rem;
        margin: 0;
      }
    }
    .videoEmbedContainer {
      margin-top: 0.8rem !important;
    }
  }

  .lightGallery {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    margin: 5.4rem 0;

    a {
      display: block;
      margin-right: 1.6rem;

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        display: block;
        border-radius: 0.6rem;
      }
    }

    @include responsive(mobile) {
      a {
        width: 100%;
      }
    }

    @include responsive(tablet) {
      a {
        width: calc(33% - 1.6rem);
      }
    }

    @include responsive(desktop) {
      a {
        width: calc(25% - 1.6rem);
      }
    }
  }

  p {
    a {
      color: $highlight-dark;
      text-decoration: underline;
    }
  }
}

@media (max-width: 767px) {
  .eventDetails {
    margin: 5.4rem 0 0 0;
  }
}

.logisticList {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 3.2rem;

  .infoItem {
    width: 100%;
    font-family: $display-font;
    margin-bottom: 3.2rem;
    padding: 3.2rem;
    border: 0.2rem solid $mid-gray;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;

    h3 {
      font-size: 1.6rem;
      font-weight: 700;
      color: $highlight-dark;
      text-transform: capitalize;
      line-height: 3.2rem;
      display: inline-block;
      vertical-align: middle;
      margin: 0;

      svg {
        top: 0.5rem;
        position: relative;
      }
    }

    h4 {
      font-size: 2.8rem;
      font-weight: 700;
      color: #000;
      text-transform: capitalize;
      margin: 0.8rem 0 1.6rem 0;
    }

    .logisticDescription {
      font-size: 1.6rem;

      p {
        a {
          text-decoration: underline;
          border: none;
          padding: 0;
          margin: 0;
          color: inherit;
          font-size: inherit;
        }
      }
    }

    a {
      padding: 1rem 2rem;
      display: inline-block;
      margin: 0 1.6rem 1.6rem 0;
      border-radius: 0.4rem;
      border: 0.2rem solid $mid-gray;
      color: #000;
      font-size: 1.2rem;
      font-weight: 600;
      transition: all 0.2s ease-out;

      &:hover {
        background: lighten(#000, 90%);
      }
    }
  }
}

@include responsive(mobile) {
  .logisticList {
    width: 100%;
    justify-content: center;
    .infoItem {
      margin: 0 auto 3.2rem auto;
      width: auto;
      font-family: $display-font;
      margin-bottom: 3.2rem;
      text-align: center;
    }
  }
}

.tableList {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin: 3.2rem 0;

  ul {
    width: 50%;
    padding: 0 2.4rem 0 0;
    list-style: none;
    font-family: $display-font;

    li {
      border-bottom: 0.1rem solid $mid-gray;
      width: 100%;
      padding: 0.4rem 0;

      h3 {
        font-size: 2.1rem;
      }
      p {
        font-size: 1.6rem;
      }
    }
  }
}

.questionsList {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 3.2rem;

  h3 {
    font-size: 2.4rem;
    font-family: $display-font;
    font-weight: 700;
  }

  p {
    font-size: 1.8rem;
    line-height: 2.4rem;
    font-family: $display-font;
    font-weight: 500;
  }
}

.sectionFooter {
  width: 100%;
  display: flex;
  margin: 0 0 8rem 0;
  h4 {
    font-size: 2.1rem;
    font-family: $display-font;
  }
  a {
    width: 26rem;
  }

  @include responsive(mobile) {
    > a {
      width: 100%;
    }
  }
}

.login {
  width: 40rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3.2rem;
  border: 0.1rem solid $mid-gray;
  margin: 0 auto;

  h2 {
    font-size: 2.1rem;
    font-weight: 600;
    font-family: $display-font;
    text-align: center;
    color: $highlight-dark;
    margin: 1.6rem 0;
  }

  h3 {
    font-size: 1.4rem;
    font-weight: 600;
    font-family: $display-font;
    text-align: center;
    margin: 1.6rem 0 3.2rem 0;

    strong {
      font-weight: 700;
    }
  }

  form {
    width: 100%;
  }

  @include responsive(mobile) {
    width: 32rem;
  }
}

.registrationForm {
  width: 100%;
  padding: 5.4rem 0;
  font-family: $display-font;

  .heading {
    width: 100%;
    display: block;
    margin-bottom: 5.4rem;

    h1 {
      font-size: 5.4rem;
    }

    h3 {
      font-size: 1.8rem;
      line-height: 2.1rem;
      color: $mid-gray;

      > span {
        padding: 0 3.2rem 0 0;
        color: #000;

        &:first-child {
          svg {
            margin-left: 0;
          }
        }

        svg {
          color: $highlight-color;
          margin: 0 0.8rem 0 3.2rem;
          position: relative;
          top: 0.4rem;
        }
      }
    }
  }

  @include responsive(mobile) {
    .heading {
      h1 {
        font-size: 3.2rem;
      }
      h3 {
        font-size: 1.6rem;
        span {
          padding-right: 1.6rem;
          svg {
            margin-left: 0;
          }
        }
      }
    }
  }
}

.masonry {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(15%, 1fr));
  grid-auto-flow: dense;
  gap: 0.8rem;
  margin: 3.2rem 0 5.4rem 0;

  img {
    max-width: 100%;
    display: block;
    border-radius: 0.6rem;
  }

  .item {
    width: 100%;
    margin: 0;
    display: grid;
    grid-template-rows: 1fr auto;

    img {
      width: 100%;
      height: auto;
    }
  }

  .landscape {
    overflow: hidden;
    position: relative;

    img {
      height: 100%;
      width: auto;
    }

    grid-column: span 2 !important;
  }
  .portrait {
    overflow: hidden;
    position: relative;

    img {
      height: 100%;
      width: auto;
      position: absolute;
      left: 0;
      top: 0;
    }
    grid-row: span 2 !important;
    grid-column: span 2 !important;
  }
}

@media screen and (min-width: 320px) and (max-width: 540px) {
  .masonry {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(50%, 1fr));

    .landscape {
      overflow: hidden;
      position: relative;

      img {
        height: 100%;
        width: auto;
      }

      grid-column: span 1 !important;
    }
    .portrait {
      overflow: hidden;
      position: relative;

      img {
        height: 100%;
        width: auto;
        position: absolute;
        left: 0;
        top: 0;
      }
      grid-row: span 1 !important;
      grid-column: span 1 !important;
    }
  }
}

.featured {
  .featuredThumb {
    width: 100%;
    border-radius: 0.8rem;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: block;

    img {
      width: 100%;
      height: auto;
      border-radius: 0.8rem;
    }
  }

  .featuredDescription {
    width: 100%;
    h2 {
      font-size: 5.4rem;
      font-family: $display-font;
      margin: 0 0 3.2rem 0;
    }

    p {
      color: #000;
      font-family: $display-font;
      font-size: 1.8rem;
      line-height: 2.4rem;
      font-weight: 400;
      margin: 0 0 2.4rem 0;
    }
    a {
      width: 25rem;
    }
  }
}

@include responsive(tablet) {
  .horizontal {
    flex-direction: horizontal;
    flex-wrap: wrap;

    .thumb {
      order: 0;
    }

    .description {
      order: 1;
      width: 40%;
    }

    .dateAndPlace {
      width: 36rem;
      flex-direction: row;
      justify-content: space-between;
      order: 2;
    }

    .info {
      width: 17rem;
      margin: 0.8rem 0.8rem 0 0;
      p {
        font-size: 1.2rem;
      }
    }
  }

  .vertical {
    width: calc(33% - 3.2rem);

    .heading {
      h2 {
        font-size: 1.6rem;
      }
    }

    .description {
      h2 {
        font-size: 1.6rem;
      }
      a {
        width: 100%;
        font-size: 1.2rem;
        padding: 0 0.8rem;
      }
    }
  }
}

@include responsive(desktop) {
  .vertical {
    width: calc(25% - 3.2rem);
  }
}

.eventPage {
  .dateAndPlaceWide {
    .info {
      width: auto !important;
      margin: 0 3.2rem 0 0;
      padding: 0;

      h4 {
        span {
          padding-right: 0;
          color: #000;
          border-right: none;
        }
      }
    }
  }
}

.sectionContent {
  h4 {
    margin-bottom: 0;
    font-family: $display-font;
    font-weight: 700;
    font-size: 2.1rem;
  }
  ul {
    list-style: none;
    padding: 0;
    margin-top: 0;
    margin-bottom: 3.2rem;

    li {
      font-size: 1.6rem;
      line-height: 2.4rem;
      font-family: $display-font;
      padding-left: 0.8rem;
      position: relative;

      &:before {
        content: '• ';
      }
    }
  }
}

.stepIndicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 3.2rem;
  padding: 1.6rem 0;
  border-bottom: 0.1rem solid $light-gray;

  .step {
    display: flex;
    align-items: center;
    margin: 0 2.4rem;
    color: $mid-gray;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 600;

    &.active {
      color: $highlight-color;

      .stepNumber {
        background-color: $highlight-color;
        color: white;
      }
    }

    &.completed {
      color: $highlight-dark;

      .stepNumber {
        background-color: $highlight-dark;
        color: white;
      }
    }

    .stepNumber {
      width: 3.2rem;
      height: 3.2rem;
      border-radius: 50%;
      background-color: $light-gray;
      color: $mid-gray;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 1.2rem;
      font-weight: 700;
    }
  }

  .stepDivider {
    width: 4.8rem;
    height: 0.2rem;
    background-color: $light-gray;
    margin: 0 0.8rem;

    &.completed {
      background-color: $highlight-dark;
    }
  }
}

.selectCard {
  width: 100%;
  background: white;
  border: 0.1rem solid $mid-gray;
  appearance: none;
  border-radius: 1.6rem;
  padding: 1.6rem;
  margin-bottom: 2.4rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-out;

  &:hover {
    border-color: $highlight-color;
    box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.05);
  }

  &.selectedItem {
    box-shadow: 0 0.4rem 1.6rem rgba(0, 0, 0, 0.1);
  }

  .checked {
    position: absolute;
    top: -1.6rem;
    right: -1.6rem;
    color: $highlight-color;
    background: #000;
    padding: 0.4rem;
    border-radius: 50%;
    width: 4rem;
    height: 4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.6rem;
    font-weight: 700;
  }

  .cardImage {
    width: 100%;
    margin-bottom: 1.6rem;

    img {
      width: 100%;
      height: auto;
      border-radius: 0.4rem;
    }
  }

  .cardBody {
    margin-bottom: 1.6rem;

    h4 {
      font-family: $display-font;
      font-size: 1.8rem;
      font-weight: 600;
      color: #000;
      margin: 0 0 0.8rem 0;
    }

    p {
      font-size: 1.4rem;
      line-height: 2rem;
      color: $dark-gray;
      margin: 0;
    }
  }

  .cardFooter {
    margin-top: 1.6rem;
    padding-top: 1.6rem;
    display: flex;
    flex-direction: column;
    gap: 1.2rem;

    button {
      width: 100%;
      justify-content: center;
    }
  }

  .ax_field {
    margin-bottom: 1.6rem;

    select {
      width: 100%;
      padding: 0.8rem;
      border-radius: 0.4rem;
      border: 0.1rem solid $mid-gray;
      font-family: $display-font;
      font-size: 1.4rem;
      color: $dark-gray;
      background-color: white;
      cursor: pointer;

      &:disabled {
        background-color: $light-gray;
        cursor: not-allowed;
      }
    }

    small {
      display: block;
      margin-top: 0.4rem;
      color: $dark-gray;
      font-size: 1.2rem;
    }
  }
}

.selectedItem {
  border: 0.2rem solid #000;
  background: $highlight-color;
  h4,
  p,
  li,
  label {
    color: white !important;
  }

  li {
    &:before,
    &::marker {
      color: white !important;
    }
  }
  select {
    color: #000 !important;
  }

  button {
    transition: all 0.2s ease-out;
    &:hover {
      background: #000 !important;
      color: #fff !important;
      transform: scale(1.02);
    }
  }

  &:hover {
    border: 0.2rem solid #000;
  }
  // button {
  //   display: none;
  // }
}

@include responsive(mobile) {
  .giftItem {
    padding: 0 !important;
  }
}
