// styles/Balloons.module.scss

@import 'theme';
@import 'mixins';

.balloonContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9999;
}

.birthdayMessage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10000;
  pointer-events: none;
  font-family: $display-font;

  h1 {
    font-size: 5.4rem;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: $radial-gradient-blue;
    padding: 20px;
    border-radius: 10px;
    white-space: nowrap;
  }
}
