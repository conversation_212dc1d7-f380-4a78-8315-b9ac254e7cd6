@import 'theme';
@import 'mixins';

.heading {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-items: center;

  button {
    margin-left: 2.4rem;
  }
}
.content {
  width: 100%;
  padding: 1.6rem;
  background: $bright-color;
  border-radius: 0.8rem;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  position: relative;
  margin-top: 1.6rem;

  h2 {
    width: 100%;
    font-family: $display-font;
    font-size: 1.8rem;
    margin-bottom: 2.4rem;
    margin-top: 0;
  }

  .qrForm {
    width: calc(100% - 56rem);
    margin-right: 3.2rem;
    display: flex;
    flex-direction: row;
  }

  label {
    width: 40rem;
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 600;
  }

  input[type='text'],
  input[type='email'] {
    width: 100%;
    height: 3.8rem;
    border-radius: 0.4rem;
    border: 0.1rem solid $mid-gray;
    background: $superlight-gray;
    padding: 0 0.4rem;
    color: $base-color;
    font-family: $body-font;
    font-size: 1.4rem;
    display: inline-block;
    margin: 0 1.6rem;
    font-weight: 400;

    &::placeholder {
      font-weight: 400;
      color: $mid-gray;
    }
  }

  button {
    margin-top: 2.6rem;
    margin-left: 0;
  }

  .qrBlock {
    width: 36rem;
    height: 40rem;
    background: $light-gray;
    padding: 3.2rem 1.6rem;
    border-radius: 0.8rem;
    text-align: center;

    h3 {
      font-family: $display-font;
      font-size: 1.6rem;
      font-weight: 600;
      color: $highlight-dark;
    }
  }
}

.qrcodesList {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;

  .ax_card_list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    position: relative;
  }
}

.noQrs {
  font-family: $body-font;
  font-size: 1.6rem;
}

.cardItem {
  margin-right: 1.6rem;
  margin-bottom: 1.6rem;
  padding: 3.2rem;
  border-radius: 0.8rem;
  background: $bright-color;
  display: flex;
  flex-direction: column;
  text-align: center;

  img {
    width: 80%;
    max-width: 80%;
    height: auto;
    display: block;
    margin-left: auto;
    margin-right: auto;
    pointer-events: none;
  }

  > svg {
    pointer-events: none;
  }

  p {
    font-family: $body-font;
    font-size: 1.4rem;
    word-break: break-all;
  }

  .cardItemFooter {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: auto;

    a {
      display: block;
      width: calc(100% - 4rem);
      height: 3.4rem;
      line-height: 3.4rem;
      padding: 0 2.4rem;
      border-radius: 0.4rem;
      background: $highlight-color;
      color: $bright-color;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 0;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease-out;

      &:hover {
        background: darken($highlight-color, 15);
      }
    }

    button {
      width: 3rem;
      height: 3.4rem;
      padding: 0;
      border-radius: 0.4rem;
      background: $red;
      color: $bright-color;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 0;
      border: none;
      appearance: none;
      cursor: pointer;
      transition: all 0.2s ease-out;

      &:hover {
        background: darken($red, 15);
      }
    }
  }
}

@include responsive(desktop) {
  .cardItem {
    width: calc(25% - 1.6rem);
    display: flex;
    align-self: stretch;
  }
}

.qrCodeSvg {
  border: 0.6rem solid white;
  box-sizing: initial;
}

@include responsive(tablet) {
  .cardItem {
    width: calc(33% - 1.6rem);
    display: block;
  }
}

@include responsive(mobile) {
  .cardItem {
    width: 100%;
    display: block;
  }
}

.contentContainer {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.listColumn {
  width: 28rem;
  height: 100%;
  background: $bright-color;
  list-style: none;
  display: flex;
  flex-direction: column;
  padding: 1.6rem 3.2rem 0 1.6rem;
  border-radius: 0.8rem;

  > li {
    width: 100%;
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 700;
    padding: 1.2rem 0;
    border-bottom: 0.1rem solid $mid-gray;

    &:last-child {
      border-bottom: none;
    }

    > button {
      background: transparent;
      border: none;
      width: auto;
      padding: 0;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 700;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);

      &:hover {
        color: $highlight-color;
      }

      span {
        font-weight: 400;
        color: $highlight-dark;
        margin-left: 0.8rem;
        pointer-events: none;
      }
    }

    svg {
      width: 2.4rem;
      height: 2.4rem;
      margin-right: 0.8rem;
      pointer-events: none;
    }
    time {
      pointer-events: none;
    }

    > ul {
      width: 100%;
      list-style: none;
      padding: 0 0 0 1.6rem;
      display: none;

      button {
        background: transparent;
        border: none;
        font-family: $body-font;
        font-size: 1.4rem;
        color: $highlight-dark;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
        text-align: left;

        &:hover {
          color: $highlight-color;
        }

        span {
          color: $base-color;
        }
      }
    }
  }

  .opened {
    display: block;
  }
}

.contentColumn {
  width: calc((100% - 31.6rem));
  display: flex;
  flex-wrap: wrap;
}

.listColumnMob {
  background: $bright-color;
  border-radius: 0.4rem;
  font-family: $display-font;
  font-weight: 600;
  color: $base-color;
  border: none;
  appearance: none;
  display: none;
  box-shadow: $shadow;
  padding: 0.8rem 1.6rem;

  svg {
    vertical-align: middle;
  }

  > ul {
    display: none;
    padding: 0;
    list-style: none;

    li {
      width: 100%;

      button {
        border: none;
        appearance: none;
        font-size: 1.6rem;
        color: $base-color;
        font-weight: 600;
        background: transparent;
        height: 3.4rem;

        &:hover {
          color: $highlight-color;
          cursor: pointer;
        }
      }
    }

    ul {
      padding: 0;
      list-style: none;
    }
  }
}

.listColumnMobOpen {
  display: table;
  text-align: left;
  > ul {
    display: block;
  }
}

@include responsive(tablet) {
  .listColumn {
    display: none;
  }

  .listColumnMob {
    width: 40rem;
    display: block;
  }

  .contentColumn {
    margin-top: 3.2rem;
    width: 100%;
  }
}

@include responsive(mobile) {
  .listColumn {
    display: none;
  }

  .listColumnMob {
    width: 100%;
    display: block;
  }

  .contentColumn {
    margin-top: 3.2rem;
    width: 100%;
  }
}

.qrCodeJpgContainer {
  width: 30rem;
  height: 30rem;
  display: block;
  margin: 0;
  padding: 0;

  img {
    width: 30rem;
    height: 30rem;
    display: block;
    margin: 0;
    padding: 0;
  }
}

.qrCodes {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin: 3.2rem 0;

  h3 {
    width: 100%;
    font-family: $display-font;
    font-size: 1.6rem;
    font-weight: 600;
  }

  .ax_card_list {
    justify-content: flex-start !important;

    .ax_card_body {
      h3 {
        word-wrap: wrap;
      }
    }
  }
}

.filter {
  width: 100%;
  display: flex;
  flex-direction: row;

  label {
    width: calc(100% - 13rem);
  }
  select {
    width: 100%;
    height: 5.6rem;
    border: 1px solid $mid-gray;
    background: white;
    border-radius: 0.6rem;
    font-family: $display-font;
    font-size: 1.6rem;
    padding: 0 0.8rem;
  }

  button {
    width: 55%;
  }
}

.sheet_actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.delete_button {
  background-color: transparent;

  &:hover {
    background-color: rgba(220, 53, 69, 0.1);
  }
  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
}
