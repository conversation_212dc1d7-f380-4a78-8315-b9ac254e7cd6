@import 'theme';
@import 'mixins';

.canvas {
  width: 100rem;
  height: 100rem;
  display: block;
  border: 1px solid red;
  position: relative;
}

.handle {
  width: auto;
  cursor: move;
  display: table;
}

.dragglableInfo {
  font-family: $display-font;
  font-size: 1.6rem;
  padding: 1.6rem;
  display: table;
  width: auto;
  cursor: move;

  input {
    background: transparent;
    font-family: $display-font;
    font-size: 2.4rem;
    font-weight: 700;
    appearance: none;
    box-shadow: none;
    border: none;
  }
}

.actions {
  position: fixed;
  top: 12rem;
  right: 8rem;
  background: $bright-color;
  border-radius: 0.8rem;
  box-shadow: $shadow-large;
  padding: 1.2rem;
  z-index: 300;
  width: 30rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;

  label {
    font-family: $display-font;
    font-size: 1.4rem;
    color: $highlight-dark;
    font-weight: 600;

    input[type='checkbox'] {
      font-family: $display-font;
      font-size: 1.4rem;
      color: $base-color;
      font-weight: 400;
    }
  }

  .boundingBox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}

.posts {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;

  h1 {
    width: 100%;
    display: block;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;
    line-height: 3.2rem;
  }

  h2 {
    margin: 0;
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;

    svg {
      margin-right: 0.5rem;
      transform: translateY(-0.1rem);
    }

    span {
      color: $highlight-color;
    }
  }
}

.caption {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 1.6rem 0 3.2rem 0;
  background: $mid-gray;
  border-radius: 0.8rem;
  padding: 3.2rem;

  h2 {
    width: 100%;
    font-family: $display-font;
    font-size: 2.1rem;
    display: block;
    margin: 0 0 1.6rem 0;
  }

  p {
    width: 100%;
    font-family: $body-font;
    font-size: 1.4rem;
    display: block;
    margin: 0 0 0.8rem 0;
  }

  h3 {
    width: 100%;
    font-family: $display-font;
    font-size: 1.6rem;
    display: block;
    margin: 1.6rem 0 0 0;
  }

  > div {
    width: 100%;
    font-family: $body-font;
    font-size: 1.8rem;
    line-height: 2.4rem;
    display: block;
    margin: 1.6rem 0 2.4rem 0;
  }

  button {
    margin: 0;
  }
}

.imageList {
  padding: 0;
  list-style: none;
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;

  .imageItem {
    display: block;
    height: auto;
    position: relative;
    overflow: hidden;

    .imageContainer {
      display: block;
      position: relative;

      .imageOverlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.85);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
        z-index: 10;
      }
    }

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0;
    }

    caption {
      width: 100%;
      display: block;
      text-align: left;
      font-family: $display-font;
      font-size: 1.4rem;
      line-height: 1.6rem;
      color: $base-color;
      font-weight: 600;
      margin: 0.4rem 0;
    }

    &:hover .imageOverlay {
      opacity: 1;
      visibility: visible;
    }
  }

  @include responsive(mobile) {
    .imageItem {
      width: 100%;
      margin-bottom: 3.2rem;
      margin-right: 0;
    }
  }

  @include responsive(tablet) {
    .imageItem {
      width: calc(33% - 3.2rem);
      margin-right: 3.2rem;
      margin-bottom: 3.2rem;
    }
  }

  @include responsive(desktop) {
    .imageItem {
      width: calc(25% - 3.2rem);
      margin-right: 3.2rem;
      margin-bottom: 3.2rem;
    }
  }
}

.btnCopy {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3.4rem;
  font-size: 1.2rem;
  color: $highlight-dark;
  font-weight: 600;
  letter-spacing: 0.1rem;
  border-radius: 0.4rem;
  border: none;
  background-color: $bright-color;
  transition: all 0.3s ease-out;
  padding: 0 1.6em;
  cursor: pointer;
  margin: 0 0 2.4rem 0;

  svg {
    margin-right: 0.8rem;
  }

  &:hover {
    background: $base-color;
    color: $bright-color;
  }
}

.btnDownload {
  height: 4rem;
  font-size: 1.6rem;
  color: $bright-color;
  font-weight: 600;
  letter-spacing: 0.1rem;
  border-radius: 0.4rem;
  border: none;
  background-color: $highlight-color;
  transition: all 0.3s ease-out;
  padding: 0 3.2rem;
  cursor: pointer;
  margin: 0 0 3.2rem 0;

  &:hover {
    background: $base-color;
    color: $bright-color;
  }
}

.calendar {
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  h2 {
    margin: 1.6rem 0;
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-family: $display-font;
    font-size: 2.4rem;
    color: $base-color;
  }

  img {
    width: 100%;
    max-width: 100%;
    height: auto;
  }
}

.btnDisabled {
  opacity: 0.6;
  cursor: not-allowed;

  &:hover {
    opacity: 0.6;
  }
}

.overlayDownloadButton {
  padding: 0.8rem 1.6rem;
  font-size: 1.4rem;
  color: $bright-color;
  background-color: $highlight-color;
  border: none;
  border-radius: 0.4rem;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: darken($highlight-color, 10%);
  }

  &.loading {
    cursor: progress;
    opacity: 0.8;
  }
}
