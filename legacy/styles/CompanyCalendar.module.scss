@import 'theme';

.toolBar {
  width: 100%;
  height: 4.8rem;
  padding: 1.2rem;
  border-radius: 0.8rem;
  background: white;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1.2rem;

  .colorFilterList {
    width: auto;
    height: 4.8rem;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    list-style: none;
    padding: 0;

    li {
      display: flex;
      align-items: center;
      height: 3.6rem;
      margin-right: 0.4rem;

      button {
        height: 2.8rem;
        display: inline-block;
        color: #000;
        font-family: $display-font;
        font-size: 1.1rem;
        line-height: 2.8rem;
        margin-left: 1.2rem;
        font-weight: 700;
        appearance: none;
        border: none;
        border-radius: 1.4rem;
        padding: 0 1.2rem;
        cursor: pointer;
      }
    }
  }
}
