@import 'theme';
@import 'mixins';

.ax_card_list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;

  > div {
    display: block;
    margin-right: 1.6rem;
    margin-bottom: 1.6rem;
  }
}

.frame {
  width: 100%;
  iframe {
    width: 100%;
  }
}

@include responsive(desktop) {
  .ax_card_list {
    > div {
      width: calc(20% - 1.6rem);
      display: flex;
      align-self: stretch;
    }
  }

  .frame {
    width: 100%;
    iframe {
      width: 100rem;
    }
  }
}

@include responsive(tablet) {
  .ax_card_list {
    > div {
      width: calc(33% - 1.6rem);
      display: block;
    }
  }
}

@include responsive(mobile) {
  .ax_card_list {
    > div {
      width: 100%;
      display: block;
    }
  }
}

.heading {
  width: 100%;
  padding: 3.2rem;
  background: $bright-color;
  border-radius: 0.8rem;
  margin-bottom: 3.2rem;
  position: relative;
}

.options {
  display: flex;
  flex-wrap: wrap;
  border-radius: 0.4rem;

  h3 {
    width: 100%;
    font-family: $display-font;
    font-size: 1.4rem;
    color: #ca0202;
    margin: 0 0 1.6rem 0;
  }

  h4 {
    width: 100%;
    font-family: $display-font;
    font-size: 1.6rem;
    color: $highlight-dark;
    margin: 0 0 1.6rem 0;
  }

  form {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;

    select {
      width: auto;
      height: 4rem;
      border-radius: 0.4rem;
      border: 0.1rem solid $mid-gray;
      background: $light-gray;
      padding: 0 4rem 0 1.6rem;
      color: $base-color;
      font-family: $display-font;
      font-size: 1.4rem;
      display: inline-block;
      transform: translateX(-1.6rem);
      position: relative;
      z-index: 1;
    }

    label {
      width: auto;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 600;
      line-height: 1.6rem;
      display: flex;
      align-items: center;
      margin-bottom: 0.8rem;
      margin-right: 2.4rem;
    }
  }
}

.validationErrors {
  background-color: #fff3f3;
  border: 1px solid #ffcaca;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 24px;

  h3 {
    color: #d32f2f;
    margin-top: 0;
  }

  ul {
    margin-bottom: 16px;
  }

  li {
    margin-bottom: 8px;
  }

  p {
    font-style: italic;
    margin-bottom: 0;
  }
}
