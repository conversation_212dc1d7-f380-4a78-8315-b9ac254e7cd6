@import './styles/theme';
@import './styles/mixins';

.ax_carousel {
  width: 100%;
  // padding-left: 4rem;
  // padding-right: 4rem;
  position: relative;
  margin-bottom: 3.2rem;
  > div {
    > div[role='listbox'] {
      padding: 0;
      position: relative;
    }

    @include responsive(mobile) {
      > div[role='listbox'] {
        padding: 0;

        &:after,
        &:before {
          display: none;
        }

        li {
          box-shadow: $shadow;
        }
      }
    }
  }

  li {
    display: block;
    text-align: center;
    box-sizing: border-box;
    margin: 0 1.6rem 3.2rem 1.6rem;
    border-radius: 0.6rem;
    position: relative;
    background: $bright-color;
    box-shadow: $shadow-large;
    transition: all 0.2s $cubic-transition;

    &:hover {
      transform: perspective(400px) rotateX(2.5deg) scale(1.06);
      top: -2rem;
    }

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
      display: block;
      margin: 0 auto;
      padding: 0;
      border-radius: 0.6rem 0.6rem 0 0;
    }

    h3 {
      width: 90%;
      display: block;
      margin: 1.6rem auto;
      font-size: 2.1rem;
      color: $base-color;
      font-family: $display-font;
    }

    p {
      width: 90%;
      display: block;
      margin: 1.6rem auto;
      padding-bottom: 6.4rem;
      font-size: 1.4rem;
      color: $base-color;
      font-family: $body-font;
    }

    > div {
      position: relative !important;
    }

    a {
      width: 180px;
      position: absolute;
      bottom: 3.2rem;
      left: calc((100% - 180px) / 2);
    }
  }

  .ax_back,
  .ax_next {
    width: 3.4rem;
    height: 3.4rem;
    background-color: $bright-color;
    background-size: 40% auto;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 0.8rem;
    box-shadow: $shadow;
    position: absolute;
    border: none;
    outline: none;
    transition: all 0.2s cubic-bezier(0.075, 0.82, 0.165, 1);

    &:hover {
      background-color: $highlight-color;
    }
  }

  .ax_back {
    top: 45%;
    left: 3.2rem;
    background-image: url('/images/chevron-left.svg');
  }

  .ax_next {
    top: 45%;
    right: 3.2rem;
    background-image: url('/images/chevron-right.svg');
  }

  .ax_dots {
    width: 100%;
    top: -4.4rem;
    position: relative;
    text-align: center;

    button {
      width: 1.6rem;
      height: 1.6rem;
      border: none;
      background: $light-gray;
      padding: 0;
      margin: 0.8rem;
      border-radius: 50%;
    }

    [disabled] {
      background: $highlight-color;
    }
  }
}

@include responsive(desktop) {
  .ax_carousel_dkt {
    display: block;
  }

  .ax_carousel_tablet {
    display: none;
  }

  .ax_carousel_mob {
    display: none;
  }

  .ax_carousel_testimonials {
    display: block !important;

    p {
      padding-bottom: 0 !important;
    }
  }
}

@include responsive(mobile) {
  .ax_carousel {
    width: 80%;
    padding-left: 0;
    padding-right: 0;
    margin: 0 auto !important;
  }

  // .ax_carousel_dkt {
  //   display: none;
  // }

  .ax_carousel_tablet {
    display: none;
  }

  .ax_carousel_mob {
    display: block;
  }

  .ax_carousel_testimonials {
    display: block !important;

    p {
      padding-bottom: 0 !important;
    }
  }
}

@include responsive(tablet) {
  .ax_carousel_dkt {
    display: none;
  }

  .ax_carousel_tablet {
    display: block;
  }

  .ax_carousel_mob {
    display: none;
  }

  .ax_carousel_testimonials {
    display: block !important;

    p {
      padding-bottom: 0 !important;
    }
  }
}

.logoCarousel,
.imageCarousel {
  a {
    width: 100%;
    display: block;

    img {
      width: 100%;
      max-width: 100%;
      height: auto;
    }
  }

  .ax_back {
    left: -4rem;
  }

  .ax_next {
    right: -4rem;
  }
}

.imageCarousel {
  margin: 3.2rem 0 5.4rem 0;
  a {
    padding: 0.8rem;

    img {
      border-radius: 0.6rem;
    }
  }
}

.carousel_center {
  margin-left: auto;
  margin-right: auto;
}
