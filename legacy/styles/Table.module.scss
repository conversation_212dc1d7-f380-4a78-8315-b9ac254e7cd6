@import 'theme';
@import 'mixins';

.table {
  width: 100%;

  table {
    display: table;
    font-family: $display-font;
    background: $bright-color;
    padding: 0;
    border-radius: 0.8rem;
    margin-bottom: 3.2rem;
    width: 100%;

    thead,
    tbody {
      width: 100%;

      tr {
        th {
          font-size: 1.2rem;
          border-bottom: 0.2rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0 0.8rem;
          text-align: left;
          color: $highlight-dark;

          &:last-child {
            text-align: right;
          }

          h3 {
            font-size: 1.8rem;
            color: $highlight-dark;
          }
        }

        td {
          font-size: 1.6rem;
          border-bottom: 0.1rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0.4rem 0.8rem;
          transition: all 0.2s ease-out;
          min-width: 10rem;

          &:last-child {
            text-align: right;
          }

          .icon {
            img {
              width: 2.4rem;
              height: 2.4rem;
              display: inline-block;
              margin: 0.4rem;
              border-radius: 50%;
            }
          }
        }

        &:hover {
          td {
            background-color: $light-gray;
          }
        }
      }
    }

    tfoot,
    .footer {
      width: 100%;

      tr {
        th {
          font-size: 1.2rem;
          border-bottom: 0.2rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0 0.8rem;
          text-align: left;
          color: $highlight-dark;

          &:last-child {
            text-align: right;
          }

          h3 {
            font-size: 1.8rem;
            color: $highlight-dark;
          }
        }

        td {
          font-size: 1.6rem;
          border-bottom: 0.1rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0.4rem 0.8rem;
          transition: all 0.2s ease-out;
          min-width: 10rem;

          &:last-child {
            text-align: right;
          }

          .icon {
            img {
              width: 2.4rem;
              height: 2.4rem;
              display: inline-block;
              margin: 0.4rem;
              border-radius: 50%;
            }
          }

          h3 {
            font-size: 1.8rem;
            color: $highlight-dark;
          }
        }

        &:hover {
          td {
            background-color: $light-gray;
          }
        }
      }
    }
  }
}

.buttonCell {
  text-align: right;

  button {
    margin-left: auto;
  }
}
