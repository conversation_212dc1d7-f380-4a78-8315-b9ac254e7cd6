@import 'theme';

.ax_calc_App {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
  font-family: $display-font !important;
  position: relative;

  .askingPriceLabel {
    padding: 0.8rem 1.6rem;
    border-radius: 1.2rem;
    background: #e9ecf1;
    width: 100%;

    p {
      color: black;
      font-size: 2.1rem;
      line-height: 4rem;
    }

    .askingPriceInputGroup {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
  }

  @media screen and (min-width: 992px) {
    .askingPriceLabel {
      .askingPriceInputGroup {
        span {
          font-size: 2.4rem;
          line-height: 4rem;
          color: $highlight-color;
          flex-grow: 1;
        }
        input {
          flex-grow: 1;
          height: 5.6rem !important;
          width: 100%;
          border-radius: 0.8rem;
          font-size: 2.1rem !important;
        }
      }
    }
  }
  .calculartorBody {
    .calcHeading {
      p,
      h3 {
        margin: 0;
        padding: 1.6rem 0;
        line-height: 4rem;
      }

      input {
        margin: 0 !important;
      }
    }
    .ax_calc_row {
      &:nth-child(odd) {
        background-color: #e9ecf1;
      }

      &:nth-child(even) {
        background-color: #f8f8fc;
      }

      &:first-child {
        border-radius: 0.8rem 0.8rem 0 0;
      }

      &:last-child {
        background-color: transparent;
      }
    }
  }

  label {
    span {
      color: $red !important;
    }
  }

  form {
    width: 100%;

    .photoForm {
      input {
        width: 100%;
        height: 4rem;
      }

      button {
        width: 100%;
        background: $highlight-color;
        color: #fff;
        font-family: $display-font;
        font-weight: 600;
        border: none;
        height: 3.6rem;
        border-radius: 0.4rem;
        padding: 0.8rem 2.4rem;
        clear: both;
      }

      small {
        width: 100%;
        display: block;
        margin-top: 1.6rem;
      }
    }

    .ax_image_options {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;

      h4 {
        width: 100%;
        display: block;
      }

      button {
        margin-top: 0;
      }
    }

    .photo {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 1.6rem;

      svg {
        margin: 3rem auto;
        fill: $mid-gray;
      }

      img {
        width: 100%;
        max-width: 100%;
        height: auto;
        border-radius: 0.8rem 0.8rem 0 0;
        background: $light-gray;
        display: block;
      }

      .imgFull {
        width: 100%;
        max-width: 100%;
        height: auto;
        display: block;
        border-radius: 0.8rem;
      }

      .imgThumb {
        width: 30rem;
        height: 30rem;
        display: block;
        border-radius: 0.8rem;
      }

      .photoLoading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        display: table;
        text-align: center;
        background: white;
        border-bottom: 0.1rem solid $light-gray;

        img {
          width: 80%;
          max-width: 80%;
          height: auto;
        }

        p {
          font-family: $display-font;
          font-size: 1.6rem;
          color: $highlight-color;
          margin: 1.6rem 0;
          width: 100%;
          display: block;
          text-align: center;
        }
      }
    }
  }

  h3 {
    letter-spacing: normal !important;
  }

  h2 {
    font-size: 18px;
  }

  input[type='text'],
  input[type='email'] {
    height: 40px;
    background-color: #fff !important;
    border: 1px solid #8acbc8 !important;
    text-indent: 8px !important;
    font-size: 16px !important;
    color: $base-color !important;
    margin: 0.8rem 0 !important;
    border-radius: 0.4rem;
  }

  input[readonly] {
    border: 1px solid $mid-gray !important;
  }

  textarea {
    width: 100%;
    display: block;
    height: 140px !important;
    background-color: #fff !important;
    border: 1px solid #8acbc8 !important;
    font-size: 16px !important;
    color: $base-color !important;
    margin: 0.8rem 0 1.6rem 0 !important;
    border-radius: 0.4rem;
    font-family: $body-font;
    padding: 1rem 1.6rem;
    font-size: 1.4rem;
  }

  .ax_calc_btn_go {
    border: none;
    background-color: #8db004;
    color: #fff;
    box-shadow: none;
    height: 38px;
    padding: 0 30px;
    margin-left: 10px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    border-radius: 0.4rem;

    &:hover {
      background-color: $base-color;
    }
  }
}

.ax_calc_calculator_section {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 15px;
}

.ax_calc_section_footer {
  background-color: black;
  padding-bottom: 1.6rem;

  border-radius: 0 0 0.8rem 0.8rem;

  h3 {
    color: white;
    font-size: 16px;
    margin: 0 !important;
    padding: 20px 0 !important;
  }

  input {
    width: 100%;
    color: #000;
    font-size: 18px;
    font-weight: 700 !important;
    border: none;
    background-color: initial;
    text-indent: 0;
  }

  p {
    color: $highlight-color !important;
    font-family: $display-font;
    font-size: 1.3rem;
    font-weight: 800;
    margin-top: 0;
  }

  table {
    input {
      width: 100% !important;
      background-color: initial !important;
      border: none !important;
      color: #000 !important;
    }
  }

  .ax_calc_left_col {
    h3 {
      color: black !important;
      font-size: 1.8rem !important;
    }
  }
}

.ax_select {
  display: inline-block;
  width: calc(100% - 1.6rem) !important;
  height: 40px !important;
  background-color: #fff !important;
  background-image: url('../public/images/arrow-down.png');
  background-repeat: no-repeat;
  background-size: 20px 10px;
  background-position: right center;
  border: 1px solid #8acbc8 !important;
  text-indent: 8px;
  font-size: 16px !important;
  color: $base-color !important;
  border-radius: 4px;
  -webkit-appearance: none;
  box-shadow: none !important;
  margin: 0.8rem 0 1.6rem;
}

.ax_calc_primary_font {
  font-family: 'Montserrat', sans-serif !important;
  text-transform: uppercase;
  font-weight: 700;
}

.totalsInput {
  width: auto !important;
  font-weight: 700 !important;
}

@media screen and (min-width: 320px) {
  .ax_calc_App {
    .ax_calc_container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      margin: 30px auto 0;
      padding: 0;
      background-color: #fff;
    }

    .ax_calc_sidebar {
      width: 100%;
      display: table;
      margin: 30px auto 0;
      padding: 0;
      background-color: #fff;
    }

    .ax_calc_inner_container {
      width: 100%;
      width: -webkit-calc(100% - 30px);
      width: -moz-calc(100% - 30px);
      width: -o-calc(100% - 30px);
      width: -ms-calc(100% - 30px);
      width: calc(100% - 30px);
      display: flex;
      flex-wrap: wrap;
      margin: 30px auto;
      padding: 15px;
      border: 1px solid #8acbc8;
      background-color: #fff;
      box-sizing: border-box;
    }

    .ax_calc_dropdown_row {
      width: 100%;
      display: flex;
      margin-bottom: 0.8rem;
    }

    .ax_calc_row {
      p {
        font-family: $body-font;
        font-size: 1.3rem;
        font-weight: 800;
        line-height: 1.8rem;
      }

      h3 {
        font-size: 1.4rem;
        line-height: 1.8rem;
      }

      label {
        display: table;
        float: left;
        line-height: 30px;
        color: $base-color;
        font-weight: 700;
        margin-bottom: 0;
        font-size: 16px;
        width: auto !important;
      }
    }
    .ax_calc_row_inner {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-end;
      padding: 0;
    }

    .ax_calc_left_col,
    .ax_calc_right_col {
      width: 100%;
      display: block;
      float: left;

      > h3 {
        margin: 0.8rem 0 !important;
      }
    }

    .ax_calc_left_col {
      height: 100%;
      padding-left: 1.6rem;
    }

    .ax_calc_col_20,
    .ax_calc_col_25,
    .ax_calc_col_33,
    .ax_calc_col_50 {
      width: 100%;
      display: table;
      float: left;
      h3 {
        margin: 0.8rem 0;
      }
    }

    .ax_calc_col_20 input[type='text'],
    .ax_calc_col_25 input[type='text'],
    .ax_calc_col_33 input[type='text'],
    .ax_calc_col_50 input[type='text'] {
      width: calc(100% - 1.6rem) !important;
    }

    .ax_calc_col_20 {
      h3 {
        .inlineInput {
          margin: 0 !important;
          height: 2.1rem !important;
          width: 4.5rem !important;
          background: $light-gray;
          padding: 0 !important;
          font-family: $display-font;
          font-size: 1.2rem !important;
          font-weight: 700;
        }
      }
    }

    .ax_calc_right_col p {
      color: $base-color;
      font-family: $display-font;
      padding-right: 1.5rem;
      font-size: 1.3rem;
      font-weight: 800;
      margin-bottom: 5px;
      text-align: center;
    }

    .ax_calc_left_col p {
      color: $base-color;
      font-family: $display-font;
      font-weight: 700;
      margin-bottom: 0;
      display: flex;
      align-items: center;
      padding-top: 0;
    }

    .ax_calc_asking_section {
      padding-bottom: 15px;
      display: flex;
      align-items: center;
      border-bottom: 3px solid #8acbc8;
    }
    .ax_calc_asking_section h2 {
      color: $base-color;
      margin: 0;
    }
    .ax_calc_cash_needed_item {
      width: 100%;
      display: table;
      height: 30px;
      font-size: 14px;
      border-bottom: thin solid #8acbc8;
      padding: 3px 0;

      label {
        display: table;
        float: left;
        line-height: 30px;
        color: $base-color;
        font-weight: 700;
        margin-bottom: 0;
        font-size: 16px;
      }
      input {
        display: table;
        float: right;
        height: 30px;
        text-align: right;
        padding-right: 8px;
        border: none !important;
        background-color: initial !important;
        color: $base-color;
        font-size: 16px;
      }
    }

    .cash_needed_totals {
      height: 40px;
      border-top: 3px solid #8acbc8;
      border-bottom: none;
      label {
        display: table;
        float: left;
        line-height: 40px;
        color: $base-color;
        font-weight: 700;
        margin-bottom: 0;
        font-size: 18px;
      }
      input {
        display: table;
        float: right;
        height: 40px;
        border: none !important;
        color: $base-color !important;
        font-weight: 700;
        font-size: 18px;
        background-color: initial !important;
        text-align: right !important;
      }
    }

    .expense input[type='text'] {
      width: 150px !important;
      background-color: #fff !important;
      border: 1px solid #8acbc8 !important;
      position: relative;
      font-size: 16px !important;
    }
    table {
      input[type='text'] {
        background: transparent !important;
        border: none !important;
        color: $base-color !important;
        text-align: center !important;
      }
    }
    .ax_calc_dropdown_section {
      margin-top: 30px;
      h2 {
        margin-top: 0;
      }
    }

    .dropdown_toggle {
      color: $base-color;
      border-bottom: 3px solid #8acbc8;
      position: relative;
      cursor: pointer;
      margin: 0;

      h2 {
        margin: 0;
      }

      &:hover {
        color: $highlight-color;
        -webkit-transition: all 02s ease-out;
        transition: all 02s ease-out;
      }

      .dropdown_button {
        display: block;
        border: none;
        border-radius: 50%;
        width: 2.4rem;
        height: 2.4rem;
        background-color: $light-green;
        line-height: 20px;
        text-align: center;
        position: absolute;
        right: 0;
        top: -0.8rem;
        margin: 0;
        padding: 0;
        color: #fff;
        z-index: 50;
        transform-origin: center;
        transition: all 0.2s ease-out;
        cursor: pointer;
      }

      .dropdown_button_up {
        transform: rotate(180deg) !important;
      }
    }

    .dropdown_body {
      display: none;
      -webkit-transition: all 0 2s ease-out;
      transition: all 0 2s ease-out;
    }

    .show_dropdown_body {
      display: block !important;
      width: 100%;

      .ax_calc_col_20,
      .ax_calc_col_25,
      .ax_calc_col_33,
      .ax_calc_col_50 {
        h3 {
          margin: 0.8rem 0;
        }
      }
    }

    .ax_calc_downpay_label {
      padding-top: 0;
    }

    .ax_amortization_table {
      position: relative;
      display: table;
      width: 100%;
    }

    /* .ax-table-box {
      width: 100%;
      height: 400px;
      overflow-y: scroll;
    } */

    .ax_table_head {
      color: #fff !important;
      font-size: 16px;
      width: 100%;

      th {
        color: #fff !important;
        text-align: center;
        font-weight: 600;
        background-color: $base-color;
        width: '20%';
        height: 38px;
        color: '#000';
        text-align: 'center';
        padding: '0 15px';
      }
    }

    .mortgage_totals {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 45px;
      z-index: 10;
      background-color: $base-color;
      padding-bottom: 45px;
    }

    tfoot {
      width: 100%;
    }

    .total_row {
      background-color: $base-color;
      color: #fff !important;
      font-size: 16px;
      font-weight: 700;
      line-height: 45px;
      width: 20%;
      text-align: center !important;

      input[type='text'] {
        color: #fff !important;
        text-align: center !important;
        font-size: 16px !important;
        line-height: 45px !important;
        text-align: center !important;
        font-weight: 700;
      }
    }

    .ax-amortization-chart {
      display: table;
      width: 100%;
      height: 280px;
      position: relative;
    }
  }
}

@media screen and (min-width: 992px) {
  .ax_calc_App {
    .ax_calc_container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
    }
    .ax_calc_sidebar {
      width: 100%;
      display: table;
      margin: 30px auto 0;
      padding: 0;
      background-color: #fff;
    }
    .ax_calc_left_col {
      width: 30%;
    }
    .ax_calc_right_col {
      width: 70%;
    }
    .ax_calc_col_25 {
      width: 25%;
    }
    .ax_calc_col_33 {
      width: 33%;
    }
    .ax_calc_col_20 {
      width: 20%;
    }
    .ax_calc_col_50 {
      width: 50%;
    }
  }
}

@media screen and (min-width: 1364px) {
  .ax_calc_App {
    .ax_calc_container {
      width: 100%;
    }
  }
}

.ax_calc_no_margin {
  margin: 0;
}

.sampleCol {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  background: $light-gray;
  padding: 1.6rem;
  border-radius: 0.6rem;

  img {
    width: 100%;
    max-width: 100%;
    height: auto;
    margin-bottom: 1.6rem;
  }
}

// Scenario Selector styles
.ax_calc_scenario_selector {
  margin: 20px 0;
  padding: 15px 20px;
  background-color: #f9f9f9;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ax_calc_scenario_title {
  font-size: 16px;
  margin-bottom: 15px;
  font-weight: 600;
  color: #333;
}

.ax_calc_scenario_tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.ax_calc_scenario_tab {
  flex: 1;
  min-width: 150px;
  padding: 10px 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  text-align: center;
  color: #333;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:hover {
    background-color: #f0f0f0;
    border-color: #ccc;
  }
}

.ax_calc_scenario_tab_active {
  background-color: #29c6be;
  border-color: #29c6be;
  color: white;

  &:hover {
    background-color: #26b5ae;
    border-color: #26b5ae;
  }
}

.ax_calc_scenario_badge {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  font-size: 11px;
  border-radius: 10px;
  background-color: rgba(255, 255, 255, 0.3);
  color: currentColor;
  font-weight: 600;
}

.ax_calc_scenario_info {
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  border-left: 3px solid #29c6be;
  font-size: 13px;
  color: #555;
  line-height: 1.4;
}

.ax_calc_note {
  font-size: 12px;
  color: #666;
  font-style: italic;
  margin-top: 5px;
}

.ax_calc_error {
  font-size: 12px;
  color: #e53935;
  margin-top: 5px;
}
