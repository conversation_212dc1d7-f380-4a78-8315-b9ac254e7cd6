@import 'theme';
@import 'mixins';

.heading{
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  h3{
    display: block;
    width: 100%;
    font-size: 2.4rem;
    font-family: $display-font;
    font-weight: 600;
    margin: 0 0 3.2rem 0;
  }
}

.widgetSection{
  width: calc(50% - 3.2rem);
  display: flex;  
}


.ax_card_list{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;    
}

@include responsive(desktop){
  .widgetSection{
    width: calc(50% - 3.2rem);
    display: flex;  
  
    .ax_card_list {
      > div {
        width: calc(20% - 3.2rem) !important;
        display: block;     
      }
    }
  }
}

@include responsive(tablet){
  .widgetSection{
    width: calc(50% - 3.2rem);
    display: flex; 

    .ax_card_list {
      > div {
        width: calc(25% - 3.2rem) !important;
      }
    }
  }
}

@include responsive(mobile){
  .widgetSection{
    width: 100%;
    display: flex; 

    .ax_card_list {
      > div {
        width: calc(25% - 3.2rem) !important;
      }
    }
  }
}
