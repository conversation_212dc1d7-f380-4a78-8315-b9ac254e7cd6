@import 'theme';
@import 'mixins';

.ax_signature {
  margin: 0 0 3.2rem 0;
  font-family: arial, verdana, sans-serif;
  padding: 1.6rem;
  background: $bright-color;
  border-radius: 0.8rem;
}

.ax_btn_copy,
.ax_btn_html {
  height: 4rem;
  border-radius: 0.4rem;
  background: $highlight-color;
  color: $bright-color;
  font-family: $display-font;
  font-size: 1.6rem;
  font-weight: 600;
  border: none;
  padding: 0 16px;
  float: right;
  margin-left: 1.6rem;
  cursor: pointer;
}

.ax_btn_html {
  cursor: pointer;
  background: transparent;
  color: $highlight-dark;
  border: 0.1rem solid $highlight-dark;
}

.htmlCodeContainer {
  max-height: 60vh; /* Limit to 60% of viewport height */
  overflow: auto; /* Enable scrolling in both directions */
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin: 0 0 15px 0; /* Add bottom margin to ensure space for buttons */
  width: 100%;
  box-sizing: border-box;

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: monospace;
    font-size: 12px;
    line-height: 1.5;
    margin: 0;
    tab-size: 2;
    -moz-tab-size: 2;
  }
}

.signatureButtons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: flex-start;
  flex-direction: row-reverse;
}

.ax_btn_html {
  background-color: transparent;
  color: $highlight-color;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s;
  border: 0.1rem solid $highlight-color;
}
