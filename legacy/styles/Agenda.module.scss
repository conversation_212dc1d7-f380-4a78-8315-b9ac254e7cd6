@import 'theme';
@import 'mixins';

.agenda {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .day {
    padding: 3.2rem;
    border: 0.2rem solid $mid-gray;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    margin: 0 3.2rem 5.4rem 3.2rem;

    h3 {
      font-size: 3.2rem;
      font-family: $display-font;
    }

    .activity {
      display: flex;
      flex-direction: column;
      margin-bottom: 1.6rem;
      h4 {
        font-size: 1.8rem;
        font-family: $display-font;
        color: $highlight-dark;
        margin: 0;
        text-transform: uppercase;
        font-weight: 600;
      }
      p {
        font-size: 1.6rem;
        font-family: $display-font;
        margin: 0.4rem 0 1.6rem 0;
        font-weight: 600;
      }
    }
  }

  .singleCol {
    width: calc(33% - 6.4rem);

    .activity {
      width: 100%;
    }
  }
  .wideCol {
    width: 100%;
    height: 28rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;

    .activityList {
      width: calc(77% - 3.2rem);
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .activity {
      width: calc(33.3% - 3.2rem);
      margin-right: 3.2rem;
    }
  }

  @include responsive(mobile) {
    .singleCol,
    .wideCol {
      width: 100%;
      height: auto !important;
      align-items: flex-start;

      .activityList {
        width: 100%;
        height: auto !important;
      }
      .activity {
        width: 100%;
        text-align: center;
      }
    }

    .day {
      width: 100%;
      text-align: center;

      h3 {
        width: 100%;
      }
    }
  }
}
