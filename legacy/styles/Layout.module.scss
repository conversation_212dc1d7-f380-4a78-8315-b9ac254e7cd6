@import 'theme';

.ax_layout {
  width: 100vw;
  display: grid;
  grid-template-areas:
    'topbar topbar'
    'menu main';
  position: relative;

  button {
    svg {
      pointer-events: none;
    }
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: $display-font;
  }

  p {
    font-family: $body-font;
  }
}

@media screen and (min-width: 320px) and (max-width: 991px) {
  .ax_layout {
    grid-template-areas:
      'topbar topbar'
      'main main';
  }
}
