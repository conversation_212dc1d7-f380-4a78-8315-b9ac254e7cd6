@import 'theme';
@import 'mixins';

.table {
  width: 100%;

  table {
    display: table;
    font-family: $display-font;
    background: $bright-color;
    padding: 0;
    border-radius: 0.8rem;
    margin-bottom: 3.2rem;
    width: 100%;

    thead,
    tbody {
      width: 100%;

      tr {
        th {
          font-size: 1.2rem;
          border-bottom: 0.2rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0 0.8rem;
          text-align: left;
          color: $highlight-dark;
        }

        td {
          font-size: 1.2rem;
          border-bottom: 0.1rem solid $mid-gray;
          height: 3.4rem;
          vertical-align: middle;
          line-height: 1.4rem;
          padding: 0.4rem 0.8rem;
          transition: all 0.2s ease-out;
          min-width: 10rem;
        }

        &:hover {
          td {
            background-color: $light-gray;
          }
        }
      }
    }
  }
}
