@import 'theme';
@import 'mixins';

.list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  background: $bright-color;
  padding: 1.6rem;
  border-radius: 0.8rem;

  > h2,
  a {
    width: 100%;
    display: block;
    font-family: $display-font;
    color: $highlight-dark;
    font-size: 3.2rem;
    text-align: left;
    list-style: none;
  }

  .listItem {
    width: 100%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: space-between;
    padding: 1.6rem 0;
    border-bottom: 0.1rem solid $mid-gray;

    &:hover {
      background: $light-gray;
    }

    h3,
    a {
      width: 100%;
      display: block;
      font-family: $display-font;
      font-size: 1.4rem;
      margin-bottom: 0;
      color: $gray;
      font-weight: 400;

      span {
        color: $gray;
        font-weight: 600;
      }

      small {
        padding: 0.2rem 0.4rem;
        border-radius: 0.8rem;
        font-weight: 700;
        font-size: 1.4rem;
        color: $teal;
        background: $light-gray;
      }
    }

    .description {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      border-radius: 0 0.8rem 0.8rem 0;
      padding: 0;

      h2,
      a {
        width: 100%;
        display: block;
        font-family: $display-font;
        font-size: 1.8rem;
        line-height: 2.4rem;
        color: $highlight-dark;
        margin: 0;
        padding: 0;
        transition: all 0.2s ease-out;
        font-weight: 700;
      }

      p {
        font-family: $body-font;
        font-size: 1.6rem;
        color: $base-color;
      }
    }

    &:hover {
      .description {
        > h2,
        a {
          color: $base-color;
          cursor: pointer;
        }
      }
    }

    @include responsive(mobile) {
      .description {
        width: 100%;
        border-radius: 0.8rem;
        > div {
          width: 100%;
          p {
            width: 100%;
          }
        }
      }
    }
  }

  @include responsive(mobile) {
    > h2 {
      text-align: center;
    }
  }
}

.viewSwitcher {
  width: 100%;
  padding: 0.8rem 0;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 0.8rem;
}
