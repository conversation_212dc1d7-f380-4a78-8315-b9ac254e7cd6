@import 'theme';
@import 'mixins';

.eventBanner,
.eventBannerSvg {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  position: relative;

  img {
    display: block;
    width: 100%;
    height: auto;
    z-index: 0;
  }

  .content {
    position: absolute;
    bottom: 0;
    left: 1.66%;
    z-index: 2;
    padding: 2rem 1.5rem;
    width: 100%;
  }

  &:before {
    content: '';
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1;
  }

  h2 {
    font-size: 3.6rem;
    line-height: 4rem;
    font-family: $display-font;
    color: #fff;
    position: relative;
    z-index: 2;
    font-weight: 700;
    margin: 0 2.4rem 0 0;
    padding: 0.8rem 0.5rem;
    background-color: rgba(0, 0, 0, 0.8);
    display: inline-block;

    @media screen and (min-width: 992px) {
      font-size: 4.1rem;
      line-height: 5.6rem;
    }

    @media screen and (min-width: 1200px) {
      font-size: 5.4rem;
    }
  }

  h3 {
    font-size: 2.4rem;
    font-family: $display-font;
    color: #fff;
    position: relative;
    text-transform: uppercase;
    z-index: 2;
    font-weight: 500;
    margin: 0;
    letter-spacing: 0.2rem;
    background-color: rgba(0, 0, 0, 0.8);
    padding: 0.8rem 1rem;
    display: inline-block;
  }
}

.eventBannerSvg {
  min-height: 4rem;
  padding: 0;

  &:before {
    background: transparent;
  }
}
