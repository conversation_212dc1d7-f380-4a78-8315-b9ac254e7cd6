@import 'theme';
@import 'mixins';

.switcher {
  display: flex;
  flex-direction: column;
  margin-bottom: 2.4rem;

  .iconHeading {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: bottom;
    img {
      width: 1.6rem;
      height: 1.6rem;
      display: block;
      margin-right: 0.4rem;
    }
    label {
      width: 100%;
      display: block;
      font-family: $display-font;
      font-size: 1.4rem;
      line-height: 1.4rem;
      color: $base-color;
      font-weight: 600;
      margin-bottom: 0.4rem;
    }
  }

  label {
    width: 100%;
    display: block;
    font-family: $display-font;
    font-size: 1.4rem;
    line-height: 1.4rem;
    color: $base-color;
    font-weight: 600;
    margin-bottom: 0.4rem;
  }

  input {
    width: 4.8rem;
    height: 2.4rem;
    transition: all 0.2s ease-out;
    background: transparent;
    appearance: none;
    position: relative;
    z-index: 3;
    margin: 0;
    cursor: pointer;

    &:before {
      content: 'OFF\00a0';
      width: 5.2rem;
      height: 2.4rem;
      border-radius: 1.2rem;
      background: red;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      color: #fff;
      font-weight: 600;
      text-align: right;
      text-indent: 0.4rem;
      line-height: 2.4rem;
      font-size: 1.2rem;
    }

    &:after {
      content: '';
      width: 2.2rem;
      height: 2.2rem;
      border-radius: 50%;
      background: #f2f2f2;
      display: block;
      position: absolute;
      left: 0.1rem;
      top: 0.1rem;
      z-index: 2;
      transition: all 0.2s ease-out;
    }
  }

  input[checked] {
    &:before {
      content: 'ON';
      background: $highlight-color;
      color: #fff;
      font-weight: 600;
      text-align: left;
      text-indent: 0.4rem;
      line-height: 2.4rem;
      font-size: 1.2rem;
    }

    &:after {
      content: '';
      left: 2.9rem;
    }
  }
}

.labelLeft {
  flex-direction: row;
  justify-content: flex-start;

  label {
    width: auto;
    margin-right: 0.8rem;
    display: inline-block;
    text-align: left;
    line-height: 2.2rem;
  }
}
