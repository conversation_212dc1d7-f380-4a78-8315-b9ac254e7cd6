@import 'theme';

@media screen and (max-width: 1200px) {
  .commandWrapper {
    display: none;
  }
}

.commandWrapper {
  width: 100%;
  max-width: 60rem;
  background: $light-gray;
  border: 0.2rem solid $mid-gray;
  border-radius: 0.8rem;
  overflow: hidden;
  align-self: flex-start;
  margin: 0.8rem auto;
}

.searchHeader {
  display: flex;
  align-items: center;
  transform: translateY(0.1rem);
  padding: 0 1.6rem;
  border-bottom: 0.1rem solid $mid-gray;

  svg {
    color: $gray;
    width: 1.6rem;
    height: 1.6rem;
    margin-right: 1.2rem;
  }

  input {
    width: 100%;
    height: 4.8rem;
    border: none;
    background: transparent;
    font-family: $body-font;
    font-size: 1.4rem;
    color: $base-color;
    outline: none;

    &::placeholder {
      color: $dark-gray;
    }
  }
}

.resultsList {
  max-height: 30rem;
  overflow-y: auto;
  padding: 0.8rem;

  &:empty {
    display: none;
  }
}

.emptyState {
  padding: 3.2rem 1.6rem;
  text-align: center;
  color: $gray;
  font-family: $body-font;
  font-size: 1.4rem;
}

.loadingState {
  padding: 3.2rem 1.6rem;
  text-align: center;
  color: $gray;
  font-family: $body-font;
  font-size: 1.4rem;
}

.resultItem {
  width: 100%;
  padding: 1.2rem;
  border-radius: 0.4rem;
  cursor: pointer;
  transition: all 0.2s $cubic-transition;

  &:hover,
  &:focus {
    background: #fff;
  }

  .resultHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.4rem;

    .title {
      font-family: $display-font;
      font-size: 1.4rem;
      color: $base-color;
      font-weight: 600;
    }

    .badge {
      background: $low-gray;
      padding: 0.2rem 0.8rem;
      border-radius: 1rem;
      font-family: $body-font;
      font-size: 1.2rem;
      color: $gray;
    }
  }

  .description {
    font-family: $body-font;
    font-size: 1.3rem;
    color: $gray;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
