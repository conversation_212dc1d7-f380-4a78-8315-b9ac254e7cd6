@import 'theme';
@import 'mixins';

.widgetArea {
  display: flex;
  padding: 1.6rem;
  border: 0.2rem dashed $gray;
  border-radius: 0.8rem;
  margin-bottom: 3.2rem;
}

@include responsive(desktop) {
  .widgetArea {
    width: calc(50% - 3.2rem);
  }
}

@include responsive(tablet) {
  .widgetArea {
    width: calc(50% - 3.2rem);
  }
}

@include responsive(mobile) {
  .widgetArea {
    width: 100%;
  }
}

.widget {
  padding: 1.6rem;
  border-radius: 0.8rem;
  background: #fff;
  box-shadow: $shadow;
}
