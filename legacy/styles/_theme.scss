/* _theme.scss */
$highlight-color: #3caba7;
$highlight-dark: #2a7a94;
$display-font: 'herobold', sans-serif;
$body-font: 'Quicksand', sans-serif;
$base-color: #000;
$base-mid-color: #414141;
$gray: #666;
$mid-gray: #d7dfe9;
$low-gray: #edf1f6;
$light-gray: #f2f5f9;
$dark-gray: #a6b3c4;
$superlight-gray: #f7faff;
$light-green: #aae2e0;
$bright-color: #fff;
$shadow: 2px 2px 8px rgba(45, 45, 52, 0.35);
$shadow-large: 8px 8px 24px rgba(45, 45, 52, 0.35);
$cubic-transition: cubic-bezier(0, 0.83, 0.42, 1.01);
$purple: #7020bc;
$orange: #e2591b;
$green: #8eb440;
$grassgreen: #22882e;
$yellow: #e6b800;
$blue: #2d4bd2;
$light-blue: #6da2ed;
$teal: #00a8a8;
$dark: #414141;
$red: #de1600;
$gradient-green: linear-gradient(135.91deg, #d3e062 2.47%, #cddc4c 98.32%);
$gradient-purple: linear-gradient(135.88deg, #b685ff 1.49%, #684cb7 100%);
$gradient-red: linear-gradient(135.22deg, #c72f20 2.65%, #ad0707 100%);
$gradient-teal: linear-gradient(135.91deg, #00d2ac 2.47%, #478482 98.32%);
$gradient-blue: linear-gradient(135.91deg, #00a0df 2.47%, #6d9db8 98.32%);
$gradient-blue-dark: linear-gradient(135.91deg, #00a0df 2.47%, #6d9db8 98.32%);
$gradient-bronze: linear-gradient(135.91deg, #ffe53b 2.47%, #754500 98.32%);
$gradient-lilac: linear-gradient(135.91deg, #bbaed0 2.47%, #a898c2 98.32%);
$gradient-pink: linear-gradient(135.91deg, #ffbbec 2.47%, #84003f 98.32%);
$gradient-yellow: linear-gradient(135.91deg, #dcbb00 2.47%, #c6a143 98.32%);
$gradient-dark: linear-gradient(135.91deg, #bbaed0 2.47%, #a898c2 98.32%);
$gradient-grey: linear-gradient(135.91deg, #fafdff 2.47%, #dde9ef 98.32%);
$radial-gradient-green: radial-gradient(circle, #dfff52 2.47%, #5c800b 98.32%);
$radial-gradient-purple: radial-gradient(circle, #f785ff 1.49%, #230086 100%);
$radial-gradient-red: radial-gradient(circle, #ffc573 2.65%, #ad0707 100%);
$radial-gradient-teal: radial-gradient(circle, #58fcd6 2.47%, #009176 98.32%);
$radial-gradient-blue: radial-gradient(circle, #5effea 2.47%, #023986 98.32%);
$radial-gradient-blue-dark: radial-gradient(circle, #2ed5ff 2.47%, #002d7a 98.32%);
$radial-gradient-bronze: radial-gradient(circle, #ffe53b 2.47%, #754500 98.32%);
$radial-gradient-lilac: radial-gradient(circle, #fcabff 2.47%, #271d50 98.32%);
$radial-gradient-pink: radial-gradient(circle, #ffbbec 2.47%, #84003f 98.32%);
$radial-gradient-yellow: radial-gradient(circle, #fffc69 2.47%, #dc8400 98.32%);
$radial-gradient-dark: radial-gradient(circle, #958aff 2.47%, #14253e 98.32%);
$radial-gradient-grey: radial-gradient(circle, #ebf8ff 2.47%, #cde9f4 98.32%);

@media screen and (min-width: 0) and (max-width: 767px) {
  .ax_section {
    p {
      font-size: 1.6rem;
      font-family: $body-font;
    }
  }

  .ax_container {
    width: 90%;
    display: block;
    margin: 0 auto;

    p {
      font-family: $body-font;
      font-size: 1.4rem;
      color: $base-color;
    }
  }
}

@media screen and (min-width: 768px) and (max-width: 1199px) {
  .ax_section {
    p {
      font-size: 1.6rem;
      font-family: $body-font;
    }
  }

  .ax_container {
    width: 74rem;
    display: block;
    margin: 0 auto;
  }
}

@media screen and (min-width: 1200px) {
  .ax_section {
    p {
      font-size: 1.6rem;
      font-family: $body-font;
    }
  }

  .ax_container {
    width: 114rem;
    display: block;
    margin: 0 auto;
  }
}

.ax_page_title {
  font-family: $display-font;
  font-size: 3.2rem;
  color: $base-color;
  text-transform: capitalize;

  span {
    color: $highlight-color;
    font-weight: 600;
  }
}

.ax_page_subtitle {
  font-family: $display-font;
  font-size: 2.4rem;
  color: $highlight-dark;
  display: flex !important;
  align-items: center !important;
  span {
    color: $highlight-color;
    font-weight: 600;
    line-height: 2.4rem;
    margin-right: 0.8rem;
    svg {
      height: 2.4rem;
      margin: 0;
      padding: 0;
      line-height: 2.4rem;
    }
  }
}

.ax_field {
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: 1.6rem;

  label {
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 700;
    display: block;
    width: 100%;
    margin-bottom: 0;

    span {
      color: red;
      font-weight: 800;
      font-size: 1.6rem;
    }
  }
}

.ax_form {
  width: 100%;
  .ax_form_heading {
    width: 100%;
    margin-bottom: 1.6rem;

    p {
      font-size: 1.6rem;
      font-family: $body-font;
    }
  }

  .ax_field {
    display: flex;
    flex-direction: column;
    position: relative;

    label {
      color: $highlight-dark;
      font-family: $display-font;
      font-size: 1.4rem;
      font-weight: 700;
      display: block;
      width: 100%;
      margin-bottom: 0.4rem;

      span {
        color: red;
        font-weight: 800;
        font-size: 1.6rem;
      }

      .popup {
        display: inline-block;
        width: 1.6rem;
        height: 1.6rem;
        border-radius: 50%;
        background: $base-color;
        color: white;
        font-size: 1.1rem;
        vertical-align: bottom;
        line-height: 1.6rem;
        text-align: center;
        position: relative;
        cursor: pointer;

        > .popupContent {
          display: none;
          width: 20rem;
          padding: 1.2rem;
          background: $base-color;
          color: white;
          font-size: 1.1rem;
          line-height: 1.4rem;
          font-family: $body-font;
          position: absolute;
          left: 0;
          bottom: 2rem;
          z-index: 100;
          border-radius: 0.3rem;
          text-align: left;
        }

        &:hover {
          color: $highlight-color;

          > .popupContent {
            display: block;
          }
        }
      }
    }

    textarea,
    input[type='text'],
    input[type='email'],
    input[type='password'],
    input[type='number'],
    input[type='tel'],
    input[type='date'],
    input[type='date'],
    input[type='time'],
    input[type='number'],
    input[type='url'],
    select {
      width: 100%;
      border-radius: 0.4rem;
      border: 0.1rem solid $mid-gray;
      background: $light-gray;
      padding: 0 4rem 0 1.6rem;
      color: $base-color;
      font-family: $body-font;
      font-size: 1.6rem;
      display: inline-block;
      margin-bottom: 1.6rem;
      font-weight: 500;
    }

    input[type='text'],
    input[type='file'],
    input[type='email'],
    input[type='password'],
    input[type='number'],
    input[type='tel'],
    input[type='date'],
    input[type='time'],
    input[type='number'],
    input[type='url'],
    select {
      height: 4rem;

      &::placeholder {
        font-weight: 400;
        color: $mid-gray;
      }
    }

    textarea {
      padding: 1.2rem 4rem 0.8rem 1.6rem;
    }

    input[disabled] {
      cursor: not-allowed;
      color: $gray;
    }

    button.see {
      position: absolute;
      top: 2.2rem;
      right: 0;
      width: 4rem;
      height: 4rem;
      border: none;
      background: transparent;

      svg {
        color: $base-mid-color;
      }
    }

    small {
      font-family: $display-font;
      font-size: 1.4rem;
    }

    input[type='checkbox'] {
      margin-bottom: 1.6rem;
    }

    input[type='submit'] {
      height: 4rem;
      border-radius: 0.4rem;
      background: $highlight-color;
      color: $bright-color;
      font-family: $display-font;
      font-size: 1.6rem;
      font-weight: 600;
      border: none;
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin-left: 3.2rem;
    margin-bottom: 3.2rem;

    li {
      position: relative;
      padding-left: 0.8rem;
      display: block;
      font-family: $body-font;
      font-size: 1.6rem;
      line-height: 2.8rem;

      &:before {
        content: '';
        width: 0.8rem;
        height: 0.8rem;
        display: block;
        border-radius: 50%;
        position: absolute;
        top: 1rem;
        left: -0.8rem;
        z-index: 1;
        background-color: $light-green;
      }
    }
  }

  ol {
    padding: 0;
    margin-left: 3.2rem;
    margin-bottom: 3.2rem;

    li {
      position: relative;
      margin-left: 0.8rem;
      font-family: $body-font;
      font-size: 1.6rem;
      line-height: 2.8rem;

      &::marker {
        color: $highlight-dark;
      }
    }
  }

  pre {
    width: 100%;
    background: $light-gray;
    border-radius: 0.8rem;
    overflow-x: scroll;
    padding: 1.6rem;
    margin-bottom: 3.2rem;

    code {
      width: 100%;
      display: table;
      font-family: monospace;
      font-size: 1.4rem;
      line-height: 1.8rem;
    }
  }

  .ax_form_footer {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin: 1.6rem 0;
  }
}

.fieldGroup {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 1.6rem;

  label,
  h3,
  h4,
  h5,
  p {
    display: inline-block;
    flex-grow: 1;
    margin: 0 !important;
  }
  input[type='text'],
  select,
  input[type='number'] {
    width: 100%;
    flex-grow: 1;
  }
}

.ax_image_field {
  background: $light-gray;
  padding: 1.6rem;
  border-radius: 0.8rem;
  position: relative;

  h4 {
    font-size: 1.6rem;
    margin: 0.8rem 0;

    span {
      color: $red !important;
    }
  }

  p {
    margin: 0.4rem 0;
  }

  input[type='file'] {
    width: calc(100% - 12rem);
    height: 4.4rem;
    background: #fff;
    border: 0.1rem dashed $mid-gray;
    border-radius: 0.4rem;
    display: block;
  }

  button {
    width: 10rem;
    height: 4rem;
    background: $highlight-color;
    color: #fff;
    border: none;
    border-radius: 0.4rem;
    font-family: $display-font;
    font-size: 1.4rem;
    font-weight: 700;
    margin-top: 0;
    svg {
      fill: #fff;
    }
  }
}

.code {
  font-family: monospace;
  font-size: 1.4rem;
  line-height: 1.8rem;
}

.ax_card_list {
  width: 100%;
  display: flex;
  gap: 3.2rem;
  flex-direction: row;
  justify-content: flex-start;
  gap: 0.8rem;
  flex-wrap: wrap;

  > h1,
  h3,
  h3,
  h4 {
    font-family: $display-font;
  }
}

.boxedLoading {
  width: 100%;
  height: 100%;
  background: $light-green;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 500;
}

.primary {
  color: $highlight-color;
}

.base {
  color: $base-color;
}

.red,
.error {
  color: #850707;
}

.purple {
  color: $purple;
}
.orange {
  color: $orange;
}
.green {
  color: $green;
}
.yellow {
  color: $yellow;
}
.blue {
  color: $blue;
}
.teal {
  color: $teal;
}

.validation {
  width: 100%;
  display: block;
  padding: 1.6rem;
  background: transparentize(red, 0.9);
  border: 0.1rem solid transparentize(red, 0.7);
  border-radius: 0.8rem;

  h3,
  h4 {
    font-family: $display-font;
    font-size: 1.6rem;
    margin: 0 0 1.6rem 0;
    color: #aa1133 !important;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding-left: 0.8rem;
      position: relative;
      color: #aa1133;
      font-family: $display-font;
      font-size: 1.2rem;
      line-height: 1.8rem;
      font-weight: 600;

      &:before {
        content: '';
        width: 0.4rem;
        height: 0.4rem;
        display: block;
        border-radius: 50%;
        background: #aa1133 !important;
        position: absolute;
        top: 0.6rem;
        left: 0;
      }
    }
  }
}

.success {
  width: 100%;
  display: block;
  padding: 1.6rem;
  background: transparentize($light-green, 0.9);
  border: 0.1rem solid transparentize($highlight-color, 0.7);
  border-radius: 0.8rem;

  h3,
  h4 {
    font-family: $display-font;
    font-size: 1.6rem;
    margin: 1.6rem 0;
    color: $highlight-dark !important;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      padding-left: 0.8rem;
      position: relative;
      color: $highlight-dark;
      font-family: $display-font;
      font-size: 1.2rem;
      line-height: 1.8rem;
      font-weight: 600;

      &:before {
        content: '';
        width: 0.4rem;
        height: 0.4rem;
        display: block;
        border-radius: 50%;
        background: $highlight-dark;
        position: absolute;
        top: 0.6rem;
        left: 0;
      }
    }
  }
}

.ax_wrapper {
  width: 100%;
  display: block;
  background: #fff;
  padding: 1.6rem;
  border-radius: 0.8rem;
}

.bordered {
  border: 0.1rem solid $mid-gray;
}

.rounded {
  border-radius: 0.8rem;
}

.contentBox {
  padding: 3.2rem;
  background: #fff;
  border-radius: 0.8rem;
  margin-bottom: 3.2rem;
  width: 100%;
  display: flex;
  flex-wrap: wrap;

  h2,
  h3,
  h4,
  h5,
  h6 {
    width: 100%;
    display: block;
    font-family: $display-font;
    color: $base-color;
    margin: 0 0 1.6rem 0;
  }

  h2 {
    font-size: 2.4rem;
    line-height: 2.8rem;
  }

  h3 {
    font-size: 2.1rem;
    line-height: 2.4rem;
  }

  h4 {
    font-size: 1.8rem;
    line-height: 2.1rem;
  }

  h5 {
    font-size: 1.6rem;
    line-height: 2.1rem;
  }

  h6 {
    font-size: 1.4rem;
    line-height: 1.8rem;
  }

  p {
    font-family: $body-font;
    font-size: 1.6rem;
    line-height: 2.1rem;
    color: $base-color;
    margin: 0 0 1.6rem 0;

    img {
      max-width: 100%;
      height: auto;
      display: inline-block;
    }

    a {
      color: $highlight-dark;
      text-decoration: underline;

      &:hover {
        color: $highlight-color;
      }

      img {
        max-width: 100%;
        height: auto;
        display: inline-block;
      }
    }
  }

  img {
    max-width: 100%;
    height: auto;
    display: inline-block;
  }

  ul {
    list-style: none;
    padding: 0;
    margin-left: 3.2rem;
    margin-bottom: 3.2rem;

    li {
      position: relative;
      padding-left: 0.8rem;
      display: block;
      font-family: $body-font;
      font-size: 1.6rem;
      line-height: 2.8rem;

      &:before {
        content: '';
        width: 0.8rem;
        height: 0.8rem;
        display: block;
        border-radius: 50%;
        position: absolute;
        top: 1rem;
        left: -0.8rem;
        z-index: 1;
        background-color: $light-green;
      }
    }
  }

  ol {
    padding: 0;
    margin-left: 3.2rem;
    margin-bottom: 3.2rem;

    li {
      position: relative;
      margin-left: 0.8rem;
      font-family: $body-font;
      font-size: 1.6rem;
      line-height: 2.8rem;

      &::marker {
        color: $highlight-dark;
      }
    }
  }

  pre {
    width: 100%;
    background: $light-gray;
    border-radius: 0.8rem;
    overflow-x: scroll;
    padding: 1.6rem;
    margin-bottom: 3.2rem;

    code {
      width: 100%;
      display: table;
      font-family: monospace;
      font-size: 1.4rem;
      line-height: 1.8rem;
    }
  }

  blockquote {
    border-left: 0.4rem solid $light-green;
    padding-left: 1.6rem;
    font-family: $display-font;
    font-weight: 600;
    margin: 3.2rem 0 3.2rem 4rem;

    p {
      color: $gray;

      a {
        color: $highlight-dark;
        text-decoration: underline;
      }
    }
  }
}

.showInDesktop {
  width: 100%;
}

@media (max-width: 767px) {
  .contentBox {
    padding: 1.6rem;
  }
  .showInMobile {
    display: inline-block;
  }
  .showInDesktop {
    display: none;
  }
}

@media (min-width: 768px) {
  .showInMobile {
    display: none;
  }
  .showInDesktop {
    display: inline-block;
  }
}

.sepparator {
  width: 100%;
  height: 0.2rem;
  background: $mid-gray;
  border-radius: 0.1rem;
  display: block;
}

.dragzone {
  width: 100%;
  min-height: 10rem;
  height: auto;
  display: block;

  position: relative;
  z-index: 1;

  span {
    width: 100%;
    line-height: 4.8rem;
    font-family: $display-font;
    color: $gray;
    font-weight: 700;
    pointer-events: none;
    position: absolute;
    top: 28%;
    text-align: center;
    z-index: 1;
    transition: all 0.2s ease-out;
  }

  .dragarea {
    width: 100%;
    min-height: 10rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background: $light-gray;
    border-radius: 0.8rem;
    border: 0.2rem dashed $mid-gray;
    z-index: 10;
    transition: all 0.2s ease-out;
    cursor: grab;

    &:hover {
      background: $dark-gray;
      cursor: grab;

      span {
        color: white;
      }
    }
  }
}

.alertNote {
  display: block;
  width: 100%;
  padding: 0.8rem 1.6rem;
  position: relative;

  svg {
    margin-right: 0.4rem;
    transform: translateY(3px);
  }
}

.alertInfo {
  background: #eefbff;
  border: 0.1rem solid $highlight-color;
  padding: 1.6rem;
  border-radius: 0.8rem;

  strong {
    color: $highlight-dark;
  }

  svg {
    fill: $highlight-dark;
  }

  > div {
    p:last-child {
      margin-bottom: 0;
    }
  }
}

.alertDanger {
  background: $bright-color;

  strong {
    color: $red;
  }

  &:before {
    background: $red;
  }
  svg {
    fill: $red;
  }
}

.alertWarning {
  background: $bright-color;

  strong {
    color: #dc8400;
  }

  &:before {
    background: #dc8400;
  }
  svg {
    fill: #dc8400;
  }
}

.img_responsive {
  width: 100%;
  max-width: 100%;
  height: auto;
}
