@import 'theme';
@import 'mixins';

.ax_main {
  grid-area: main;
  width: calc(100vw - 20rem);
  min-height: 100vh;
  padding: 7rem 1.6rem 1.6rem 1.6rem;
  background: $light-gray;
  position: relative;
  overflow: hidden;
  padding-bottom: 12rem;

  > h1,
  h3,
  h3,
  h4,
  h5,
  h6 {
    font-family: $display-font;
  }
}

@include responsive(tablet) {
  .ax_main {
    width: 100%;
  }
}

@include responsive(mobile) {
  .ax_main {
    width: 100%;
  }
}
