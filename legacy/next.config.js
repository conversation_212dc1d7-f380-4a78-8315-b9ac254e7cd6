const { PHASE_DEVELOPMENT_SERVER } = require('next/constants')
const { execSync } = require('child_process')

module.exports = (phase) => {
  const isDev = phase === PHASE_DEVELOPMENT_SERVER

  const getGitHash = () => {
    try {
      return execSync('git rev-parse HEAD').toString().trim().slice(0, 7)
    } catch (error) {
      console.warn('Failed to get git hash:', error)
      return 'unknown'
    }
  }

  const gitHash = getGitHash()
  const timestamp = Date.now()

  const config = {
    env: {
      API_URL: isDev ? 'http://127.0.0.1:1337' : 'https://axiomapi.herokuapp.com',
      BASE_URL: isDev ? 'http://127.0.0.1:3003' : 'https://indicentral.ca',
      BASE_URL_INDI: isDev ? 'http://127.0.0.1:3003' : 'https://indicentral.ca',
      AWS_BUCKET: 'indi-strapi-v2',
      AWS_BUCKET_URL: 'https://indi-strapi-v2.s3.us-east-1.amazonaws.com',
      MAINTENANCE_MODE: process.env.MAINTENANCE_MODE || 'false',
      MAINTENANCE_END_TIME: process.env.MAINTENANCE_END_TIME || ' Monday February 24th , 8:00AM (MST)',
      NEXT_PUBLIC_BUILD_ID: `build-${gitHash}-${timestamp}`,
      CLOUDFLARE_API_TOKEN: process.env.CLOUDFLARE_API_TOKEN,
      CLOUDFLARE_ZONE_ID: process.env.CLOUDFLARE_ZONE_ID,
      BUILD_TIME: new Date().toISOString()
    },
    generateBuildId: async () => {
      return `build-${gitHash}-${timestamp}`
    },
    images: {
      domains: ['res.cloudinary.com', 'indi-strapi.s3.amazonaws.com', '*.s3.amazonaws.com'],
      disableStaticImages: true
    },
    swcMinify: true,
    eslint: {
      ignoreDuringBuilds: true
    },
    experimental: {
      largePageDataBytes: 128 * 100000
    },
    webpack: (config, options) => {
      config.module.rules.push({
        test: /\.pdf$/i,
        type: 'asset/source'
      })
      return config
    },
    // Add redirects configuration for maintenance mode
    async redirects() {
      if (process.env.MAINTENANCE_MODE === 'true') {
        return [
          {
            source: '/:path((?!maintenance|_next|images|fonts|favicon.ico).*)',
            destination: '/maintenance',
            permanent: false
          }
        ]
      }
      return [] // Return empty array if maintenance mode is off
    },
    async headers() {
      return [
        {
          // Apply to all routes
          source: '/(.*)',
          headers: [
            {
              key: 'Cache-Control',
              value: 'no-store, max-age=0, must-revalidate'
            }
          ]
        },
        {
          // Apply stricter rules to JavaScript files to ensure they're never cached
          source: '/_next/static/chunks/(.*)',
          headers: [
            {
              key: 'Cache-Control',
              value: 'no-store, no-cache, must-revalidate, proxy-revalidate'
            }
          ]
        },
        {
          source: '/resources/document/:slug*',
          headers: [
            {
              key: 'Cache-Control',
              value: 'no-store, no-cache, must-revalidate, proxy-revalidate'
            },
            {
              key: 'Pragma',
              value: 'no-cache'
            },
            {
              key: 'Expires',
              value: '0'
            }
          ]
        },
        {
          source: '/compliance/document/:slug*',
          headers: [
            {
              key: 'Cache-Control',
              value: 'no-store, no-cache, must-revalidate, proxy-revalidate'
            },
            {
              key: 'Pragma',
              value: 'no-cache'
            },
            {
              key: 'Expires',
              value: '0'
            }
          ]
        },
        {
          source: '/printables/document/:slug*',
          headers: [
            {
              key: 'Cache-Control',
              value: 'no-store, no-cache, must-revalidate, proxy-revalidate'
            },
            {
              key: 'Pragma',
              value: 'no-cache'
            },
            {
              key: 'Expires',
              value: '0'
            }
          ]
        }
      ]
    }
  }

  if (!isDev) {
    config.headers = async () => [
      {
        source: '/',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store'
          }
        ]
      }
    ]
  }

  return config
}
