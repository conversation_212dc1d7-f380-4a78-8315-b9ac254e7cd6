import { useContext } from 'react'
import { useRouter } from 'next/router'
import Markdown from '../components/Markdown'
import style from '../styles/ContentPage.module.scss'
import InfiniteCarousel from './InfiniteCarousel'
import AuthContext from '../context/authContext'
import Button from './Button'
import { Container, Row, Col } from 'react-grid-system'

const StaticPageLayout = (props) => {
  const { data } = props
  const { userAuth } = useContext(AuthContext)
  const router = useRouter()

  const carousel = data?.features

  // Get userId from AuthContext or fallback to null
  const userId = userAuth?.userInfo?.id || null

  // Get current path
  const currentPath = router.asPath

  // Check if we're on the AOD page
  const isAODPage = currentPath === '/aod'

  // Construct the handoff URL with strapiId parameter
  const handoffUrl = process.env.AOD_HANDOFF_URL
    ? `${process.env.AOD_HANDOFF_URL}?strapiID=${userId}`
    : `https://forms.zohopublic.com/blumortgage4460621/form/AODHandoff/formperma/oG3XPuPhENDBo-bq99sVjdDC9gPCPYefQBMKeN6nSO4?strapiID=${userId}`

  return (
    <>
      <h1 className={style.ax_page_title}>{data.pageTitle}</h1>
      <section className={style.pageBanner}>
        <img src={data.topBanner.url} alt="Banner Image" />
        <div className={style.pageThumb}>
          <img src={data.pageThumbnail.url} alt={data.pageTitle} />
        </div>
      </section>
      <section className={style.pageContent}>
        <Container>
          <Row justify="center">
            <Col xs={12} md={10}>
              <Markdown>{data.pageContent}</Markdown>
              {data?.features.length > 0 && (
                <div className={style.features}>
                  <InfiniteCarousel
                    items={carousel}
                    autoScrollInterval={6000}
                    userId={userId}
                    currentPath={currentPath}
                  />
                </div>
              )}
            </Col>
            {/* Centralized AOD Handoff Button - Only show on AOD page */}

            {isAODPage && (
              <Col xs={12} md={10}>
                <Button
                  label="Go to AOD Handoff"
                  color="highlight"
                  sizing="large"
                  isLink
                  linkPath={handoffUrl}
                  blank
                  isCentered
                />
              </Col>
            )}
          </Row>
        </Container>
      </section>
    </>
  )
}

export default StaticPageLayout
