import style from '../styles/CardUser.module.scss'
import Link from 'next/link'
import { UilEditAlt } from '@iconscout/react-unicons'

const CardUser = (props) => {
  const {
    id,
    photo,
    firstname,
    lastname,
    titles,
    phone,
    email,
    clickEvent,
    editable,
    company,
    website,
    position,
    wide,
    shadow
  } = props

  return (
    <div
      className={`${style.cardUser} ${wide ? style.cardWide : ''} ${shadow ? style.shadow : ''}`}
      onClick={clickEvent}
    >
      <div className={style.photo} style={{ backgroundImage: `url(${photo})` }} alt="photo"></div>

      <div className={style.cardBody}>
        {firstname ? <h3>{`${firstname} ${lastname ? lastname : ''} ${titles ? ', ' + titles : ''}`}</h3> : ''}
        {editable ? (
          <Link href={`/realtors/${id}`} legacyBehavior>
            <a className={style.editButton}>
              <UilEditAlt size={16} /> Edit Realtor
            </a>
          </Link>
        ) : (
          ''
        )}
        {position ? <h4>{position}</h4> : ''}
        {company ? (
          <p>
            <strong>At: </strong>
            {company}
          </p>
        ) : (
          ''
        )}
        {email ? (
          <p>
            <strong>E-mail: </strong>
            {email}
          </p>
        ) : (
          ''
        )}
        {phone ? (
          <p>
            <strong>Phone: </strong>
            {phone}
          </p>
        ) : (
          ''
        )}
        {website ? (
          <p>
            <strong>Website: </strong>
            {website}
          </p>
        ) : (
          ''
        )}
      </div>
    </div>
  )
}

export default CardUser
