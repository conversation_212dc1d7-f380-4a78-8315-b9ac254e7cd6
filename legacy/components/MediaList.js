import style from '../styles/ContentPage.module.scss'

const MediaList = (props) => {
  const { media, downloadItem, source } = props

  const sub = source !== null && source !== undefined ? source.slice(0, 16) : source

  console.log(source, sub)

  const title = (str) => {
    if (str !== undefined && str !== '' && str !== null) {
      const newTitle = []
      for (let i = 0; i < str.length; i++) {
        newTitle.push(/[A-Z]/g.test(str[i]) ? ` ${str[i]}` : str[i])
      }
      const clearTitle = newTitle.join('')
      return clearTitle
    }

    return ''
  }

  return media.map((m) => {
    console.log('THUMB', m.media)

    const thumbpath =
      m.media && m.media.formats && m.media.formats.thumbnail && m.media.formats.thumbnail.url
        ? m.media.formats.thumbnail.url
        : '/images/ico-img.svg'

    if (source && sub === '/brand-materials') {
      return (
        <div className={style.card} key={m.id}>
          <a href={m.media.url} download target="_blank">
            <img
              src={thumbpath}
              alt={title(m.media.name)}
              style={m.thumb && m.thumb.url ? { width: '100%' } : { width: '50%', marginTop: '30%' }}
            />
          </a>
          <h3>{title(m.media.name)}</h3>

          <a className={style.btn} href={m.media.url} target="_blank">
            Download
          </a>
        </div>
      )
    }

    return (
      <div className={style.card} key={m.id}>
        <a href={m.media[0].url} download target="_blank">
          <img
            src={m.thumb && m.thumb.url ? m.thumb.url : '/images/ico-img.svg'}
            alt={title(m.media[0].name)}
            style={m.thumb && m.thumb.url ? { width: '100%' } : { width: '50%', marginTop: '30%' }}
          />
        </a>
        <h3>{title(m.media[0].name)}</h3>

        <a className={style.btn} href={m.media[0].url} target="_blank">
          Download
        </a>
      </div>
    )
  })
}

export default MediaList
