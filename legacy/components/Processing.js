import styles from '../styles/Loaders.module.scss'

const Processing = (props) => {
  const { processing, message, style, position } = props

  const spinnerPosition = () => {
    if (position === 'top') {
      return styles.spinnerPositionTop
    }
    if (position === 'bottom') {
      return styles.spinnerPositionBottom
    }

    return ''
  }

  if (processing === true) {
    return (
      <div className={`${styles.ax_processing} ${position ? spinnerPosition() : ''}`}>
        <div className={styles.spinner} />
        <p>{message}</p>
      </div>
    )
  }
  return <div className={styles.ax_processing_hidden} />
}

export default Processing
