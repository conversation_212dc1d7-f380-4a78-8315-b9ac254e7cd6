import { useEffect, useState } from 'react'
import FsLightbox from 'fslightbox-react'
import { CarouselProvider, Slider, Slide, ButtonBack, ButtonNext, DotGroup } from 'pure-react-carousel'
import 'pure-react-carousel/dist/react-carousel.es.css'
import style from '../styles/Carousel.module.scss'

const ImageCarousel = (props) => {
  let { images, photosPerSlide } = props
  const [gallery, setGallery] = useState([])
  const [toggler, setToggler] = useState(false)
  const [responsive, setResponsive] = useState()
  const [windowWidth, setWindowWidth] = useState(null)
  const sources = images.map((i) => i.url)

  const toggle = (e) => {
    e.preventDefault()
    setToggler(!toggler)
  }

  const setSlideToShow = () => {
    switch (windowWidth) {
      case 'mobile':
        return 2
      case 'tablet':
        return 3
      case 'desktop':
        return 4
      default:
        return 4
    }
  }

  useEffect(() => {
    if (window.outerWidth < 767) {
      setWindowWidth('mobile')
    }
    if (window.outerWidth >= 768 && window.outerWidth < 992) {
      setWindowWidth('tablet')
    }

    if (window.outerWidth >= 992) {
      setWindowWidth('desktop')
    }
  }, [])

  useEffect(() => {
    setGallery(images)
  }, [responsive])

  useEffect(() => {
    const toShow = setSlideToShow()
    setResponsive(toShow)
  }, [windowWidth])

  const showCarousel = () => {
    if (gallery && gallery.length > 0) {
      return (
        <>
          <FsLightbox toggler={toggler} sources={sources} />
          <div className={`${style.ax_carousel} ${style.imageCarousel}`}>
            <CarouselProvider
              className={`${style.ax_carousel_dkt}`}
              naturalSlideWidth={125}
              naturalSlideHeight={125}
              visibleSlides={responsive}
              totalSlides={images && images.length > 0 ? images.length : 1}
              infinite={true}
              isPlaying={true}
              isIntrinsicHeight={true}
            >
              <Slider>
                {images.map((item, index) => {
                  return (
                    <Slide index={index} key={index}>
                      {item.url ? (
                        <a href={item.url} onClick={(e) => toggle(e)}>
                          <img src={item.url} alt={item.title} title={item.name} />{' '}
                        </a>
                      ) : (
                        ''
                      )}
                    </Slide>
                  )
                })}
              </Slider>
              <ButtonBack className={style.ax_back} />
              <ButtonNext className={style.ax_next} />
            </CarouselProvider>
          </div>
        </>
      )
    }

    return ''
  }

  return showCarousel()
}

export default ImageCarousel
