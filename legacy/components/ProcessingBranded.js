import styles from '../styles/Loaders.module.scss'

const ProcessingBranded = (props) => {
  const { processing, message, size } = props

  if (processing === true) {
    return (
      <section
        className={styles.ax_processing_branded}
        style={size !== null && size !== '' ? { width: `${size}px`, height: `${size}px` } : ''}
      >
        <div className={styles.brand}>
          <span></span>
          <p>{message}</p>
        </div>
      </section>
    )
  }
  return <div className={styles.ax_processing_hidden}></div>
}

export default ProcessingBranded
