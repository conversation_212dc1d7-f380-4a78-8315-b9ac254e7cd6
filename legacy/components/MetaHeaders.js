import Head from 'next/head'

const MetaHeaders = ({ title = 'Indi Central' }) => {
  // Generate a timestamp to bust caches (only updates on app rebuild)
  const buildTime = process.env.BUILD_TIME || new Date().toISOString()

  return (
    <Head>
      <title>{title}</title>
      <link rel="icon" href="/favicon.ico" />
      <meta name="robots" content="noindex,nofollow" />

      {/* Cache busting meta tags */}
      <meta httpEquiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
      <meta httpEquiv="Pragma" content="no-cache" />
      <meta httpEquiv="Expires" content="0" />

      {/* Add a version parameter to force browser to request new assets */}
      <meta name="version" content={buildTime} />

      {/* Error recovery script */}
      <script src={`/error-recovery.js?v=${buildTime}`} />

      {/* Add preload for the standalone error recovery page */}
      <link rel="prefetch" href="/error-recovery.html" />

      {/* Add meta tag to help error recovery script detect auth errors */}
      <meta name="indi-app-version" content={buildTime} />
    </Head>
  )
}

export default MetaHeaders
