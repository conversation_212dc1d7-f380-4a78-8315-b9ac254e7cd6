import React from 'react'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error }
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo)
  }

  handleCacheBustingReload = () => {
    // Check if we're in a browser environment
    if (typeof window !== 'undefined') {
      // Generate a unique timestamp for cache busting
      const timestamp = Date.now()

      // Use a direct page reload instead of modifying the URL
      // This avoids issues with React router and Link components
      window.location.reload()
    }
  }

  handleClearSiteData = () => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') return

    if ('caches' in window) {
      caches.keys().then((keyList) => {
        return Promise.all(
          keyList.map((key) => {
            return caches.delete(key)
          })
        )
      })
    }

    // Clear localStorage
    localStorage.clear()

    // Clear sessionStorage
    sessionStorage.clear()

    // Force reload after clearing data
    setTimeout(() => {
      this.handleCacheBustingReload()
    }, 500)
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            textAlign: 'center',
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
          }}
        >
          <img
            src="/images/indi-central-logo.svg"
            alt="Indi Central Logo"
            style={{ marginBottom: '30px', maxWidth: '200px' }}
          />

          <div
            style={{
              backgroundColor: 'white',
              padding: '30px',
              borderRadius: '10px',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              maxWidth: '500px'
            }}
          >
            <h1 style={{ color: '#333', marginTop: 0 }}>Application Error</h1>

            <p style={{ color: '#666', lineHeight: '1.5' }}>
              We've detected an error with the application that might be caused by outdated cached files.
            </p>

            <p style={{ color: '#666', lineHeight: '1.5' }}>Please try these solutions:</p>

            <div style={{ marginTop: '20px', display: 'flex', flexDirection: 'column', gap: '15px' }}>
              <button
                onClick={this.handleCacheBustingReload}
                style={{
                  backgroundColor: '#0070f3',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
              >
                Reload Application
              </button>

              <button
                onClick={this.handleClearSiteData}
                style={{
                  backgroundColor: '#ff4b4b',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  fontSize: '16px'
                }}
              >
                Clear Site Data & Reload
              </button>

              <p style={{ fontSize: '14px', color: '#888', marginTop: '15px' }}>
                If the problem persists, please try opening the site in an incognito window or contact support.
              </p>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
