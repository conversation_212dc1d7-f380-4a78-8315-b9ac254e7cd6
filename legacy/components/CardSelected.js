import { UilCheck } from '@iconscout/react-unicons'
import style from '../styles/Card.module.scss'

const CardSelect = (props) => {
  const { selected } = props

  return (
    <div className={`${style.cardSelect} ${selected ? style.cardSelectActive : style.cardSelectDeactive}`}>
      <div className={style.cardIcon}>
        <UilCheck size={16} />
      </div>
      {props.children}
    </div>
  )
}

export default CardSelect
