import { useRef, useEffect } from 'react'
import style from '../styles/Switcher.module.scss'

const Switcher = (props) => {
  const { id, name, checked, action, label, labelPos, iconPos, icon } = props
  const isChecked = useRef()

  const handleChecked = () => {
    if (checked) {
      isChecked.current.setAttribute('checked', true)
    } else {
      isChecked.current.removeAttribute('checked')
    }
  }

  const handleLabelPosition = () => {
    switch (labelPos) {
      case 'top':
        return style.labelTop
      case 'left':
        return style.labelLeft
      default:
        return style.labelLeft
    }
  }

  useEffect(() => {
    handleChecked()
  }, [checked])

  return (
    <div className={`${style.switcher} ${handleLabelPosition()} ${icon ? style.switcherIcon : ''}`} {...props}>
      {icon ? (
        <div className={style.iconHeading}>
          {(icon && iconPos === 'left') || iconPos === null ? <img src={icon} /> : ''}
          <label htmlFor={name}>{label}</label>
          {(icon && iconPos === 'right') || iconPos === null ? <img src={icon} /> : ''}
        </div>
      ) : (
        <label htmlFor={name}>{label}</label>
      )}

      <input id={id} ref={isChecked} name={name} type="checkbox" checked={checked} onChange={(e) => action(e)} />
    </div>
  )
}

export default Switcher
