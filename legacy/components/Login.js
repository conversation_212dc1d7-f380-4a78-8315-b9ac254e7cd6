import { useState, useContext } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import axios from 'axios'
import { setCookie } from 'nookies'
import { UilEye, UilEyeSlash, UilExclamationTriangle } from '@iconscout/react-unicons'
import styles from '../styles/Login.module.scss'
import AuthContext from '../context/authContext'

const Login = () => {
  const router = useRouter()
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [processing, setProcessing] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [loginError, setLoginError] = useState(false)
  const [loginErrorMessage, setLoginErrorMessage] = useState('')
  const { setUserAuth } = useContext(AuthContext)

  const handleLogin = async (e) => {
    e.preventDefault()
    if (processing) return

    try {
      setProcessing(true)
      setLoginError(false)
      setLoginErrorMessage('')

      const API_URL = `${process.env.API_URL}/auth/local`
      const loginInfo = { identifier: username, password }

      const response = await axios.post(API_URL, loginInfo)
      const { jwt, cookie_token, user } = response.data

      // Set cookies
      setCookie(null, 'jwt', jwt, {
        maxAge: 30 * 24 * 60 * 60,
        path: '/'
      })

      setCookie(null, 'userId', user.id, {
        maxAge: 30 * 24 * 60 * 60,
        path: '/'
      })

      setCookie(null, '__cld_token__', cookie_token, {
        maxAge: 30 * 24 * 60 * 60,
        path: '/'
      })

      // Update auth context
      setUserAuth({
        isAuth: true,
        userInfo: user,
        initialized: true
      })

      // Navigate after context is updated
      await router.push('/dashboard')
    } catch (error) {
      setLoginError(true)
      setLoginErrorMessage(
        error.response?.data?.message?.[0]?.messages?.[0]?.message || 'An error occurred during login'
      )
      console.error('Login error:', error)
    } finally {
      setProcessing(false)
    }
  }

  const handlePassword = () => {
    setShowPassword(!showPassword)
  }

  return (
    <section className={styles.ax_login}>
      <div className={styles.login_bg_icon}></div>
      <div className={styles.login_bg_1}></div>
      <div className={styles.login_bg_2}></div>
      <div className={styles.login_bg_quote}>
        Your source for brokerage<br></br> news, resources & more...
      </div>
      <div className={styles.ax_login_left_column} />
      <div className={styles.ax_login_right_column}>
        <img src="./images/indi-central-logo-white.svg" alt="Indi Logo" />
        <form className={`${styles.ax_login_form} ${styles.ax_form}`} onSubmit={handleLogin}>
          <div className={styles.ax_field}>
            <label htmlFor="email">Email</label>
            <input
              type="text"
              name="email"
              placeholder="Email"
              onChange={(e) => setUsername(e.target.value)}
              disabled={processing}
            />
          </div>

          <div className={styles.ax_field}>
            <label htmlFor="password">Password</label>
            <input
              type={showPassword ? 'text' : 'password'}
              name="password"
              placeholder="Password"
              onChange={(e) => setPassword(e.target.value)}
              disabled={processing}
            />
            <button className={styles.btnShowPassword} type="button" onClick={handlePassword} disabled={processing}>
              {showPassword ? <UilEye size={16} /> : <UilEyeSlash size={16} />}
            </button>
          </div>

          {loginError && (
            <div className={styles.formError}>
              <p>
                <UilExclamationTriangle size={16} />
                {loginErrorMessage}
              </p>
            </div>
          )}

          <button type="submit" disabled={processing}>
            {processing ? <img src="/images/spinner-white.svg" alt="spinner" /> : 'Login'}
          </button>
        </form>
        <Link href="/forgot-password" style={{ textAlign: 'center' }}>
          Forgot Your Password? <br />
          Click here.
        </Link>
      </div>
    </section>
  )
}

export default Login
