// components/BirthdayBalloons.js
import React, { useState, useEffect } from 'react'
import moment from 'moment'
import <PERSON><PERSON> from 'lottie-react'
import balloonsAnimation from '../lib/balloons-animation.json'
import styles from '../styles/Balloons.module.scss'

const BirthdayBalloons = ({ user }) => {
  const [showBalloons, setShowBalloons] = useState(false)

  useEffect(() => {
    const checkBirthday = () => {
      const today = moment()
      const birthday = moment(user.birthday)
      return today.format('MM-DD') === birthday.format('MM-DD')
    }

    const checkLocalStorage = () => {
      if (typeof window === 'undefined') return false
      const lastShown = localStorage.getItem('lastBalloonShown')
      const today = moment().format('YYYY-MM-DD')
      return lastShown !== today
    }

    if (checkBirthday() && checkLocalStorage()) {
      setShowBalloons(true)
      setTimeout(() => {
        setShowBalloons(false)
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastBalloonShown', moment().format('YYYY-MM-DD'))
        }
      }, 10000) // Duration of the animation (adjust as needed)
    }
  }, [user.birthday])

  if (!showBalloons) return null

  return (
    <div className={styles.balloonContainer}>
      <Lottie animationData={balloonsAnimation} loop={true} autoplay={true} style={{ width: '100%', height: '100%' }} />
      <div className={styles.birthdayMessage}>
        <h1>
          Happy Birthday, {user.firstname} {user.lastname}!
        </h1>
      </div>
    </div>
  )
}

export default BirthdayBalloons
