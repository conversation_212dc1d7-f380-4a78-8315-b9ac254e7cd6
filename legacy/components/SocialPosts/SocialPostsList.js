import { useState, useEffect, useMemo } from 'react'
import { saveAs } from 'file-saver'
import * as J<PERSON>Z<PERSON> from 'jszip'
import style from '../../styles/SocialPosts.module.scss'
import Moment from 'react-moment'
import Markdown from '../Markdown'
import { UilCopy } from '@iconscout/react-unicons'
import Processing from '../Processing'
import { fetchImage } from '../../helpers/fetchImage'

const BATCH_SIZE = 5 // Process 5 images at a time
const RETRY_ATTEMPTS = 2

const processBatch = async (batch, zip) => {
  const results = await Promise.all(
    batch.map(async (image) => {
      const filename = image.name
      const originalUrl = image.url
      if (!originalUrl) {
        console.error(`Missing original URL for image: ${filename}`)
        return { success: false, filename, error: new Error('Missing original URL') }
      }
      try {
        const { arrayBuffer } = await fetchImage(originalUrl)
        zip.file(filename, arrayBuffer)
        return { success: true, filename }
      } catch (err) {
        console.error(`Error downloading ${filename} from ${originalUrl}:`, err)
        return { success: false, filename, error: err }
      }
    })
  )
  return results
}

const SocialPostsList = ({ images, captions, calendar, month, isExtra }) => {
  const [isDownloading, setIsDownloading] = useState(false)
  const [singleImageDownloads, setSingleImageDownloads] = useState({})
  const [iframeSize, setIframeSize] = useState(null)
  const [imagesReady, setImagesReady] = useState(false)
  const [loadedImages, setLoadedImages] = useState(new Set())

  // Memoize sorted images
  const sortedImages = useMemo(() => {
    if (!images) return []
    setImagesReady(false) // Reset images ready state when images change
    setLoadedImages(new Set()) // Reset loaded images tracking
    return [...images].sort((a, b) => a.name.slice(1, 3) - b.name.slice(1, 3))
  }, [images])

  // Track image loading
  const handleImageLoad = (imageId) => {
    setLoadedImages((prev) => {
      const newSet = new Set(prev)
      newSet.add(imageId)
      return newSet
    })
  }

  // Check if all images are loaded
  useEffect(() => {
    if (!sortedImages.length) return

    if (loadedImages.size === sortedImages.length) {
      setImagesReady(true)
    }
  }, [loadedImages, sortedImages])

  // Handle responsive iframe sizing
  const handleResponsiveWidth = () => {
    if (typeof window === 'undefined') return

    const windowWidth = window.document.body.getBoundingClientRect().width
    const sizes = {
      xs: { width: 400, height: 400 },
      sm: { width: 600, height: 480 },
      md: { width: 720, height: 540 },
      lg: { width: 960, height: 600 }
    }

    if (windowWidth < 480) setIframeSize(sizes.xs)
    else if (windowWidth < 768) setIframeSize(sizes.sm)
    else if (windowWidth < 992) setIframeSize(sizes.md)
    else setIframeSize(sizes.lg)
  }

  // Handle window resize
  useEffect(() => {
    handleResponsiveWidth()

    const handleResize = () => {
      handleResponsiveWidth()
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Verify image URL is accessible
  const verifyImageUrl = async (url) => {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok
    } catch (error) {
      console.error(`Error verifying image URL ${url}:`, error)
      return false
    }
  }

  // Handle zip download
  const zipAndDownload = async () => {
    if (!imagesReady || !sortedImages || sortedImages.length === 0) {
      console.log('Images not ready or empty array')
      return
    }

    setIsDownloading(true)
    const zip = new JSZip()
    const safeMonth = month ? month.replace(/[^a-zA-Z0-9-]/g, '_') : 'posts'
    const zipFilename = `social-media-${safeMonth}.zip`
    const errors = []

    try {
      for (let i = 0; i < sortedImages.length; i += BATCH_SIZE) {
        const batch = sortedImages.slice(i, i + BATCH_SIZE)
        const results = await processBatch(batch, zip)

        results.forEach((result) => {
          if (!result.success) {
            errors.push({ filename: result.filename, error: result.error?.message || 'Unknown error' })
          }
        })
      }

      const content = await zip.generateAsync({ type: 'blob' })
      saveAs(content, zipFilename)

      if (errors.length > 0) {
        console.warn('Some files failed to download:', errors)
        const errorFiles = errors.map((e) => e.filename).join(', ')
        alert(
          `Download completed, but ${errors.length} image(s) failed to download: ${errorFiles}. Check console for details.`
        )
      } else {
        console.log('Download completed successfully')
      }
    } catch (error) {
      console.error('Download error:', error)
      alert('Failed to download images. Please try again.')
    } finally {
      setIsDownloading(false)
    }
  }

  // Handle single image download
  const handleSingleImageDownload = async (item) => {
    const imageUrl = item.url
    const filename = item.name

    if (!imageUrl || !filename) {
      console.error('Missing image URL or filename for download.', item)
      alert('Could not download image: missing data.')
      return
    }

    // Set loading state for this specific image
    setSingleImageDownloads((prev) => ({ ...prev, [item.id]: true }))

    try {
      // Use fetchImage helper
      const { arrayBuffer } = await fetchImage(imageUrl)
      // Save using file-saver
      saveAs(new Blob([arrayBuffer]), filename)
    } catch (error) {
      console.error(`Error downloading single image ${filename}:`, error)
      alert(`Failed to download ${filename}. Please check the console for details.`)
    } finally {
      // Remove loading state for this specific image
      setSingleImageDownloads((prev) => {
        const newState = { ...prev }
        delete newState[item.id]
        return newState
      })
    }
  }

  // Handle copy to clipboard
  const copyToClipboard = (elementId) => {
    try {
      const range = document.createRange()
      const node = document.getElementById(elementId)
      range.selectNode(node)
      window.getSelection().removeAllRanges()
      window.getSelection().addRange(range)
      document.execCommand('copy')
      window.getSelection().removeAllRanges()
      alert('Caption text has been copied to clipboard.')
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      alert('Failed to copy text. Please try again.')
    }
  }

  // Render calendar based on file type
  const renderCalendar = useMemo(() => {
    if (!calendar) return null

    const fileType = calendar.split('.').pop().toLowerCase()

    if (fileType === 'pdf' && iframeSize) {
      return (
        <iframe
          width={iframeSize.width}
          height={iframeSize.height}
          title="calendar"
          src={calendar}
          type="application/pdf"
        />
      )
    }

    if (['png', 'jpg', 'jpeg'].includes(fileType)) {
      return (
        <img
          title="calendar"
          src={calendar}
          alt="calendar"
          style={{ width: '100%', height: 'auto', display: 'table' }}
        />
      )
    }

    return null
  }, [calendar, iframeSize])

  return (
    <div className={style.posts}>
      <h1>
        <Moment format="MMMM Y">{month}</Moment> Posts
      </h1>

      <button
        className={`${style.btnDownload} ${!imagesReady ? style.btnDisabled : ''}`}
        type="button"
        onClick={zipAndDownload}
        disabled={isDownloading || !imagesReady}
      >
        {isDownloading ? 'Downloading...' : !imagesReady ? 'Loading Images...' : 'Download Images'}
      </button>

      {isDownloading && <Processing processing={true} message="Preparing download..." />}

      <ul className={style.imageList}>
        {sortedImages.map((item) => {
          const clippedCaption = item.name.replace(/.jpg|.jpeg|.png/gi, '')
          const imageUrl = item.formats?.small?.url || item.formats?.medium?.url || item.url

          return (
            <li key={item.id} className={style.imageItem}>
              <div className={style.imageContainer}>
                <div className={style.imageOverlay}>
                  <button
                    type="button"
                    className={`${style.overlayDownloadButton} ${singleImageDownloads[item.id] ? style.loading : ''}`}
                    onClick={() => handleSingleImageDownload(item)}
                    disabled={singleImageDownloads[item.id]}
                  >
                    {singleImageDownloads[item.id] ? 'Downloading...' : 'Download Image'}
                  </button>
                </div>
                <img
                  src={imageUrl}
                  alt={item.title || item.name}
                  loading="lazy"
                  onLoad={() => handleImageLoad(item.id)}
                  onError={(e) => {
                    console.error(`Error loading image ${item.name}:`, e)
                    // Optionally handle failed image loads
                  }}
                />
              </div>

              <caption>{clippedCaption}</caption>
            </li>
          )
        })}
      </ul>

      <button
        className={`${style.btnDownload} ${!imagesReady ? style.btnDisabled : ''}`}
        type="button"
        onClick={zipAndDownload}
        disabled={isDownloading || !imagesReady}
      >
        {isDownloading ? 'Downloading...' : !imagesReady ? 'Loading Images...' : 'Download Images'}
      </button>

      {captions && (
        <>
          <h2>Caption Text</h2>
          {captions.map((txt) => (
            <section key={txt.id} className={style.caption}>
              <h2>{txt.title}</h2>

              {txt.backgroundInfo && (
                <p>
                  <strong>Background info:</strong> <span>{txt.backgroundInfo}</span>
                </p>
              )}

              {txt.postDate && (
                <p>
                  <strong>Post date:</strong> <span>{txt.postDate}</span>
                </p>
              )}

              {txt.text && (
                <>
                  <h3>Caption idea:</h3>
                  <div id={`caption-${txt.id}`}>
                    <Markdown>{txt.text}</Markdown>
                  </div>
                </>
              )}

              <button className={style.btnCopy} type="button" onClick={() => copyToClipboard(`caption-${txt.id}`)}>
                <UilCopy size={16} /> Copy Caption
              </button>
            </section>
          ))}
        </>
      )}

      {calendar && (
        <section className={style.calendar}>
          <h2>Social Calendar</h2>
          {renderCalendar}
        </section>
      )}
    </div>
  )
}

export default SocialPostsList
