import React, { useEffect, useState, useRef } from 'react'
import style from '../styles/Banner.module.scss'

const EventBanner = ({ eventStatus, imagePath, showTitle, title, svg, calcBannerHeight }) => {
  const [bannerHeight, setBannerHeight] = useState(null)
  const [windowWidth, setWindowWidth] = useState(null)
  const imageRef = useRef(null)
  const containerRef = useRef(null)

  useEffect(() => {
    const updateDimensions = () => {
      const currentWidth = window.innerWidth
      setWindowWidth(currentWidth)

      if (imageRef.current && containerRef.current) {
        if (currentWidth < 1200) {
          // Mobile and tablet: calculate height based on aspect ratio
          const containerWidth = containerRef.current.offsetWidth
          const aspectRatio = 9 / 16
          const calculatedHeight = Math.floor(containerWidth * aspectRatio)
          setBannerHeight(calculatedHeight)
        } else {
          // Desktop: let the height be determined by the content
          setBannerHeight('auto')
        }
      }
    }

    // Initial update
    updateDimensions()

    // Add resize listener
    window.addEventListener('resize', updateDimensions)

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateDimensions)
    }
  }, [])

  // Notify parent of height changes when image loads
  const handleImageLoad = () => {
    if (imageRef.current && containerRef.current) {
      const height = windowWidth >= 1200 ? imageRef.current.offsetHeight : bannerHeight
      calcBannerHeight(height)
    }
  }

  if (svg) {
    return <div className={style.eventBannerSvg} dangerouslySetInnerHTML={{ __html: svg }}></div>
  }

  return (
    <div
      ref={containerRef}
      className={style.eventBanner}
      style={{ height: typeof bannerHeight === 'number' ? `${bannerHeight}px` : bannerHeight }}
    >
      <img
        src={imagePath}
        ref={imageRef}
        alt={title || 'Event banner'}
        onLoad={handleImageLoad}
        style={{
          objectFit: windowWidth < 1200 ? 'cover' : 'contain',
          width: '100%',
          height: windowWidth < 1200 ? '100%' : 'auto',
          position: windowWidth < 1200 ? 'absolute' : 'relative'
        }}
      />
    </div>
  )
}

export default EventBanner
