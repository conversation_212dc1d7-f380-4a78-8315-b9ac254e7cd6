import { useContext, useState, useEffect } from 'react'
import Cookies from 'js-cookie'
import Link from 'next/link'
import {
  UilFileDownloadAlt,
  UilBrowser,
  UilSignOutAlt,
  UilClock,
  UilFileCheckAlt,
  UilEnvelopeAlt,
  UilFidgetSpinner,
  UilSchedule,
  UilAward,
  UilBell,
  UilHeart,
  UilMoneyWithdraw,
  UilUniversity,
  UilMegaphone,
  UilApps,
  UilUser,
  UilUserSquare,
  UilUmbrella,
  UilBullseye,
  UilServerNetworkAlt,
  UilPresentationPlay,
  UilUsersAlt,
  UilWindow
} from '@iconscout/react-unicons'
import AuthContext from '../context/authContext'
import { logout } from '../auth/auth'
import styles from '../styles/Menu.module.scss'

const MenuMob = (props) => {
  const { userAuth, setUserAuth } = useContext(AuthContext)
  const { isMenuOpen } = props
  const eventsUrl = 'https://indievents.ca'
  const academyUrl = 'https://academy.indimortgage.ca'

  const handleLogout = () => {
    setUserAuth({ ...userAuth, isAuth: false })
    setTimeout(() => {
      logout()
    }, 500)
  }

  return (
    <aside className={`${styles.ax_menu_mob} ${isMenuOpen ? styles.axMenuOpened : ''}`}>
      <nav>
        <div className={styles.ax_menu_item}>
          <UilWindow size={20} className={styles.primary} />
          <Link href="/indi-app"> Indi App</Link>
        </div>
        <div className={styles.ax_menu_item}>
          <UilAward size={20} className={styles.primary} />
          <Link href="/awards"> Indi Awards</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilHeart size={20} className={styles.primary} />
          <Link href="/indi-cares"> Indi Cares</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilFidgetSpinner size={20} className={styles.primary} />
          <Link href="/indi-fit-club"> Indi Fit Club</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilBrowser size={20} className={styles.primary} />
          <Link href="/indi-sites"> My Indi Website</Link>
        </div>

        <div className={`${styles.ax_menu_item} ${styles.menuGreen}`}>
          <UilBullseye size={20} className={styles.primary} />
          <Link href="/branding"> Branding</Link>

          <div className={styles.ax_submenu_item}>
            <Link href="/brand-guidelines"> Brand Guidelines</Link>
          </div>

          <div className={`${styles.ax_submenu_item}`}>
            <Link href="/brand-materials"> Brand Materials</Link>
          </div>
        </div>

        <div className={styles.ax_menu_item}>
          <UilSchedule size={20} className={styles.primary} />
          <Link href="/company-calendar"> Company Calendar</Link>
        </div>
        <div className={styles.ax_menu_item}>
          <UilUserSquare size={20} className={styles.primary} />
          <Link href="/company-directory"> Company Directory</Link>
        </div>
        <div className={styles.ax_menu_item}>
          <UilFileCheckAlt size={20} className={styles.primary} />
          <Link href="/compliance">Compliance</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilFileCheckAlt size={20} className={styles.primary} />
          <Link href="/fintrac">FINTRAC</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilApps size={20} className={styles.primary} />
          <Link href="/dashboard"> Dashboard</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilClock size={20} className={styles.primary} />
          <Link href={eventsUrl} target="_blank">
            Events
          </Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilUmbrella size={20} className={styles.primary} />
          <Link href="/group-benefits"> Group Benefits</Link>
        </div>

        <div className={`${styles.ax_menu_item} ${styles.menuGreen}`}>
          <UilUniversity size={20} className={styles.primary} />
          <Link href="/lenders">Lender Lounge</Link>
        </div>

        <div className={`${styles.ax_menu_item} ${styles.menuGreen}`}>
          <UilMegaphone size={20} className={styles.primary} />
          <Link href="/marketing"> Marketing</Link>

          <div className={`${styles.ax_submenu_item}`}>
            <Link href="/social-media"> Social Media</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/email-signature"> Email Signature</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/printables"> Printables</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/listing-sheet"> Listing Sheet</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/custom-shop"> The Custom Shop</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/client-gift"> Operation Impact</Link>
          </div>

          <div className={styles.ax_submenu_item}>
            <Link href="/qr-codes"> QR Codes</Link>
          </div>
        </div>

        <div className={styles.ax_menu_item}>
          <UilEnvelopeAlt size={20} className={styles.primary} />
          <Link href="/newsletter-archive"> Newsletter Archive</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilBell size={20} className={styles.primary} />
          <Link href="/notifications"> Notifications</Link>
        </div>

        <div className={`${styles.ax_menu_item} ${styles.menuGreen}`}>
          <UilMegaphone size={20} className={styles.primary} />
          <Link href="/partnerships"> Partners</Link>

          <div className={`${styles.ax_submenu_item}`}>
            <Link href={academyUrl} target="_blank">
              Prospr by Sun Life
            </Link>
          </div>
        </div>

        <div className={styles.ax_menu_item}>
          <UilMoneyWithdraw size={20} className={styles.primary} />
          <Link href="https://portal.scarlettnetwork.com/portal/dashboard" target="_blank">
            Payroll
          </Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilUser size={20} className={styles.primary} />
          <Link href="/profile"> Profile</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilUsersAlt size={20} className={styles.primary} />
          <Link href="/realtors">My Realtors</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilFileDownloadAlt size={20} className={styles.primary} />
          <Link href="/resources">Resources</Link>
        </div>

        <div className={`${styles.ax_menu_item} ${styles.menuBlue}`}>
          <UilServerNetworkAlt size={20} className={styles.primary} />
          <Link href="/technology"> Technology</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilPresentationPlay size={20} className={styles.primary} />
          <Link href="/tutorials">Tutorials & Videos</Link>
        </div>

        <div className={styles.ax_menu_item}>
          <UilSignOutAlt size={20} className={styles.primary} />
          <button type="button" onClick={handleLogout}>
            Logout
          </button>
        </div>

        {/* <div className={styles.ax_menu_item}>
          <UilBooks size={20} className={styles.primary} />
          <Link href="/knowledge-base">Knowledge Base</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilGlobe size={20} />
          <Link href="/indi-sites"> My Indi Website</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilShieldCheck size={20} />
          <Link href="/mpp"> MPP</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilClinicMedical size={20} />
          <Link href="/insurers"> Insurers</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilUsdCircle size={20} />
          <Link href="/appraisers"> Appraisers</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilBooks size={20} />
          <Link href="/book-club"> Indi Book Club</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilBullseye size={20} className={styles.primary}/>
          <Link href="/branding"> Branding</Link>
        </div> */}

        {/* <div className={styles.ax_menu_item}>
          <UilUserPlus size={20} />
          <Link href="/add-broker"> Add Broker</Link>
        </div> */}
      </nav>
    </aside>
  )
}

export default MenuMob
