// components/ListFilter.js
import { useEffect, useState } from 'react'
import { UilSpinner } from '@iconscout/react-unicons'
import style from '../styles/Filter.module.scss'

const CANADIAN_PROVINCES = [
  { value: 'alberta', label: 'Alberta' },
  { value: 'britishColumbia', label: 'British Columbia' },
  { value: 'manitoba', label: 'Manitoba' },
  { value: 'newBrunswick', label: 'New Brunswick' },
  { value: 'newFoundlandAndLabrador', label: 'Newfoundland and Labrador' },
  { value: 'northwestTerritories', label: 'Northwest Territories' },
  { value: 'novaScotia', label: 'Nova Scotia' },
  { value: 'nunavut', label: 'Nunavut' },
  { value: 'ontario', label: 'Ontario' },
  { value: 'princeEdwardIsland', label: 'Prince Edward Island' },
  { value: 'quebec', label: 'Quebec' },
  { value: 'saskatchewan', label: 'Saskatchewan' },
  { value: 'yukon', label: 'Yukon' }
]

const ListFilter = ({ filterAction, initialProvince = 'all' }) => {
  const [loading, setLoading] = useState(false)
  const [selectedProvince, setSelectedProvince] = useState(initialProvince)

  const handleProvinceChange = async (e) => {
    const province = e.target.value
    setLoading(true)
    setSelectedProvince(province)

    try {
      await filterAction(province)
    } finally {
      setLoading(false)
    }
  }

  // Sync with initialProvince when it changes
  useEffect(() => {
    if (initialProvince !== selectedProvince) {
      setSelectedProvince(initialProvince)
    }
  }, [initialProvince])

  // No automatic filter on mount - rely on URL parameters instead

  return (
    <div className={style.filter}>
      <label htmlFor="province">
        Province:
        <select
          name="province"
          id="province"
          value={selectedProvince}
          onChange={handleProvinceChange}
          disabled={loading}
          className={loading ? style.loading : ''}
        >
          <option value="all">All Provinces</option>
          {CANADIAN_PROVINCES.map(({ value, label }) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
      </label>

      {loading && (
        <div className={style.loadingIndicator}>
          <UilSpinner size="20" className={style.spinner} />
        </div>
      )}
    </div>
  )
}

export default ListFilter
