import { useState, useEffect, useRef } from 'react'
import Markdown from '../components/Markdown'
import ReactPlayer from 'react-player'
import { Row, Col, Container } from 'react-grid-system'
import style from '../styles/ContentPage.module.scss'
import poststyle from '../styles/Posts.module.scss'

const StaticPageLayout = (props) => {
  const { data } = props
  const [iframeSize, setIframeSize] = useState({ width: 1000, height: 800 })
  const viewer = useRef(null)

  console.log(data)

  const calcIframeSize = () => {
    if (viewer.current) {
      const width = () => {
        if (window.innerWidth < 540) {
          return window.innerWidth * 0.7
        }
        if (window.innerWidth < 768 && window.innerWidth > 540) {
          return window.innerWidth * 0.6
        }
        if (window.innerWidth > 768 && window.innerWidth < 1024) {
          return window.innerWidth * 0.6
        }

        if (window.innerWidth > 1024) {
          return window.innerWidth * 0.46
        }
      }

      let height = viewer.current ? (window.innerHeight - 70) * 1.41 : 0
      return { width: width(), height }
    }

    return 800
  }

  useEffect(() => {
    if (viewer.current) {
      setIframeSize(calcIframeSize())
    }
  }, [viewer.current])

  return (
    <Container>
      <Row>
        <Col>
          <section className={style.pageBanner}>
            <img src={data.banner.url} alt="Banner Image" />
          </section>
        </Col>
      </Row>
      <Row>
        <Col>
          <section className={style.pageContent} style={{ marginTop: 0, borderRadius: '0 0 12px 12px' }}>
            <Row justify="center">
              <Col sm={12} lg={data && data.videoUrl ? 8 : 12}>
                <h1 className={style.ax_page_title}>{data.Title}</h1>

                <Markdown>{data.content}</Markdown>
              </Col>

              {data && data.videoUrl ? (
                <Col sm={12} lg={8}>
                  <div className={poststyle.videoEmbedContainer} style={{ margin: '32px 0' }}>
                    <ReactPlayer url={data.videoUrl} controls={true} width="100%" height="100%" />
                  </div>
                </Col>
              ) : (
                ''
              )}

              <div className={style.frame} style={{ margin: '16px auto' }}>
                {data && data.pdf && data.pdf.url ? (
                  <iframe
                    width={iframeSize.width || '1000'}
                    height={iframeSize.height || '2000'}
                    title="printable"
                    src={data.pdf.url}
                    ref={viewer}
                    type="application/pdf"
                  />
                ) : (
                  ''
                )}
              </div>
            </Row>
          </section>
        </Col>
      </Row>
    </Container>
  )
}

export default StaticPageLayout
