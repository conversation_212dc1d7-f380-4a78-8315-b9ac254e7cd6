import React, { useState, useCallback, useEffect } from 'react'
import { useRouter } from 'next/router'
import debounce from 'lodash/debounce'
import { UilSearch } from '@iconscout/react-unicons'
import jsCookie from 'js-cookie'
import styles from '../styles/Command.module.scss'
import axios from 'axios'

const GlobalSearch = () => {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [results, setResults] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  const performSearch = async (query) => {
    if (!query) {
      setResults([])
      return
    }

    const config = {
      headers: {
        Authorization: `Bearer ${jsCookie.get('jwt')}`
      }
    }

    setIsLoading(true)
    try {
      const response = await axios.get(
        `${process.env.API_URL}/global-search?query=${encodeURIComponent(query)}`,
        config
      )
      // Make sure we're getting the data array from the response
      setResults(response.data?.data || [])
    } catch (error) {
      console.error('Search failed:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }

  const debouncedSearch = useCallback(
    debounce((query) => performSearch(query), 300),
    []
  )

  useEffect(() => {
    debouncedSearch(searchQuery)
  }, [searchQuery, debouncedSearch])

  const handleSelect = (result) => {
    router.push(result.url)
  }

  // Debug log to see the structure

  return (
    <div className={styles.commandWrapper}>
      <div className={styles.searchHeader}>
        <UilSearch size="24" />
        <input
          placeholder="Search everything..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {searchQuery && (
        <div className={styles.resultsList}>
          {isLoading ? (
            <div className={styles.loadingState}>Searching...</div>
          ) : !Array.isArray(results) || results.length === 0 ? (
            <div className={styles.emptyState}>No results found.</div>
          ) : (
            results.map((result, index) => (
              <div
                key={`${result.type}-${index}`}
                className={styles.resultItem}
                onClick={() => handleSelect(result)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleSelect(result)
                  }
                }}
              >
                <div className={styles.resultHeader}>
                  <span className={styles.title}>{result.title}</span>
                  <span className={styles.badge}>{result.type}</span>
                </div>
                {result.description && <p className={styles.description}>{result.description}</p>}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  )
}

export default GlobalSearch
