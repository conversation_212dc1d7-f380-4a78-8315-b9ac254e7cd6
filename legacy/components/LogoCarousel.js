import { useEffect, useState } from 'react'
import { CarouselProvider, Slider, Slide, ButtonBack, ButtonNext, DotGroup } from 'pure-react-carousel'
import 'pure-react-carousel/dist/react-carousel.es.css'
import style from '../styles/Carousel.module.scss'

const LogoCarousel = (props) => {
  const carousel = props.carouselData
  const [windowWidth, setWindowWidth] = useState(null)

  const setLogoSize = (level) => {
    switch (level) {
      case 'platinum':
        return {}
      case 'gold':
        return { padding: '0 16px' }
      case 'silver':
        return { padding: '0 32px' }
      case 'bronze':
        return { padding: '0 40px' }
      case 'participant':
        return { padding: '0 54px' }
      default:
        return { padding: '0' }
    }
  }

  useEffect(() => {
    if (window.outerWidth < 767) {
      setWindowWidth('mobile')
    }
    if (window.outerWidth >= 768 && window.outerWidth < 1200) {
      setWindowWidth('tablet')
    }

    if (window.outerWidth >= 1200) {
      setWindowWidth('desktop')
    }
  }, [])

  return (
    <>
      <div className={`${style.ax_carousel} ${style.logoCarousel}`}>
        {windowWidth && windowWidth !== null && windowWidth === 'desktop' ? (
          <CarouselProvider
            className={`${style.ax_carousel_dkt} `}
            visibleSlides={6}
            totalSlides={carousel.length}
            infinite
            isPlaying={carousel.length > 6}
            isIntrinsicHeight
            naturalSlideWidth={125}
            naturalSlideHeight={125}
          >
            <Slider classNameTray={`${style.ax_carousel_tray} ${carousel.length <= 6 ? style.carousel_center : ''}`}>
              {carousel.map((item, index) => (
                <Slide index={index} key={index}>
                  {item.logo ? (
                    <a href={item.link} target="_blank" rel="noreferrer">
                      <img src={item.logo.url} alt={item.title} title={item.name} style={setLogoSize(item.level)} />
                    </a>
                  ) : (
                    ''
                  )}
                </Slide>
              ))}
            </Slider>
            {carousel.length > 6 ? (
              <>
                <ButtonBack className={style.ax_back} />
                <ButtonNext className={style.ax_next} />
              </>
            ) : (
              ''
            )}
          </CarouselProvider>
        ) : (
          ''
        )}

        {windowWidth && windowWidth !== null && windowWidth === 'tablet' ? (
          <CarouselProvider
            className={`${style.ax_carousel_tablet} `}
            visibleSlides={3}
            totalSlides={carousel.length}
            infinite
            isPlaying={carousel.length > 3}
            isIntrinsicHeight
            naturalSlideWidth={125}
            naturalSlideHeight={125}
          >
            <Slider classNameTray={`${style.ax_carousel_tray} ${carousel.length <= 3 ? style.carousel_center : ''}`}>
              {carousel.map((item, index) => (
                <Slide index={index} key={index}>
                  {item.logo ? (
                    <a href={item.link} target="_blank" rel="noreferrer">
                      <img src={item.logo.url} alt={item.title} title={item.name} style={setLogoSize(item.level)} />
                    </a>
                  ) : (
                    ''
                  )}
                </Slide>
              ))}
            </Slider>
            {carousel.length > 3 ? (
              <>
                <ButtonBack className={style.ax_back} />
                <ButtonNext className={style.ax_next} />
              </>
            ) : (
              ''
            )}
          </CarouselProvider>
        ) : (
          ''
        )}

        {windowWidth && windowWidth !== null && windowWidth === 'mobile' ? (
          <CarouselProvider
            className={`${style.ax_carousel_mob} `}
            visibleSlides={1}
            totalSlides={carousel.length}
            infinite
            isPlaying={carousel.length > 1}
            isIntrinsicHeight
            naturalSlideWidth={125}
            naturalSlideHeight={125}
          >
            <Slider classNameTray={`${style.ax_carousel_tray} ${carousel.length <= 1 ? style.carousel_center : ''}`}>
              {carousel.map((item, index) => (
                <Slide index={index} key={index}>
                  {item.logo ? (
                    <a href={item.link} target="_blank" rel="noreferrer">
                      <img src={item.logo.url} alt={item.title} title={item.name} style={setLogoSize(item.level)} />
                    </a>
                  ) : (
                    ''
                  )}
                </Slide>
              ))}
            </Slider>
            {carousel.length > 1 ? (
              <>
                <ButtonBack className={style.ax_back} />
                <ButtonNext className={style.ax_next} />
              </>
            ) : (
              ''
            )}
          </CarouselProvider>
        ) : (
          ''
        )}
      </div>
    </>
  )
}

export default LogoCarousel
