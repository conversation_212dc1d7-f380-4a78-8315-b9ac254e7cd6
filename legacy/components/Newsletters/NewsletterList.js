import Card from '../../components/Card'
import { Container, Col, Row } from 'react-grid-system'
import style from '../../styles/Newsletters.module.scss'

const NewsletterList = (props) => {
  const { data } = props

  const printItem = () => {
    return data.map((print) => {
      if (print.thumbnail) {
        return (
          <Col xs={12} sm={6} md={4} lg={3} style={{ marginBottom: '1.6rem' }}>
            <Card
              key={print.id}
              title={print.title}
              date={print.date}
              hasButton
              linkUrl={`/newsletter-archive/${print.slug}`}
              buttonLabel="View"
              image={print.thumbnail.url}
            />
          </Col>
        )
      } else {
        return (
          <Col xs={12} sm={6} md={4} lg={3} style={{ marginBottom: '1.6rem' }}>
            <Card
              key={print.id}
              title={print.title}
              date={print.date}
              hasButton
              linkUrl={`/newsletter-archive/${print.slug}`}
              buttonLabel="View"
              iconSquared="./images/ico-pdf.svg"
            />
          </Col>
        )
      }
    })
  }

  return (
    <section>
      <Container>
        <Row>{printItem()}</Row>
      </Container>
    </section>
  )
}

export default NewsletterList
