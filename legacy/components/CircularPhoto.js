const CircularPhoto = ({ user, size }) => {
  const photoUrl =
    user &&
    (user.circularPhotoUrl ||
      user?.circularPhoto?.url ||
      (user && user.photo && user.photo.formats
        ? user?.photo?.formats?.thumbnail?.url
        : user.photo && user.photo.url && user.photo.ext.toLowerCase() !== '.heic'
        ? user.photo.url
        : ''))

  if (!photoUrl) {
    return null
  }

  return (
    <table cellPadding="0" cellSpacing="0" border="0" style={{ pointerEvents: 'none' }}>
      <tbody>
        <tr>
          <td width={size} height={size} style={{ fontSize: 0, lineHeight: 0 }}>
            {`<!--[if mso]>
          <v:oval style="width:130px;height:130px;" strokecolor="#000000" fillcolor="#FFFFFF">
          <v:fill type="frame" src="${photoUrl}" aspect="atmost" />
          </v:oval>
          <![endif]-->`}
            {`<!--[if !mso]><!-->`}
            <div
              style={{
                width: `${size}px`,
                height: `${size}px`,
                borderRadius: '65px',
                overflow: 'hidden',
                display: 'inline-block'
              }}
            >
              <img
                src={photoUrl}
                alt={`${user.firstname} ${user.lastname}`}
                width={size}
                height={size}
                style={{
                  display: 'block',
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  objectPosition: 'center'
                }}
              />
            </div>
            {`<!--<![endif]-->`}
          </td>
        </tr>
      </tbody>
    </table>
  )
}

export default CircularPhoto
