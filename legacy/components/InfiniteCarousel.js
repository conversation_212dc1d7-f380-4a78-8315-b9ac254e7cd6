import { useState, useEffect, useCallback } from 'react'
import Markdown from './Markdown'
import style from '../styles/InfiniteCarousel.module.scss'
import J<PERSON><PERSON>ip from 'jszip'

const InfiniteCarousel = ({ items, autoScrollInterval = 6000, userId, currentPath }) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [windowWidth, setWindowWidth] = useState(null)
  const [isDownloading, setIsDownloading] = useState(false)

  // Calculate how many items are visible and item width percentage
  const getVisibleItems = () => {
    switch (windowWidth) {
      case 'mobile':
        return { visible: 1, itemWidth: 100 }
      case 'tablet':
        return { visible: 2, itemWidth: 50 }
      case 'desktop':
        return { visible: 4, itemWidth: 25 }
      default:
        return { visible: 4, itemWidth: 25 }
    }
  }

  const { visible: visibleItems, itemWidth } = getVisibleItems()

  // Create extended array with enough clones for infinite effect
  // Clone enough items to fill the visible area at each end
  const createExtendedItems = () => {
    if (!items || items.length === 0) return []

    const cloneCount = Math.max(visibleItems, items.length)
    const startClones = []
    const endClones = []

    // Create clones for the beginning (last items)
    for (let i = 0; i < cloneCount; i++) {
      startClones.unshift(items[items.length - 1 - (i % items.length)])
    }

    // Create clones for the end (first items)
    for (let i = 0; i < cloneCount; i++) {
      endClones.push(items[i % items.length])
    }

    return [...startClones, ...items, ...endClones]
  }

  const extendedItems = createExtendedItems()
  const cloneCount = Math.max(visibleItems, items.length)

  // Function to download file as zip
  const downloadAsZip = async (downloadItem, filename) => {
    try {
      setIsDownloading(true)
      const zip = new JSZip()

      // Use the S3 proxy endpoint
      const proxyUrl = `/api/s3proxy?url=${encodeURIComponent(downloadItem.url)}`

      // Fetch the file through proxy
      const response = await fetch(proxyUrl)
      if (!response.ok) {
        throw new Error(`Failed to fetch file: ${response.statusText}`)
      }
      const blob = await response.blob()

      // If the file is already a zip, download it directly without creating another zip
      if (downloadItem.ext && downloadItem.ext.toLowerCase() === 'zip') {
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      } else {
        // Add file to zip for other file types (images, PDFs, etc.)
        zip.file(filename, blob)

        // Generate zip file
        const content = await zip.generateAsync({ type: 'blob' })

        // Create download link
        const downloadUrl = window.URL.createObjectURL(content)
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `${filename}.zip`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }
    } catch (error) {
      console.error('Error downloading file:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  // Link processing function
  const processLink = (item) => {
    // If downloadItem exists and has a valid URL, prepare for download
    if (item.downloadItem && item.downloadItem.url) {
      // Create filename based on title and extension
      const baseFilename = item.title || 'download'
      const extension = item.downloadItem.ext || ''
      const filename = extension ? `${baseFilename}.${extension}` : baseFilename

      return {
        downloadItem: item.downloadItem,
        filename: filename,
        isDownloadable: true
      }
    }

    // If no downloadItem or no URL, don't show any button
    return null
  }

  const nextSlide = useCallback(() => {
    if (isTransitioning || !items || items.length === 0) return

    setIsTransitioning(true)
    setCurrentIndex((prev) => prev + 1)

    setTimeout(() => {
      // If we've reached the end clones, jump back to the real items
      if (currentIndex + 1 >= cloneCount + items.length) {
        setCurrentIndex(cloneCount)
        setIsTransitioning(false)
      } else {
        setIsTransitioning(false)
      }
    }, 500)
  }, [currentIndex, cloneCount, items, isTransitioning])

  const prevSlide = useCallback(() => {
    if (isTransitioning || !items || items.length === 0) return

    setIsTransitioning(true)
    setCurrentIndex((prev) => prev - 1)

    setTimeout(() => {
      // If we've reached the beginning clones, jump to the real items at the end
      if (currentIndex - 1 < cloneCount) {
        setCurrentIndex(cloneCount + items.length - 1)
        setIsTransitioning(false)
      } else {
        setIsTransitioning(false)
      }
    }, 500)
  }, [currentIndex, cloneCount, items, isTransitioning])

  // Auto-scroll functionality
  useEffect(() => {
    if (!isHovered && autoScrollInterval && items && items.length > 0) {
      const interval = setInterval(nextSlide, autoScrollInterval)
      return () => clearInterval(interval)
    }
  }, [nextSlide, isHovered, autoScrollInterval, items])

  // Set up window width detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      if (width < 768) {
        setWindowWidth('mobile')
      } else if (width >= 768 && width < 992) {
        setWindowWidth('tablet')
      } else {
        setWindowWidth('desktop')
      }
    }

    // Set initial window width
    handleResize()

    // Add event listener
    window.addEventListener('resize', handleResize)

    // Cleanup
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Initialize to first real item when items or window width changes
  useEffect(() => {
    if (items && items.length > 0 && windowWidth) {
      const newCloneCount = Math.max(visibleItems, items.length)
      setCurrentIndex(newCloneCount)
    }
  }, [items, windowWidth, visibleItems])

  const handleMouseEnter = () => setIsHovered(true)
  const handleMouseLeave = () => setIsHovered(false)

  if (!items || items.length === 0) return null

  return (
    <div className={style.carousel} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <div className={style.carouselContainer}>
        <div
          className={style.carouselTrack}
          style={{
            transform: `translateX(-${currentIndex * itemWidth}%)`,
            transition: isTransitioning ? 'transform 0.5s ease-in-out' : 'none'
          }}
        >
          {extendedItems.map((item, index) => {
            const linkInfo = processLink(item)

            return (
              <div key={`${item.title}-${index}`} className={style.carouselSlide}>
                <div className={style.carouselCard}>
                  <div className={style.cardImage}>
                    <img src={item.image.url} alt={item.title} />
                  </div>
                  <div className={style.cardContent}>
                    <h3 className={style.cardTitle}>{item.title}</h3>
                    <div className={style.cardDescription}>
                      <Markdown>{item.description}</Markdown>
                    </div>
                    {linkInfo && (
                      <a
                        href={linkInfo.url}
                        className={style.cardButton}
                        target={linkInfo.target}
                        onClick={(e) => {
                          if (linkInfo.isDownloadable) {
                            e.preventDefault()
                            downloadAsZip(linkInfo.downloadItem, linkInfo.filename)
                          }
                        }}
                        rel={linkInfo.target === '_blank' ? 'noopener noreferrer' : undefined}
                        style={{ margin: 'auto' }}
                      >
                        {isDownloading ? 'Downloading...' : 'Download'}
                      </a>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        <button className={`${style.navButton} ${style.prevButton}`} onClick={prevSlide} aria-label="Previous slide">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M15 18L9 12L15 6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>

        <button className={`${style.navButton} ${style.nextButton}`} onClick={nextSlide} aria-label="Next slide">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M9 18L15 12L9 6"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default InfiniteCarousel
