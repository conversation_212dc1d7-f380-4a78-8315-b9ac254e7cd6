import React from 'react'
import style from '../styles/Table.module.scss'

const Table = ({ headers, rows }) => {
  return (
    <div className={style.table}>
      <table cellPadding="0" cellSpacing="0">
        <thead>
          <tr>
            {headers.map((header) => (
              <th key={header}>{header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {headers.map((header, cellIndex) => {
                const cellContent = row[header.toLowerCase()]
                const isButton = React.isValidElement(cellContent) && cellContent.type === 'button'
                return (
                  <td key={`${rowIndex}-${cellIndex}`} className={isButton ? style.buttonCell : ''}>
                    {cellContent}
                  </td>
                )
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

export default Table
