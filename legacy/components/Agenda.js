import Moment from 'react-moment'
import mmt from 'moment'
import style from '../styles/Agenda.module.scss'
const Agenda = (props) => {
  const { agenda, colors } = props

  const timeFmt = (time) => mmt(time, ['HH.mm']).format('h:mm a')

  const showDates = () => {
    if (agenda.length === 1) {
      return agenda.map((d, index) => {
        return (
          <div
            className={`${style.day} ${style.wideCol}`}
            style={colors && colors.borderHex ? { borderColor: colors.borderHex } : {}}
          >
            <h3>
              <Moment format="MMM DD">{d.activityDate}</Moment>
            </h3>
            <div className={style.activityList}>
              {d && d.activity
                ? d.activity.map((a) => {
                    return (
                      <div className={style.activity}>
                        <h4 style={colors && colors.textHighlightHex ? { color: colors.textHighlightHex } : {}}>
                          {timeFmt(a.startTime)} - {timeFmt(a.endTime)}
                        </h4>
                        <p>{a.title}</p>
                      </div>
                    )
                  })
                : ''}
            </div>
          </div>
        )
      })
    }
    return agenda.map((d, index) => {
      if ((index + 1) % 4 !== 0) {
        return (
          <div
            className={`${style.day} ${style.singleCol}`}
            style={colors && colors.borderHex ? { borderColor: colors.borderHex } : {}}
          >
            <h3>
              <Moment format="MMM DD">{d.activityDate}</Moment>
            </h3>
            <div className={style.activityList}>
              {d && d.activity
                ? d.activity.map((a) => {
                    return (
                      <div className={style.activity}>
                        <h4 style={colors && colors.textHighlightHex ? { color: colors.textHighlightHex } : {}}>
                          {timeFmt(a.startTime)} - {timeFmt(a.endTime)}
                        </h4>
                        <p>{a.title}</p>
                      </div>
                    )
                  })
                : ''}
            </div>
          </div>
        )
      } else {
        return (
          <div
            className={`${style.day} ${style.wideCol}`}
            style={colors && colors.borderHex ? { borderColor: colors.borderHex } : {}}
          >
            <h3>
              <Moment format="MMM DD">{d.activityDate}</Moment>
            </h3>
            <div className={style.activityList}>
              {d && d.activity
                ? d.activity.map((a) => {
                    return (
                      <div className={style.activity}>
                        <h4 style={colors && colors.textHighlightHex ? { color: colors.textHighlightHex } : {}}>
                          {timeFmt(a.startTime)} - {timeFmt(a.endTime)}
                        </h4>
                        <p>{a.title}</p>
                      </div>
                    )
                  })
                : ''}
            </div>
          </div>
        )
      }
    })
  }

  return <div className={style.agenda}>{showDates()}</div>
}

export default Agenda
