import { useContext } from 'react'
import AuthContext from '../context/authContext'
import PrintablesContext from '../context/printablesContext'
import Topbar from './Topbar'
import Menu from './Menu'
import Main from './Main'
import Toast from './Toast'
import ProcessingBranded from './ProcessingBranded'
import styles from '../styles/Layout.module.scss'

const ShowAlerts = (props) => {
  const { showToast, message, toastType } = props

  if (showToast) {
    return <Toast message={message} toastType={toastType} showToast={showToast} />
  }
  return <></>
}

const Layout = (props) => {
  const { userAuth } = useContext(AuthContext)
  const { showToast, message, toastType, children } = props

  const showLayout = () => {
    return (
      <PrintablesContext.Provider value={{ qrId: props.qrId }}>
        <section className={styles.ax_layout}>
          <Topbar className={styles.ax_topbar} />
          <Menu className={styles.ax_menu} />
          <Main className={styles.ax_main}>
            <ShowAlerts message={message} toastType={toastType} showToast={showToast} />
            {children}
          </Main>
        </section>
      </PrintablesContext.Provider>
    )
  }

  // Enhanced safety check
  if (!userAuth || !userAuth.initialized) {
    return <ProcessingBranded processing message="Initializing..." />
  }

  // Make sure user is authenticated and has user info
  if (userAuth.isAuth && userAuth.userInfo) {
    return showLayout()
  } else {
    return <ProcessingBranded processing message="Loading..." />
  }
}

export default Layout
