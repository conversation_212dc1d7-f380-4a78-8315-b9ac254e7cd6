import { useContext, useEffect, useState, useCallback } from 'react'
import { UilSpinner } from '@iconscout/react-unicons'
import BrokersFilterContext from '../context/brokersFilterContext'
import Cookies from 'js-cookie'
import axios from 'axios'
import style from '../styles/Filter.module.scss'
import Processing from './Processing'

const SearchByName = (props) => {
  const { handleSearch } = props
  const [searchTerm, setSearchTerm] = useState('')
  const [isSearching, setIsSearching] = useState(false)
  const [error, setError] = useState(null)
  const { setBrokers } = useContext(BrokersFilterContext)

  const API_URL = process.env.NEXT_PUBLIC_API_URL
  const jwt = Cookies.get('jwt')
  const config = {
    headers: {
      Authorization: `Bearer ${jwt}`
    }
  }

  // Debounced search function
  const debounce = (func, wait) => {
    let timeout
    return (...args) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(this, args), wait)
    }
  }

  const performSearch = async (term) => {
    if (!term || term.length < 3) {
      // Reset to default state if search term is cleared
      if (term === '') {
        try {
          const defaultResults = await axios.get(
            `${API_URL}/users-permissions/directory?_start=0&_limit=12&isOnboarding=0&_sort=firstname:ASC`,
            config
          )
          setBrokers(defaultResults.data)
          handleSearch({ resetPagination: true })
        } catch (err) {
          console.error('Error resetting search:', err)
        }
      }
      setError(null)
      return
    }

    setIsSearching(true)
    setError(null)

    try {
      const results = await axios.get(
        `${API_URL}/users-permissions/directory?search=${encodeURIComponent(term)}`,
        config
      )
      setBrokers(results.data)
      handleSearch({ resetPagination: true })
    } catch (err) {
      console.error('Search error:', err)
      setError('An error occurred while searching. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  // Create memoized debounced function
  const debouncedSearch = useCallback(
    debounce((term) => performSearch(term), 300),
    []
  )

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }

  return (
    <div className={`${style.filter} ${style.searchFilter}`}>
      <div className={style.groupField}>
        <label htmlFor="search">
          Search By Name:
          <input
            type="text"
            id="search"
            name="search"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Type to search..."
            aria-label="Search by name"
          />
        </label>

        {isSearching && (
          <div className={style.searchingIndicator}>
            <UilSpinner size="20" className={style.spinner} />
          </div>
        )}
      </div>

      {error && <div className={style.error}>{error}</div>}

      {searchTerm && searchTerm.length > 0 && searchTerm.length < 3 && (
        <div className={style.searchHint}>Please enter at least 3 characters to search</div>
      )}
    </div>
  )
}

export default SearchByName
