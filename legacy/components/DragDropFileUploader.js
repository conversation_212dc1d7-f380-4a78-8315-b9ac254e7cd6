import React, { useState, useRef } from 'react'
import axios from 'axios'
import { UilUpload, UilScenery, UilTrashAlt } from '@iconscout/react-unicons'
import Image from 'next/image'
import But<PERSON> from './Button'
import Processing from './Processing' // Import the Processing component
import style from '../styles/DragDropFileUploader.module.scss'

const allowedFileTypes = [
  'image/jpeg',
  'image/png',
  'image/heic',
  'application/zip',
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
]

const FileTypeIcon = ({ fileType }) => {
  if (fileType && fileType.startsWith('image/')) {
    return <UilScenery size={64} />
  }
  return <Image src="/images/ico-file.svg" alt="File icon" width={64} height={64} />
}

const DragDropFileUploader = ({ onUploadComplete, maxFileSize = 15 * 1024 * 1024, apiUrl, identifier = '' }) => {
  const [file, setFile] = useState(null)
  const [uploadedFile, setUploadedFile] = useState(null)
  const [error, setError] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef(null)

  const handleDrop = (e) => {
    e.preventDefault()
    const droppedFile = e.dataTransfer.files[0]
    handleFile(droppedFile)
  }

  const handleFileSelect = (e) => {
    const selectedFile = e.target.files[0]
    handleFile(selectedFile)
  }

  const handleFile = (file) => {
    if (file) {
      if (!allowedFileTypes.includes(file.type)) {
        setError('Invalid file type. Please upload an image (jpg, png, heic), zip, pdf, xlsx, csv, or docx file.')
        return
      }

      const fileSize = Math.round(file.size / 1024)
      if (fileSize >= maxFileSize / 1024) {
        setError(`The file size must be ${maxFileSize / 1024 / 1024}MB or less.`)
      } else {
        setFile(file)
        setError('')
      }
    }
  }

  const handleUpload = async () => {
    if (!file) return

    setIsUploading(true)
    setError('')

    const formData = new FormData()
    formData.append('files', file)
    formData.append('identifier', identifier)

    try {
      const response = await axios.post(`${apiUrl}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      const uploadedFileData = response.data[0]
      const enhancedUploadData = {
        ...uploadedFileData,
        identifier: identifier
      }
      setUploadedFile(enhancedUploadData)
      onUploadComplete(enhancedUploadData)
    } catch (err) {
      setError(`Upload failed: ${err.message}`)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemove = () => {
    setFile(null)
    setUploadedFile(null)
    setError('')
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    onUploadComplete(null) // Inform the parent component that the file has been removed
  }

  const getPreviewUrl = (fileData) => {
    if (fileData.formats && fileData.formats.medium && fileData.formats.medium.url) {
      return fileData.formats.medium.url
    }
    return fileData.url
  }

  const renderFilePreview = () => {
    if (!file && !uploadedFile) return null

    const fileToPreview = uploadedFile || file
    const isImage =
      fileToPreview &&
      (fileToPreview.mime || fileToPreview.type) &&
      (fileToPreview.mime || fileToPreview.type).startsWith('image/')

    if (isImage && uploadedFile) {
      const previewUrl = getPreviewUrl(uploadedFile)
      return (
        <div className={style.filePreview}>
          <img src={previewUrl} alt="Uploaded file" />
        </div>
      )
    }

    return (
      <div className={style.filePreview}>
        <FileTypeIcon fileType={fileToPreview.mime || fileToPreview.type} />
        <p>{fileToPreview.name}</p>
      </div>
    )
  }

  const renderRemoveButton = () => {
    if (!file && !uploadedFile) return null

    return (
      <Button
        type="button"
        action={handleRemove}
        icon={<UilTrashAlt size={16} />}
        iconPos="left"
        label="Remove"
        color="danger"
      />
    )
  }

  return (
    <div className={style.dragzone} style={{ position: 'relative' }}>
      {isUploading && <Processing processing={true} message="Uploading..." />}
      <div
        className={uploadedFile ? `${style.dragarea} ${style.previewOn}` : style.dragarea}
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onClick={() => !uploadedFile && fileInputRef.current.click()}
      >
        {renderFilePreview() || (
          <span className={style.message}>
            <UilUpload size={64} />
            <p>Drag/drop your file here or click to choose it.</p>
          </span>
        )}
      </div>
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        accept={allowedFileTypes.join(',')}
        style={{ display: 'none' }}
      />
      <div className={style.footer}>
        <Button
          disabled={!file || isUploading || uploadedFile}
          type="button"
          action={handleUpload}
          icon={<UilUpload size={16} />}
          iconPos="left"
          label={isUploading ? 'Uploading...' : 'Upload'}
          color="highlight"
        />
        {renderRemoveButton()}
      </div>
      {error && <p className={style.alertDanger}>{error}</p>}
    </div>
  )
}

export default DragDropFileUploader
