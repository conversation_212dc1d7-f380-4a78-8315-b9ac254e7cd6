import Router from 'next/router'
import NProgress from 'nprogress'
import { useState, useContext, useEffect } from 'react'
import Link from 'next/link'
import Cookies from 'js-cookie'
import { UilBell, UilArrowLeft, UilSetting, UilFileCheckAlt } from '@iconscout/react-unicons'
import GlobalSearch from './GlobalSearch'
import AuthContext from '../context/authContext'
import NotificationsContext from '../context/notificationsContext'
import Avatar from './Avatar'
import Notifications from './Notifications'
import MenuMob from './MenuMob'
import styles from '../styles/Topbar.module.scss'
import Button from './Button'

export const customLoader = () => `
      <div class="${styles.ax_loading_bar} bar" role="bar">
        <div class="${styles.ax_loading_peg} peg">
        </div>
      </div>
      <div class="${styles.ax_loading_spinner} spinner" role="spinner">
        <div class="${styles.ax_loading_spinner_icon} spinner-icon">
        </div>
      </div>
   `

NProgress.configure({
  template: customLoader()
})

Router.events.on('routeChangeStart', () => {
  NProgress.start()
})
Router.events.on('routeChangeComplete', () => NProgress.done())
Router.events.on('routeChangeError', () => NProgress.done())

const Topbar = () => {
  const { userAuth } = useContext(AuthContext)
  const [menuMobIsOpen, setMenuMobIsOpen] = useState(false)
  const [notifications, setNotifications] = useState({ notfs: null })
  const [dismissedNotes, setDismissedNotes] = useState([])
  const [notesCounter, setNotesCounter] = useState(0)
  const [userData, setUserData] = useState()

  useEffect(() => {
    if (userAuth?.initialized && userAuth?.notifications) {
      const filteredNotifications = userAuth.notifications.filter((note) => {
        if (!userAuth?.userInfo) return false

        if (note.showToAll) return true
        if (note.teams.some((t) => t.name === userAuth.userInfo?.team?.name)) return true
        if (note.users.some((u) => u.id === userAuth.userInfo?.id)) return true
        return false
      })
      setNotifications({ notfs: filteredNotifications })
    }
  }, [userAuth?.notifications, userAuth?.userInfo, userAuth?.initialized])

  useEffect(() => {
    if (userAuth?.initialized && userAuth?.userInfo) {
      setUserData(userAuth.userInfo)
    }
  }, [userAuth?.userInfo, userAuth?.initialized])

  const handleOpenMenuMob = () => {
    setMenuMobIsOpen(!menuMobIsOpen)
  }

  const handleBack = () => {
    history.back()
  }

  const [showNotfs, setShowNotfs] = useState(false)

  useEffect(() => {
    let dismissed = Cookies.get('dismissed_notes')
    if (dismissed !== undefined) {
      setDismissedNotes(JSON.parse(dismissed))
    }
  }, [])

  useEffect(() => {
    if (notifications.notfs !== null) {
      let filteredDismiss = notifications.notfs.filter((x) => !dismissedNotes.includes(x.id))
      setNotesCounter(filteredDismiss.length)
    }
  }, [notifications, dismissedNotes])

  const isUserDataAvailable = userAuth?.initialized && userAuth?.userInfo
  const isAdmin = isUserDataAvailable && userAuth?.userInfo?.role?.type === 'admin'

  return (
    <NotificationsContext.Provider value={{ notifications, setNotifications }}>
      <>
        <header className={styles.ax_topbar}>
          <Link href="/dashboard" legacyBehavior>
            {isAdmin ? (
              <a>
                <img src="/images/indi-symbol.svg" alt="indi central logo" width="40" height="40" />
              </a>
            ) : (
              <a className={styles.ax_logo}>
                <img src="/images/indi-central-logo.svg" alt="indi central logo" />
              </a>
            )}
          </Link>
          <button className={styles.back} role="button" onClick={handleBack}>
            <UilArrowLeft size={16} /> Back
          </button>

          <GlobalSearch />

          <div className={styles.ax_topbar_actions}>
            {isAdmin && (
              <div style={{ marginRight: '12px' }}>
                <Button
                  label="Admin"
                  color="lined"
                  blank
                  isLink
                  linkPath="https://admin.indicentral.ca"
                  icon={<UilSetting />}
                  iconPos="right"
                  sizing="small"
                />
              </div>
            )}

            {userAuth?.userInfo?.isComplianceStaff || userAuth?.userInfo?.role?.type === 'admin' ? (
              <div style={{ marginRight: '12px' }}>
                <Button
                  label="Compliance Form"
                  color="lined"
                  blank
                  isLink
                  linkPath={`https://forms.zohopublic.ca/indimortgage1/form/ComplianceReviewForm/formperma/VGLmiaDqzoTzpEWLXQMq0MnrscknLHf0Yf0ZpOBfh_g?strapiID=${userAuth?.userInfo?.id}`}
                  icon={<UilFileCheckAlt />}
                  iconPos="right"
                  sizing="small"
                />
              </div>
            ) : (
              ''
            )}
            <div>
              <button type="button" className={styles.ax_noftfs_button} onClick={() => setShowNotfs(!showNotfs)}>
                {notesCounter !== 0 ? <span>{notesCounter}</span> : ''}
                <UilBell size={32} />
              </button>
              <Notifications isopen={showNotfs} />
            </div>
            {isUserDataAvailable ? (
              <>
                <h3>
                  {userAuth.userInfo.firstname} {userAuth.userInfo.lastname}
                </h3>
                <Avatar user={userData || ''} size={40} />
              </>
            ) : null}
            <button
              className={`${styles.btnMenuMob} ${menuMobIsOpen ? styles.btnMenuMobOpen : ''}`}
              onClick={handleOpenMenuMob}
            >
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </header>
        <MenuMob isMenuOpen={menuMobIsOpen} />
      </>
    </NotificationsContext.Provider>
  )
}

export default Topbar
