import { useContext, useRef, useState, useEffect } from 'react'

import SignatureContext from '../../context/signatureContext'
import iconsLinks from './iconsLinks'
import SocialIcons from './SocialIcons'
import style from '../../styles/Signature.module.scss'
import Toast from '../Toast'
import formatUrl from '../../helpers/formatUrl'
import Modal from '../Modal'

const Signature = (props) => {
  const { user, customStyle, dark, extraButton, secondContact } = props
  const signatureHTML = useRef(null)
  const [processing, setProcessing] = useState(false)
  const [context] = useContext(SignatureContext)
  const [showHtmlModal, setShowHtmlModal] = useState(false)
  const [htmlContent, setHtmlContent] = useState('')

  const photoUrl = () => {
    if (user.circularPhotoUrl) {
      return user.circularPhotoUrl
    }
    if (context && context.circularPhotoUrl) {
      return context.circularPhotoUrl
    }
    if (user && user.photo && user.photo.formats && user.photo.formats.thumbnail) {
      return user.photo.formats.thumbnail.url
    }
    return ''
  }

  const getLogoUrl = () => {
    if (context.team && context.team.logo) {
      if (context.team.logo.formats && context.team.logo.formats.small && context.team.logo.formats.small.url) {
        return context.team.logo.formats.small.url
      }
      if (context.team.logo.url) {
        return context.team.logo.url
      }
    }
    return ''
  }

  const logoUrl = getLogoUrl()

  const setIcon = (icon) => {
    if (customStyle && customStyle.styleEnabled) {
      if (dark) {
        return iconsLinks[icon].white
      }
      if (!dark || dark === null || dark === undefined) {
        return iconsLinks[icon].black
      }
    } else {
      if (dark) {
        return iconsLinks[icon].white
      }
      if (!dark || dark === null || dark === undefined) {
        return iconsLinks[icon].standard
      }
    }
  }

  const networks = () => {
    const socials = ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube']
    let socialNets = {}

    Object.keys(context).forEach((social) => {
      socials.filter((item) => {
        if (item === social) {
          socialNets = { ...socialNets, [social]: context[social] }
        }
        return socialNets
      })
    })
    return socialNets
  }

  const colors = () => {
    if (customStyle && customStyle.styleEnabled) {
      return {
        primary: customStyle.styles.highlightColorHex,
        secondary: customStyle.styles.darkColorHex
      }
    }
    return null
  }

  const socialNetworks = networks()

  const formatPhone = (phone) => {
    let newPhone = phone.replace(/\D/g, '')

    return `${newPhone.slice(0, 3)}.${newPhone.slice(3, 6)}.${newPhone.slice(6, 10)}`
  }

  //Copying HTML Email Signature
  const copyHtml = (e) => {
    e.preventDefault()
    setProcessing(true)
    const signature = signatureHTML.current
    const range = document.createRange()
    range.selectNode(signature)
    window.getSelection().removeAllRanges()
    window.getSelection().addRange(range)
    document.execCommand('copy')
    setTimeout(() => {
      setProcessing(false)
    }, 3000)
  }

  const prettifyHtml = (html) => {
    // Simple HTML prettifier
    let formatted = ''
    let indent = ''

    // Replace all > followed by < with >\n<
    html = html.replace(/>\s*</g, '>\n<')

    // Process each line
    html.split('\n').forEach((line) => {
      // If this line is a closing tag, decrease indent before printing
      if (line.match(/^<\//)) {
        indent = indent.substring(2)
      }

      // Add the line with proper indentation
      formatted += indent + line + '\n'

      // If this line is an opening tag and not a self-closing tag, increase indent after printing
      if (line.match(/^<[^/]/) && !line.match(/\/>/)) {
        // Don't indent for inline elements or text nodes
        if (!line.match(/<(span|a|strong|em|img|br|input|hr|meta|link)/)) {
          indent += '  '
        }
      }
    })

    return formatted
  }

  const showHtmlCode = () => {
    const signature = signatureHTML.current
    setHtmlContent(prettifyHtml(signature.outerHTML))
    setShowHtmlModal(true)
  }

  const copyHtmlFromModal = (confirmed) => {
    if (confirmed) {
      navigator.clipboard.writeText(htmlContent)
      setShowHtmlModal(false)
      setProcessing(true)
      setTimeout(() => {
        setProcessing(false)
      }, 3000)
    } else {
      setShowHtmlModal(false)
    }
  }

  const { logo } = props

  return (
    <>
      <table
        className={style.ax_signature}
        width="560"
        border="0"
        cellPadding="0"
        cellSpacing="0"
        style={{
          fontFamily: 'Arial, sans-serif !important',
          backgroundImage:
            'url(https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/white_pixel_0d3f6a60cf.jpg)',
          backgroundRepeat: 'repeat'
        }}
        ref={signatureHTML}
      >
        <tbody style={{ backgroundColor: '#fff' }}>
          <tr>
            <td colSpan="3" style={{ backgroundColor: '#fff' }}>
              <table cellPadding="0" cellSpacing="0">
                <tbody>
                  <tr>
                    <td
                      width="150"
                      style={{
                        borderRight: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                        verticalAlign: 'top',
                        paddingRight: '16px'
                      }}
                    >
                      <p>
                        {' '}
                        <img
                          src={photoUrl()}
                          alt={`${context.firstname} ${context.lastname}`}
                          width="130"
                          height="130"
                          style={{
                            width: '130px',
                            height: '130px',
                            borderRadius: '65px',
                            border: '2px solid #415A71',
                            objectFit: 'cover',
                            marginLeft: 'auto',
                            marginRight: 'auto',
                            display: 'block'
                          }}
                        />
                      </p>
                      {logo && context.team.logo && context.team.logo.url ? (
                        <p style={{ minWidth: '150px' }}>
                          <img
                            src={logoUrl}
                            style={{ verticalAlign: 'middle', margin: '0 auto 5px auto', width: '130px' }}
                            alt="logo"
                            width="130"
                          />
                        </p>
                      ) : (
                        ''
                      )}
                      {context.applicationLink && context.applicationLink.length > 0 ? (
                        <p>
                          <a
                            href={formatUrl(context.applicationLink)}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '0 auto 16px auto',
                              background: `${colors() !== null ? colors().primary : '#2a7a94'}`,
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Apply Now
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.appointmentScheduleLink && context.appointmentScheduleLink.length > 0 ? (
                        <p>
                          <a
                            href={formatUrl(context.appointmentScheduleLink)}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '16px auto 16px auto',
                              background: `${colors() !== null ? colors().primary : '#2a7a94'}`,
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Schedule a Call
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.googleReviewsLink && context.googleReviewsLink.length > 0 ? (
                        <p>
                          <a
                            href={formatUrl(context.googleReviewsLink)}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '16px auto 16px auto',
                              background: `${colors() !== null ? colors().primary : '#2a7a94'}`,
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Leave a Review
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {extraButton && extraButton.enabled ? (
                        <p>
                          <a
                            href={extraButton && extraButton.link ? formatUrl(extraButton.link) : '#'}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '0 auto 16px auto',
                              background: `${colors() !== null ? colors().primary : '#2a7a94'}`,
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            {extraButton && extraButton.label ? extraButton.label : 'Click Here'}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}
                    </td>

                    <td width="370" style={{ verticalAlign: 'top', paddingTop: '8px' }}>
                      <h1
                        style={{
                          fontFamily: 'Arial, sans-serif !important',
                          fontSize: '21px',
                          lineHeight: '24px',
                          color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                          margin: '0 0 0 20px'
                        }}
                      >
                        {`${context.firstname} `}
                        {context.lastname}
                        {context.titles.length > 0 ? ', ' : ''}
                        <span style={{ fontSize: '12px' }}>{context.titles.length > 0 ? context.titles : ''}</span>
                      </h1>
                      <h4
                        style={{
                          fontFamily: 'Arial, sans-serif !important',
                          fontSize: '14px',
                          lineHeight: '18px',
                          color: `${colors() !== null ? colors().primary : '#2a7a94'}`,
                          margin: '0 0 16px 20px'
                        }}
                      >
                        {context.position}
                        <span />
                      </h4>

                      {context.tagline && context.tagline.length > 0 ? (
                        <h4
                          style={{
                            fontFamily: 'Arial, sans-serif !important',
                            fontSize: '12px',
                            lineHeight: '16px',
                            color: '#444444',
                            margin: '0 0 16px 20px',
                            fontWeight: 'normal'
                          }}
                        >
                          {context.tagline}
                        </h4>
                      ) : (
                        ''
                      )}

                      {context.phone && context.phone.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a
                            href={`tel:${context.phone === null ? '#' : context.phone.replace(/\D/g, '')}${
                              context.ext && context.ext.length > 0 ? ',' + context.ext : ''
                            }`}
                            style={
                              context.phone === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '8px 0 4px 16px'
                                  }
                            }
                          >
                            <img
                              src={setIcon('phone')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle' }}
                              alt="phone icon"
                            />
                            {context.phone === null ? '-----' : formatPhone(context.phone)}{' '}
                            {context.ext && context.ext.length > 0 ? 'ext. ' + context.ext : ''}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.secondPhone && context.secondPhone.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a
                            href={`tel:${context.secondPhone === null ? '#' : context.secondPhone.replace(/\D/g, '')}${
                              context.ext && context.ext.length > 0 ? ',' + context.ext : ''
                            }`}
                            style={
                              context.secondPhone === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '0 0 4px 16px'
                                  }
                            }
                          >
                            <img
                              src={setIcon('phone')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle' }}
                              alt="phone icon"
                            />
                            {context.secondPhone === null ? '-----' : formatPhone(context.secondPhone)}{' '}
                            {context.secondExt && context.secondExt.length > 0 ? 'ext. ' + context.secondExt : ''}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.email.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a
                            href={`mailto:${context.email === null ? '#' : context.email}`}
                            style={
                              context.email === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '0 0 4px 16px'
                                  }
                            }
                          >
                            <img
                              src={setIcon('email')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle', marginRight: '2px' }}
                              alt="email icon"
                            />
                            {context.email === null ? '-----' : context.email}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.website.length > 0 && context.website !== '#' ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a
                            href={context.website === null ? '#' : formatUrl(context.website)}
                            target="_blank"
                            style={
                              context.website === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '0 0 4px 16px'
                                  }
                            }
                            rel="noreferrer"
                          >
                            <img
                              src={setIcon('website')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle' }}
                              alt="website icon"
                            />
                            {context.website === null ? '-----' : context.website}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {context.license ? (
                        <p
                          style={{
                            fontSize: '12px',
                            paddingLeft: '8px',
                            lineHeight: '24px',
                            display: 'block',
                            textDecoration: 'none',
                            margin: '0 0 16px 16px',
                            color: `${colors() !== null ? colors().secondary : '#415A71'}`
                          }}
                        >
                          License {context.license}
                        </p>
                      ) : (
                        ''
                      )}

                      {secondContact &&
                      secondContact.secondaryName &&
                      secondContact.secondaryName &&
                      secondContact.secondaryName.length > 0 ? (
                        <p
                          style={{
                            marginTop: '0',
                            marginBottom: '0',
                            color: `${colors() !== null ? colors().primary : '#415A71'}`,
                            fontSize: '15px',
                            paddingLeft: '4px',
                            lineHeight: '24px',
                            height: '24px',
                            display: 'block',
                            textDecoration: 'none',
                            margin: '24px 0 0 16px'
                          }}
                        >
                          <strong>{secondContact.secondaryName}</strong>
                        </p>
                      ) : (
                        ''
                      )}

                      {secondContact &&
                      secondContact.secondaryPosition &&
                      secondContact.secondaryPosition &&
                      secondContact.secondaryPosition.length > 0 ? (
                        <p
                          style={{
                            marginTop: '0',
                            marginBottom: '0',
                            color: `${colors() !== null ? colors().primary : '#415A71'}`,
                            fontSize: '12px',
                            paddingLeft: '4px',
                            lineHeight: '18px',
                            height: '24px',
                            display: 'block',
                            textDecoration: 'none',
                            margin: '0 0 4px 16px'
                          }}
                        >
                          <strong>{secondContact.secondaryPosition}</strong>
                        </p>
                      ) : (
                        ''
                      )}

                      {secondContact && secondContact.secondaryPhone && secondContact.secondaryPhone.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a
                            href={`tel:${secondContact.secondaryPhone === null ? '#' : secondContact.secondaryPhone}${
                              secondContact.ext && secondContact.ext.length > 0 ? ',' + secondContact.ext : ''
                            }`}
                            style={
                              secondContact.secondaryPhone === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '0 0 4px 16px'
                                  }
                            }
                          >
                            <img
                              src={setIcon('phone')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle' }}
                              alt="phone icon"
                            />
                            {secondContact.secondaryPhone === null ? '-----' : secondContact.secondaryPhone}{' '}
                            {secondContact.ext && secondContact.ext.length > 0 ? 'ext. ' + secondContact.ext : ''}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {secondContact && secondContact.secondaryEmail && secondContact.secondaryEmail.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }} id="emailField">
                          <a
                            href={`mailto:${
                              secondContact.secondaryEmail === null ? '#' : secondContact.secondaryEmail
                            }`}
                            style={
                              secondContact.secondaryEmail === null
                                ? { display: 'none' }
                                : {
                                    color: `${colors() !== null ? colors().secondary : '#415A71'}`,
                                    fontSize: '15px',
                                    paddingLeft: '4px',
                                    lineHeight: '24px',
                                    height: '24px',
                                    display: 'block',
                                    textDecoration: 'none',
                                    margin: '0 0 4px 16px'
                                  }
                            }
                          >
                            <img
                              src={setIcon('email')}
                              width="20"
                              height="20"
                              style={{ verticalAlign: 'middle', marginRight: '2px' }}
                              alt="email icon"
                            />
                            {secondContact.secondaryEmail === null ? '-----' : secondContact.secondaryEmail}
                          </a>
                        </p>
                      ) : (
                        ''
                      )}

                      {user && user.showBadges && user.showBadges.emailSignature ? (
                        <p style={{ paddingLeft: '16px' }}>
                          {user.badges.map((b) => {
                            const badgeName = typeof b.image !== 'undefined' ? `${b.image.hash}${b.image.ext}` : ''
                            // const badgeUrl = `https://res.cloudinary.com/axiom-mortgage/image/upload/w_105,h_105,q_100,e_improve,e_sharpen,dpr_2.0/${badgeName}`
                            const badgeUrl = b?.image?.url

                            if (b.enabled) {
                              return (
                                <img
                                  key={b.id}
                                  src={badgeUrl}
                                  alt={b.title}
                                  style={{
                                    width: '105px',
                                    height: '105px',
                                    display: 'inline-block',
                                    margin: '0 8px 0 0'
                                  }}
                                  width="105"
                                  height="105"
                                />
                              )
                            }
                          })}
                        </p>
                      ) : (
                        ''
                      )}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td colSpan="3" style={{ paddingTop: '8px', backgroundColor: '#fff' }} />
          </tr>
          <tr>
            {context && context.team && context.team.id === '60c0aa8960fa67001794f578' ? (
              <td
                style={{
                  borderTop: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                  paddingTop: '8px',
                  backgroundColor: '#fff'
                }}
              ></td>
            ) : (
              <td
                style={{
                  borderTop: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                  paddingTop: '8px',
                  backgroundColor: '#fff'
                }}
              >
                {context.team && context.team.customSignatureLogo ? (
                  <img
                    src={context.team.customSignatureLogo.url}
                    alt={context.brokerage}
                    style={{ width: '200', height: 'auto' }}
                    width="200"
                    height="auto"
                  />
                ) : (
                  <img
                    src={setIcon('logo')}
                    alt={context.brokerage}
                    style={{ width: '200px', height: '38px' }}
                    width="200"
                    height="38"
                  />
                )}
              </td>
            )}

            {context && context.team && context.team.id === '60c0aa8960fa67001794f578' ? (
              <td
                style={{
                  verticalAlign: 'top',
                  borderTop: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                  width: '160px',
                  backgroundColor: '#fff'
                }}
              ></td>
            ) : (
              <td
                style={{
                  verticalAlign: 'top',
                  borderTop: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                  width: '160px',
                  backgroundColor: '#fff'
                }}
              >
                {props.user && props.user.team && props.user.team.showFSRA ? (
                  <p
                    style={{
                      margin: 0,
                      paddingTop: '25px',
                      paddingLeft: '8px',
                      verticalAlign: 'middle',
                      height: '40px',
                      fontSize: '10px',
                      fontWeight: 'bold'
                    }}
                  >
                    FSRA&nbsp;12403
                  </p>
                ) : (
                  ''
                )}

                {context.team &&
                context.team.signatureAdditionalImages &&
                context.team.signatureAdditionalImages.length > 0 ? (
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between', maxWidth: '160px', paddingTop: '5px' }}
                  >
                    {context.team.signatureAdditionalImages.map((img, index) => {
                      const imgWidth = Math.floor(
                        (160 - 12 * (context.team.signatureAdditionalImages.length - 1)) /
                          context.team.signatureAdditionalImages.length
                      )
                      return (
                        <img
                          key={index}
                          src={img.url}
                          alt={`Additional logo ${index + 1}`}
                          style={{
                            height: '45px',
                            width: `${imgWidth}px`,
                            marginRight: index < context.team.signatureAdditionalImages.length - 1 ? '12px' : '0'
                          }}
                        />
                      )
                    })}
                  </div>
                ) : null}
              </td>
            )}
            <td
              width="154"
              style={{
                verticalAlign: 'top',
                borderTop: `1px solid ${colors() !== null ? colors().primary : '#2a7a94'}`,
                backgroundColor: '#fff'
              }}
            >
              <table
                width="154"
                height="32"
                border="0"
                cellPadding="0"
                cellSpacing="0"
                style={{ textAlign: 'right', margin: '0 0 0 10px' }}
              >
                <tbody>
                  <tr>
                    <td style={{ width: '100px' }} />
                    <SocialIcons
                      networks={socialNetworks}
                      isDark={dark}
                      isBranded={customStyle && customStyle.styleEnabled ? true : false}
                    />
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
      <div className={style.signatureButtons}>
        <button type="button" className={style.ax_btn_copy} onClick={(e) => copyHtml(e)}>
          Copy Signature
        </button>
        <button type="button" className={style.ax_btn_html} onClick={showHtmlCode}>
          Copy HTML Code
        </button>
      </div>
      <Toast
        showToast={processing}
        toastType="success"
        message="Your signature was copied to clipboard. Paste it on your signature area."
      />
      {showHtmlModal && (
        <Modal
          title="Signature HTML Code"
          isVisible={showHtmlModal}
          content={
            <div className={style.htmlCodeContainer}>
              <pre>{htmlContent}</pre>
            </div>
          }
          callback={copyHtmlFromModal}
          label="Copy HTML Code"
        />
      )}
    </>
  )
}

export default Signature
