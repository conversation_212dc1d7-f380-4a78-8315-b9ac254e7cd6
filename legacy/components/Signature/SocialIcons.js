import { useContext } from 'react'
import iconsLinks from './iconsLinks'
import SignatureContext from '../../context/signatureContext'

const SocialIcons = (props) => {
  const [context] = useContext(SignatureContext)
  const { isDark, isBranded } = props

  const setIcon = (icon) => {
    if (isBranded) {
      if (isDark) {
        return iconsLinks[icon].white
      }
      if (!isDark || isDark === null || isDark === undefined) {
        return iconsLinks[icon].black
      }
    } else {
      if (isDark) {
        return iconsLinks[icon].white
      }
      if (!isDark || isDark === null || isDark === undefined) {
        return iconsLinks[icon].standard
      }
    }
  }

  return (
    <>
      {context.facebook ? (
        <td
          width="27"
          style={
            context.facebook !== null
              ? {
                  display: 'table-cell',
                  verticalAlign: 'middle',
                  lineHeight: '22px',
                  height: '22px',
                  paddingBottom: '0',
                  marginBottom: '0',
                  paddingTop: '8px'
                }
              : { display: 'none' }
          }
        >
          <a
            style={{ width: '22px', height: '22px', display: 'table', marginLeft: '4px', textAlign: 'right' }}
            href={context.facebook === null ? '#' : context.facebook}
            target="_blank"
            rel="noreferrer"
          >
            <img src={setIcon('facebook')} width="22" height="22" alt="" />
          </a>
        </td>
      ) : (
        <td></td>
      )}
      {context.instagram ? (
        <td
          width="27"
          style={
            context.instagram !== null
              ? {
                  display: 'table-cell',
                  verticalAlign: 'middle',
                  lineHeight: '22px',
                  height: '22px',
                  paddingBottom: '0',
                  marginBottom: '0',
                  paddingTop: '8px'
                }
              : { display: 'none' }
          }
        >
          <a
            style={{ width: '22px', height: '22px', display: 'table', marginLeft: '4px', textAlign: 'right' }}
            href={context.instagram === null ? '#' : context.instagram}
            target="_blank"
            rel="noreferrer"
          >
            <img src={setIcon('instagram')} width="22" height="22" alt="" />
          </a>
        </td>
      ) : (
        <td></td>
      )}
      {context.linkedin ? (
        <td
          width="27"
          style={
            context.linkedin !== null
              ? {
                  display: 'table-cell',
                  verticalAlign: 'middle',
                  lineHeight: '22px',
                  height: '22px',
                  paddingBottom: '0',
                  marginBottom: '0',
                  paddingTop: '8px'
                }
              : { display: 'none' }
          }
        >
          <a
            style={{ width: '22px', height: '22px', display: 'table', marginLeft: '4px', textAlign: 'right' }}
            href={context.linkedin === null ? '#' : context.linkedin}
            target="_blank"
            rel="noreferrer"
          >
            <img src={setIcon('linkedin')} width="22" height="22" alt="" />
          </a>
        </td>
      ) : (
        <td></td>
      )}
      {context.twitter ? (
        <td
          width="27"
          style={
            context.twitter !== null
              ? {
                  display: 'table-cell',
                  verticalAlign: 'middle',
                  lineHeight: '20px',
                  height: '20px',
                  paddingBottom: '0',
                  marginBottom: '0',
                  paddingTop: '8px'
                }
              : { display: 'none' }
          }
        >
          <a
            style={{ width: '22px', height: '22px', display: 'table', marginLeft: '4px', textAlign: 'right' }}
            href={context.twitter === null ? '#' : context.twitter}
            target="_blank"
            rel="noreferrer"
          >
            <img src={setIcon('twitter')} width="22" height="22" alt="" />
          </a>
        </td>
      ) : (
        <td></td>
      )}
      {context.youtube ? (
        <td
          width="27"
          style={
            context.youtube !== null
              ? {
                  display: 'table-cell',
                  verticalAlign: 'middle',
                  lineHeight: '22px',
                  height: '22px',
                  paddingBottom: '0',
                  marginBottom: '0',
                  paddingTop: '8px'
                }
              : { display: 'none' }
          }
        >
          <a
            style={{ width: '22px', height: '22px', display: 'table', marginLeft: '4px', textAlign: 'right' }}
            href={context.youtube === null ? '#' : context.youtube}
            target="_blank"
            rel="noreferrer"
          >
            <img src={setIcon('youtube')} width="22" height="22" alt="" />
          </a>
        </td>
      ) : (
        <td></td>
      )}
      <td
        width="1"
        style={{
          display: 'table-cell',
          verticalAlign: 'middle',
          lineHeight: '22px',
          height: '22px',
          paddingBottom: '0',
          marginBottom: '0',
          paddingTop: '8px'
        }}
      ></td>
    </>
  )
}

export default SocialIcons
