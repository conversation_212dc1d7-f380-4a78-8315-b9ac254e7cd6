// components/Signature/Form.js

import { useState, useContext, useRef, useEffect } from 'react'
import axios from 'axios'
import Cookies from 'js-cookie'
import formatUrl from '../../helpers/formatUrl'
import SignatureContext from '../../context/signatureContext'
import style from '../../styles/SignatureForm.module.scss'

const Form = ({ user, isAdmin }) => {
  const [formInfo, setFormInfo] = useState(user)
  const form = useRef(null)
  const [context, setContext] = useContext(SignatureContext)
  const [processing, setProcessing] = useState(false)

  const apiURL = process.env.NEXT_PUBLIC_API_URL
  const token = Cookies.get('jwt')
  const config = { headers: { Authorization: `Bearer ${token}` } }
  const userId = Cookies.get('userId')

  const updateInfo = (e) => {
    e.preventDefault()
    const { name } = e.target
    let { value } = e.target

    if (name === 'phone' || name === 'secondPhone') {
      setFormInfo({ ...formInfo, [name]: { raw: value.replace(/\D/g, ''), input: value } })
      return
    }

    if (
      name === 'website' ||
      name === 'secondaryWebsite' ||
      name === 'appointmentScheduleLink' ||
      name === 'googleReviewsLink' ||
      name === 'applicationLink' ||
      name === 'facebook' ||
      name === 'instagram' ||
      name === 'linkedin' ||
      name === 'twitter' ||
      name === 'youtube'
    ) {
      value = formatUrl(e.target.value)
    }

    setFormInfo({ ...formInfo, [name]: value })
  }

  const generateSignature = async (e) => {
    e.preventDefault()
    setProcessing(true)

    const info = form.current.children
    const infoArr = Array.from(info)

    let signatureData = user

    infoArr.forEach((i) => {
      const item = i.querySelectorAll('input')
      item.forEach((f) => {
        let itemValue = f.value || ''
        const itemName = f.name
        signatureData = { ...signatureData, [itemName]: itemValue }
      })
    })

    try {
      let circularPhotoUrl = user.circularPhotoUrl || context.circularPhotoUrl

      // Only generate a new circular photo if one doesn't already exist
      if (!circularPhotoUrl) {
        console.log('Generating new circular photo...')
        const response = await axios.post(
          `${apiURL}/users/${user && user.id ? user.id : userId}/generate-circular-photo`,
          {}, // empty object as the request body
          config
        )
        circularPhotoUrl = response.data.circularPhotoUrl
        console.log('New circular photo generated:', circularPhotoUrl)
      } else {
        console.log('Using existing circular photo:', circularPhotoUrl)
      }

      // Update signature data with circular photo URL
      signatureData = { ...signatureData, circularPhotoUrl }

      setContext(signatureData)

      // Scroll to top after generating signature
      setTimeout(() => {
        window.scrollTo(0, 0)
      }, 300)
    } catch (error) {
      console.error('Error generating signature:', error)
      // Handle error (e.g., show error message to user)
    } finally {
      setProcessing(false)
    }
  }

  useEffect(() => {
    setFormInfo(user)
  }, [user])

  if (user && user !== null) {
    return (
      <>
        <form className={style.ax_form} onSubmit={(e) => generateSignature(e)} ref={form}>
          <div className={style.ax_field}>
            <label htmlFor="firstname">First Name</label>
            <input
              type="text"
              name="firstname"
              placeholder="First Name"
              defaultValue={user.firstname ? user.firstname : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="lastname">Last Name</label>
            <input
              type="text"
              name="lastname"
              placeholder="Last Name"
              defaultValue={user.lastname ? user.lastname : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="titles">Title After Name (e.g. AMP, BCC)</label>
            <input
              type="text"
              name="titles"
              placeholder="AMP, BCC, BCO"
              defaultValue={user.titles ? user.titles : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="position">Position</label>
            <input
              type="text"
              name="position"
              placeholder="I.E: Mortgage Broker, BCS"
              defaultValue={user.position ? user.position : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="license">License Number (Optional)</label>
            <input
              type="text"
              name="license"
              placeholder="I.E: #AXM003333"
              defaultValue={user.license ? user.license : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              defaultValue={user.email ? user.email : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <div className={style.phoneExt}>
              <div>
                <label htmlFor="phone">Phone</label>
                <input
                  type="tel"
                  name="phone"
                  placeholder="************"
                  defaultValue={user.phone ? user.phone : ''}
                  value={formInfo && formInfo.phone ? formInfo.phone.input : ''}
                  onChange={(e) => updateInfo(e)}
                />
              </div>
              <div>
                <label htmlFor="ext">Ext.</label>
                <input
                  type="tel"
                  name="ext"
                  placeholder="123"
                  defaultValue={user.ext ? user.ext : ''}
                  onChange={(e) => updateInfo(e)}
                />
              </div>
            </div>
          </div>

          <div className={style.ax_field}>
            <div className={style.phoneExt}>
              <div>
                <label htmlFor="phone">Second Phone</label>
                <input type="tel" name="secondPhone" placeholder="************" onChange={(e) => updateInfo(e)} />
              </div>
              <div>
                <label htmlFor="ext">Ext.</label>
                <input type="tel" name="secondExt" placeholder="123" onChange={(e) => updateInfo(e)} />
              </div>
            </div>
          </div>

          <div className={style.ax_field}>
            <label htmlFor="tagline">Tagline</label>
            <input
              type="text"
              name="tagline"
              placeholder="Team tagline"
              defaultValue={user.team && user.team.tagline ? user.team.tagline : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="website">Website</label>
            <input
              type="text"
              name="website"
              placeholder="I.E: https://axiommortgage.ca"
              defaultValue={user.website ? user.website : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="instagram">Instagram Page</label>
            <input
              type="text"
              name="instagram"
              placeholder="I.E: https://instagram.com/jane-doe"
              defaultValue={user.instagram ? user.instagram : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="facebook">Facebook Page</label>
            <input
              type="text"
              name="facebook"
              placeholder="I.E: https://facebook.com/jane-doe"
              defaultValue={user.facebook ? user.facebook : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="linkedin">Linkedin Page</label>
            <input
              type="text"
              name="linkedin"
              placeholder="I.E: https://linkedin.com/in/jane-doe"
              defaultValue={user.linkedin ? user.linkedin : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="twitter">X (Twitter) Page</label>
            <input
              type="text"
              name="twitter"
              placeholder="I.E: https://twitter.com/jane-doe"
              defaultValue={user.twitter ? user.twitter : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="youtube">Youtube Channel</label>
            <input
              type="text"
              name="youtube"
              placeholder="I.E: https://youtube.com/c/jane-doe"
              defaultValue={user.youtube ? user.youtube : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="applicationLink">Mortgage Application Link</label>
            <input
              type="text"
              name="applicationLink"
              placeholder="I.E: https://mtgapp.scarlettnetwork.com/broker-name/home"
              defaultValue={user.applicationLink ? user.applicationLink : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="appointmentScheduleLink">Appointment Schedule Link (I.e. Calendly)</label>
            <input
              type="text"
              name="appointmentScheduleLink"
              id="appointmentScheduleLink"
              placeholder="Calendly or Other"
              defaultValue={user.appointmentScheduleLink ? user.appointmentScheduleLink : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>

          <div className={style.ax_field}>
            <label htmlFor="googleReviewsLink">Google Reviews Link</label>
            <input
              type="text"
              name="googleReviewsLink"
              id="googleReviewsLink"
              placeholder="Link to your Google Reviews page"
              defaultValue={user.googleReviewsLink ? user.googleReviewsLink : ''}
              onChange={(e) => updateInfo(e)}
            />
          </div>
          <div style={{ width: '100%' }}>
            <h2 style={{ marginTop: '24px' }}>Second Contact Person (Optional)</h2>
          </div>
          <div className={style.ax_field}>
            <label>Name</label>
            <input type="text" name="secondaryName" onChange={(e) => updateInfo(e)} />
          </div>
          <div className={style.ax_field}>
            <label>Position</label>
            <input type="text" name="secondaryPosition" onChange={(e) => updateInfo(e)} />
          </div>

          <div className={style.ax_field}>
            <label>Email</label>
            <input type="text" name="secondaryEmail" onChange={(e) => updateInfo(e)} />
          </div>

          <div className={style.ax_field}>
            <label>Phone</label>
            <input type="text" name="secondaryPhone" onChange={(e) => updateInfo(e)} />
          </div>
        </form>

        <div className={style.ax_field}>
          <button
            className={style.ax_btn_submit}
            name="generate"
            type="submit"
            onClick={(e) => generateSignature(e)}
            disabled={processing}
            style={{ width: '200px', alignSelf: 'flex-end', display: 'block', cursor: 'pointer' }}
          >
            {processing ? (
              <>
                <img src="/images/spinner-white.svg" alt="spinner" /> <span>Generating...</span>
              </>
            ) : (
              'Generate Signature'
            )}
          </button>
        </div>
      </>
    )
  } else {
    return ''
  }
}

export default Form
