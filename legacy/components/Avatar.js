import Link from 'next/link'
import CircularPhoto from './CircularPhoto'
import { useReducer } from 'react'

const Avatar = (props) => {
  const { size, user } = props

  // If no user data is available, don't render the avatar link
  if (!user) {
    return <div style={{ width: size, height: size }}></div>
  }

  return (
    <Link href="/profile" style={{ cursor: 'pointer' }}>
      <CircularPhoto user={user} size={size} />
    </Link>
  )
}

export default Avatar
