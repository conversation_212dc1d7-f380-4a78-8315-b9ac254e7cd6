import style from '../styles/Texts.module.scss'

const SectionTitle = (props) => {
  const { icon, title, position } = props

  const setPosition = () => {
    if (position && position === 'center') {
      return { textAlign: 'center', justifyContent: 'center' }
    } else {
      return {}
    }
  }

  return (
    <header className={style.sectionTitle} style={setPosition()}>
      {icon}
      <h2>{title}</h2>
    </header>
  )
}

export default SectionTitle
