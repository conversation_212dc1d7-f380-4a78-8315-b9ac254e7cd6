import { useState, useEffect } from 'react'
import {
  calculateRangeRate,
  calculateDownPayment,
  calculateInsurance,
  calculatePrincipal,
  calculateEffectiveRate,
  calculateMonthlyPayment,
  calculatePeriodicPayment,
  calculateTotalCashNeeded,
  calculateTotalMonthlyExpenses,
  calculateTotalYearlyExpenses
} from '../utils/calculationUtils'

/**
 * Custom hook for mortgage calculations
 * Maintains the same reactive calculation flow as the original component
 */
export const useMortgageCalculator = (initialOptions = {}) => {
  // Default options with overrides
  const options = {
    maxAmortization: 25, // Default to 25 years max
    ...initialOptions
  }

  // Main state object with all calculator values
  const [calcState, setCalcState] = useState({
    // Property info
    amount: 0,
    houseType: 'select',
    mlsCode: '',
    address: '',
    propertyPhoto: { url: null, ext: null },

    // Down payment options
    rangeRate: { type: 'normalTier', rate: null },
    downPay5: 0,
    downPay10: 0,
    downPay15: 0,
    downPay20: 0,
    downPayCustom: 0,
    downPayRange: 0,
    downPayFTHB: 0,
    chosenDownPay: { percent: 'select', amount: 0 },

    // Insurance amounts
    insurance5: 0,
    insurance10: 0,
    insurance15: 0,
    insurance20: 0,
    insuranceCustom: 0,
    insuranceRange: 0,
    insuranceFTHB: 0,

    // Mortgage principal amounts
    principal5: 0,
    principal10: 0,
    principal15: 0,
    principal20: 0,
    principalCustom: 0,
    principalRange: 0,
    principalFTHB: 0,
    chosenPrincipal: 0,

    // Interest rates
    rate5: 0,
    rate10: 0,
    rate15: 0,
    rate20: 0,
    rateCustom: 0,
    rateRange: 0,
    rateFTHB: 0,
    rate: 0, // Selected rate

    // Effective rates (calculated from nominal rates)
    effectiveRates: {
      effectiveRate5: 0,
      effectiveRate10: 0,
      effectiveRate15: 0,
      effectiveRate20: 0,
      effectiveRateCustom: 0,
      effectiveRateRange: 0,
      effectiveRateFTHB: 0
    },

    // Amortization and payment frequency
    years: 0,
    customYears: 0,
    frequency: 'monthly',
    numberOfPayments: 0,

    // Monthly payments
    monthlyPay5: 0,
    monthlyPay10: 0,
    monthlyPay15: 0,
    monthlyPay20: 0,
    monthlyPayCustom: 0,
    monthlyPayRange: 0,
    monthlyPayFTHB: 0,

    // Periodic payments (adjusted by frequency)
    periodicPay5: 0,
    periodicPay10: 0,
    periodicPay15: 0,
    periodicPay20: 0,
    periodicPayCustom: 0,
    periodicPayRange: 0,
    periodicPayFTHB: 0,
    chosenPeriodicPay: 0,

    // Custom percentage
    customPercentage: 0,

    // Cash needed inputs
    lawyerFee: 0,
    homeInspection: 0,
    appraisal: 0,
    titleInsurance: 0,
    estoppelFee: 0,
    totalCashNeeded: 0,

    // Monthly expenses
    monthlyDebtPayments: 0,
    utilities: 0,
    condoFees: 0,
    phone: 0,
    cable: 0,
    internet: 0,
    totalMonthlyPayments: 0,

    // Yearly expenses
    propertyTax: 0,
    propertyInsurance: 0,
    hoaFees: 0,
    totalYearlyPayments: 0,

    // UI state
    showMortgagePayment: true,
    showBodyCashNeeded: true,
    showBodyMonthlyExpenses: true,
    showBodyYearlyExpenses: true,
    showAdditionalInfo: true,
    showRealtorInfo: true,
    showImages: true,
    loading: false,

    // PDF options
    pdf: {
      full: false,
      short: true,
      mlsCode: null,
      address: null,
      monthlyExpenses: true,
      cashNeeded: true,
      propertyPhoto: { url: null, ext: null },
      realtorPhoto: { url: null, ext: null },
      user: null
    }
  })

  // Input handlers - Update specific fields
  const setAmount = (amount) => {
    setCalcState((prev) => ({
      ...prev,
      amount,
      askingPrice: amount
    }))
  }

  const setYears = (years) => {
    // Apply max amortization constraint if specified in options
    const constrainedYears = options.maxAmortization ? Math.min(years, options.maxAmortization) : years

    setCalcState((prev) => ({
      ...prev,
      years: constrainedYears
    }))
  }

  const setFrequency = (frequency) => {
    setCalcState((prev) => ({
      ...prev,
      frequency
    }))
  }

  const setRate = (rateKey, value) => {
    setCalcState((prev) => ({
      ...prev,
      [rateKey]: value
    }))
  }

  const setCustomPercentage = (percentage) => {
    setCalcState((prev) => ({
      ...prev,
      customPercentage: percentage
    }))
  }

  const setChosenDownPayment = (percent, amount) => {
    const rateKey =
      percent === 5
        ? 'rate5'
        : percent === 10
        ? 'rate10'
        : percent === 15
        ? 'rate15'
        : percent === 20
        ? 'rate20'
        : percent === 'range'
        ? 'rateRange'
        : percent === 'custom'
        ? 'rateCustom'
        : percent === 'fthb'
        ? 'rateFTHB'
        : 'rate5'

    setCalcState((prev) => ({
      ...prev,
      chosenDownPay: {
        percent,
        amount,
        rate: rateKey
      },
      chosenDownPayExpense: { percent, amount }
    }))
  }

  const setExpense = (key, value) => {
    setCalcState((prev) => ({
      ...prev,
      [key]: value
    }))
  }

  // Effect 1: Calculate down payments, insurance, and principal when amount changes
  useEffect(() => {
    if (!calcState.amount) return

    const amount = parseFloat(calcState.amount)
    const rangeRate = calculateRangeRate(amount)
    const isMiddleTier = rangeRate.type === 'middleTier'
    const isMaxTier = rangeRate.type === 'maxTier'

    // Calculate down payments for each percentage
    const downPay5 = isMiddleTier || isMaxTier ? 0 : calculateDownPayment(amount, 5)
    const downPay10 = isMaxTier ? 0 : calculateDownPayment(amount, 10)
    const downPay15 = isMaxTier ? 0 : calculateDownPayment(amount, 15)
    const downPay20 = calculateDownPayment(amount, 20)
    const downPayRange = isMiddleTier ? calculateDownPayment(amount, 'range', rangeRate) : 0

    // For FTHB, calculate the minimum down payment
    let downPayFTHB = amount * 0.05 // Default 5%
    if (amount > 500000 && amount <= 1500000) {
      const baseDown = 500000 * 0.05
      const additionalDown = (amount - 500000) * 0.1
      downPayFTHB = baseDown + additionalDown
    } else if (amount > 1500000) {
      downPayFTHB = amount * 0.2
    }

    // Calculate insurance amounts
    const calculateInsuranceForDownPayment = (downAmount, downPercent) => {
      if (downAmount === 0) return 0
      const mortgageAmount = amount - downAmount
      return calculateInsurance(mortgageAmount, downPercent)
    }

    const insurance5 = calculateInsuranceForDownPayment(downPay5, 5)
    const insurance10 = calculateInsuranceForDownPayment(downPay10, 10)
    const insurance15 = calculateInsuranceForDownPayment(downPay15, 15)
    const insurance20 = 0 // Always 0 for 20%
    const insuranceRange = isMiddleTier ? calculateInsuranceForDownPayment(downPayRange, rangeRate.rate) : 0

    // Calculate FTHB insurance
    const ftHBPercent = (downPayFTHB / amount) * 100
    const insuranceFTHB = calculateInsuranceForDownPayment(downPayFTHB, ftHBPercent)

    // Calculate principal amounts (loan amount + insurance)
    const principal5 = downPay5 > 0 ? amount - downPay5 + insurance5 : 0
    const principal10 = downPay10 > 0 ? amount - downPay10 + insurance10 : 0
    const principal15 = downPay15 > 0 ? amount - downPay15 + insurance15 : 0
    const principal20 = amount - downPay20 // No insurance for 20% down
    const principalRange = downPayRange > 0 ? amount - downPayRange + insuranceRange : 0
    const principalFTHB = amount - downPayFTHB + insuranceFTHB

    // Update state with all calculated values
    setCalcState((prev) => ({
      ...prev,
      rangeRate,
      downPay5,
      downPay10,
      downPay15,
      downPay20,
      downPayRange,
      downPayFTHB,
      insurance5,
      insurance10,
      insurance15,
      insurance20,
      insuranceRange,
      insuranceFTHB,
      principal5,
      principal10,
      principal15,
      principal20,
      principalRange,
      principalFTHB
    }))
  }, [calcState.amount])

  // Effect 2: Calculate custom down payment when customPercentage or amount changes
  useEffect(() => {
    if (!calcState.amount || !calcState.customPercentage) return

    const amount = parseFloat(calcState.amount)
    const customPct = parseFloat(calcState.customPercentage)
    const { rangeRate } = calcState

    // Check if custom percentage is valid for the current range rate
    const minRequiredPercent = rangeRate.type === 'middleTier' ? rangeRate.rate : rangeRate.type === 'maxTier' ? 20 : 5

    if (customPct < minRequiredPercent) {
      // Custom percentage is too low for the property tier
      setCalcState((prev) => ({
        ...prev,
        downPayCustom: 0,
        insuranceCustom: 0,
        principalCustom: 0
      }))
      return
    }

    // Calculate custom down payment
    const downPayCustom = amount * (customPct / 100)

    // Calculate insurance for custom down payment
    const mortgageAmount = amount - downPayCustom
    const insuranceCustom = calculateInsurance(mortgageAmount, customPct)

    // Calculate principal for custom down payment
    const principalCustom = mortgageAmount + insuranceCustom

    setCalcState((prev) => ({
      ...prev,
      downPayCustom,
      insuranceCustom,
      principalCustom
    }))
  }, [calcState.amount, calcState.customPercentage, calcState.rangeRate])

  // Effect 3: Calculate effective rates when interest rates change
  useEffect(() => {
    const calculateEffectiveRateFromState = (rateKey) => {
      const rate = parseFloat(calcState[rateKey])
      return isNaN(rate) || rate === 0 ? 0 : calculateEffectiveRate(rate)
    }

    const effectiveRates = {
      effectiveRate5: calculateEffectiveRateFromState('rate5'),
      effectiveRate10: calculateEffectiveRateFromState('rate10'),
      effectiveRate15: calculateEffectiveRateFromState('rate15'),
      effectiveRate20: calculateEffectiveRateFromState('rate20'),
      effectiveRateCustom: calculateEffectiveRateFromState('rateCustom'),
      effectiveRateRange: calculateEffectiveRateFromState('rateRange'),
      effectiveRateFTHB: calculateEffectiveRateFromState('rateFTHB')
    }

    setCalcState((prev) => ({
      ...prev,
      effectiveRates
    }))
  }, [
    calcState.rate5,
    calcState.rate10,
    calcState.rate15,
    calcState.rate20,
    calcState.rateCustom,
    calcState.rateRange,
    calcState.rateFTHB
  ])

  // Effect 4: Calculate monthly payments when effective rates or principals change
  useEffect(() => {
    const { years, effectiveRates } = calcState
    if (!years) return

    const calculateMonthlyPaymentForType = (principalKey, effectiveRateKey) => {
      const principal = parseFloat(calcState[principalKey])
      if (isNaN(principal) || principal <= 0) return 0

      const effectiveRate = effectiveRates[effectiveRateKey]
      if (isNaN(effectiveRate) || effectiveRate <= 0) return 0

      return calculateMonthlyPayment(principal, effectiveRate, years)
    }

    setCalcState((prev) => ({
      ...prev,
      monthlyPay5: calculateMonthlyPaymentForType('principal5', 'effectiveRate5'),
      monthlyPay10: calculateMonthlyPaymentForType('principal10', 'effectiveRate10'),
      monthlyPay15: calculateMonthlyPaymentForType('principal15', 'effectiveRate15'),
      monthlyPay20: calculateMonthlyPaymentForType('principal20', 'effectiveRate20'),
      monthlyPayCustom: calculateMonthlyPaymentForType('principalCustom', 'effectiveRateCustom'),
      monthlyPayRange: calculateMonthlyPaymentForType('principalRange', 'effectiveRateRange'),
      monthlyPayFTHB: calculateMonthlyPaymentForType('principalFTHB', 'effectiveRateFTHB')
    }))
  }, [
    calcState.years,
    calcState.effectiveRates,
    calcState.principal5,
    calcState.principal10,
    calcState.principal15,
    calcState.principal20,
    calcState.principalCustom,
    calcState.principalRange,
    calcState.principalFTHB
  ])

  // Effect 5: Calculate periodic payments when monthly payments or frequency changes
  useEffect(() => {
    const { frequency } = calcState

    const calculatePeriodicPaymentForMonthly = (monthlyPayKey) => {
      const monthlyPay = parseFloat(calcState[monthlyPayKey])
      if (isNaN(monthlyPay) || monthlyPay <= 0) return 0

      const { amount } = calculatePeriodicPayment(monthlyPay, frequency)
      return amount
    }

    let numberOfPayments
    switch (frequency) {
      case 'biweekly':
      case 'accbiweekly':
        numberOfPayments = 26
        break
      case 'weekly':
      case 'accweekly':
        numberOfPayments = 52
        break
      default:
        numberOfPayments = 12
    }

    setCalcState((prev) => ({
      ...prev,
      periodicPay5: calculatePeriodicPaymentForMonthly('monthlyPay5'),
      periodicPay10: calculatePeriodicPaymentForMonthly('monthlyPay10'),
      periodicPay15: calculatePeriodicPaymentForMonthly('monthlyPay15'),
      periodicPay20: calculatePeriodicPaymentForMonthly('monthlyPay20'),
      periodicPayCustom: calculatePeriodicPaymentForMonthly('monthlyPayCustom'),
      periodicPayRange: calculatePeriodicPaymentForMonthly('monthlyPayRange'),
      periodicPayFTHB: calculatePeriodicPaymentForMonthly('monthlyPayFTHB'),
      numberOfPayments
    }))
  }, [
    calcState.frequency,
    calcState.monthlyPay5,
    calcState.monthlyPay10,
    calcState.monthlyPay15,
    calcState.monthlyPay20,
    calcState.monthlyPayCustom,
    calcState.monthlyPayRange,
    calcState.monthlyPayFTHB
  ])

  // Effect 6: Update chosenPeriodicPay based on chosenDownPayExpense
  useEffect(() => {
    const { chosenDownPayExpense } = calcState
    if (!chosenDownPayExpense || !chosenDownPayExpense.percent) return

    let chosenPeriodicPay = 0
    let chosenPrincipal = 0

    const percent = parseFloat(chosenDownPayExpense.percent)

    if (percent === 5) {
      chosenPeriodicPay = calcState.periodicPay5
      chosenPrincipal = calcState.principal5
    } else if (percent === 10) {
      chosenPeriodicPay = calcState.periodicPay10
      chosenPrincipal = calcState.principal10
    } else if (percent === 15) {
      chosenPeriodicPay = calcState.periodicPay15
      chosenPrincipal = calcState.principal15
    } else if (percent === 20) {
      chosenPeriodicPay = calcState.periodicPay20
      chosenPrincipal = calcState.principal20
    } else if (percent > 5 && percent < 10) {
      chosenPeriodicPay = calcState.periodicPayRange
      chosenPrincipal = calcState.principalRange
    } else if (percent !== 5 && percent !== 10 && percent !== 15 && percent !== 20) {
      chosenPeriodicPay = calcState.periodicPayCustom
      chosenPrincipal = calcState.principalCustom
    }

    setCalcState((prev) => ({
      ...prev,
      chosenPeriodicPay,
      chosenPrincipal
    }))
  }, [
    calcState.chosenDownPayExpense,
    calcState.periodicPay5,
    calcState.periodicPay10,
    calcState.periodicPay15,
    calcState.periodicPay20,
    calcState.periodicPayCustom,
    calcState.periodicPayRange,
    calcState.principal5,
    calcState.principal10,
    calcState.principal15,
    calcState.principal20,
    calcState.principalCustom,
    calcState.principalRange
  ])

  // Effect 7: Calculate total cash needed
  useEffect(() => {
    const { chosenDownPay, lawyerFee, homeInspection, appraisal, titleInsurance, estoppelFee } = calcState

    const closingCosts = {
      lawyerFee,
      homeInspection,
      appraisal,
      titleInsurance,
      estoppelFee
    }

    const totalCashNeeded = calculateTotalCashNeeded(chosenDownPay.amount, closingCosts)

    setCalcState((prev) => ({
      ...prev,
      totalCashNeeded
    }))
  }, [
    calcState.chosenDownPay,
    calcState.lawyerFee,
    calcState.homeInspection,
    calcState.appraisal,
    calcState.titleInsurance,
    calcState.estoppelFee
  ])

  // Effect 8: Calculate total monthly expenses
  useEffect(() => {
    const { chosenPeriodicPay, monthlyDebtPayments, utilities, condoFees, phone, cable, internet } = calcState

    const monthlyExpenses = {
      monthlyDebtPayments,
      utilities,
      condoFees,
      phone,
      cable,
      internet
    }

    const totalMonthlyPayments = calculateTotalMonthlyExpenses(chosenPeriodicPay, monthlyExpenses)

    setCalcState((prev) => ({
      ...prev,
      totalMonthlyPayments,
      amortizationBalance: prev.chosenPrincipal // Match original behavior
    }))
  }, [
    calcState.chosenPeriodicPay,
    calcState.monthlyDebtPayments,
    calcState.utilities,
    calcState.condoFees,
    calcState.phone,
    calcState.cable,
    calcState.internet
  ])

  // Effect 9: Calculate total yearly expenses
  useEffect(() => {
    const { propertyTax, propertyInsurance, hoaFees } = calcState

    const yearlyExpenses = {
      propertyTax,
      propertyInsurance,
      hoaFees
    }

    const totalYearlyPayments = calculateTotalYearlyExpenses(yearlyExpenses)

    setCalcState((prev) => ({
      ...prev,
      totalYearlyPayments,
      amortizationBalance: prev.chosenPrincipal // Match original behavior
    }))
  }, [calcState.propertyTax, calcState.propertyInsurance, calcState.hoaFees])

  // Effect 10: Update rate based on chosen down payment
  useEffect(() => {
    if (!calcState.chosenDownPay || !calcState.chosenDownPay.rate) return

    const rate = calcState[calcState.chosenDownPay.rate] || 0

    setCalcState((prev) => ({
      ...prev,
      rate
    }))
  }, [calcState.chosenDownPay])

  // Validate the calculator state
  const validate = () => {
    let errors = []
    const { askingPrice, years, chosenDownPay } = calcState

    if (askingPrice === 0 || !askingPrice) {
      errors.push('Please add the asking price.')
    }
    if (chosenDownPay.percent === 'select' || !chosenDownPay.amount) {
      errors.push('Please select a down payment percentage.')
    }
    if (years === 0 || !years) {
      errors.push('Please add an amortization period.')
    }

    return errors
  }

  // Return the calculator state and methods
  return {
    state: calcState,
    setAmount,
    setYears,
    setFrequency,
    setRate,
    setCustomPercentage,
    setChosenDownPayment,
    setExpense,
    validate,

    // Additional methods for specific UI operations
    updateState: (updates) => setCalcState((prev) => ({ ...prev, ...updates })),
    toggleSection: (sectionKey) => {
      setCalcState((prev) => ({
        ...prev,
        [sectionKey]: !prev[sectionKey]
      }))
    },
    setPdfOption: (key, value) => {
      setCalcState((prev) => ({
        ...prev,
        pdf: {
          ...prev.pdf,
          [key]: value
        }
      }))
    }
  }
}

/**
 * Create specialized calculator hooks for each scenario
 */

export const useFTHBCalculator = (initialOptions = {}) => {
  return useMortgageCalculator({
    maxAmortization: 30, // FTHB can go up to 30 years
    ...initialOptions
  })
}

export const useNewBuildCalculator = (initialOptions = {}) => {
  return useMortgageCalculator({
    maxAmortization: 30, // New builds can go up to 30 years
    ...initialOptions
  })
}

export const usePreOwnedCalculator = (initialOptions = {}) => {
  return useMortgageCalculator({
    maxAmortization: 25, // Pre-owned restricted to 25 years
    ...initialOptions
  })
}
