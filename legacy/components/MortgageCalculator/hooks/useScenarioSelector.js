import { useState, useEffect } from 'react'
import { useFTHBCalculator, useNewBuildCalculator, usePreOwnedCalculator } from './useMortgageCalculator'

/**
 * Hook to manage the three mortgage calculator scenarios
 * - First-Time Home Buyer (FTHB)
 * - Regular Buyer for New Build
 * - Regular Buyer for Pre-owned Build
 */
export const useScenarioSelector = () => {
  // Create each calculator instance
  const fthbCalculator = useFTHBCalculator()
  const newBuildCalculator = useNewBuildCalculator()
  const preOwnedCalculator = usePreOwnedCalculator()

  // Track the currently selected scenario
  const [selectedScenario, setSelectedScenario] = useState('fthb')

  // Get the active calculator based on the selected scenario
  const getActiveCalculator = () => {
    switch (selectedScenario) {
      case 'fthb':
        return fthbCalculator
      case 'newBuild':
        return newBuildCalculator
      case 'preOwned':
        return preOwnedCalculator
      default:
        return fthbCalculator
    }
  }

  const activeCalculator = getActiveCalculator()

  // Synchronize the amount across all calculators when it changes in any of them
  useEffect(() => {
    const amount = activeCalculator.state.amount
    if (amount > 0) {
      if (selectedScenario !== 'fthb') fthbCalculator.setAmount(amount)
      if (selectedScenario !== 'newBuild') newBuildCalculator.setAmount(amount)
      if (selectedScenario !== 'preOwned') preOwnedCalculator.setAmount(amount)
    }
  }, [activeCalculator.state.amount, selectedScenario])

  // Create a merged state object with scenario-specific data for the PDF generator
  const getMergedState = () => {
    // Start with common property details from the active calculator
    const baseState = {
      amount: activeCalculator.state.amount,
      askingPrice: activeCalculator.state.amount,
      houseType: activeCalculator.state.houseType,
      mlsCode: activeCalculator.state.mlsCode,
      address: activeCalculator.state.address,

      // Add PDF options
      pdf: {
        ...activeCalculator.state.pdf,
        selectedScenario
      }
    }

    // Get scenario-specific calculation results
    const scenarioData = {
      fthb: {
        years: fthbCalculator.state.years,
        frequency: fthbCalculator.state.frequency,
        chosenDownPay: fthbCalculator.state.chosenDownPay,
        chosenPeriodicPay: fthbCalculator.state.chosenPeriodicPay,
        chosenPrincipal: fthbCalculator.state.chosenPrincipal,
        totalCashNeeded: fthbCalculator.state.totalCashNeeded,
        totalMonthlyPayments: fthbCalculator.state.totalMonthlyPayments,
        totalYearlyPayments: fthbCalculator.state.totalYearlyPayments
        // Add other FTHB-specific fields needed for the PDF
      },
      newBuild: {
        years: newBuildCalculator.state.years,
        frequency: newBuildCalculator.state.frequency,
        chosenDownPay: newBuildCalculator.state.chosenDownPay,
        chosenPeriodicPay: newBuildCalculator.state.chosenPeriodicPay,
        chosenPrincipal: newBuildCalculator.state.chosenPrincipal,
        totalCashNeeded: newBuildCalculator.state.totalCashNeeded,
        totalMonthlyPayments: newBuildCalculator.state.totalMonthlyPayments,
        totalYearlyPayments: newBuildCalculator.state.totalYearlyPayments
        // Add other new build-specific fields needed for the PDF
      },
      preOwned: {
        years: preOwnedCalculator.state.years,
        frequency: preOwnedCalculator.state.frequency,
        chosenDownPay: preOwnedCalculator.state.chosenDownPay,
        chosenPeriodicPay: preOwnedCalculator.state.chosenPeriodicPay,
        chosenPrincipal: preOwnedCalculator.state.chosenPrincipal,
        totalCashNeeded: preOwnedCalculator.state.totalCashNeeded,
        totalMonthlyPayments: preOwnedCalculator.state.totalMonthlyPayments,
        totalYearlyPayments: preOwnedCalculator.state.totalYearlyPayments
        // Add other pre-owned-specific fields needed for the PDF
      }
    }

    // Merge the base state with the selected scenario's data
    return {
      ...baseState,
      ...scenarioData[selectedScenario],

      // Include scenario-specific calculations in a sub-object for the PDF generator
      scenarioData: {
        ...scenarioData
      }
    }
  }

  // Function to prepare state for the PDF generator
  const getPdfState = () => {
    return getMergedState()
  }

  return {
    // Exposed calculators for individual scenario UI
    fthbCalculator,
    newBuildCalculator,
    preOwnedCalculator,

    // Scenario selection
    selectedScenario,
    setSelectedScenario,

    // Active calculator for the current scenario
    activeCalculator,

    // PDF state generation
    getPdfState
  }
}
