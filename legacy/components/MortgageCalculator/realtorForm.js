{
  /* <form>
                      <Row>
                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="name">First Name</label>
                            <input
                              type="text"
                              name="firstname"
                              id="firstname"
                              placeholder="Name"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.firstname ? calcInfo.realtor.firstname : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="middlename">Middle Name</label>
                            <input
                              type="text"
                              name="middlename"
                              id="middlename"
                              placeholder="Name"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.middlename ? calcInfo.realtor.middlename : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>

                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="name">Last Name</label>
                            <input
                              type="text"
                              name="lastname"
                              id="lastname"
                              placeholder="Last Name"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.lastname ? calcInfo.realtor.lastname : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="position">Position/Title</label>
                            <input
                              type="text"
                              name="position"
                              id="position"
                              placeholder="I.E: Mortgage Broker, BCS"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.position ? calcInfo.realtor.position : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="company">License With (Company)</label>
                            <input
                              type="text"
                              name="company"
                              id="company"
                              placeholder="Company Licensed With"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.company ? calcInfo.realtor.company : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="email">Email Address</label>
                            <input
                              type="email"
                              name="email"
                              id="realtorEmail"
                              placeholder="<EMAIL>"
                              defaultValue={calcInfo.realtor && calcInfo.realtor.email ? calcInfo.realtor.email : ''}
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>

                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <div>
                              <label htmlFor="workPhone">Phone</label>
                              <input
                                type="text"
                                name="workPhone"
                                id="realtorWorkPhone"
                                placeholder="************"
                                defaultValue={
                                  calcInfo.realtor && calcInfo.realtor.workPhone ? calcInfo.realtor.workPhone : ''
                                }
                                onChange={(e) => updateRealtorInfo(e)}
                              />
                            </div>
                          </div>
                        </Col>

                        <Col sm={12} md={6} lg={4}>
                          <div className={style.ax_field}>
                            <label htmlFor="website">Website</label>
                            <input
                              type="text"
                              name="website"
                              id="website"
                              placeholder="I.E: https://axiommortgage.ca"
                              defaultValue={
                                calcInfo.realtor && calcInfo.realtor.website ? calcInfo.realtor.website : ''
                              }
                              onChange={(e) => updateRealtorInfo(e)}
                            />
                          </div>
                        </Col>
                      </Row>
                    </form> */
}
