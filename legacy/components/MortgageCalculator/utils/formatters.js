/**
 * Utility functions for formatting values in the mortgage calculator
 */

/**
 * Format a number as CAD currency
 * @param {number} value - The value to format
 * @returns {string} Formatted currency string
 */
export const toMoney = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return '$0.00'
  }

  return new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currencyDisplay: 'symbol',
    currency: 'CAD'
  }).format(value)
}

/**
 * Format a percentage value
 * @param {number} value - The percentage value
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted percentage string
 */
export const toPercentage = (value, decimals = 2) => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0%'
  }

  return `${value.toFixed(decimals)}%`
}

/**
 * Format a number with thousands separators
 * @param {number} value - The value to format
 * @returns {string} Formatted number string
 */
export const formatNumber = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return '0'
  }

  return new Intl.NumberFormat('en-CA').format(value)
}

/**
 * Format an amortization period
 * @param {number} years - Years in the amortization period
 * @returns {string} Formatted amortization period
 */
export const formatAmortization = (years) => {
  if (!years) return ''
  return `${years} Year${years !== 1 ? 's' : ''}`
}

/**
 * Format a payment frequency
 * @param {string} frequency - Payment frequency code
 * @returns {string} Human-readable payment frequency
 */
export const formatFrequency = (frequency) => {
  const frequencies = {
    monthly: 'Monthly',
    biweekly: 'Bi-Weekly',
    weekly: 'Weekly',
    accbiweekly: 'Accelerated Bi-Weekly',
    accweekly: 'Accelerated Weekly'
  }

  return frequencies[frequency] || frequency
}
