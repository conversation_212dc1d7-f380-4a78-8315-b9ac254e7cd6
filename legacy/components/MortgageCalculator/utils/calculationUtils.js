/**
 * Mortgage Calculator Utility Functions
 * Pure calculation functions that don't depend on component state
 */

/**
 * Calculate range rate based on amount
 * @param {number} amount - Property price
 * @returns {object} Range rate information
 */
export const calculateRangeRate = (amount) => {
  let rangeRate = { type: 'normalTier', rate: null }

  if (amount > 500000 && amount < 1500000) {
    const baseDown = 500000 * 0.05
    const additionalDown = (amount - 500000) * 0.1
    const totalDown = baseDown + additionalDown
    const downPercent = (totalDown / amount) * 100
    rangeRate = { type: 'middleTier', rate: downPercent }
  } else if (amount >= 1500000) {
    rangeRate = { type: 'maxTier', rate: 20 }
  }

  return rangeRate
}

/**
 * Calculate down payment amount for a specific percentage
 * @param {number} amount - Property price
 * @param {number} percentage - Down payment percentage
 * @param {object} rangeRate - Range rate information (optional)
 * @returns {number} Down payment amount
 */
export const calculateDownPayment = (amount, percentage, rangeRate = null) => {
  if (!rangeRate) {
    rangeRate = calculateRangeRate(amount)
  }

  // Handle special case for range rate
  if (percentage === 'range' && rangeRate.type === 'middleTier') {
    const baseDown = 500000 * 0.05
    const additionalDown = (amount - 500000) * 0.1
    return baseDown + additionalDown
  }

  // Handle percentage thresholds based on tier
  if (rangeRate.type === 'maxTier' && percentage < 20) {
    return 0 // Invalid - requires 20% minimum
  }

  if (rangeRate.type === 'middleTier') {
    if (percentage === 5 || percentage === 10) {
      return 0 // Invalid for middle tier
    }
  }

  // Standard percentage calculation
  return amount * (percentage / 100)
}

/**
 * Calculate mortgage insurance premium
 * @param {number} principal - Mortgage principal (amount - down payment)
 * @param {number} downPercentage - Down payment percentage
 * @returns {number} Insurance premium amount
 */
export const calculateInsurance = (principal, downPercentage) => {
  if (downPercentage >= 20) return 0
  if (downPercentage >= 15) return principal * 0.028
  if (downPercentage >= 10) return principal * 0.031
  if (downPercentage >= 5) return principal * 0.04
  return 0
}

/**
 * Calculate total mortgage principal including insurance
 * @param {number} amount - Property price
 * @param {number} downPayment - Down payment amount
 * @param {number} downPercentage - Down payment percentage
 * @returns {number} Total mortgage principal
 */
export const calculatePrincipal = (amount, downPayment, downPercentage) => {
  const mortgageAmount = amount - downPayment
  const insuranceAmount = calculateInsurance(mortgageAmount, downPercentage)
  return mortgageAmount + insuranceAmount
}

/**
 * Calculate effective annual interest rate from nominal rate
 * @param {number} nominalRate - Nominal annual interest rate (percentage)
 * @returns {number} Effective annual interest rate
 */
export const calculateEffectiveRate = (nominalRate) => {
  if (!nominalRate || nominalRate === 0) return 0
  const rate = parseFloat(nominalRate) / 100
  return (Math.pow(Math.pow(1 + rate / 2, 2), 1 / 12) - 1) * 12
}

/**
 * Calculate monthly mortgage payment
 * @param {number} principal - Mortgage principal
 * @param {number} effectiveRate - Effective annual interest rate
 * @param {number} years - Amortization period in years
 * @returns {number} Monthly payment amount
 */
export const calculateMonthlyPayment = (principal, effectiveRate, years) => {
  if (principal <= 0 || effectiveRate <= 0 || years <= 0) return 0

  const monthlyRate = effectiveRate / 12
  const numberOfPayments = years * 12

  return (monthlyRate * principal) / (1 - Math.pow(1 + monthlyRate, -1 * numberOfPayments))
}

/**
 * Calculate periodic payment based on monthly payment and frequency
 * @param {number} monthlyPayment - Monthly payment amount
 * @param {string} frequency - Payment frequency (monthly, biweekly, etc.)
 * @returns {object} Periodic payment info including amount and number of payments per year
 */
export const calculatePeriodicPayment = (monthlyPayment, frequency) => {
  let amount = 0
  let paymentsPerYear = 12

  switch (frequency) {
    case 'monthly':
      amount = monthlyPayment
      paymentsPerYear = 12
      break
    case 'biweekly':
      amount = (monthlyPayment * 12) / 26
      paymentsPerYear = 26
      break
    case 'weekly':
      amount = (monthlyPayment * 12) / 52
      paymentsPerYear = 52
      break
    case 'accbiweekly':
      amount = monthlyPayment / 2
      paymentsPerYear = 26
      break
    case 'accweekly':
      amount = monthlyPayment / 4
      paymentsPerYear = 52
      break
    default:
      amount = monthlyPayment
      paymentsPerYear = 12
  }

  return {
    amount,
    paymentsPerYear
  }
}

/**
 * Calculate total cash needed to close
 * @param {number} downPayment - Down payment amount
 * @param {object} closingCosts - Object containing various closing costs
 * @returns {number} Total cash needed
 */
export const calculateTotalCashNeeded = (downPayment, closingCosts) => {
  const { lawyerFee = 0, homeInspection = 0, appraisal = 0, titleInsurance = 0, estoppelFee = 0 } = closingCosts

  return (
    parseFloat(downPayment) +
    parseFloat(lawyerFee) +
    parseFloat(homeInspection) +
    parseFloat(appraisal) +
    parseFloat(titleInsurance) +
    parseFloat(estoppelFee)
  )
}

/**
 * Calculate total monthly expenses
 * @param {number} periodicPayment - Mortgage periodic payment
 * @param {object} monthlyExpenses - Object containing monthly expenses
 * @returns {number} Total monthly expenses
 */
export const calculateTotalMonthlyExpenses = (periodicPayment, monthlyExpenses) => {
  const { monthlyDebtPayments = 0, utilities = 0, condoFees = 0, phone = 0, cable = 0, internet = 0 } = monthlyExpenses

  return (
    parseFloat(periodicPayment) +
    parseFloat(monthlyDebtPayments) +
    parseFloat(utilities) +
    parseFloat(condoFees) +
    parseFloat(phone) +
    parseFloat(cable) +
    parseFloat(internet)
  )
}

/**
 * Calculate total yearly expenses
 * @param {object} yearlyExpenses - Object containing yearly expenses
 * @returns {number} Total yearly expenses
 */
export const calculateTotalYearlyExpenses = (yearlyExpenses) => {
  const { propertyTax = 0, propertyInsurance = 0, hoaFees = 0 } = yearlyExpenses

  return parseFloat(propertyTax) + parseFloat(propertyInsurance) + parseFloat(hoaFees)
}
