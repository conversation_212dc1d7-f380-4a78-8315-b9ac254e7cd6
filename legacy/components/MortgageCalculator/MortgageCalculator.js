import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/router'
import { Container, Row, Col } from 'react-grid-system'
import axios from 'axios'
import Cookies from 'js-cookie'
import CurrencyInput from 'react-currency-input-field'
import Link from 'next/link'
import Switcher from '../Switcher'
import Button from '../Button'
import Card<PERSON><PERSON> from '../CardUser'
import GenerateListingSheet from '../../helpers/listingSheetPdfGenerator'
import Processing from '../Processing'
import { UilAngleDown, UilSync } from '@iconscout/react-unicons'
import DragDropFileUploader from '../DragDropFileUploader'
import { getUserRealtors } from '../../helpers/fetchers'
import Modal from '../Modal'
import style from '../../styles/MortgageCalculator.module.scss'

const MortgageCalculator = (props) => {
  const { user, save, data, saving: externalSaving } = props
  const calculator = useRef()

  const [realtorInfo, setRealtorInfo] = useState({})

  let initialState

  const userId = Cookies.get('userId')

  const userInfo = {
    id: user && user.id ? user.id : userId,

    photo: {
      url: user && user.photo && user.photo.url ? user.photo.url : null,
      formats: {
        thumbnail: {
          url:
            user && user.photo && user.photo.formats && user.photo.formats.thumbnail && user.photo.formats.thumbnail.url
              ? user.photo.formats.thumbnail.url
              : null
        },
        squared: {
          url:
            user && user.photo && user.photo.formats && user.photo.formats.squared && user.photo.formats.squared.url
              ? user.photo.formats.squared.url
              : null
        }
      }
    },
    firstname: user && user.firstname ? user.firstname : null,
    lastname: user && user.lastname ? user.lastname : null,
    position: user && user.position ? user.position : null,
    email: user && user.email ? user.email : null,
    workEmail: user && user.workEmail ? user.workEmail : null,
    phone: user && user.phone ? user.phone : null,
    workPhone: user && user.workPhone ? user.workPhone : null,
    cellPhone: user && user.cellPhone ? user.cellPhone : null,
    website: user && user.website ? user.website : null,
    brokerage: user && user.brokerage ? user.brokerage : null,
    team: {
      id: user && user.team ? user.team.id : null,
      logo: { url: user && user.team && user.team.logo && user.team.logo.url ? user.team.logo.url : null }
    }
  }

  let realtorData = null

  if (!data) {
    initialState = {
      loading: false,
      saving: false,
      uploading: { status: false, type: null },
      amount: 0,
      askingPrice: 0,
      customPercentage: 0,
      downPay5: 0,
      downPay10: 0,
      downPay15: 0,
      downPay20: 0,
      downPayCustom: 0,
      downPayRange: 0,
      downPayFTHB: 0,
      chosenDownPay: { percent: 'select', amount: 0 },
      principal5: 0,
      principal10: 0,
      principal15: 0,
      principal20: 0,
      principalCustom: 0,
      principalRange: 0,
      principalFTHB: 0,
      chosenPrincipal: 0,
      rate5: 0,
      rate10: 0,
      rate15: 0,
      rate20: 0,
      rateCustom: 0,
      rateRange: 0,
      rateFTHB: 0,
      insurance5: 0,
      insurance10: 0,
      insurance15: 0,
      insurance20: 0,
      insuranceCustom: 0,
      insuranceRange: 0,
      insuranceFTHB: 0,
      monthlyPay5: 0,
      monthlyPay10: 0,
      monthlyPay15: 0,
      monthlyPay20: 0,
      monthlyPayCustom: 0,
      monthlyPayRange: 0,
      monthlyPayFTHB: 0,
      periodicPay5: 0,
      periodicPay10: 0,
      periodicPay15: 0,
      periodicPay20: 0,
      periodicPayCustom: 0,
      periodicPayRange: 0,
      periodicPayFTHB: 0,
      years: 0,
      customYears: 0,
      rate: 0,
      effectiveRates: {
        effectiveRate5: 0,
        effectiveRate10: 0,
        effectiveRate15: 0,
        effectiveRate20: 0,
        effectiveRateCustom: 0,
        effectiveRateRange: 0,
        effectiveRateFTHB: 0
      },
      rangeRate: 0,
      frequency: 'monthly',
      numberOfPayments: 0,
      showMortgagePayment: true,
      showBodyCashNeeded: true,
      showBodyMonthlyExpenses: true,
      showBodyYearlyExpenses: true,
      showAdditionalInfo: true,
      showRealtorInfo: true,
      showImages: true,
      houseType: 'select',
      estoppelFee: 0,
      lawyerFee: 0,
      titleInsurance: 0,
      homeInspection: 0,
      appraisal: 0,
      totalCashNeeded: 0,
      condoFees: 0,
      hoaFees: 0,
      monthlyDebtPayments: 0,
      utilities: 0,
      propertyInsurance: 0,
      phone: 0,
      cable: 0,
      internet: 0,
      chosenPeriodicPay: 0,
      chosenDownPayExpense: {},
      totalMonthlyPayments: 0,
      totalYearlyPayments: 0,
      amortizationBalance: 0,
      pdf: {
        full: false,
        short: true,
        mlsCode: null,
        address: null,
        monthlyExpenses: true,
        cashNeeded: true,
        propertyPhoto: { url: null, ext: null },
        realtorPhoto: { url: null, ext: null },
        user: { ...userInfo }
      },
      realtor: {
        firstname: '',
        middlename: null,
        lastname: '',
        position: '',
        company: '',
        email: '',
        workPhone: '',
        website: '',
        photo: null
      }
    }
  } else {
    initialState = { ...data, pdf: { ...data.pdf, user: { ...userInfo }, realtor: { ...data.realtor } } }
  }

  const [safariChecker, setSafariChecker] = useState(null)
  const [calcInfo, setCalcInfo] = useState(initialState)
  const [validation, setValidation] = useState([])
  const [effectiveRates, setEffectiveRates] = useState({})
  const [realtors, setRealtors] = useState(null)
  const [showModal, setShowModal] = useState({ isVisible: false, content: 'hehehe', title: 'Title here' })
  const [showUploader, setShowUploader] = useState(false)

  // Replace selectedScenario with scenario toggle states
  const [scenarios, setScenarios] = useState({
    fthb: false,
    newBuild: true,
    preOwned: false
  })

  const router = useRouter()

  if (data && data !== undefined && data !== null) {
    const { realtor } = data

    realtorData = {
      id: realtor && realtor.id ? realtor.id : null,
      photo: {
        url: realtor && realtor.photo && realtor.photo.url ? realtor.photo.url : null,
        formats: {
          thumbnail: {
            url:
              realtor &&
              realtor.photo &&
              realtor.photo.formats &&
              realtor.photo.formats.thumbnail &&
              realtor.photo.formats.thumbnail.url
                ? realtor.photo.formats.thumbnail.url
                : null
          },
          squared: {
            url:
              realtor &&
              realtor.photo &&
              realtor.photo.formats &&
              realtor.photo.formats.squared &&
              realtor.photo.formats.squared.url
                ? realtor.photo.formats.squared.url
                : null
          }
        }
      },
      firstname: realtor && realtor.firstname ? realtor.firstname : null,
      middlename: realtor && realtor.middlename ? realtor.middlename : null,
      lastname: realtor && realtor.lastname ? realtor.lastname : null,
      position: realtor && realtor.position ? realtor.position : null,
      email: realtor && realtor.email ? realtor.email : null,
      company: realtor && realtor.company ? realtor.company : null,
      phone: realtor && realtor.phone ? realtor.phone : null,
      website: realtor && realtor.website ? realtor.website : null
    }
  }

  const handleValidation = () => {
    let invalid = []
    const { askingPrice, years, chosenDownPay, pdf } = calcInfo

    if (askingPrice === 0 || !askingPrice) {
      invalid.push('Please add the asking price.')
    }
    if (chosenDownPay === 0 || !chosenDownPay) {
      invalid.push('Please select a down payment percentage.')
    }
    if (years === 0 || !years) {
      invalid.push('Please add an amortization period.')
    }

    if (invalid.length > 0) {
      setValidation(invalid)
    } else {
      setValidation([])
    }

    return invalid
  }

  const handleUploadComplete = (uploadedFile) => {
    if (uploadedFile && uploadedFile.identifier === 'propertyPhoto') {
      if (uploadedFile.url.length > 0) {
        setCalcInfo({
          ...calcInfo,
          pdf: { ...calcInfo.pdf, propertyPhoto: { url: uploadedFile.url, ext: uploadedFile.ext } }
        })
      }
    }

    if (uploadedFile && uploadedFile.identifier === 'realtorPhoto') {
      if (uploadedFile.url.length > 0) {
        setCalcInfo({
          ...calcInfo,
          pdf: { ...calcInfo.pdf, realtorPhoto: { url: uploadedFile.url, ext: uploadedFile.ext } }
        })
      }
    }
  }

  const onChangeAmount = (e) => {
    // CurrencyInput passes the value directly, no need to parse
    const numericAmount = e || 0

    setCalcInfo({
      ...calcInfo,
      amount: numericAmount,
      askingPrice: numericAmount,
      pdf: { ...calcInfo.pdf, full: false, short: true }
    })

    // Calculate range rate immediately when amount changes
    calcDownPaymentMinRateByAmount()
  }

  const handleInitialCalc = () => {
    calcDownPaymentMinRateByAmount()
  }

  // Calculate Down Payment and Insurance Amounts
  const calcDownPaymentMinRateByAmount = () => {
    const amount = parseFloat(calcInfo.amount) || 0

    if (isNaN(amount)) return

    // --- Regular Buyer Calculations --- (Threshold updated from $1M to $1.5M)
    let regularRangeRate = { type: 'normalTier', rate: null }
    if (amount > 500000 && amount < 1500000) {
      const regularRate = 500000 * 0.05
      const remainingRate = (amount - 500000) * 0.1
      const rangeRate = (((100 * (regularRate + remainingRate)) / amount) * 100) / 100
      regularRangeRate = { type: 'middleTier', rate: rangeRate }
    } else if (amount >= 1500000) {
      regularRangeRate = { type: 'maxTier', rate: 20 }
    } else if (amount > 0) {
      regularRangeRate = { type: 'normalTier', rate: null }
    }

    // --- FTHB Buyer Calculations --- (Already using $1.5M threshold)
    let minDownPercentFTHB = 5
    let minDownAmountFTHB = amount * 0.05
    if (amount > 500000 && amount <= 1500000) {
      const baseDown = 500000 * 0.05
      const additionalDown = (amount - 500000) * 0.1
      minDownAmountFTHB = baseDown + additionalDown
      minDownPercentFTHB = (minDownAmountFTHB / amount) * 100
    } else if (amount > 1500000) {
      minDownPercentFTHB = 20
      minDownAmountFTHB = amount * 0.2
    }

    // --- Insurance Calculation Helper ---
    const calculateInsurance = (principal, downPercent) => {
      if (downPercent >= 20) return 0
      if (downPercent >= 15) return principal * 0.028 // 15% - 19.99%
      if (downPercent >= 10) return principal * 0.031 // 10% - 14.99%
      if (downPercent >= 5) return principal * 0.04 // 5% - 9.99%
      return 0 // Should not happen if down% >= 5
    }

    // --- Calculate all tiers based on amount and regular threshold ---
    const isMiddleTier = regularRangeRate.type === 'middleTier'
    const isMaxTier = regularRangeRate.type === 'maxTier'

    const downPay5 = amount * 0.05
    const downPay10 = amount * 0.1
    const downPay15 = amount * 0.15
    const downPay20 = amount * 0.2
    const downPayRange = regularRangeRate.type === 'middleTier' ? (amount * regularRangeRate.rate) / 100 : 0

    const insurance5 = calculateInsurance(amount - downPay5, 5)
    const insurance10 = calculateInsurance(amount - downPay10, 10)
    const insurance15 = calculateInsurance(amount - downPay15, 15)
    const insurance20 = 0 // Always 0 for 20%
    const insuranceRange = isMiddleTier
      ? (amount - (amount * regularRangeRate.rate) / 100) *
        (regularRangeRate.rate >= 15 ? 0.028 : regularRangeRate.rate >= 10 ? 0.031 : 0.04)
      : 0

    const principal5 = downPay5 > 0 ? amount - downPay5 + insurance5 : 0
    const principal10 = downPay10 > 0 ? amount - downPay10 + insurance10 : 0
    const principal15 = downPay15 > 0 ? amount - downPay15 + insurance15 : 0
    const principal20 = amount - downPay20 + insurance20
    const principalRange = isMiddleTier
      ? amount -
        (amount * regularRangeRate.rate) / 100 +
        (amount - (amount * regularRangeRate.rate) / 100) *
          (regularRangeRate.rate >= 15 ? 0.028 : regularRangeRate.rate >= 10 ? 0.031 : 0.04)
      : 0

    // --- Calculate FTHB Insurance and Principal ---
    const insuranceFTHB = calculateInsurance(amount - minDownAmountFTHB, minDownPercentFTHB)
    const principalFTHB = amount - minDownAmountFTHB + insuranceFTHB

    // Update state with new range rate and all calculated payment info
    setCalcInfo((prevState) => ({
      ...prevState,
      rangeRate: regularRangeRate, // Keep using the regular range rate for UI logic elsewhere
      downPay5,
      downPay10,
      downPay15,
      downPay20,
      downPayRange,
      downPayFTHB: minDownAmountFTHB,
      insurance5,
      insurance10,
      insurance15,
      insurance20,
      insuranceRange,
      insuranceFTHB,
      principal5,
      principal10,
      principal15,
      principal20,
      principalRange,
      principalFTHB
      // Note: Custom calculations are handled in paymentCalc
    }))
  }

  const paymentCalc = (newAmount) => {
    let percentage = calcInfo.customPercentage
    const customPct = parseFloat(percentage, 10) || 0 // Ensure it's a number, default to 0
    const { rangeRate } = calcInfo
    // const rateType = rangeRate.type // No longer needed directly here
    // const ratePct = rangeRate.rate // No longer needed directly here

    // --- Insurance Calculation Helper (repeated for custom/range) ---
    // TODO: Maybe move this helper to a common scope if used elsewhere
    const calcInsurance = (amountForInsurance, rate) => {
      if (rate >= 20) return 0
      if (rate >= 15) return amountForInsurance * 0.028
      if (rate >= 10) return amountForInsurance * 0.031
      if (rate >= 5) return amountForInsurance * 0.04
      return 0
    }

    // --- Calculate Custom Down Payment, Insurance, Principal ---
    let downPayCustom = 0
    let insuranceCustom = 0
    let principalCustom = 0
    const minRequiredPercent = rangeRate.type === 'middleTier' ? rangeRate.rate : rangeRate.type === 'maxTier' ? 20 : 5

    if (customPct > 0 && customPct >= minRequiredPercent) {
      downPayCustom = newAmount * (customPct / 100)
      const amountForInsurance = newAmount - downPayCustom
      insuranceCustom = calcInsurance(amountForInsurance, customPct)
      principalCustom = newAmount - downPayCustom + insuranceCustom
    }

    // Note: Range calculations (downPayRange, insuranceRange, principalRange) are now handled in calcDownPaymentMinRateByAmount

    const paymentInfo = {
      ...calcInfo,
      // Standard tiers (5, 10, 15, 20, FTHB) are set by calcDownPaymentMinRateByAmount
      // Range tier is set by calcDownPaymentMinRateByAmount
      // Update only custom tier here
      downPayCustom,
      insuranceCustom,
      principalCustom
    }
    setCalcInfo(paymentInfo)
  }

  const getEffectiveRate = () => {
    const rateAmount = (r) => {
      if (parseFloat(calcInfo[r]) === 0) {
        return 0
      } else {
        return parseFloat(calcInfo[r])
      }
    }

    const effectiveRateCalc = (rate) => {
      const nominalRate = parseFloat(rate) / 100
      const effectiveRate = (Math.pow(Math.pow(1 + nominalRate / 2, 2), 1 / 12) - 1) * 12

      return effectiveRate
    }

    const rates = {
      effectiveRate5: effectiveRateCalc(rateAmount('rate5')),
      effectiveRate10: effectiveRateCalc(rateAmount('rate10')),
      effectiveRate15: effectiveRateCalc(rateAmount('rate15')),
      effectiveRate20: effectiveRateCalc(rateAmount('rate20')),
      effectiveRateCustom: effectiveRateCalc(rateAmount('rateCustom')),
      effectiveRateRange: effectiveRateCalc(rateAmount('rateRange')),
      effectiveRateFTHB: effectiveRateCalc(rateAmount('rateFTHB')) // Added FTHB
    }

    // Update the effectiveRates part of the calcInfo state
    setCalcInfo((prevState) => ({
      ...prevState,
      effectiveRates: rates
    }))

    // Set the separate effectiveRates state (consider if this is still needed or can be merged)
    // setEffectiveRates(rates)
  }

  const onChangeRate = (e) => {
    const { rangeRate } = calcInfo
    const rateType = rangeRate.type
    const rateTypeLimiter = () => {
      if ((rateType && rateType === 'middleTier') || (rateType && rateType === 'maxTier')) {
        return true
      }
      return false
    }

    if (e && e.target && e.target.value.length >= 0) {
      // Allow clearing the value
      const value = e.target.value
      switch (e.target.id) {
        case 'rate5':
          setCalcInfo({ ...calcInfo, rate5: rateTypeLimiter() ? 0 : value })
          break
        case 'rate10':
          setCalcInfo({ ...calcInfo, rate10: rateType === 'maxTier' ? 0 : value })
          break
        case 'rate15':
          setCalcInfo({ ...calcInfo, rate15: rateType === 'maxTier' ? 0 : value })
          break
        case 'rate20':
          setCalcInfo({ ...calcInfo, rate20: value })
          break
        case 'rateCustom':
          setCalcInfo({ ...calcInfo, rateCustom: value })
          break
        case 'rateRange':
          // Note: rateRange is tied to the calculated minimum for middleTier, usually not user-editable
          // If it needs to be editable, the logic might need adjustment.
          // For now, assume it follows the middleTier calculation or is 0.
          setCalcInfo({ ...calcInfo, rateRange: rateType === 'middleTier' ? value : 0 }) // Adjust if needed
          break
        case 'rateFTHB': // Added FTHB
          setCalcInfo({ ...calcInfo, rateFTHB: value })
          break
        default:
          // Assuming 'rate' refers to the rate for the chosenDownPay, this might need review
          setCalcInfo({ ...calcInfo, rate: value })
      }
    }
  }

  // Calculate the Monthly Payment based on 12 months (a Year)
  const monthlyPay = () => {
    const { years, customYears } = calcInfo
    const { effectiveRates } = calcInfo // Get effective rates from calcInfo state

    const principalAmount = (p) => {
      // Check if principal exists and is valid
      const principalVal = calcInfo[p]
      if (
        principalVal === '--' ||
        principalVal === undefined ||
        principalVal === null ||
        isNaN(parseFloat(principalVal))
      ) {
        return 0
      }
      return parseFloat(principalVal)
    }

    const {
      // Destructure all effective rates
      effectiveRate5,
      effectiveRate10,
      effectiveRate15,
      effectiveRate20,
      effectiveRateCustom,
      effectiveRateRange,
      effectiveRateFTHB // Added FTHB
    } = effectiveRates

    const numberOfPayments = years !== 'custom' ? years * 12 : customYears * 12

    // Helper to calculate payment for a given effective rate and principal
    const calculateMonthlyPayment = (rate, principal) => {
      if (rate <= 0 || principal <= 0 || numberOfPayments <= 0) return 0
      const monthlyRate = rate / 12
      return (monthlyRate * principal) / (1 - Math.pow(1 + monthlyRate, -1 * numberOfPayments))
    }

    setCalcInfo({
      ...calcInfo,
      monthlyPay5: calculateMonthlyPayment(effectiveRate5, principalAmount('principal5')),
      monthlyPay10: calculateMonthlyPayment(effectiveRate10, principalAmount('principal10')),
      monthlyPay15: calculateMonthlyPayment(effectiveRate15, principalAmount('principal15')),
      monthlyPay20: calculateMonthlyPayment(effectiveRate20, principalAmount('principal20')),
      monthlyPayCustom: calculateMonthlyPayment(effectiveRateCustom, principalAmount('principalCustom')),
      monthlyPayRange: calculateMonthlyPayment(effectiveRateRange, principalAmount('principalRange')),
      monthlyPayFTHB: calculateMonthlyPayment(effectiveRateFTHB, principalAmount('principalFTHB')) // Added FTHB
    })
  }

  // Years, Rate and Frequency Handlers
  const onChangeFrequency = (event) => {
    setCalcInfo({ ...calcInfo, frequency: event.target.value })
  }

  const onChangeYears = (event) => {
    if (event && event.target && event.target.value.length > 0) {
      const inputYears = parseFloat(event.target.value)

      // Apply scenario-specific constraints
      const maxYears = scenarios.preOwned ? 25 : 30
      const constrainedYears = Math.min(inputYears, maxYears)

      if (inputYears > maxYears) {
        // Show some visual feedback that the value was constrained
        event.target.style.borderColor = '#e53935'
        setTimeout(() => {
          event.target.style.borderColor = ''
        }, 1500)
      }

      setCalcInfo({ ...calcInfo, years: constrainedYears })
    }
  }

  //Calculate the Periodic Payment Based on the Chosen Years and Frequency
  const periodicPayCalc = () => {
    const {
      frequency,
      monthlyPay5,
      monthlyPay10,
      monthlyPay15,
      monthlyPay20,
      monthlyPayCustom,
      monthlyPayRange,
      monthlyPayFTHB
    } = calcInfo // Added FTHB monthly pay

    let pay5 = 0,
      pay10 = 0,
      pay15 = 0,
      pay20 = 0,
      payCustom = 0,
      payRange = 0,
      payFTHB = 0,
      numPayments = 0

    if (frequency === 'monthly') {
      pay5 = monthlyPay5
      pay10 = monthlyPay10
      pay15 = monthlyPay15
      pay20 = monthlyPay20
      payCustom = monthlyPayCustom
      payRange = monthlyPayRange
      payFTHB = monthlyPayFTHB // Added FTHB
      numPayments = 12
    } else if (frequency === 'biweekly') {
      pay5 = (monthlyPay5 * 12) / 26
      pay10 = (monthlyPay10 * 12) / 26
      pay15 = (monthlyPay15 * 12) / 26
      pay20 = (monthlyPay20 * 12) / 26
      payCustom = (monthlyPayCustom * 12) / 26
      payRange = (monthlyPayRange * 12) / 26
      payFTHB = (monthlyPayFTHB * 12) / 26 // Added FTHB
      numPayments = 26
    } else if (frequency === 'weekly') {
      pay5 = (monthlyPay5 * 12) / 52
      pay10 = (monthlyPay10 * 12) / 52
      pay15 = (monthlyPay15 * 12) / 52
      pay20 = (monthlyPay20 * 12) / 52
      payCustom = (monthlyPayCustom * 12) / 52
      payRange = (monthlyPayRange * 12) / 52
      payFTHB = (monthlyPayFTHB * 12) / 52 // Added FTHB
      numPayments = 52
    } else if (frequency === 'accbiweekly') {
      pay5 = monthlyPay5 / 2
      pay10 = monthlyPay10 / 2
      pay15 = monthlyPay15 / 2
      pay20 = monthlyPay20 / 2
      payCustom = monthlyPayCustom / 2
      payRange = monthlyPayRange / 2
      payFTHB = monthlyPayFTHB / 2 // Added FTHB
      numPayments = 26
    } else if (frequency === 'accweekly') {
      pay5 = monthlyPay5 / 4
      pay10 = monthlyPay10 / 4
      pay15 = monthlyPay15 / 4
      pay20 = monthlyPay20 / 4
      payCustom = monthlyPayCustom / 4
      payRange = monthlyPayRange / 4
      payFTHB = monthlyPayFTHB / 4 // Added FTHB
      numPayments = 52
    }

    setCalcInfo({
      ...calcInfo,
      periodicPay5: pay5,
      periodicPay10: pay10,
      periodicPay15: pay15,
      periodicPay20: pay20,
      periodicPayCustom: payCustom,
      periodicPayRange: payRange,
      periodicPayFTHB: payFTHB, // Added FTHB
      numberOfPayments: numPayments
    })
  }

  //Changing the Periodic Payment on Monthly Expenses Section based on the chosen Down Payment
  const chosenPeriodicPayCalc = () => {
    let chosenDownPayExpense = parseFloat(calcInfo.chosenDownPayExpense.percent)

    if (chosenDownPayExpense === 5) {
      setCalcInfo({ ...calcInfo, chosenPeriodicPay: calcInfo.periodicPay5, chosenPrincipal: calcInfo.principal5 })
    } else if (chosenDownPayExpense === 10) {
      setCalcInfo({ ...calcInfo, chosenPeriodicPay: calcInfo.periodicPay10, chosenPrincipal: calcInfo.principal10 })
    } else if (chosenDownPayExpense === 15) {
      setCalcInfo({ ...calcInfo, chosenPeriodicPay: calcInfo.periodicPay15, chosenPrincipal: calcInfo.principal15 })
    } else if (chosenDownPayExpense === 20) {
      setCalcInfo({ ...calcInfo, chosenPeriodicPay: calcInfo.periodicPay20, chosenPrincipal: calcInfo.principal20 })
    } else if (chosenDownPayExpense > 5 && chosenDownPayExpense < 10) {
      setCalcInfo({
        ...calcInfo,
        chosenPeriodicPay: calcInfo.periodicPayRange,
        chosenPrincipal: calcInfo.principalRange
      })
    } else if (
      chosenDownPayExpense !== 5 ||
      (chosenDownPayExpense !== '5%' && chosenDownPayExpense !== 10) ||
      (chosenDownPayExpense !== '10%' && chosenDownPayExpense !== 15) ||
      (chosenDownPayExpense !== '15%' && chosenDownPayExpense !== 20) ||
      (chosenDownPayExpense !== '20%' && chosenDownPayExpense !== 0) ||
      chosenDownPayExpense !== '0%'
    ) {
      setCalcInfo({
        ...calcInfo,
        chosenPeriodicPay: calcInfo.periodicPayCustom,
        chosenPrincipal: calcInfo.principalCustom
      })
    } else {
      return 0
    }
  }

  const handleHouseType = (event) => {
    setCalcInfo({ ...calcInfo, houseType: event.target.value })
  }

  const getDownTypeAmount = (value) => {
    let downValue

    if (value === '5%' || value === '10%' || value === '15%' || (value === '20%' && value !== '0' && value !== null)) {
      switch (value) {
        case '5%':
          downValue = calcInfo.downPay5
          return downValue
        case '10%':
          downValue = calcInfo.downPay10
          return downValue
        case '15%':
          downValue = calcInfo.downPay15
          return downValue
        case '20%':
          downValue = calcInfo.downPay20
          return downValue
        case '20%':
          downValue = calcInfo.downPay20
          return downValue
        default:
          return 0
      }
    } else if (calcInfo.rangeRate.type === 'normalTier') {
      downValue = calcInfo.downPayCustom
    } else {
      downValue = calcInfo.downPayRange
    }

    return downValue
  }

  const handleDownType = (event) => {
    const amount = getDownTypeAmount(event.target.value)
    const index = event.target.selectedIndex

    setCalcInfo({
      ...calcInfo,
      chosenDownPay: {
        rate: event.target.options[index].id,
        percent: event.target.value,
        amount: parseFloat(amount, 10)
      },
      chosenDownPayExpense: { percent: event.target.value, amount: parseFloat(amount, 10) },
      amortizationBalance: calcInfo.chosenPrincipal
    })
  }

  const handleChosenRate = () => {
    const { chosenDownPay } = calcInfo
    let chosenRate

    if (chosenDownPay && chosenDownPay.rate) {
      switch (chosenDownPay.rate) {
        case 'rate5':
          chosenRate = calcInfo.rate5
          break
        case 'rate10':
          chosenRate = calcInfo.rate10
          break
        case 'rate15':
          chosenRate = calcInfo.rate15
          break
        case 'rate20':
          chosenRate = calcInfo.rate20
          break
        case 'rateRange':
          chosenRate = calcInfo.rangeRate.rate
          break
        case 'rateCustom':
          chosenRate = calcInfo.rateCustom
          break
        case 'rateFTHB':
          chosenRate = calcInfo.rateFTHB
          break
        default:
          chosenRate = calcInfo.rate5
      }
    }

    return chosenRate
  }

  const totalCashNeededCalc = () => {
    const titleInsurance = calcInfo.titleInsurance

    setCalcInfo({
      ...calcInfo,
      totalCashNeeded:
        parseFloat(calcInfo.chosenDownPay.amount, 10) +
        parseFloat(calcInfo.estoppelFee, 10) +
        parseFloat(titleInsurance, 10) +
        parseFloat(calcInfo.homeInspection, 10) +
        parseFloat(calcInfo.lawyerFee, 10) +
        parseFloat(calcInfo.appraisal, 10)
    })
  }

  const handleCustomPercentage = (e) => {
    if (e && e.target && e.target.value.length > 0) {
      setCalcInfo({ ...calcInfo, customPercentage: e.target.value })
    }
  }

  const handleChangePropertyTax = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, propertyTax: maskedvalue })
  }

  const handleChangeTitleInsurance = (maskedvalue) => {
    // const titleInsurance = (calcInfo.amount) * 0.001;
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, titleInsurance: maskedvalue })
  }

  const handleChangeLawyerFee = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, lawyerFee: maskedvalue })
  }

  const handleChangeEstoppelFee = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, estoppelFee: maskedvalue })
  }

  const handleChangeHomeInspection = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, homeInspection: maskedvalue })
  }

  const handleChangeAppraisal = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, appraisal: maskedvalue })
  }

  const handleChangeMonthlyDebt = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, monthlyDebtPayments: maskedvalue })
  }

  const handleChangeUtilities = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, utilities: maskedvalue })
  }

  const handleChangeCondoFees = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, condoFees: maskedvalue })
  }

  const handleChangeHoaFees = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, hoaFees: maskedvalue })
  }

  const handleChangePropertyInsurance = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, propertyInsurance: maskedvalue })
  }

  const handleChangePhone = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, phone: maskedvalue })
  }

  const handleChangeCable = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, cable: maskedvalue })
  }

  const handleChangeInternet = (maskedvalue) => {
    // const newVal = parseFloat(maskedvalue.replace('$', '').replace(',', ''))

    setCalcInfo({ ...calcInfo, internet: maskedvalue })
  }

  const handleAddRealtor = () => {
    setShowModal({
      isVisible: true,
      title: 'Confirmation',
      content:
        'You are going to be redirected to the Add Realtor page and all your editings are going to be missed if you did not save it yet. Would you like to proceed?'
    })
  }

  const redirectAddRealtor = () => {
    router.push('/realtors')
  }

  const modalCallback = (isConfirmed) => {
    if (isConfirmed) {
      redirectAddRealtor()
    }
    setShowModal({ isVisible: false, message: '' })
  }

  const monthlyExpensesCalc = () => {
    let condoFeesVal = calcInfo.condoFees
    let utilitiesVal = calcInfo.utilities
    let phoneVal = calcInfo.phone
    let cableVal = calcInfo.cable
    let internetVal = calcInfo.internet
    let MonthlyDebtVal = calcInfo.monthlyDebtPayments

    let totalExpenses =
      parseFloat(utilitiesVal, 10) +
      parseFloat(condoFeesVal, 10) +
      parseFloat(phoneVal, 10) +
      parseFloat(cableVal, 10) +
      parseFloat(internetVal, 10) +
      parseFloat(MonthlyDebtVal, 10) +
      parseFloat(calcInfo.chosenPeriodicPay, 10)

    setCalcInfo({ ...calcInfo, totalMonthlyPayments: totalExpenses, amortizationBalance: calcInfo.chosenPrincipal })
  }

  const yearlyExpensesCalc = () => {
    let hoaFeesVal = calcInfo.hoaFees
    let propertyTaxVal = calcInfo.propertyTax
    let propertyInsurance = calcInfo.propertyInsurance

    const totalYearlyExpenses =
      parseFloat(propertyTaxVal, 10) + parseFloat(propertyInsurance, 10) + parseFloat(hoaFeesVal, 10)

    setCalcInfo({
      ...calcInfo,
      totalYearlyPayments: totalYearlyExpenses,
      amortizationBalance: calcInfo.chosenPrincipal
    })
  }

  const dropdownToggleMortgagePayment = () => {
    setCalcInfo({ ...calcInfo, showMortgagePayment: !calcInfo.showMortgagePayment })
  }

  const dropdownToggleAdditionalInfo = () => {
    setCalcInfo({ ...calcInfo, showAdditionalInfo: !calcInfo.showAdditionalInfo })
  }

  const dropdownRealtorInfo = () => {
    setCalcInfo({ ...calcInfo, showRealtorInfo: !calcInfo.showRealtorInfo })
  }

  const dropdownToggleImages = () => {
    setCalcInfo({ ...calcInfo, showImages: !calcInfo.showImages })
  }

  const dropdownToggleMonthlyExpenses = () => {
    setCalcInfo({ ...calcInfo, showBodyMonthlyExpenses: !calcInfo.showBodyMonthlyExpenses })
  }

  const dropdownToggleYearlyExpenses = () => {
    setCalcInfo({ ...calcInfo, showBodyYearlyExpenses: !calcInfo.showBodyYearlyExpenses })
  }

  const showOnPdf = (e, item) => {
    if (item === 'short' || item === 'full') {
      switch (item) {
        case 'full':
          setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, full: e.target.checked, short: false } })
          return
        case 'short':
          setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, short: e.target.checked, full: false } })
          return
        default:
          setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, short: e.target.checked, full: false } })
      }
    }

    switch (item) {
      case 'monthlyExpenses':
        setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, monthlyExpenses: e.target.checked } })
        return
      case 'cashNeeded':
        setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, cashNeeded: e.target.checked } })
        return
      default:
        return true
    }
  }

  const emptyField = (event) => {
    event.target.value = ''
  }

  const updatePdfInfo = (e) => {
    setCalcInfo({ ...calcInfo, pdf: { ...calcInfo.pdf, [e.target.name]: e.target.value } })
  }

  const updateRealtorInfo = (e) => {
    const rId = e.target.selectedOptions[0].id
    const rltr = realtors.filter((r) => r.id === rId)
    const realtor = rltr[0]

    realtorData = {
      id: realtor && realtor.id ? realtor.id : null,
      photo: {
        url: realtor && realtor.photo && realtor.photo.url ? realtor.photo.url : null,
        formats: {
          thumbnail: {
            url:
              realtor &&
              realtor.photo &&
              realtor.photo.formats &&
              realtor.photo.formats.thumbnail &&
              realtor.photo.formats.thumbnail.url
                ? realtor.photo.formats.thumbnail.url
                : null
          },
          squared: {
            url:
              realtor &&
              realtor.photo &&
              realtor.photo.formats &&
              realtor.photo.formats.squared &&
              realtor.photo.formats.squared.url
                ? realtor.photo.formats.squared.url
                : null
          }
        }
      },
      firstname: realtor && realtor.firstname ? realtor.firstname : null,
      middlename: realtor && realtor.middlename ? realtor.middlename : null,
      lastname: realtor && realtor.lastname ? realtor.lastname : null,
      position: realtor && realtor.position ? realtor.position : null,
      email: realtor && realtor.email ? realtor.email : null,
      company: realtor && realtor.company ? realtor.company : null,
      phone: realtor && realtor.phone ? realtor.phone : null,
      website: realtor && realtor.website ? realtor.website : null
    }

    setRealtorInfo(realtorData)
  }

  const startGenerator = async () => {
    try {
      const generator = await GenerateListingSheet(calcInfo, safariChecker)
      if (generator.status === 'finished') {
        if (save !== false) {
          // If external saving state is provided, don't update loading here as parent component will handle it
          if (externalSaving === undefined) {
            setCalcInfo({ ...calcInfo, loading: false })
          }
          // Call save function and pass the data back to parent
          return save('saved', generator, calcInfo)
        } else {
          setCalcInfo({ ...calcInfo, loading: false })
        }
      }
    } catch (error) {
      console.error('Error generating listing sheet:', error)
      setCalcInfo({ ...calcInfo, loading: false })
    }
  }

  const generatePdf = () => {
    // If external saving state is provided, use it instead of local loading state
    if (externalSaving === undefined) {
      setCalcInfo({ ...calcInfo, loading: true })
    }

    const isValid = handleValidation()

    if (isValid.length > 0) {
      setCalcInfo({ ...calcInfo, loading: false })
      return
    } else {
      startGenerator()
      const calcRects = calculator.current.getBoundingClientRect()
      const calcHeight = calcRects.height
      window.scroll({ top: calcHeight / 2, left: 0, behavior: 'smooth' })
    }
  }

  const showCurrentRealtor = () => {
    // Directly check if calcInfo.realtor has the necessary data
    if (calcInfo?.realtor?.id && calcInfo?.realtor?.firstname) {
      return (
        <div>
          <CardUser
            editable
            wide
            key={calcInfo.realtor.id}
            id={calcInfo.realtor.id}
            company={calcInfo.realtor.company}
            position={calcInfo.realtor.position}
            website={calcInfo.realtor.website}
            firstname={calcInfo.realtor.firstname}
            lastname={calcInfo.realtor.lastname}
            email={calcInfo.realtor.email}
            phone={calcInfo.realtor.phone}
            photo={
              calcInfo.realtor.photo && calcInfo.realtor.photo.url
                ? calcInfo.realtor.photo.url
                : './images/axiom-a-logo.svg'
            }
          />
        </div>
      )
    } else {
      // Display message if no realtor is selected or loaded
      return (
        <h3 style={{ textAlign: 'center', width: '100%', display: 'block', padding: '32px' }}>No Realtor Selected.</h3>
      )
    }
  }

  const toMoney = new Intl.NumberFormat('en-CA', {
    style: 'currency',
    currencyDisplay: 'symbol',
    currency: 'CAD'
  })

  const isNaNOrMoney = (n, from) => {
    if (isNaN(n)) {
      return 0
    }

    return toMoney.format(n)
  }

  const handlePercentageSelect = () => {
    const tier = calcInfo.rangeRate.type

    if (tier === 'normalTier') {
      return (
        <>
          <option id="rate5" value="5%">
            5% Down
          </option>
          <option id="rate10" value="10%">
            10% Down
          </option>
          <option id="rate15" value="15%">
            15% Down
          </option>
          <option id="rate20" value="20%">
            20% Down
          </option>
        </>
      )
    }

    if (tier === 'middleTier') {
      return (
        <>
          <option id="rateRange" value={`${calcInfo.rangeRate.rate}%`}>
            {Math.round(calcInfo.rangeRate.rate * 100) / 100}% Down
          </option>
          <option id="rate15" value="15%">
            15% Down
          </option>
          <option id="rate20" value="20%">
            20% Down
          </option>
        </>
      )
    }

    if (tier === 'maxTier') {
      return (
        <>
          <option id="rate20" value="20%">
            20% Down
          </option>
        </>
      )
    }
  }

  const handleCustomPercentageSelect = () => {
    const tier = calcInfo.rangeRate.type

    if (
      tier === 'normalTier' &&
      calcInfo.customPercentage &&
      calcInfo.customPercentage !== null &&
      calcInfo.customPercentage !== undefined &&
      calcInfo.customPercentage > 5
    ) {
      return (
        <option id="rateCustom" value={`${calcInfo.customPercentage}%`}>
          {calcInfo.customPercentage}% Down
        </option>
      )
    }

    if (
      tier === 'middleTier' &&
      calcInfo.customPercentage &&
      calcInfo.customPercentage !== null &&
      calcInfo.customPercentage !== undefined &&
      calcInfo.customPercentage > calcInfo.rangeRate.rate
    ) {
      return (
        <option id="rateCustom" value={`${calcInfo.customPercentage}%`}>
          {calcInfo.customPercentage}% Down
        </option>
      )
    }

    if (
      tier === 'maxTier' &&
      calcInfo.customPercentage &&
      calcInfo.customPercentage !== null &&
      calcInfo.customPercentage !== undefined &&
      calcInfo.customPercentage > 20
    ) {
      return (
        <option id="rateCustom" value={`${calcInfo.customPercentage}%`}>
          {calcInfo.customPercentage}% Down
        </option>
      )
    }

    return ''
  }

  const checkValidation = () => {
    if (validation.length > 0) {
      return (
        <>
          <section className={style.validation}>
            <h3>The following fields are Required:</h3>
            <ul>
              {validation.map((i) => {
                return <li key={i}>{i}</li>
              })}
            </ul>
          </section>
        </>
      )
    }
    return ''
  }

  //----- Effects ------

  useEffect(() => {
    paymentCalc(calcInfo.amount)
  }, [calcInfo.rangeRate, calcInfo.years, calcInfo.chosenDownPay.percent])

  useEffect(() => {
    if (realtorInfo && realtorInfo !== null && realtorInfo !== undefined) {
      setCalcInfo({ ...calcInfo, realtor: { ...realtorInfo } })
    }
  }, [realtorInfo])

  useEffect(() => {
    const timer = setTimeout(() => {
      handleValidation()
    }, 1000)
    return () => clearTimeout(timer)
  }, [
    calcInfo.askingPrice,
    calcInfo.years,
    calcInfo.rate5,
    calcInfo.rate10,
    calcInfo.rate15,
    calcInfo.rate20,
    calcInfo.rateCustom,
    calcInfo.chosenDownPay
  ])

  useEffect(() => {
    setCalcInfo({ ...calcInfo, rate: handleChosenRate() })
  }, [calcInfo.chosenDownPay])

  useEffect(() => {
    handleValidation()
  }, [calcInfo.pdf.propertyPhoto.url, calcInfo.pdf.realtorPhoto.url])

  useEffect(
    (e) => {
      setCalcInfo({ ...calcInfo, rate: handleChosenRate() })
      getEffectiveRate()
    },
    [
      calcInfo.rate5,
      calcInfo.rate10,
      calcInfo.rate15,
      calcInfo.rate20,
      calcInfo.rateCustom,
      // calcInfo.rangeRate.rate, // rangeRate is complex object, careful with dependency
      calcInfo.rangeRate, // Depend on the object itself might be safer if structure changes
      calcInfo.rateFTHB // Added FTHB
    ]
  )

  useEffect(() => {
    monthlyPay()
  }, [
    calcInfo.effectiveRates, // This object now contains all effective rates
    calcInfo.principal5,
    calcInfo.principal10,
    calcInfo.principal15,
    calcInfo.principal20,
    calcInfo.principalRange,
    calcInfo.principalCustom,
    calcInfo.principalFTHB, // Added FTHB
    calcInfo.years, // Added years/customYears as monthlyPay depends on numberOfPayments
    calcInfo.customYears
  ])

  useEffect(
    (e) => {
      periodicPayCalc()
    },
    [
      calcInfo.monthlyPay5,
      calcInfo.monthlyPay10,
      calcInfo.monthlyPay15,
      calcInfo.monthlyPay20,
      calcInfo.monthlyPayRange,
      calcInfo.monthlyPayCustom,
      calcInfo.monthlyPayFTHB, // Added FTHB
      calcInfo.frequency
    ]
  )

  useEffect(
    (e) => {
      const timer = setTimeout(() => {
        paymentCalc(calcInfo.amount)
      }, 1000)
      return () => clearTimeout(timer)
    },
    [calcInfo.customPercentage]
  )

  useEffect(
    (e) => {
      chosenPeriodicPayCalc()
    },
    [
      calcInfo.periodicPay5,
      calcInfo.periodicPay10,
      calcInfo.periodicPay15,
      calcInfo.periodicPay20,
      calcInfo.periodicPayCustom,
      calcInfo.periodicPayRange
    ]
  )

  useEffect(() => {
    totalCashNeededCalc()
  }, [
    calcInfo.houseType,
    calcInfo.lawyerFee,
    calcInfo.homeInspection,
    calcInfo.appraisal,
    calcInfo.estoppelFee,
    calcInfo.titleInsurance
  ])

  useEffect(() => {
    totalCashNeededCalc()
    monthlyExpensesCalc()
    chosenPeriodicPayCalc()
  }, [calcInfo.chosenDownPayExpense])

  useEffect(() => {
    monthlyExpensesCalc()
  }, [
    calcInfo.propertyTax,
    calcInfo.monthlyDebtPayments,
    calcInfo.utilities,
    calcInfo.condoFees,
    calcInfo.propertyInsurance,
    calcInfo.phone,
    calcInfo.cable,
    calcInfo.internet,
    calcInfo.chosenPeriodicPay
  ])

  useEffect(() => {
    yearlyExpensesCalc()
  }, [calcInfo.hoaFees])

  useEffect(() => {
    if (navigator) {
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
      setSafariChecker(isSafari)
    }
    if ((data && data !== null) || (data && data !== undefined)) {
      setCalcInfo(data)
    }

    const getRealtors = async () => {
      const currentUserId = user && user.id ? user.id : Cookies.get('userId')
      try {
        const realtors = await getUserRealtors(currentUserId)
        setRealtors(realtors)
      } catch (error) {
        console.error('Error fetching realtors:', error)
        setRealtors([])
      }
    }

    getRealtors()
  }, [])

  useEffect(() => {
    handleInitialCalc()
  }, [calcInfo.askingPrice])

  useEffect(() => {
    if (calcInfo.amount) {
      calcDownPaymentMinRateByAmount()
      paymentCalc(calcInfo.amount)
    }
  }, [calcInfo.amount])

  // Add this function to handle scenario selection
  const handleScenarioToggle = (scenario) => {
    // Create a new scenarios object with all values set to false
    const newScenarios = {
      fthb: false,
      newBuild: false,
      preOwned: false
    }

    // Set the selected scenario to true
    newScenarios[scenario] = true

    // Update the scenarios state
    setScenarios(newScenarios)

    // Adjust the years based on the scenario
    if (scenario === 'fthb' || scenario === 'newBuild') {
      // First-Time Home Buyer and New Build can have up to 30 years
      setCalcInfo((prev) => ({ ...prev, years: Math.min(prev.years || 25, 30) }))
    } else if (scenario === 'preOwned') {
      // Pre-owned homes are limited to 25 years
      setCalcInfo((prev) => ({ ...prev, years: Math.min(prev.years || 25, 25) }))
    }
  }

  return (
    <div className={style.ax_calc_App} ref={calculator}>
      <Processing processing={externalSaving || calcInfo.loading} message="Generating Listing Sheet PDF" />
      <Modal
        title={showModal.title}
        isVisible={showModal.isVisible}
        content={showModal.content}
        vCard={false}
        callback={(e) => modalCallback(e)}
        label="Proceed"
      />
      <Container>
        <Row className={style.ax_calc_row}>
          <Col sm={12} md={12} lg={12}>
            <div className={style.ax_calc_container}>
              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showAdditionalInfo
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownToggleAdditionalInfo(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Property Details</h2>
                  </div>
                </div>

                <div
                  className={
                    calcInfo.showAdditionalInfo
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <Row>
                    <Col sm={12} md={5} xl={5} xxl={3}>
                      <form>
                        <div className={style.ax_field}>
                          <label>#MLS</label>
                          <input
                            placeholder="#MLS"
                            type="text"
                            value={calcInfo.pdf.mlsCode}
                            name="mlsCode"
                            onChange={(e) => updatePdfInfo(e)}
                          />
                        </div>
                        <div className={style.ax_field}>
                          <label>Property Address</label>
                          <textarea
                            placeholder="Address"
                            type="text"
                            value={calcInfo.pdf.address}
                            name="address"
                            onChange={(e) => updatePdfInfo(e)}
                          />
                        </div>
                      </form>
                    </Col>
                    <Col sm={12} md={5} xl={5} xxl={3}>
                      {!showUploader &&
                      calcInfo &&
                      calcInfo.pdf &&
                      calcInfo.pdf.propertyPhoto &&
                      calcInfo.pdf.propertyPhoto.url ? (
                        <>
                          <div className={style.ax_calc_image_upload}>
                            <img src={calcInfo.pdf.propertyPhoto.url} alt="Property Photo" />
                          </div>
                          <Button action={() => setShowUploader(true)} label="Change Photo" color="highlight" />
                        </>
                      ) : (
                        <DragDropFileUploader
                          onUploadComplete={handleUploadComplete}
                          apiUrl={process.env.NEXT_PUBLIC_API_URL}
                          identifier="propertyPhoto" // or any other identifier you want to use
                        />
                      )}
                    </Col>
                  </Row>
                </div>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showAdditionalInfo
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownRealtorInfo(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Realtor Info</h2>
                  </div>
                </div>

                <div
                  className={
                    calcInfo.showRealtorInfo
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <div className={style.ax_calc_row}>
                    <form className={style.ax_form}>
                      <Row>
                        <Col sm={12} md={5} xl={5} xxl={3}>
                          {realtors && realtors.length > 0 ? (
                            <>
                              <div className={style.ax_field}>
                                <label htmlFor="name">Select one of your Realtors</label>
                                <select onChange={(e) => updateRealtorInfo(e)}>
                                  {realtors && realtors !== null && realtors.length > 0 ? (
                                    <>
                                      <option key="Select" value="">
                                        Select a Realtor
                                      </option>

                                      {realtors.map((rltr) => {
                                        return (
                                          <option key={rltr.firstname} value={rltr.id} id={rltr.id}>
                                            {rltr.firstname ? rltr.firstname : ''} {rltr.lastname ? rltr.lastname : ''}
                                          </option>
                                        )
                                      })}
                                    </>
                                  ) : (
                                    <option value="...loading">...Loading Realtors</option>
                                  )}
                                </select>
                              </div>
                              <h3 style={{ marginBottom: '8px' }}>
                                Didn't find the Realtor on the list? Add a new Realtor.
                              </h3>
                              <Button
                                sizing="small"
                                label="Add a Realtor"
                                color="highlight"
                                action={(e) => handleAddRealtor(e)}
                              />
                            </>
                          ) : (
                            <>
                              <h3>
                                We couldn't find any realtor in your account. Please add your first realtor on the{' '}
                                <Link href="/realtors">Realtors</Link> section.
                              </h3>
                              <Button
                                sizing="small"
                                label="Add a Realtor"
                                color="highlight"
                                isLink
                                linkPath="/realtors"
                              />
                            </>
                          )}
                        </Col>
                        <Col sm={12} md={5} xl={5} xxl={3}>
                          <div className={`${style.bordered} ${style.rounded}`} style={{ marginTop: '16px' }}>
                            {showCurrentRealtor()}
                          </div>
                        </Col>
                      </Row>
                    </form>
                  </div>
                </div>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showMortgagePayment
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownToggleMortgagePayment(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Mortgage Payment</h2>
                  </div>
                </div>
                <Container fluid style={{ width: '100%', padding: 0 }}>
                  <Row nogutter>
                    <Col sm={12} md={6} lg={6}>
                      <div className={style.askingPriceLabel}>
                        <Row align="center" nogutter>
                          <Col sm={12} md={3} lg={3}>
                            <p style={{ fontSize: '18px', lineHeight: '40px' }}>Asking Price:</p>
                          </Col>
                          <Col sm={12} md={6} lg={9}>
                            <div className={style.askingPriceInputGroup}>
                              <span style={{ fontSize: '24px', lineHeight: '40px', color: '#29c6be' }}>$</span>
                              <CurrencyInput
                                intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                                className={style.ax_asking_input}
                                value={calcInfo.amount}
                                data-number-to-fixed="2"
                                data-number-stepfactor="100"
                                groupSeparator=","
                                decimalSeparator="."
                                prefix="$"
                                allowDecimals
                                decimalsLimit="2"
                                disableAbbreviations
                                onValueChange={(e) => onChangeAmount(e)}
                                onClick={(e) => emptyField(e)}
                              />
                            </div>
                          </Col>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                </Container>

                <Container
                  fluid
                  className={`${style.calculartorBody} 
                    
                    ${
                      calcInfo.showMortgagePayment
                        ? style.show_dropdown_body + ' ' + style.dropdown_body
                        : style.dropdown_body
                    }`}
                  style={{ marginTop: '16px' }}
                >
                  <div className={`${style.showInDesktop} ${style.calcHeading}`}>
                    <Row className={style.ax_calc_row} style={{ backgroundColor: '#f8f8fc' }} align="center">
                      <Col sm={12} md={2} lg={2}></Col>
                      <Col sm={12} md={10} lg={10}>
                        <Row className={style.ax_calc_row_inner} style={{ textAlign: 'center' }}>
                          <Col sm={12}>
                            <Row>
                              <Col sm={2.4}>
                                <p>
                                  <strong>5% DOWN</strong>
                                </p>
                              </Col>
                              <Col sm={2.4}>
                                <p>
                                  <strong>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`} DOWN</strong>
                                </p>
                              </Col>
                              <Col sm={2.4}>
                                <p>
                                  <strong>15% DOWN</strong>
                                </p>
                              </Col>
                              <Col sm={2.4}>
                                <p>
                                  <strong>20% DOWN</strong>
                                </p>
                              </Col>
                              <Col sm={2.4}>
                                <p>
                                  <strong>
                                    Custom
                                    <input
                                      className={style.inlineInput}
                                      type="text"
                                      id="rateCustom"
                                      value={calcInfo.customPercentage}
                                      onChange={(e) => handleCustomPercentage(e)}
                                      onClick={(e) => emptyField(e)}
                                      style={{ width: '40px', height: '28px !important', padding: '0' }}
                                    />
                                    %
                                  </strong>
                                </p>
                              </Col>
                            </Row>
                          </Col>
                        </Row>
                      </Col>
                    </Row>
                  </div>

                  {/* Down Payment Row */}
                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={2} lg={2}>
                      <p className={style.ax_calc_downpay_label}>Down Payment:</p>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>5%</h3>
                            </div>

                            <input
                              id="rate5"
                              type="text"
                              disabled
                              readOnly
                              value={isNaNOrMoney(calcInfo.downPay5)}
                            ></input>
                          </div>
                        </Col>
                        {calcInfo.rangeRate.type === 'middleTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`}</h3>
                              </div>

                              <input
                                id="rateRange"
                                type="text"
                                readOnly
                                value={isNaNOrMoney(calcInfo.downPayRange)}
                              ></input>
                            </div>
                          </Col>
                        ) : calcInfo.rangeRate.type !== 'maxTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>

                              <input id="rate10" type="text" readOnly value={isNaNOrMoney(calcInfo.downPay10)}></input>
                            </div>
                          </Col>
                        ) : (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>

                              <input id="rate10" type="text" readOnly value={isNaNOrMoney(calcInfo.downPay10)}></input>
                            </div>
                          </Col>
                        )}

                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>15%</h3>
                            </div>

                            <input id="rate15" type="text" readOnly value={isNaNOrMoney(calcInfo.downPay15)}></input>
                          </div>
                        </Col>

                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>20%</h3>
                            </div>

                            <input id="rate20" type="text" readOnly value={isNaNOrMoney(calcInfo.downPay20)}></input>
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3 style={{ marginBottom: '15px', marginTop: '13px' }}>
                                Custom
                                <input
                                  className={style.inlineInput}
                                  type="text"
                                  id="rateCustom"
                                  value={calcInfo.customPercentage}
                                  onChange={(e) => handleCustomPercentage(e)}
                                  onClick={(e) => emptyField(e)}
                                  style={{ width: '40px', height: '28px !important', padding: '0' }}
                                />
                                %
                              </h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.downPayCustom)}></input>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={2} lg={2}>
                      <p>Mortgage Default Insurance:</p>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>5%</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.insurance5)}></input>
                          </div>
                        </Col>

                        {calcInfo.rangeRate.type === 'middleTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`}</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.insuranceRange)}></input>
                            </div>
                          </Col>
                        ) : calcInfo.rangeRate.type !== 'maxTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.insurance10)}></input>
                            </div>
                          </Col>
                        ) : (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.insurance10)}></input>
                            </div>
                          </Col>
                        )}

                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>15%</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.insurance15)}></input>
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>20%</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.insurance20)}></input>
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>Custom</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.insuranceCustom)}></input>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={2} lg={2}>
                      <h3>Total Mortgage Required:</h3>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>5%</h3>
                            </div>
                            <input
                              type="text"
                              readOnly
                              value={calcInfo.principal5 <= 0 ? '--' : isNaNOrMoney(calcInfo.principal5)}
                            ></input>
                          </div>
                        </Col>

                        {calcInfo.rangeRate.type === 'middleTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`}</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.principalRange)}></input>
                            </div>
                          </Col>
                        ) : calcInfo.rangeRate.type !== 'maxTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.principal10)}></input>
                            </div>
                          </Col>
                        ) : (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>
                              <input type="text" readOnly value={isNaNOrMoney(calcInfo.principal10)}></input>
                            </div>
                          </Col>
                        )}

                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>15%</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.principal15)}></input>
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>20%</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.principal20)}></input>
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>Custom</h3>
                            </div>
                            <input type="text" readOnly value={isNaNOrMoney(calcInfo.principalCustom)}></input>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={2} lg={2}>
                      <h3>Insert The Interest Rate (%):</h3>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>5%</h3>
                            </div>
                            <input
                              type="text"
                              id="rate5"
                              name="rate5"
                              value={calcInfo.rate5}
                              onChange={(e) => onChangeRate(e)}
                              onClick={(e) => emptyField(e)}
                            />
                          </div>
                        </Col>

                        {calcInfo.rangeRate.type === 'middleTier' ? (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`}</h3>
                              </div>
                              <input
                                type="text"
                                id="rateRange"
                                name="rateRange"
                                value={calcInfo.rateRange}
                                onChange={(e) => onChangeRate(e)}
                                onClick={(e) => emptyField(e)}
                              />
                            </div>
                          </Col>
                        ) : (
                          <Col sm={12} md={2.4} lg={2.4}>
                            <div className={style.fieldGroup}>
                              <div className={style.showInMobile}>
                                <h3>10%</h3>
                              </div>
                              <input
                                type="text"
                                id="rate10"
                                name="rate10"
                                value={calcInfo.rate10}
                                onChange={(e) => onChangeRate(e)}
                                onClick={(e) => emptyField(e)}
                              />
                            </div>
                          </Col>
                        )}

                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>15%</h3>
                            </div>
                            <input
                              type="text"
                              id="rate15"
                              name="rate15"
                              value={calcInfo.rate15}
                              onChange={(e) => onChangeRate(e)}
                              onClick={(e) => emptyField(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>20%</h3>
                            </div>
                            <input
                              type="text"
                              id="rate20"
                              name="rate20"
                              value={calcInfo.rate20}
                              onChange={(e) => onChangeRate(e)}
                              onClick={(e) => emptyField(e)}
                            />
                          </div>
                        </Col>
                        <Col sm={12} md={2.4} lg={2.4}>
                          <div className={style.fieldGroup}>
                            <div className={style.showInMobile}>
                              <h3>Custom</h3>
                            </div>
                            <input
                              type="text"
                              id="rateCustom"
                              name="rateCustom"
                              value={calcInfo.rateCustom}
                              onChange={(e) => onChangeRate(e)}
                              onClick={(e) => emptyField(e)}
                            />
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* Scenario Selection - Replace with toggle switches */}
                  <Row className={style.ax_calc_row} align="center" style={{ paddingTop: 16 }}>
                    <Col sm={12}>
                      <h2 style={{ margin: '15px 0', fontWeight: 'bold' }}>SELECT PURCHASE TYPE</h2>

                      <Row>
                        <Col sm={4}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              margin: '10px 0'
                            }}
                          >
                            <Switcher
                              label="First Time Home Buyer"
                              labelPos="top"
                              checked={scenarios.fthb}
                              action={() => handleScenarioToggle('fthb')}
                            />
                          </div>
                        </Col>

                        <Col sm={4}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              margin: '10px 0'
                            }}
                          >
                            <Switcher
                              label="New Build"
                              labelPos="top"
                              checked={scenarios.newBuild}
                              action={() => handleScenarioToggle('newBuild')}
                            />
                          </div>
                        </Col>

                        <Col sm={4}>
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              margin: '10px 0'
                            }}
                          >
                            <Switcher
                              label="Resale Property"
                              labelPos="top"
                              checked={scenarios.preOwned}
                              action={() => handleScenarioToggle('preOwned')}
                            />
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* Inputs for Down Payment, Amortization Period, and Payment Frequency */}
                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12}>
                      <Row>
                        <Col sm={4}>
                          <div style={{ margin: '10px 0' }}>
                            <label>Down Payment</label>
                            <select
                              className={style.ax_select}
                              value={calcInfo.chosenDownPay.percent}
                              onChange={(e) => handleDownType(e)}
                              style={{ width: '100%' }}
                            >
                              <option value="select">Select</option>
                              {handlePercentageSelect()}
                              {handleCustomPercentageSelect()}
                            </select>
                          </div>
                        </Col>

                        <Col sm={4}>
                          <div style={{ margin: '10px 0' }}>
                            <label>Amortization Period</label>
                            <input
                              type="text"
                              value={calcInfo.years}
                              onChange={(e) => onChangeYears(e)}
                              onClick={(e) => emptyField(e)}
                              style={{ width: '100%' }}
                            />
                            <div className={style.ax_calc_note}>
                              Maximum {scenarios.preOwned ? '25' : '30'} years for
                              {scenarios.fthb
                                ? ' First-Time Home Buyers'
                                : scenarios.newBuild
                                ? ' New Build properties'
                                : ' Resale properties'}
                            </div>
                          </div>
                        </Col>

                        <Col sm={4}>
                          <div style={{ margin: '10px 0' }}>
                            <label>Payment Frequency</label>
                            <select
                              className={style.ax_select}
                              value={calcInfo.frequency}
                              onChange={(e) => onChangeFrequency(e)}
                              style={{ width: '100%' }}
                            >
                              <option value="monthly">Monthly</option>
                              <option value="biweekly">Bi-Weekly</option>
                              <option value="weekly">Weekly</option>
                              <option value="accbiweekly">Accelerated Bi-Weekly</option>
                              <option value="accweekly">Accelerated Weekly</option>
                            </select>
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* Total Mortgage Payment section */}
                  <Row className={style.ax_calc_section_footer} align="center">
                    <Col sm={12}>
                      <h3 style={{ fontSize: '24px', padding: '10px' }}>Total Mortgage Payment:</h3>

                      <Row>
                        <Col sm={2.4}>
                          <p>5% DOWN</p>
                          <input type="text" readOnly value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay5))}></input>
                        </Col>
                        {calcInfo.rangeRate.type === 'middleTier' ? (
                          <Col sm={2.4}>
                            <p>{`${Math.round(calcInfo.rangeRate.rate * 100) / 100}%`} DOWN</p>
                            <input
                              type="text"
                              readOnly
                              value={isNaNOrMoney(Math.trunc(calcInfo.periodicPayRange))}
                            ></input>
                          </Col>
                        ) : (
                          <Col sm={2.4}>
                            <p>10% DOWN</p>
                            <input
                              type="text"
                              readOnly
                              value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay10))}
                            ></input>
                          </Col>
                        )}

                        <Col sm={2.4}>
                          <p>15% DOWN</p>
                          <input type="text" readOnly value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay15))}></input>
                        </Col>
                        <Col sm={2.4}>
                          <p>20% DOWN</p>
                          <input type="text" readOnly value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay20))}></input>
                        </Col>
                        <Col sm={2.4}>
                          <p>{calcInfo?.customPercentage ? `${calcInfo?.customPercentage}%` : '--'} DOWN</p>
                          <input
                            type="text"
                            readOnly
                            value={isNaNOrMoney(Math.trunc(calcInfo.periodicPayCustom))}
                          ></input>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Container>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showBodyCashNeeded
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownToggleCashNeeded(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Estimated Cash Needed</h2>
                  </div>
                </div>

                <div
                  className={
                    calcInfo.showBodyCashNeeded
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <div className={style.ax_calc_row}>
                    <p style={{ marginBottom: 0 }}>
                      When you purchase a house, there are a number of costs you'll need to put cash aside for in
                      addition to your down payment. These costs depend on a number of factors including things like
                      what kind of home you are buying (i.e. house vs. condo) and where the home is located. Our tool
                      will help you calculate these costs, so you know how much you'll need to save.
                    </p>
                    <h3>Please enter all applicable monthly expenses; sum will generate upon input.</h3>

                    <div className={style.ax_calc_right_col}>
                      <div className={style.ax_calc_col_50}>
                        <p style={{ textAlign: 'left', marginTop: '15px' }}>Type of Home: </p>
                        <select
                          className={style.ax_select}
                          value={calcInfo.houseType}
                          onChange={(e) => handleHouseType(e)}
                        >
                          <option value="select">Select</option>
                          <option value="House">House</option>
                          <option value="Condominium">Condominium</option>
                          <option value="Townhome">Townhome</option>
                          <option value="Row Housing">Row Housing</option>
                          <option value="Duplex">Duplex</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className={`${style.ax_calc_row} ${style.no_margin}`}>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Down Payment: </label>

                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.chosenDownPay.amount}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeLawyerFee(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Lawyer Fees: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.lawyerFee}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeLawyerFee(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Home Inspection: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.homeInspection}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeHomeInspection(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Appraisal: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.appraisal}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeAppraisal(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Title Insurance: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.titleInsurance}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeTitleInsurance(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Estoppel certificate fee: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.estoppelFee}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeEstoppelFee(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.cash_needed_totals}`}>
                      <label>Total Cash Needed: </label>
                      <input
                        className={style.totalsInput}
                        type="text"
                        readOnly
                        value={isNaNOrMoney(calcInfo.totalCashNeeded)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showBodyMonthlyExpenses
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownToggleMonthlyExpenses(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Estimated Monthly Costs</h2>
                  </div>
                </div>

                <div
                  className={
                    calcInfo.showBodyMonthlyExpenses
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <div className={`${style.ax_calc_row} ${style.no_margin}`}>
                    <h3>Please enter all applicable monthly expenses; sum will generate upon input.</h3>

                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Mortgage Payment: </label>
                      <input
                        type="text"
                        readOnly
                        value={isNaNOrMoney(Math.trunc(calcInfo.chosenPeriodicPay))}
                        style={{ border: 'none !important', backgroundColor: 'transparent !important' }}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Monthly Debt Payments: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.monthlyDebtPayments}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeMonthlyDebt(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Utilities: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.utilities}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeUtilities(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Condo Fees: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.condoFees}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeCondoFees(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>

                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Phone: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.phone}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangePhone(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Cable: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.cable}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeCable(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Internet: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.internet}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeInternet(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>

                    <div className={`${style.ax_calc_cash_needed_item} ${style.cash_needed_totals}`}>
                      <label>Total Monthly Expenses: </label>
                      <input
                        type="text"
                        className={style.totalsInput}
                        readOnly
                        value={isNaNOrMoney(calcInfo.totalMonthlyPayments)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcInfo.showBodyYearlyExpenses
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={(e) => dropdownToggleYearlyExpenses(e)}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Estimated Yearly Costs</h2>
                  </div>
                </div>
                <div
                  className={
                    calcInfo.showBodyYearlyExpenses
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <div className={`${style.ax_calc_row} ${style.no_margin}`}>
                    <h3>Please enter all applicable Yearly expenses; sum will generate upon input.</h3>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Property Tax: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.propertyTax}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangePropertyTax(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>Property Insurance: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.propertyInsurance}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangePropertyInsurance(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.expense}`}>
                      <label>HOA Fees: </label>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        value={calcInfo.hoaFees}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={(e) => handleChangeHoaFees(e)}
                        onClick={(e) => emptyField(e)}
                      />
                    </div>
                    <div className={`${style.ax_calc_cash_needed_item} ${style.cash_needed_totals}`}>
                      <label>Total Yearly Expenses: </label>
                      <input
                        type="text"
                        className={style.totalsInput}
                        readOnly
                        value={isNaNOrMoney(calcInfo.totalYearlyPayments)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <Row>
                    <Col sm={12}>
                      <h2 className={style.ax_calc_primary_font}>PDF Options</h2>
                      <p>Select below the infomation that you would like to show on your Listing Sheet PDF</p>
                    </Col>
                    <Col sm={12} md={4} lg={4}>
                      <Switcher
                        labelPos="top"
                        label="Show Yearly and Montlhy Expenses"
                        name="monthlyExpenses"
                        checked={calcInfo.pdf.monthlyExpenses}
                        action={(e) => showOnPdf(e, 'monthlyExpenses')}
                      />
                    </Col>

                    <Col sm={12} md={4} lg={4}>
                      <Switcher
                        labelPos="top"
                        label="Show Estimated Cash Needed"
                        name="cashNeeded"
                        checked={calcInfo.pdf.cashNeeded}
                        action={(e) => showOnPdf(e, 'cashNeeded')}
                      />
                    </Col>
                  </Row>
                </div>

                <div className={style.ax_calc_row}>
                  {checkValidation()}
                  <Button
                    color="highlight"
                    label="Generate And Save Listing Sheet"
                    disabled={validation.length > 0 ? true : false}
                    action={() => generatePdf()}
                    icon={<UilSync size={16} />}
                    iconPos="right"
                  />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default MortgageCalculator
