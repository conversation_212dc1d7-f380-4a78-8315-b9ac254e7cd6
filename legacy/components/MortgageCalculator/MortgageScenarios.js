import { useState, useEffect } from 'react'
import { Container, Row, Col } from 'react-grid-system'
import CurrencyInput from 'react-currency-input-field'
import { UilAngleDown, UilSync } from '@iconscout/react-unicons'
import { toMoney } from './utils/formatters'
import ScenarioSelector from './ScenarioSelector'
import { useScenarioSelector } from './hooks/useScenarioSelector'
import style from '../../styles/MortgageCalculator.module.scss'

const MortgageScenarios = (props) => {
  const { onSave, userData } = props

  // Get the scenario selector hook
  const {
    selectedScenario,
    setSelectedScenario,
    activeCalculator,
    fthbCalculator,
    newBuildCalculator,
    preOwnedCalculator,
    getPdfState
  } = useScenarioSelector()

  // Destructure the active calculator state and methods
  const {
    state: calcState,
    setAmount,
    setYears,
    setFrequency,
    setRate,
    setCustomPercentage,
    setChosenDownPayment,
    setExpense,
    validate,
    toggleSection,
    updateState
  } = activeCalculator

  // Track validation errors
  const [validation, setValidation] = useState([])

  // Initialize UI with any preset values from props
  useEffect(() => {
    if (props.initialData) {
      const { amount, years, frequency } = props.initialData
      if (amount) setAmount(amount)
      if (years) setYears(years)
      if (frequency) setFrequency(frequency)
    }
  }, [props.initialData])

  // Update validation when key fields change
  useEffect(() => {
    const errors = validate()
    setValidation(errors)
  }, [calcState.amount, calcState.years, calcState.chosenDownPay])

  // Handle amount change from currency input
  const handleAmountChange = (value) => {
    const numericAmount = value || 0
    setAmount(numericAmount)
  }

  // Generate PDF with the current calculator state
  const handleGeneratePdf = () => {
    const errors = validate()

    if (errors.length > 0) {
      setValidation(errors)
      return
    }

    // Get the combined state for PDF generation
    const pdfState = getPdfState()

    // Call the save function with the PDF state
    if (onSave) {
      onSave(pdfState)
    }
  }

  // Display validation errors
  const renderValidationErrors = () => {
    if (validation.length === 0) return null

    return (
      <section className={style.validation}>
        <h3>The following fields are Required:</h3>
        <ul>
          {validation.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </section>
    )
  }

  return (
    <div className={style.ax_calc_App}>
      <Container fluid>
        <Row>
          <Col sm={12}>
            {/* Scenario Selector */}
            <ScenarioSelector selectedScenario={selectedScenario} onSelectScenario={setSelectedScenario} />

            <div className={style.ax_calc_container}>
              {/* Property Details Section */}
              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={style.ax_calc_row}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcState.showAdditionalInfo
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={() => toggleSection('showAdditionalInfo')}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Property Details</h2>
                  </div>
                </div>

                <div
                  className={
                    calcState.showAdditionalInfo
                      ? `${style.show_dropdown_body} ${style.dropdown_body}`
                      : `${style.dropdown_body}`
                  }
                >
                  <Row>
                    <Col sm={12} md={5} xl={5} xxl={3}>
                      <form>
                        <div className={style.ax_field}>
                          <label>#MLS</label>
                          <input
                            placeholder="#MLS"
                            type="text"
                            value={calcState.pdf.mlsCode || ''}
                            name="mlsCode"
                            onChange={(e) =>
                              updateState({
                                pdf: { ...calcState.pdf, mlsCode: e.target.value }
                              })
                            }
                          />
                        </div>
                        <div className={style.ax_field}>
                          <label>Property Address</label>
                          <textarea
                            placeholder="Address"
                            type="text"
                            value={calcState.pdf.address || ''}
                            name="address"
                            onChange={(e) =>
                              updateState({
                                pdf: { ...calcState.pdf, address: e.target.value }
                              })
                            }
                          />
                        </div>
                      </form>
                    </Col>
                  </Row>
                </div>
              </div>

              {/* Mortgage Payment Section */}
              <div className={`${style.ax_calc_calculator_section} ${style.dropdown_section}`}>
                <div className={`${style.ax_calc_row} ${style.ax_calc_dropdown_row}`}>
                  <div
                    className={`${style.ax_calc_row_inner} ${style.dropdown_toggle}`}
                    style={{ marginBottom: 0, marginTop: 10 }}
                  >
                    <button
                      className={
                        calcState.showMortgagePayment
                          ? `${style.dropdown_button} ${style.dropdown_button_up}`
                          : `${style.dropdown_button}`
                      }
                      onClick={() => toggleSection('showMortgagePayment')}
                    >
                      <UilAngleDown size={24} color="black" />
                    </button>
                    <h2 className={style.ax_calc_primary_font}>Mortgage Payment</h2>
                  </div>
                </div>

                <Container
                  fluid
                  className={`${style.calculartorBody} 
                    ${
                      calcState.showMortgagePayment
                        ? style.show_dropdown_body + ' ' + style.dropdown_body
                        : style.dropdown_body
                    }`}
                  style={{ marginTop: '16px' }}
                >
                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={3} lg={3}>
                      <p style={{ marginTop: '32px' }}>Asking Price:</p>
                    </Col>
                    <Col sm={12} md={9} lg={9}>
                      <strong style={{ fontSize: '24px', lineHeight: '40px', color: '#29c6be' }}>$</strong>
                      <CurrencyInput
                        intlConfig={{ locale: 'en-CA', currency: 'CAD' }}
                        className={style.ax_asking_input}
                        value={calcState.amount}
                        data-number-to-fixed="2"
                        data-number-stepfactor="100"
                        groupSeparator=","
                        decimalSeparator="."
                        prefix="$"
                        allowDecimals
                        decimalsLimit="2"
                        disableAbbreviations
                        onValueChange={handleAmountChange}
                      />
                    </Col>
                  </Row>

                  {/* Down Payment Row */}
                  <Row className={style.ax_calc_row} align="center">
                    <Col sm={12} md={2} lg={2}>
                      <p className={style.ax_calc_downpay_label}>Down Payment:</p>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        {/* Display down payment options based on scenario */}
                        {selectedScenario === 'fthb' && (
                          <>
                            <Col sm={12} md={6} lg={6}>
                              <h3>FTHB Minimum</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPayFTHB)} />
                            </Col>
                            <Col sm={12} md={6} lg={6}>
                              <h3>20% Down</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPay20)} />
                            </Col>
                          </>
                        )}

                        {selectedScenario === 'newBuild' && (
                          <>
                            <Col sm={12} md={3} lg={3}>
                              <h3>5%</h3>
                              <input
                                type="text"
                                readOnly
                                value={calcState.rangeRate.type === 'normalTier' ? toMoney(calcState.downPay5) : '--'}
                              />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>
                                {calcState.rangeRate.type === 'middleTier'
                                  ? `${Math.round(calcState.rangeRate.rate * 100) / 100}%`
                                  : '10%'}
                              </h3>
                              <input
                                type="text"
                                readOnly
                                value={
                                  calcState.rangeRate.type === 'middleTier'
                                    ? toMoney(calcState.downPayRange)
                                    : toMoney(calcState.downPay10)
                                }
                              />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>15%</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPay15)} />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>20%</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPay20)} />
                            </Col>
                          </>
                        )}

                        {selectedScenario === 'preOwned' && (
                          <>
                            <Col sm={12} md={3} lg={3}>
                              <h3>5%</h3>
                              <input
                                type="text"
                                readOnly
                                value={calcState.rangeRate.type === 'normalTier' ? toMoney(calcState.downPay5) : '--'}
                              />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>
                                {calcState.rangeRate.type === 'middleTier'
                                  ? `${Math.round(calcState.rangeRate.rate * 100) / 100}%`
                                  : '10%'}
                              </h3>
                              <input
                                type="text"
                                readOnly
                                value={
                                  calcState.rangeRate.type === 'middleTier'
                                    ? toMoney(calcState.downPayRange)
                                    : toMoney(calcState.downPay10)
                                }
                              />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>15%</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPay15)} />
                            </Col>
                            <Col sm={12} md={3} lg={3}>
                              <h3>20%</h3>
                              <input type="text" readOnly value={toMoney(calcState.downPay20)} />
                            </Col>
                          </>
                        )}
                      </Row>
                    </Col>
                  </Row>

                  {/* Down Payment Selection */}
                  <Row className={style.ax_calc_row} align="center" style={{ paddingTop: 15 }}>
                    <Col sm={12} md={2} lg={2}>
                      <h3>Down Payment Selection:</h3>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={4} lg={4}>
                          <select
                            className={style.ax_select}
                            value={calcState.chosenDownPay.percent || 'select'}
                            onChange={(e) => {
                              // Extract percentage value from the option
                              const percent = e.target.value.replace('%', '')
                              const percentVal = parseFloat(percent)

                              // Find the corresponding down payment amount
                              let amount = 0
                              if (percentVal === 5) amount = calcState.downPay5
                              else if (percentVal === 10) amount = calcState.downPay10
                              else if (percentVal === 15) amount = calcState.downPay15
                              else if (percentVal === 20) amount = calcState.downPay20
                              else if (e.target.value === 'fthb') amount = calcState.downPayFTHB
                              else if (e.target.value === 'range') amount = calcState.downPayRange

                              setChosenDownPayment(e.target.value, amount)
                            }}
                          >
                            <option value="select">Select</option>

                            {/* Dynamic options based on range rate type */}
                            {calcState.rangeRate.type === 'normalTier' && (
                              <>
                                <option value="5%">5% Down</option>
                                <option value="10%">10% Down</option>
                                <option value="15%">15% Down</option>
                                <option value="20%">20% Down</option>
                                {selectedScenario === 'fthb' && <option value="fthb">FTHB Minimum</option>}
                              </>
                            )}

                            {calcState.rangeRate.type === 'middleTier' && (
                              <>
                                <option value="range">{Math.round(calcState.rangeRate.rate * 100) / 100}% Down</option>
                                <option value="15%">15% Down</option>
                                <option value="20%">20% Down</option>
                                {selectedScenario === 'fthb' && <option value="fthb">FTHB Minimum</option>}
                              </>
                            )}

                            {calcState.rangeRate.type === 'maxTier' && (
                              <>
                                <option value="20%">20% Down</option>
                              </>
                            )}
                          </select>
                        </Col>
                      </Row>
                    </Col>
                  </Row>

                  {/* Amortization Period */}
                  <Row className={style.ax_calc_row} align="center" style={{ paddingTop: 15 }}>
                    <Col sm={12} md={2} lg={2}>
                      <h3>Amortization Period (Years):</h3>
                    </Col>
                    <Col sm={12} md={10} lg={10}>
                      <Row className={style.ax_calc_row_inner}>
                        <Col sm={12} md={2} lg={2}>
                          <input
                            type="text"
                            value={calcState.years}
                            onChange={(e) => {
                              const value = e.target.value
                              if (value === '' || /^\d+$/.test(value)) {
                                setYears(parseInt(value || '0', 10))
                              }
                            }}
                          />
                          {selectedScenario === 'preOwned' && calcState.years > 25 && (
                            <p className={style.ax_calc_error}>Max 25 years for pre-owned homes</p>
                          )}
                        </Col>
                        <Col sm={12}>
                          <p className={style.ax_calc_note}>
                            {selectedScenario === 'fthb' && 'First-Time Home Buyers can amortize up to 30 years'}
                            {selectedScenario === 'newBuild' && 'New builds can be amortized up to 30 years'}
                            {selectedScenario === 'preOwned' && 'Pre-owned homes are limited to 25 years amortization'}
                          </p>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </Container>
              </div>

              {/* Submit Button */}
              <div className={style.ax_calc_row}>
                {renderValidationErrors()}
                <button
                  className={style.ax_calc_generate_btn}
                  disabled={validation.length > 0}
                  onClick={handleGeneratePdf}
                >
                  Generate Mortgage Calculation
                  <UilSync size={16} />
                </button>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default MortgageScenarios
