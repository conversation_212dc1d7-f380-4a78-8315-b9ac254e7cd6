import React from 'react'
import style from '../../styles/MortgageCalculator.module.scss'

/**
 * Component that allows switching between mortgage calculator scenarios
 */
const ScenarioSelector = ({ selectedScenario, onSelectScenario }) => {
  return (
    <div className={style.ax_calc_scenario_selector}>
      <h3 className={style.ax_calc_scenario_title}>Choose Mortgage Scenario:</h3>

      <div className={style.ax_calc_scenario_tabs}>
        <button
          className={`${style.ax_calc_scenario_tab} ${
            selectedScenario === 'fthb' ? style.ax_calc_scenario_tab_active : ''
          }`}
          onClick={() => onSelectScenario('fthb')}
        >
          First-Time Home Buyer
          <span className={style.ax_calc_scenario_badge}>30-Year</span>
        </button>

        <button
          className={`${style.ax_calc_scenario_tab} ${
            selectedScenario === 'newBuild' ? style.ax_calc_scenario_tab_active : ''
          }`}
          onClick={() => onSelectScenario('newBuild')}
        >
          New Build
          <span className={style.ax_calc_scenario_badge}>30-Year</span>
        </button>

        <button
          className={`${style.ax_calc_scenario_tab} ${
            selectedScenario === 'preOwned' ? style.ax_calc_scenario_tab_active : ''
          }`}
          onClick={() => onSelectScenario('preOwned')}
        >
          Pre-Owned Home
          <span className={style.ax_calc_scenario_badge}>25-Year</span>
        </button>
      </div>

      <div className={style.ax_calc_scenario_info}>
        {selectedScenario === 'fthb' && (
          <p>
            First-Time Home Buyer benefits include access to 30-year amortization, potential tax credits, and special
            programs.
          </p>
        )}
        {selectedScenario === 'newBuild' && (
          <p>New Build properties qualify for 30-year amortization periods for regular buyers.</p>
        )}
        {selectedScenario === 'preOwned' && (
          <p>Pre-owned properties have a maximum 25-year amortization period for regular buyers.</p>
        )}
      </div>
    </div>
  )
}

export default ScenarioSelector
