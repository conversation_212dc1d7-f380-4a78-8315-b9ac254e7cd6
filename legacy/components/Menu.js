import { useContext, useState, useEffect, useRef } from 'react'
import Cookies from 'js-cookie'
import Link from 'next/link'
import {
  UilFileDownloadAlt,
  UilAngleDoubleLeft,
  UilAngleDoubleRight,
  UilBrowser,
  UilBolt,
  UilSignOutAlt,
  UilClock,
  UilAngleDown,
  UilFileCheckAlt,
  UilEnvelopeAlt,
  UilFidgetSpinner,
  UilSchedule,
  UilAward,
  UilBell,
  UilPresentationPlay,
  UilHeart,
  UilMoneyWithdraw,
  UilUniversity,
  UilMegaphone,
  UilApps,
  UilUser,
  UilUserSquare,
  UilUmbrella,
  UilBullseye,
  UilServerNetworkAlt,
  UilUsersAlt,
  UilWindow
} from '@iconscout/react-unicons'
import AuthContext from '../context/authContext'
import { logout } from '../auth/auth'
import styles from '../styles/Menu.module.scss'

const Menu = () => {
  const baseUrl = process.env.BASE_URL
  const { userAuth, setUserAuth } = useContext(AuthContext)
  const [menuHeight, setMenuHeight] = useState(0)
  const [collapse, setCollapse] = useState(null)
  const [submenuOpen, setSubmenuOpen] = useState({
    marketing: {
      isOpen: false,
      subHeight: 0
    },
    branding: {
      isOpen: false,
      subHeight: 0
    },
    partners: {
      isOpen: false,
      subHeight: 0
    }
  })
  const mktSubmenu = useRef()
  const brandSubmenu = useRef()
  const partnersSubmenu = useRef()
  const navMenu = useRef()
  const eventsUrl = 'https://indievents.ca'
  const collapsedCookie = Cookies.get('isMenuCollapse')

  const handleLogout = () => {
    setUserAuth({ ...userAuth, isAuth: false })
    setTimeout(() => {
      logout()
    }, 500)
  }

  const handleCollapse = (e) => {
    if (collapsedCookie === null || collapsedCookie === undefined) {
      Cookies.set('isMenuCollapse', true)
      setCollapse(true)
    } else {
      Cookies.set('isMenuCollapse', !collapse)
      setCollapse(!collapse)
    }
  }

  const handleSubmenuHeight = (i, type) => {
    let items
    if (type === 'onepage') {
      items = userAuth.onePages
    } else {
      items = Array.from(i.querySelectorAll('a'))
    }

    const heightArr = items.map((itm) => 40)
    const total = heightArr.reduce((sum, curr) => sum + curr)
    return total
  }

  const handleSubmenu = (e) => {
    const menuId = e.target.id

    if (menuId === 'marketing') {
      const sumHeight = handleSubmenuHeight(mktSubmenu.current)
      if (submenuOpen.marketing.isOpen) {
        setSubmenuOpen({ ...submenuOpen, marketing: { ...marketing, isOpen: false, subHeight: 0 } })
      } else {
        setSubmenuOpen({ ...submenuOpen, marketing: { ...marketing, isOpen: true, subHeight: `${sumHeight}px` } })
      }
    }

    if (menuId === 'branding') {
      const sumHeight = handleSubmenuHeight(brandSubmenu.current)
      if (submenuOpen.branding.isOpen) {
        setSubmenuOpen({ ...submenuOpen, branding: { ...branding, isOpen: false, subHeight: 0 } })
      } else {
        setSubmenuOpen({ ...submenuOpen, branding: { ...branding, isOpen: true, subHeight: `${sumHeight}px` } })
      }
    }

    if (menuId === 'partners') {
      const sumHeight = handleSubmenuHeight(partnersSubmenu.current, 'onepage')
      if (submenuOpen.partners.isOpen) {
        setSubmenuOpen({ ...submenuOpen, partners: { ...partners, isOpen: false, subHeight: 0 } })
      } else {
        setSubmenuOpen({ ...submenuOpen, partners: { ...partners, isOpen: true, subHeight: `${sumHeight}px` } })
      }
    }
  }

  const calcMenuHeight = () => {
    const items = navMenu.current.querySelectorAll('div')
    const itemsArr = Array.from(items)
    let sum = 0
    itemsArr.forEach((i) => {
      sum += 36
    })
    return sum
  }

  useEffect(() => {
    setCollapse(Cookies.get('isMenuCollapse'))
    setMenuHeight(calcMenuHeight())
  }, [])

  return (
    <aside className={`${styles.ax_menu} ${collapse ? styles.axCollapsed : ''}`}>
      <button className={styles.btnCollapse} onClick={(e) => handleCollapse(e)}>
        {collapse ? <UilAngleDoubleRight size={16} /> : <UilAngleDoubleLeft size={16} />}
      </button>
      <div className={styles.scrollContainer}>
        <nav ref={navMenu} style={{ height: `${menuHeight}px` }}>
          <div className={styles.ax_menu_item}>
            <UilApps size={20} onClick={() => (window.parent.location.href = `${baseUrl}/dashboard`)} />
            <Link href="/dashboard"> Dashboard</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilWindow size={20} onClick={() => (window.parent.location.href = `${baseUrl}/indi-app`)} />
            <Link href="/indi-app"> Indi App</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilAward size={20} onClick={() => (window.parent.location.href = `${baseUrl}/awards`)} />
            <Link href="/awards"> Indi Awards</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilHeart size={20} onClick={() => (window.parent.location.href = `${baseUrl}/indi-cares`)} />
            <Link href="/indi-cares"> Indi Cares</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilFidgetSpinner size={20} onClick={() => (window.parent.location.href = `${baseUrl}/indi-fit-club`)} />
            <Link href="/indi-fit-club"> Indi Fit Club</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilBrowser size={20} onClick={() => (window.parent.location.href = `${baseUrl}/indi-sites`)} />
            <Link href="/indi-sites"> My Indi Site</Link>
          </div>

          <div className={`${styles.ax_menu_item} ${submenuOpen.branding.isOpen ? styles.childsOpened : ''}`}>
            <UilBullseye size={20} onClick={() => (window.parent.location.href = `${baseUrl}/branding`)} />
            {collapse ? (
              <Link href="/branding"> Branding</Link>
            ) : (
              <button onClick={(e) => handleSubmenu(e)} id="branding">
                {' '}
                Branding <UilAngleDown size={16} />
              </button>
            )}

            <div
              className={`${styles.submenu} ${submenuOpen.branding.isOpen ? styles.submenuOpen : ''}`}
              ref={brandSubmenu}
              style={{ height: submenuOpen.branding.subHeight }}
            >
              <div className={`${styles.ax_submenu_item}`}>
                <Link href="/logos-fonts"> Logos, Fonts & Guidelines</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/brand-materials"> Brand Materials</Link>
              </div>
            </div>
          </div>

          <div className={styles.ax_menu_item}>
            <UilSchedule size={20} onClick={() => (window.parent.location.href = `${baseUrl}/company-calendar`)} />
            <Link href="/company-calendar"> Company Calendar</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilUserSquare size={20} onClick={() => (window.parent.location.href = `${baseUrl}/company-directory`)} />
            <Link href="/company-directory"> Company Directory</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilFileCheckAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/compliance`)} />
            <Link href="/compliance"> Compliance</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilFileCheckAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/fintrac`)} />
            <Link href="/fintrac"> FINTRAC</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilClock size={20} onClick={() => (window.parent.location.href = eventsUrl)} />
            <Link href={eventsUrl} target="_blank">
              {' '}
              Events
            </Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilUmbrella size={20} onClick={() => (window.parent.location.href = `${baseUrl}/group-benefits`)} />
            <Link href="/group-benefits"> Group Benefits</Link>
          </div>

          <div className={`${styles.ax_menu_item}`}>
            <UilUniversity size={20} onClick={() => (window.parent.location.href = `${baseUrl}/lenders`)} />
            <Link href="/lenders">Lender Lounge</Link>
          </div>

          <div className={`${styles.ax_menu_item} ${submenuOpen.marketing.isOpen ? styles.childsOpened : ''}`}>
            <UilMegaphone size={20} onClick={() => (window.parent.location.href = `${baseUrl}/marketing`)} />
            {collapse ? (
              <Link href="/marketing"> Marketing</Link>
            ) : (
              <button onClick={(e) => handleSubmenu(e)} id="marketing">
                {' '}
                Marketing <UilAngleDown size={16} />
              </button>
            )}

            <div
              className={`${styles.submenu} ${submenuOpen.marketing.isOpen ? styles.submenuOpen : ''}`}
              ref={mktSubmenu}
              style={{ height: submenuOpen.marketing.subHeight }}
            >
              <div className={`${styles.ax_submenu_item}`}>
                <Link href="/social-media"> Social Media</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/email-signature"> Email Signature</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/printables"> Printables</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/listing-sheet"> Listing Sheet</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/custom-shop"> The Custom Shop</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/client-gift"> Operation Impact</Link>
              </div>

              <div className={styles.ax_submenu_item}>
                <Link href="/qr-codes"> QR Codes</Link>
              </div>
            </div>
          </div>

          <div className={styles.ax_menu_item}>
            <UilEnvelopeAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/newsletter-archive`)} />
            <Link href="/newsletter-archive"> Newsletter Archive</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilBell size={20} onClick={() => (window.parent.location.href = `${baseUrl}/notifications`)} />
            <Link href="/notifications"> Notifications</Link>
          </div>

          <div className={`${styles.ax_menu_item} ${submenuOpen.partners.isOpen ? styles.childsOpened : ''}`}>
            <UilBolt size={20} />
            {collapse ? (
              <Link href="/partnerships">Partners</Link>
            ) : (
              <button onClick={(e) => handleSubmenu(e)} id="partners">
                {' '}
                Partners <UilAngleDown size={16} />
              </button>
            )}

            <div
              className={`${styles.submenu} ${submenuOpen.partners.isOpen ? styles.submenuOpen : ''}`}
              ref={partnersSubmenu}
              style={{ height: submenuOpen.partners.subHeight }}
            >
              {userAuth.onePages && userAuth.onePages.length > 0 ? (
                userAuth.onePages.map((link) => {
                  return (
                    <div className={`${styles.ax_submenu_item}`} key={`${baseUrl}/partnerships/${link.slug}`}>
                      <Link href={`${baseUrl}/partnerships/${link.slug}`}> {link.Title}</Link>
                    </div>
                  )
                })
              ) : (
                <div className={`${styles.ax_submenu_item}`}>
                  <span>Loading partners...</span>
                </div>
              )}
            </div>
          </div>

          <div className={styles.ax_menu_item}>
            <UilMoneyWithdraw
              size={20}
              onClick={() => window.open('https://portal.scarlettnetwork.com/portal/dashboard', '_blank')}
            />
            <Link target="_blank" href="https://portal.scarlettnetwork.com/portal/dashboard">
              Payroll
            </Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilUser size={20} onClick={() => (window.parent.location.href = `${baseUrl}/profile`)} />
            <Link href="/profile"> Profile</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilUsersAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/realtors`)} />
            <Link href="/realtors"> My Realtors</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilFileDownloadAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/resources`)} />
            <Link href="/resources"> Resources</Link>
          </div>

          <div className={`${styles.ax_menu_item} ${styles.menuBlue}`}>
            <UilServerNetworkAlt size={20} onClick={() => (window.parent.location.href = `${baseUrl}/technology`)} />
            <Link href="/technology"> Technology</Link>
          </div>

          <div className={styles.ax_menu_item}>
            <UilPresentationPlay size={20} onClick={() => (window.parent.location.href = `${baseUrl}/tutorials`)} />
            <Link href="/tutorials"> Tutorials & Videos</Link>
          </div>

          {/* <div className={styles.ax_menu_item}>
            <UilBooks
              size={20}              
              onClick={() => (window.parent.location.href = `${baseUrl}/knowledge-base`)}
            />
            <Link href="/knowledge-base"> Knowledge Base</Link>
          </div> */}

          <div className={styles.ax_menu_item}>
            <UilSignOutAlt size={20} onClick={handleLogout} />
            <button type="button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </nav>
      </div>
    </aside>
  )
}

export default Menu
