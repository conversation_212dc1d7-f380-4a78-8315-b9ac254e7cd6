{"id": 61, "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "provider": "local", "password": "$2a$10$tmlekyltbszFufutATtia.F7ua0q6./3p.7NC78NMQB482E9x6jyC", "confirmed": true, "blocked": false, "confirmationToken": null, "resetPasswordToken": null, "license": "", "phone": "**********", "bio": "I'm 6.2 ft tall and now I live in Calgary.", "address": "223 14 Street NW ", "firstname": "<PERSON>", "lastname": "<PERSON>", "position": "IT Director", "bioTitle": "Let’s Get to Know Each Other!", "applicationLink": "https://", "brokerage": "Indi Mortgage ", "mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2502.8922914360755!2d-113.97212979999999!3d51.1473373!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x537163cbfe45ad89%3A0xd1ca946f48cc377d!2s4310%20104%20Ave%20NE%20%233242%2C%20Calgary%2C%20AB%20", "facebook": "https://facebook.com/indimortgage/", "instagram": "https://instagram.com/indimortgage/", "twitter": "https://twitter.com/IndiMortgage", "linkedin": "https://ca.linkedin.com/company/indimortgage", "youtube": "https://youtube.com/codesigners", "whatsapp": false, "hasLogo2": false, "fax": null, "hasCustomBanner": false, "titles": "", "photoOnPrintable": true, "website": "https://brunosousa.cc", "ext": null, "qrCodes": null, "qrCodeOnPrintable": false, "isOnboarding": false, "homePhone": null, "cellPhone": "**********", "emergencyContact": "<PERSON><PERSON><PERSON>", "emergencyPhone": "3688872122", "birthdate": "2024-10-16", "sin": "ENC:jvRZDJbIYcQrGqohEmAC7g==:E30kQu/73/9ITNeStakOPw==:DH9QdIwpFaCQliU=", "startDate": "2016-09-05", "dietRestriction": "Fructosis", "additionalNotes": "2 cats: <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>.", "middlename": "", "city": "Calgary", "postalCode": "N2H3X6", "tollfree": null, "workEmail": "<EMAIL>", "websiteOptIn": true, "ownDomain": true, "providedDomain": false, "websiteDomainName": "brunao.me", "websiteDomainRegistrar": "gandi.net", "tollfreeExt": null, "gender": "male", "appointmentScheduleLink": "https://brunosousa.cc", "province": "manitoba", "tshirtSize": "XL", "additionalDomainNames": "brunosousa.com.br\ncodesigners.cc\ndjfsldkjf,\r\nksjdlfkjlk,\r\nlksjdf,\r\njjj\nj", "secondaryWebsite": "https://youtube.com/codesigners", "onboardingStartDate": "2022-09-16", "onboardingEndDate": "2022-10-06", "loginCount": "48", "legalName": "", "preferredName": "", "googleTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "facebookPixelTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleWebsiteVerification": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleTagManagerInHead": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "googleTagManagerInBody": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "thirdPartyScriptTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "emptyPrintableFooter": false, "googleReviewsLink": "https://", "facebookHandler": null, "instagramHandler": null, "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "notListed": null, "personalAddress": "850 11 St SW", "personalCity": "Calgary", "personalProvince": "Alberta", "personalPostalCode": "T2P 1P6", "personalSuiteUnit": "3303", "suiteUnit": null, "licensed": true, "chatWidgetCode": "Testing New", "reviewWidgetCode": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "emailNotifications": true, "circularPhotoUrl": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "isStaffMember": true, "tiktok": null, "tiktokHandler": null, "pinterest": null, "pinterestHandler": null, "threads": null, "threadsHandler": null, "bluesky": null, "blueskyHandler": null, "websiteGitBranch": "<PERSON><PERSON><PERSON><PERSON>", "isComplianceStaff": true, "createdAt": "2021-04-20T19:16:53.149Z", "updatedAt": "2025-06-16T15:49:40.734Z", "documentId": "wklbl295b6jqqs3gohyl17hx", "locale": null, "publishedAt": "2025-06-27T21:47:05.978Z", "role": {"id": 2, "name": "Admin", "description": "Administrators", "type": "admin", "createdAt": null, "updatedAt": "2025-07-14T23:03:58.636Z", "documentId": "wtomvm325t0hn7itpsx494tm", "locale": null, "publishedAt": "2025-06-27T21:47:05.975Z"}, "team": {"id": 208, "name": "Indi Calgary", "officePhone": "***********", "address": "223 14 Street NW", "province": "alberta", "mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2507.9327151227085!2d-114.09720278396097!3d51.05433017956321!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53716fea1ba82deb%3A0xa52b9002ec27e690!2sAxiom%20Mortgage%20Solutions!5e0!3m2!1sen!2sbr!4", "city": "Calgary", "cobranded": false, "showFSRA": false, "showPoweredBy": false, "postal": "T2N 1Z6", "corporateName": null, "applicationLink": "https://access-on-demand.mtg-app.com/signup", "facebook": "https://www.facebook.com/indimortgage/", "instagram": "https://www.instagram.com/indimortgage", "linkedin": "https://ca.linkedin.com/company/indimortgage", "x": "https://x.com/i/flow/login?redirect_after_login=%2FIndiMortgage", "youtube": null, "googleTag": null, "facebookPixelTag": null, "googleWebsiteVerification": null, "GoogleTagManagerInHead": null, "googleTagManagerInBody": null, "thirdPartyScriptTag": null, "chatWidgetCode": null, "reviewWidgetCode": null, "appointmentScheduleLink": null, "email": null, "license": null, "websiteCompanyName": "Indi Mortgage", "showCustomFaq": null, "website": null, "websiteGitBranch": null, "tagline": null, "createdAt": "2021-05-25T17:57:38.857Z", "updatedAt": "2025-01-06T22:43:33.175Z", "publishedAt": null, "documentId": "you6bvo7u4rf45y5wg07826x", "locale": "en"}, "branch": {"id": 90, "title": "Calgary", "slug": "calgary", "address": "223 14 Street NW ", "city": "Calgary", "province": "alberta", "postal": "T2N 1Z6", "mapEmbedSrc": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63559730.7313724!2d-120.64714481286511!3d13.512372954476643!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x53716fea1ba82deb%3A0xa52b9002ec27e690!2sAxiom%20Mortgage%20Solutions!5e0!3m2!1sen!2sbr!4v", "mapLocationUrl": "https://goo.gl/maps/ajntTDbJQgCnQkNN6", "provinceLicenseNumber": null, "suiteUnit": null, "isCorporate": null, "createdAt": "2023-04-03T22:24:43.870Z", "updatedAt": "2023-04-20T13:17:43.390Z", "publishedAt": null, "documentId": "pstxc0oxfefz4v29c557d337", "locale": "en"}, "onboardingProcess": null, "photo": {"id": 13897, "name": "Bruno Portrait-1 low.jpg", "alternativeText": "", "caption": "", "width": null, "height": null, "formats": {"small": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "hash": "1745278540247_0f8wg_small", "mime": "image/jpeg", "name": "1745278540247_0f8wg_small.jpg", "path": null, "size": 8634, "width": 300, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "medium": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "hash": "1745278540247_0f8wg_medium", "mime": "image/jpeg", "name": "1745278540247_0f8wg_medium.jpg", "path": null, "size": 33430, "width": 750, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "squared": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "hash": "1745278540247_0f8wg_squared", "mime": "image/jpeg", "name": "1745278540247_0f8wg_squared.jpg", "path": null, "size": 8634, "width": 300, "folder": null, "height": 300, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}}, "hash": "1745278540247_0f8wg", "ext": ".jpg", "mime": "image/jpeg", "size": 34066, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2025-04-21T23:35:40.463Z", "updatedAt": "2025-04-21T23:35:43.237Z", "documentId": "ole747876ndcv9t3t8mz63fk", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "logoHeader": {"id": 79, "name": "AMS-Logo-Standard.svg", "alternativeText": "", "caption": "", "width": 578, "height": 434, "formats": {}, "hash": "AMS_Logo_Standard_d383969509", "ext": ".svg", "mime": "image/svg+xml", "size": 13.29, "url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/AMS_Logo_Standard_d383969509.svg", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2020-12-15T14:54:33.902Z", "updatedAt": "2025-02-20T18:33:13.712Z", "documentId": "zsvw5bb669tts58ci88prc0e", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "logoHeader2": null, "logoFooter": {"id": 102, "name": "AMS-Logo-white-green.svg", "alternativeText": "", "caption": "", "width": 578, "height": 434, "formats": {}, "hash": "AMS_Logo_white_green_1a8c153d75", "ext": ".svg", "mime": "image/svg+xml", "size": 13.99, "url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/AMS_Logo_white_green_1a8c153d75.svg", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2020-12-15T14:54:34.045Z", "updatedAt": "2025-02-14T21:02:23.988Z", "documentId": "oy2z53enk3mo3i234zcw46ek", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "homeBanner": null, "printPhoto": {"id": 13264, "name": "DSC01234.JPG", "alternativeText": "", "caption": "", "width": null, "height": null, "formats": {"small": {"ext": ".JPG", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/1741883690586_spb3x-DSC01234.JPG", "hash": "1741883690586_spb3x_small", "mime": "image/jpeg", "name": "1741883690586_spb3x_small.JPG", "path": null, "size": 6746, "width": 300, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "medium": {"ext": ".JPG", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/1741883690586_spb3x-DSC01234.JPG", "hash": "1741883690586_spb3x_medium", "mime": "image/jpeg", "name": "1741883690586_spb3x_medium.JPG", "path": null, "size": 25281, "width": 750, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "squared": {"ext": ".JPG", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1741883690586_spb3x-DSC01234.JPG", "hash": "1741883690586_spb3x_squared", "mime": "image/jpeg", "name": "1741883690586_spb3x_squared.JPG", "path": null, "size": 9910, "width": 300, "folder": null, "height": 300, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "thumbnail": {"ext": ".JPG", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/thumbnail/1741883690586_spb3x-DSC01234.JPG", "hash": "1741883690586_spb3x_thumbnail", "mime": "image/jpeg", "name": "1741883690586_spb3x_thumbnail.JPG", "path": null, "size": 1496064, "width": null, "folder": null, "height": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}}, "hash": "1741883690586_spb3x", "ext": ".JPG", "mime": "image/jpeg", "size": 11206656, "url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/1741883690586_spb3x.JPG", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2025-03-13T16:34:58.378Z", "updatedAt": "2025-03-13T16:35:10.777Z", "documentId": "fc76o1cjrugabd75os3xvncz", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "signature": {"id": 2477, "name": "applicantSignature-Brunelvis-de Sousa.png", "alternativeText": null, "caption": null, "width": 1000, "height": 406, "formats": {"small": {"ext": ".png", "url": "https://indi-strapi-v2.s3.amazonaws.com/private/images/small/applicant_Signature_B<PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0.png", "hash": "small_applicant_Signature_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0", "mime": "image/png", "name": "small_applicantSignature-Brunelvis-de Sousa.png", "path": null, "size": 11.35, "width": 500, "folder": null, "height": 203, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "medium": {"ext": ".png", "url": "https://indi-strapi-v2.s3.amazonaws.com/private/images/medium/applicant_Signature_B<PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0.png", "hash": "medium_applicant_Signature_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0", "mime": "image/png", "name": "medium_applicantSignature-Brunelvis-de Sousa.png", "path": null, "size": 23.38, "width": 750, "folder": null, "height": 305, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.amazonaws.com/private/images/thumbnail/applicant_Signature_B<PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0.png", "hash": "thumbnail_applicant_Signature_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0", "mime": "image/png", "name": "thumbnail_applicantSignature-Brunelvis-de Sousa.png", "path": null, "size": 3.86, "width": 245, "folder": null, "height": 99, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}}, "hash": "applicant_Signature_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0", "ext": ".png", "mime": "image/png", "size": 33.72, "url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/applicant_Signature_B<PERSON><PERSON><PERSON>_de_Sousa_d6ce9fcdb0.png", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2023-01-13T16:23:14.069Z", "updatedAt": "2023-04-13T13:36:43.902Z", "documentId": "l5osixz9lk60tqk0ge010i8p", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "initials": {"id": 2542, "name": "applicantInitials-Brunelvis-de Sousa.png", "alternativeText": null, "caption": null, "width": 500, "height": 406, "formats": {"thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.amazonaws.com/private/images/thumbnail/applicant_Initials_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_ca05d9b655.png", "hash": "thumbnail_applicant_<PERSON>s_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_ca05d9b655", "mime": "image/png", "name": "thumbnail_applicantInitials-Brunelvis-de Sousa.png", "path": null, "size": 4.31, "width": 192, "folder": null, "height": 156, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}}, "hash": "applicant_Initials_<PERSON><PERSON><PERSON><PERSON>_de_Sousa_ca05d9b655", "ext": ".png", "mime": "image/png", "size": 18.29, "url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/applicant_Initials_B<PERSON><PERSON><PERSON>_de_Sousa_ca05d9b655.png", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2023-01-13T16:24:06.684Z", "updatedAt": "2023-01-13T16:24:38.261Z", "documentId": "n48ub80nrt8sxstezbckz7rj", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "circularPhoto": {"id": 14660, "name": "1745278540247_0f8wg-Bruno_Portrait-1_low.png", "alternativeText": null, "caption": null, "width": 300, "height": 300, "formats": {"small": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_small", "mime": "image/png", "name": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_small.png", "path": null, "size": 29362, "width": 300, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "medium": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_medium", "mime": "image/png", "name": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_medium.png", "path": null, "size": 157518, "width": 750, "folder": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_squared", "mime": "image/png", "name": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_squared.png", "path": null, "size": 29362, "width": 300, "folder": null, "height": 300, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}, "thumbnail": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/thumbnail_1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-thumbnail_1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195", "mime": "image/png", "name": "thumbnail_1745278540247_0f8wg-Bruno_Portrait-1_low.png", "path": null, "size": 31.67, "width": 156, "folder": null, "height": 156, "formats": {"small": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/small/thumbnail_1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-thumbnail_1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_small", "mime": "image/png", "name": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_small.png", "size": 32384, "width": 300}, "medium": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/medium/thumbnail_1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-thumbnail_1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_medium", "mime": "image/png", "name": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_medium.png", "size": 135567, "width": 750}, "squared": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/thumbnail_1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-thumbnail_1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "hash": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_squared", "mime": "image/png", "name": "thumbnail_1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195_squared.png", "size": 32384, "width": 300, "height": 300}}, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "folderPath": "/", "provider_metadata": null}}, "hash": "1745278540247_0f8wg_Bruno_Portrait_1_low_f9c6849195", "ext": ".png", "mime": "image/png", "size": 35.16, "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg_<PERSON>_Portrait_1_low_f9c6849195-1745278540247_0f8wg-<PERSON>_Portrait-1_low.png", "previewUrl": null, "provider": "strapi-provider-upload-aws-s3-resizing-and-optimisation", "provider_metadata": null, "folderPath": "/", "createdAt": "2025-06-16T15:49:39.456Z", "updatedAt": "2025-06-16T15:49:40.652Z", "documentId": "ho7dsn67tfw86vlce1et9jpl", "locale": "en", "publishedAt": "2025-06-27T21:47:05.288Z"}, "customOnboardingForms": null, "badges": [], "showBadges": null, "onboarding": [], "testimonialsList": [], "notification": [], "events": [{"id": 81, "title": "Axiom Conference 2024 - Jamaica", "slug": "axiom-conference-2024-jamaica", "termsAndConditionLink": null, "eventStatus": "finished", "shortDescription": "", "isPrivate": false, "accessCode": "", "startDate": "2024-02-03", "endDate": "2024-02-07", "location": "Ocean Eden Bay Resort ", "locationFullAddress": "Mountain Spring Bay Trelawny St, Jamaica", "locationMapEmbedCode": "<iframe src=\"https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d2089.565962842947!2d-77.57208710357378!3d18.488402129345154!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8eda3572b97eb7a3%3A0x7bcf5a420aa9ba1!2sOcean%20Eden%20Bay%20-%20Adults%20Only!5e0!3m2!1sen!2sbr!4v1693854661608!5m2!1sen!2sbr\" width=\"600\" height=\"450\" style=\"border:0;\" allowfullscreen=\"\" loading=\"lazy\" referrerpolicy=\"no-referrer-when-downgrade\"></iframe>", "guestsNumberLimit": "2", "guestsNote": "There is a lot more flexibility this year, including direct flights to/from Calgary, Edmonton, Winnipeg, Toronto and Ottawa and the option to extend your stay to February 10th. \n\n* If you are travelling with children you must contact <PERSON><PERSON> (<EMAIL>) to make arrangements for booking the family section of the resort - the Axiom events will be taking place on the adult only side of the resort.\n\n* If you have specific travel needs and/or requirements the new travel agent we are working with will be in touch to discuss your options.\n\nREGISTRATION CLOSES: October 25, 2023 (we will not be taking registration requests after this date).\n", "isListed": true, "svgBanner": "", "attachmentsTitle": "ZIP files only", "attachmentsDescription": "**Please upload a photo of the 2 initial pages of your passport document. Add both images to a ZIP file and attach it below.\nOnly ZIP files are allowed.**\n", "bgColorHex": "#f785ff", "startTime": null, "endTime": null, "showOnCalendar": true, "attendingToQuestionLabel": null, "closeRegistrationAt": null, "createdAt": "2023-09-05T12:54:54.493Z", "updatedAt": "2025-06-23T16:50:37.963Z", "publishedAt": null, "documentId": "jhidb4mo5sd86ae054tkn3rp", "locale": "en"}], "gifts": [], "languages": [], "realtors": [], "listingSheets": [{"id": 601, "title": "MLS: TEST", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "60c0aa8960fa67001794f578", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Ideal_Mortgage_Solutions_Logo_71d010cf00.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743112395470_9lkxo-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1743112395470_9lkxo-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": "tEST", "mlsCode": "TEST", "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743572369865_icxy5-<PERSON>_Portrait-1_low.jpg"}, "monthlyExpenses": true}, "rate": 5.762711864406779, "cable": 0, "phone": 0, "rate5": 0, "years": 25, "amount": "590000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"id": "67ecc732e62a06070f45ace6", "email": "<EMAIL>", "phone": "9998877777", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743570737054_n7bap-<PERSON>_<PERSON>_<PERSON>.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1743570737054_n7bap-<PERSON>_<PERSON>_<PERSON>.jpg"}, "thumbnail": {"url": null}}}, "company": "Rome", "website": "https://cesar.rome", "lastname": "<PERSON>", "position": "Emperor", "firstname": "Cesar", "middlename": null}, "downPay5": 0, "internet": 0, "appraisal": 0, "condoFees": 0, "downPay10": 0, "downPay15": 88500, "downPay20": 118000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 5.762711864406779, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 0, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "590000", "customYears": 0, "estoppelFee": 0, "insurance10": 0, "insurance15": 14042, "insurance20": 0, "monthlyPay5": null, "principal10": 547461, "principal15": 515542, "principal20": 472000, "propertyTax": 0, "downPayRange": 34000, "monthlyPay10": null, "monthlyPay15": 2998.4179719595304, "monthlyPay20": 2745.1755293747133, "periodicPay5": null, "chosenDownPay": {"rate": "rateRange", "amount": 34000, "percent": "5.762711864406779%"}, "downPayCustom": 135700, "periodicPay10": null, "periodicPay15": 2998.4179719595304, "periodicPay20": 2745.1755293747133, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0, "effectiveRate20": 0, "effectiveRateRange": 0, "effectiveRateCustom": 0}, "homeInspection": 0, "insuranceRange": 22240, "principalRange": 578240, "titleInsurance": 0, "chosenPrincipal": 578240, "insuranceCustom": 0, "monthlyPayRange": 3363.0726654780387, "principalCustom": 454300, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "23", "monthlyPayCustom": 2642.2314470231618, "numberOfPayments": 12, "periodicPayRange": 3363.0726654780387, "chosenPeriodicPay": 3363.0726654780387, "periodicPayCustom": 2642.2314470231618, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 578240, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 34000, "percent": "5.762711864406779%"}, "totalMonthlyPayments": 3363.0726654780387, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "mls-TEST-241202c9-23ab-a7d8-3a02-9b893528bc1a", "createdAt": "2025-04-02T05:40:08.813Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "avxw2vithnve2yrytrpensfy", "locale": "en"}, {"id": 602, "title": "price-900000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "60c0aa8960fa67001794f578", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Ideal_Mortgage_Solutions_Logo_71d010cf00.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743574892058_uwqkh-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1743574892058_uwqkh-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 7.222222222222223, "cable": 0, "phone": 0, "rate5": 0, "years": 25, "amount": "900000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"id": "67ecc732e62a06070f45ace6", "email": "<EMAIL>", "phone": "9998877777", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743570737054_n7bap-<PERSON>_<PERSON>_<PERSON>.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1743570737054_n7bap-<PERSON>_<PERSON>_<PERSON>.jpg"}, "thumbnail": {"url": null}}}, "company": "Rome", "website": "https://cesar.rome", "lastname": "<PERSON>", "position": "Emperor", "firstname": "Cesar", "middlename": null}, "downPay5": 45000, "internet": 0, "appraisal": 0, "condoFees": 0, "downPay10": 90000, "downPay15": 135000, "downPay20": 180000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 7.222222222222223, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 34200, "principal5": 889200, "rateCustom": "5", "showImages": true, "askingPrice": "900000", "customYears": 0, "estoppelFee": 0, "insurance10": 25110, "insurance15": 21420, "insurance20": 0, "monthlyPay5": null, "principal10": 835110, "principal15": 786420, "principal20": 720000, "propertyTax": 0, "downPayRange": 65000.00000000001, "monthlyPay10": null, "monthlyPay15": 4573.857923328098, "monthlyPay20": 4187.555892266512, "periodicPay5": null, "chosenDownPay": {"rate": "rateRange", "amount": 65000.00000000001, "percent": "7.222222222222223%"}, "downPayCustom": 0, "periodicPay10": null, "periodicPay15": 4573.857923328098, "periodicPay20": 4187.555892266512, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0, "effectiveRate20": 0, "effectiveRateRange": 0, "effectiveRateCustom": 0}, "homeInspection": 0, "insuranceRange": 33400, "principalRange": 868400, "titleInsurance": 0, "chosenPrincipal": 868400, "insuranceCustom": 0, "monthlyPayRange": 5050.657690061443, "principalCustom": 0, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": 0, "monthlyPayCustom": 0, "numberOfPayments": 12, "periodicPayRange": 5050.657690061443, "chosenPeriodicPay": 5050.657690061443, "periodicPayCustom": 0, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 868400, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 65000.00000000001, "percent": "7.222222222222223%"}, "totalMonthlyPayments": 5050.657690061443, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-900000-66b85d5d-782f-e98d-6eaf-7332a522af6e", "createdAt": "2025-04-03T17:52:12.481Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "s0e3384olk8ujiz45zb4rlxt", "locale": "en"}, {"id": 603, "title": "price-900000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "60c0aa8960fa67001794f578", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Ideal_Mortgage_Solutions_Logo_71d010cf00.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1743574892058_uwqkh-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1743574892058_uwqkh-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 7.222222222222223, "cable": 0, "phone": 0, "rate5": 0, "years": 25, "amount": "900000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"id": "6709955074fea9e00e19c918", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/<PERSON>_<PERSON>_<PERSON>_fcbe39c813.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.amazonaws.com/images/squared/<PERSON>_<PERSON>_<PERSON>_fcbe39c813.jpg"}, "thumbnail": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/thumbnail/<PERSON>_<PERSON>_<PERSON>_fcbe39c813.jpg"}}}, "company": "<PERSON><PERSON><PERSON><PERSON>", "website": "https://realtor.com", "lastname": "<PERSON><PERSON><PERSON>", "position": "<PERSON><PERSON><PERSON><PERSON>", "firstname": "<PERSON> ", "middlename": null}, "downPay5": 45000, "internet": 0, "appraisal": 0, "condoFees": 0, "downPay10": 90000, "downPay15": 135000, "downPay20": 180000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 7.222222222222223, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 34200, "principal5": 889200, "rateCustom": "5", "showImages": true, "askingPrice": "900000", "customYears": 0, "estoppelFee": 0, "insurance10": 25110, "insurance15": 21420, "insurance20": 0, "monthlyPay5": null, "principal10": 835110, "principal15": 786420, "principal20": 720000, "propertyTax": 0, "downPayRange": 65000.00000000001, "monthlyPay10": null, "monthlyPay15": 4573.857923328098, "monthlyPay20": 4187.555892266512, "periodicPay5": null, "chosenDownPay": {"rate": "rateRange", "amount": 65000.00000000001, "percent": "7.222222222222223%"}, "downPayCustom": 0, "periodicPay10": null, "periodicPay15": 4573.857923328098, "periodicPay20": 4187.555892266512, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0, "effectiveRate20": 0, "effectiveRateRange": 0, "effectiveRateCustom": 0}, "homeInspection": 0, "insuranceRange": 33400, "principalRange": 868400, "titleInsurance": 0, "chosenPrincipal": 868400, "insuranceCustom": 0, "monthlyPayRange": 5050.657690061443, "principalCustom": 0, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": 0, "monthlyPayCustom": 0, "numberOfPayments": 12, "periodicPayRange": 5050.657690061443, "chosenPeriodicPay": 5050.657690061443, "periodicPayCustom": 0, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 868400, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 65000.00000000001, "percent": "7.222222222222223%"}, "totalMonthlyPayments": 5050.657690061443, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-900000-321dae22-cea3-db82-3887-46cd511077dc", "createdAt": "2025-04-03T17:52:42.637Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "mzdv1q00sa6z23b25ocpzi62", "locale": "en"}, {"id": 627, "title": "price-1200000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "60c0a56360fa67001794f569", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/AMS_Logo_Standard_d383969509.svg"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": "5", "cable": 0, "phone": 0, "rate5": 0, "years": 30, "amount": "1200000", "rate10": 0, "rate15": 0, "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"id": "680bfd84af775a0015c3832d", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg"}, "thumbnail": {"url": null}}}, "company": "Test", "website": "https://test.com", "lastname": "Sousa", "position": "Testing", "firstname": "<PERSON><PERSON><PERSON><PERSON>", "middlename": null}, "downPay5": 0, "internet": 0, "appraisal": 0, "condoFees": 0, "downPay10": 0, "downPay15": 0, "downPay20": 240000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 20, "type": "maxTier"}, "rateRange": 0, "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 0, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "1200000", "customYears": 0, "estoppelFee": 0, "insurance10": 0, "insurance15": 0, "insurance20": 0, "monthlyPay5": null, "principal10": 0, "principal15": 0, "principal20": 960000, "propertyTax": 0, "downPayRange": 240000, "monthlyPay10": null, "monthlyPay15": null, "monthlyPay20": 5123.430516564053, "periodicPay5": null, "chosenDownPay": {"rate": "rate20", "amount": 240000, "percent": "20%"}, "downPayCustom": 420000, "periodicPay10": null, "periodicPay15": null, "periodicPay20": 5123.430516564053, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0, "effectiveRate20": 0, "effectiveRateRange": 0, "effectiveRateCustom": 0}, "homeInspection": 0, "insuranceRange": 0, "principalRange": 960000, "titleInsurance": 0, "chosenPrincipal": 960000, "insuranceCustom": 0, "monthlyPayRange": null, "principalCustom": 780000, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "35", "monthlyPayCustom": 4162.787294708293, "numberOfPayments": 12, "periodicPayRange": null, "chosenPeriodicPay": 5123.430516564053, "periodicPayCustom": 4162.787294708293, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 960000, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 240000, "percent": "20%"}, "totalMonthlyPayments": 5123.430516564053, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-1200000-c8871124-a830-f830-d590-94cb6de47fa5", "createdAt": "2025-04-25T22:03:09.882Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "jydg0bb6hpdyxaow617ny574", "locale": "en"}, {"id": 636, "title": "price-1450000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 8.275862068965518, "cable": 0, "phone": 0, "rate5": 0, "years": 30, "amount": "1450000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 0, "internet": 0, "rateFTHB": "5", "appraisal": 0, "condoFees": 0, "downPay10": 145000, "downPay15": 217500, "downPay20": 290000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 8.275862068965518, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 58000, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "1450000", "customYears": 0, "downPayFTHB": 120000, "estoppelFee": 0, "insurance10": 40455, "insurance15": 34510, "insurance20": 0, "monthlyPay5": 0, "principal10": 1345455, "principal15": 1267010, "principal20": 1160000, "downPayRange": 120000, "monthlyPay10": 0, "monthlyPay15": 6761.914269574813, "monthlyPay20": 6190.811874181564, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 120000, "percent": "8.275862068965518%"}, "downPayCustom": 0, "insuranceFTHB": 53200, "periodicPay10": 0, "periodicPay15": 6761.914269574813, "periodicPay20": 6190.811874181564, "principalFTHB": 1383200, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0.049486985581730814, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": 0, "insuranceRange": 53200, "monthlyPayFTHB": 7382.009469282707, "principalRange": 1383200, "titleInsurance": 0, "chosenPrincipal": 1383200, "insuranceCustom": 0, "monthlyPayRange": 7382.009469282707, "periodicPayFTHB": 7382.009469282707, "principalCustom": 0, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": 0, "monthlyPayCustom": 0, "numberOfPayments": 12, "periodicPayRange": 7382.009469282707, "chosenPeriodicPay": 7382.009469282707, "periodicPayCustom": 0, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 1383200, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 120000, "percent": "8.275862068965518%"}, "totalMonthlyPayments": 7382.009469282707, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-1450000-d021ad87-efb9-d465-e892-0cca83b94839", "createdAt": "2025-05-06T15:59:34.163Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "ec6fkypqsdxyaqnl5fbw9ulx", "locale": "en"}, {"id": 637, "title": "price-550000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "cable": 0, "phone": 0, "rate5": 0, "years": 30, "amount": "550000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 0, "internet": 0, "rateFTHB": 0, "appraisal": 0, "condoFees": 0, "downPay10": 55000, "downPay15": 82500, "downPay20": 110000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 5.454545454545454, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 22000, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "550000", "customYears": 0, "downPayFTHB": 30000, "estoppelFee": 0, "insurance10": 15345, "insurance15": 13090, "insurance20": 0, "monthlyPay5": 0, "principal10": 510345, "principal15": 480590, "principal20": 440000, "downPayRange": 30000, "monthlyPay10": 0, "monthlyPay15": 2564.864033286998, "monthlyPay20": 2348.2389867585243, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 30000, "percent": "5.454545454545454%"}, "downPayCustom": 104500, "insuranceFTHB": 20800, "periodicPay10": 0, "periodicPay15": 2564.864033286998, "periodicPay20": 2348.2389867585243, "principalFTHB": 540800, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": 0, "insuranceRange": 20800, "monthlyPayFTHB": 0, "principalRange": 540800, "titleInsurance": 0, "chosenPrincipal": 540800, "insuranceCustom": 12474, "monthlyPayRange": 2886.1991909977496, "periodicPayFTHB": 0, "principalCustom": 457974, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "19", "monthlyPayCustom": 2444.16454936761, "numberOfPayments": 12, "periodicPayRange": 2886.1991909977496, "chosenPeriodicPay": 2886.1991909977496, "periodicPayCustom": 2444.16454936761, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 540800, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 30000, "percent": "5.454545454545454%"}, "totalMonthlyPayments": 2886.1991909977496, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-550000-b9d424e0-ede5-f353-359c-983eb13bfcff", "createdAt": "2025-05-06T17:25:21.062Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "a9pp4so449f2fyibno1tl5gh", "locale": "en"}, {"id": 638, "title": "price-1340000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 8.134328358208956, "cable": 0, "phone": 0, "rate5": 0, "years": 25, "amount": "1340000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": 0, "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 0, "internet": 0, "rateFTHB": 0, "appraisal": 0, "condoFees": 0, "downPay10": 134000, "downPay15": 201000, "downPay20": 268000, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 8.134328358208956, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 53600, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "1340000", "customYears": 0, "downPayFTHB": 109000, "estoppelFee": 0, "insurance10": 37386, "insurance15": 31892, "insurance20": 0, "monthlyPay5": 0, "principal10": 1243386, "principal15": 1170892, "principal20": 1072000, "downPayRange": 109000, "monthlyPay10": 0, "monthlyPay15": 6809.966241399611, "monthlyPay20": 6234.8054395968065, "periodicPay5": 0, "chosenDownPay": {"rate": "rateCustom", "amount": 109000, "percent": "19%"}, "downPayCustom": 254600, "insuranceFTHB": 49240, "periodicPay10": 0, "periodicPay15": 6809.966241399611, "periodicPay20": 6234.8054395968065, "principalFTHB": 1280240, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": 0, "insuranceRange": 49240, "monthlyPayFTHB": 0, "principalRange": 1280240, "titleInsurance": 0, "chosenPrincipal": 1115791.2, "insuranceCustom": 30391.2, "monthlyPayRange": 7445.939660437887, "periodicPayFTHB": 0, "principalCustom": 1115791.2, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "19", "monthlyPayCustom": 6489.497241804336, "numberOfPayments": 12, "periodicPayRange": 7445.939660437887, "chosenPeriodicPay": 6489.497241804336, "periodicPayCustom": 6489.497241804336, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 1115791.2, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 109000, "percent": "19%"}, "totalMonthlyPayments": 6489.497241804336, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-1340000-fb5b2c68-f84a-8d36-4467-bea4c167aac5", "createdAt": "2025-05-06T17:32:29.567Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "a6dyi6p1i5r6iui29fad43jf", "locale": "en"}, {"id": 639, "title": "price-690000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 6.3768115942028984, "cable": "80", "phone": "80", "rate5": 0, "years": 30, "amount": "690000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": "76", "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 0, "internet": "80", "rateFTHB": 0, "appraisal": "90", "condoFees": "80", "downPay10": 69000, "downPay15": 103500, "downPay20": 138000, "frequency": "monthly", "houseType": "House", "lawyerFee": "90", "rangeRate": {"rate": 6.3768115942028984, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": "80", "insurance5": 27600, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "690000", "customYears": 0, "downPayFTHB": 44000, "estoppelFee": "90", "insurance10": 19251, "insurance15": 16422, "insurance20": 0, "monthlyPay5": 0, "principal10": 640251, "principal15": 602922, "principal20": 552000, "propertyTax": "98", "downPayRange": 44000, "monthlyPay10": 0, "monthlyPay15": 3217.738514487325, "monthlyPay20": 2945.9725470243307, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 44000, "percent": "6.3768115942028984%"}, "downPayCustom": 193200.00000000003, "insuranceFTHB": 25840, "periodicPay10": 0, "periodicPay15": 3217.738514487325, "periodicPay20": 2945.9725470243307, "principalFTHB": 671840, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": "90", "insuranceRange": 25840, "monthlyPayFTHB": 0, "principalRange": 671840, "titleInsurance": "90", "chosenPrincipal": 671840, "insuranceCustom": 0, "monthlyPayRange": 3585.547456508743, "periodicPayFTHB": 0, "principalCustom": 496800, "showRealtorInfo": true, "totalCashNeeded": 44450, "customPercentage": "28", "monthlyPayCustom": 2651.3752923218976, "numberOfPayments": 12, "periodicPayRange": 3585.547456508743, "chosenPeriodicPay": 3585.547456508743, "periodicPayCustom": 2651.3752923218976, "propertyInsurance": "76", "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 671840, "monthlyDebtPayments": "80", "showMortgagePayment": true, "totalYearlyPayments": 250, "chosenDownPayExpense": {"amount": 44000, "percent": "6.3768115942028984%"}, "totalMonthlyPayments": 4065.547456508743, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-690000-c33737ae-7303-17ab-f2d7-96fde4735e57", "createdAt": "2025-05-06T21:45:54.764Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "cszs12u5dr2dihc4pprxosde", "locale": "en"}, {"id": 640, "title": "price-690000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "rate": 6.3768115942028984, "cable": "80", "phone": "80", "rate5": 0, "years": 25, "amount": "690000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": "76", "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 0, "internet": "80", "rateFTHB": 0, "appraisal": "90", "condoFees": "80", "downPay10": 69000, "downPay15": 103500, "downPay20": 138000, "frequency": "monthly", "houseType": "House", "lawyerFee": "90", "rangeRate": {"rate": 6.3768115942028984, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": "80", "insurance5": 27600, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "690000", "customYears": 0, "downPayFTHB": 44000, "estoppelFee": "90", "insurance10": 19251, "insurance15": 16422, "insurance20": 0, "monthlyPay5": 0, "principal10": 640251, "principal15": 602922, "principal20": 552000, "propertyTax": "98", "downPayRange": 44000, "monthlyPay10": 0, "monthlyPay15": 3506.624407884875, "monthlyPay20": 3210.459517404326, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 44000, "percent": "6.3768115942028984%"}, "downPayCustom": 131100, "insuranceFTHB": 25840, "periodicPay10": 0, "periodicPay15": 3506.624407884875, "periodicPay20": 3210.459517404326, "principalFTHB": 671840, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": "90", "insuranceRange": 25840, "monthlyPayFTHB": 0, "principalRange": 671840, "titleInsurance": "90", "chosenPrincipal": 671840, "insuranceCustom": 15649.2, "monthlyPayRange": 3907.454931472685, "periodicPayFTHB": 0, "principalCustom": 574549.2, "showRealtorInfo": true, "totalCashNeeded": 44450, "customPercentage": "19", "monthlyPayCustom": 3341.606788690292, "numberOfPayments": 12, "periodicPayRange": 3907.454931472685, "chosenPeriodicPay": 3907.454931472685, "periodicPayCustom": 3341.606788690292, "propertyInsurance": "76", "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 671840, "monthlyDebtPayments": "80", "showMortgagePayment": true, "totalYearlyPayments": 250, "chosenDownPayExpense": {"amount": 44000, "percent": "6.3768115942028984%"}, "totalMonthlyPayments": 4387.454931472685, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-690000-4f3a4a28-69cc-2b18-327f-2b1b540bac36", "createdAt": "2025-05-06T21:46:48.943Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "vmmdbr93djxrz84h5hol2bt1", "locale": "en"}, {"id": 641, "title": "MLS: #9998887777", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": "Some Cool Address\nTesting Now\nTBB TP6\nCalgary, AB", "mlsCode": "#9998887777", "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746568037325_2dcvq-floor-home-decoration-fireplace-property-living-room.png"}, "monthlyExpenses": true}, "rate": 6.3768115942028984, "cable": "80", "phone": "80", "rate5": 0, "years": 25, "amount": "690000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": "76", "loading": false, "realtor": {"id": "680bfd84af775a0015c3832d", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg"}, "thumbnail": {"url": null}}}, "company": "Test", "website": "https://test.com", "lastname": "Sousa", "position": "Testing", "firstname": "<PERSON><PERSON><PERSON><PERSON>", "middlename": null}, "downPay5": 0, "internet": "80", "rateFTHB": 0, "appraisal": "90", "condoFees": "80", "downPay10": 69000, "downPay15": 103500, "downPay20": 138000, "frequency": "monthly", "houseType": "House", "lawyerFee": "90", "rangeRate": {"rate": 6.3768115942028984, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": "80", "insurance5": 27600, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "690000", "customYears": 0, "downPayFTHB": 44000, "estoppelFee": "90", "insurance10": 19251, "insurance15": 16422, "insurance20": 0, "monthlyPay5": 0, "principal10": 640251, "principal15": 602922, "principal20": 552000, "propertyTax": "98", "downPayRange": 44000, "monthlyPay10": 0, "monthlyPay15": 3506.624407884875, "monthlyPay20": 3210.459517404326, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 44000, "percent": "6.3768115942028984%"}, "downPayCustom": 131100, "insuranceFTHB": 25840, "periodicPay10": 0, "periodicPay15": 3506.624407884875, "periodicPay20": 3210.459517404326, "principalFTHB": 671840, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": "90", "insuranceRange": 25840, "monthlyPayFTHB": 0, "principalRange": 671840, "titleInsurance": "90", "chosenPrincipal": 671840, "insuranceCustom": 15649.2, "monthlyPayRange": 3907.454931472685, "periodicPayFTHB": 0, "principalCustom": 574549.2, "showRealtorInfo": true, "totalCashNeeded": 44450, "customPercentage": "19", "monthlyPayCustom": 3341.606788690292, "numberOfPayments": 12, "periodicPayRange": 3907.454931472685, "chosenPeriodicPay": 3907.454931472685, "periodicPayCustom": 3341.606788690292, "propertyInsurance": "76", "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 671840, "monthlyDebtPayments": "80", "showMortgagePayment": true, "totalYearlyPayments": 250, "chosenDownPayExpense": {"amount": 44000, "percent": "6.3768115942028984%"}, "totalMonthlyPayments": 4387.454931472685, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "mls-#9998887777-aacf0b74-0372-6da7-81db-860442956eba", "createdAt": "2025-05-06T21:47:54.182Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "r7nvp92i418oskxeku00wfkp", "locale": "en"}, {"id": 642, "title": "MLS: #9998887777", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": "Some Cool Address\nTesting Now\nTBB TP6\nCalgary, AB", "mlsCode": "#9998887777", "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746568037325_2dcvq-floor-home-decoration-fireplace-property-living-room.png"}, "monthlyExpenses": true}, "rate": 6.3768115942028984, "cable": "80", "phone": "80", "rate5": 0, "years": 25, "amount": "790000", "rate10": 0, "rate15": "5", "rate20": "5", "hoaFees": "76", "loading": false, "realtor": {"id": "680bfd84af775a0015c3832d", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745616259316_g30v6-<PERSON>_<PERSON>_<PERSON>.jpg"}, "thumbnail": {"url": null}}}, "company": "Test", "website": "https://test.com", "lastname": "Sousa", "position": "Testing", "firstname": "<PERSON><PERSON><PERSON><PERSON>", "middlename": null}, "downPay5": 0, "internet": "80", "rateFTHB": 0, "appraisal": "90", "condoFees": "80", "downPay10": 79000, "downPay15": 118500, "downPay20": 158000, "frequency": "monthly", "houseType": "House", "lawyerFee": "90", "rangeRate": {"rate": 6.8354430379746836, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": "80", "insurance5": 31600, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "790000", "customYears": 0, "downPayFTHB": 54000, "estoppelFee": "90", "insurance10": 22041, "insurance15": 18802, "insurance20": 0, "monthlyPay5": 0, "principal10": 733041, "principal15": 690302, "principal20": 632000, "propertyTax": "98", "downPayRange": 54000, "monthlyPay10": 0, "monthlyPay15": 4014.830843810219, "monthlyPay20": 3675.7435054339385, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 54000, "percent": "6.8354430379746836%"}, "downPayCustom": 150100, "insuranceFTHB": 29440, "periodicPay10": 0, "periodicPay15": 4014.830843810219, "periodicPay20": 3675.7435054339385, "principalFTHB": 765440, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": "90", "insuranceRange": 29440, "monthlyPayFTHB": 0, "principalRange": 765440, "titleInsurance": "90", "chosenPrincipal": 765440, "insuranceCustom": 17917.2, "monthlyPayRange": 4451.837197467332, "periodicPayFTHB": 0, "principalCustom": 657817.2, "showRealtorInfo": true, "totalCashNeeded": 44450, "customPercentage": "19", "monthlyPayCustom": 3825.897627630914, "numberOfPayments": 12, "periodicPayRange": 4451.837197467332, "chosenPeriodicPay": 4451.837197467332, "periodicPayCustom": 3825.897627630914, "propertyInsurance": "76", "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 765440, "monthlyDebtPayments": "80", "showMortgagePayment": true, "totalYearlyPayments": 250, "chosenDownPayExpense": {"amount": 54000, "percent": "6.8354430379746836%"}, "totalMonthlyPayments": 4931.837197467332, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "mls-#9998887777-679a2be7-e0bf-3215-99c0-575ed710d029", "createdAt": "2025-05-06T21:48:52.986Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "r4dnfcf60s6tav8sc8sg1408", "locale": "en"}, {"id": 643, "title": "MLS: 999888777666", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": "Some Cool Address\nRight Here\nIn this Place", "mlsCode": "999888777666", "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1746568870753_4ffq0-floor-home-decoration-fireplace-property-living-room.png"}, "monthlyExpenses": true}, "cable": "90", "phone": "90", "rate5": 0, "years": 30, "amount": "1324000", "rate10": 0, "rate15": "5", "rate20": "5", "saving": false, "hoaFees": "90", "loading": false, "realtor": {"id": "671815a23c9adf6a573f4a20", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.amazonaws.com/images/squared/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}, "thumbnail": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/thumbnail/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}}}, "company": "Rome", "website": "https://rome.ca", "lastname": "O Grande", "position": "Emperor", "firstname": "<PERSON>", "middlename": null}, "downPay5": 0, "internet": "90", "rateFTHB": 0, "appraisal": "90", "condoFees": "90", "downPay10": 132400, "downPay15": 198600, "downPay20": 264800, "frequency": "monthly", "houseType": "House", "lawyerFee": "90", "rangeRate": {"rate": 8.11178247734139, "type": "middleTier"}, "rateRange": "5", "uploading": {"type": null, "status": false}, "utilities": "90", "insurance5": 52960, "principal5": 0, "rateCustom": "5", "showImages": true, "askingPrice": "1324000", "customYears": 0, "downPayFTHB": 107400, "estoppelFee": "90", "insurance10": 36939.6, "insurance15": 31511.2, "insurance20": 0, "monthlyPay5": 0, "principal10": 1228539.6, "principal15": 1156911.2, "principal20": 1059200, "propertyTax": "789", "downPayRange": 107400, "monthlyPay10": 0, "monthlyPay15": 6174.327236494519, "monthlyPay20": 5652.851669942339, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 107400, "percent": "8.11178247734139%"}, "downPayCustom": 251560, "insuranceFTHB": 48664, "periodicPay10": 0, "periodicPay15": 6174.327236494519, "periodicPay20": 5652.851669942339, "principalFTHB": 1265264, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.049486985581730814, "effectiveRate20": 0.049486985581730814, "effectiveRateFTHB": 0, "effectiveRateRange": 0.049486985581730814, "effectiveRateCustom": 0.049486985581730814}, "homeInspection": "90", "insuranceRange": 48664, "monthlyPayFTHB": 0, "principalRange": 1265264, "titleInsurance": "90", "chosenPrincipal": 1265264, "insuranceCustom": 30028.32, "monthlyPayRange": 6752.596030322812, "periodicPayFTHB": 0, "principalCustom": 1102468.32, "showRealtorInfo": true, "totalCashNeeded": 107850, "customPercentage": "19", "monthlyPayCustom": 5883.770660659483, "numberOfPayments": 12, "periodicPayRange": 6752.596030322812, "chosenPeriodicPay": 6752.596030322812, "periodicPayCustom": 5883.770660659483, "propertyInsurance": "90", "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 1265264, "monthlyDebtPayments": "90", "showMortgagePayment": true, "totalYearlyPayments": 969, "chosenDownPayExpense": {"amount": 107400, "percent": "8.11178247734139%"}, "totalMonthlyPayments": 7292.596030322812, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "mls-999888777666-6e5f59aa-37ca-1ec2-01d3-6645bfa5057f", "createdAt": "2025-05-06T22:02:13.076Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "wnzhclgpr9n3m1huoqwc6xtg", "locale": "en"}, {"id": 654, "title": "MLS: A2221916", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": "Indi Mortgage ", "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": null}, "short": true, "address": "Test Address\nTest\nTEST30", "mlsCode": "A2221916", "realtor": {"id": "671815a23c9adf6a573f4a20", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.amazonaws.com/images/squared/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}, "thumbnail": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/thumbnail/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}}}, "company": "Rome", "website": "https://rome.ca", "lastname": "O Grande", "position": "Emperor", "firstname": "<PERSON>", "middlename": null}, "cashNeeded": false, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".png", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1747928145384_kgucf-floor-home-decoration-fireplace-property-living-room.png"}, "monthlyExpenses": false}, "cable": 0, "phone": 0, "rate5": 0, "years": 25, "amount": "888800", "rate10": 0, "rate15": "4.09", "rate20": "4.29", "saving": false, "hoaFees": 0, "loading": false, "realtor": {"id": "671815a23c9adf6a573f4a20", "email": "<EMAIL>", "phone": "9998887777", "photo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.amazonaws.com/images/squared/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}, "thumbnail": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/thumbnail/<PERSON>_<PERSON>_<PERSON>_5eca89b36e.jpg"}}}, "company": "Rome", "website": "https://rome.ca", "lastname": "O Grande", "position": "Emperor", "firstname": "<PERSON>", "middlename": null}, "downPay5": 0, "internet": 0, "rateFTHB": 0, "appraisal": 0, "condoFees": 0, "downPay10": 88880, "downPay15": 133320, "downPay20": 177760, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": 7.1872187218721875, "type": "middleTier"}, "rateRange": "4.09", "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 35552, "principal5": 0, "rateCustom": "4.09", "showImages": true, "askingPrice": "888800", "customYears": 0, "downPayFTHB": 63880, "estoppelFee": 0, "insurance10": 24797.52, "insurance15": 21153.44, "insurance20": 0, "monthlyPay5": 0, "principal10": 824717.52, "principal15": 776633.44, "principal20": 711040, "downPayRange": 63880, "monthlyPay10": 0, "monthlyPay15": 4123.228441054806, "monthlyPay20": 3852.8319070713947, "periodicPay5": 0, "chosenDownPay": {"rate": "rateRange", "amount": 63880, "percent": "7.1872187218721875%"}, "downPayCustom": 311080, "insuranceFTHB": 32996.8, "periodicPay10": 0, "periodicPay15": 4123.228441054806, "periodicPay20": 3852.8319070713947, "principalFTHB": 857916.8, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.040555791109296635, "effectiveRate20": 0.04252153210176424, "effectiveRateFTHB": 0, "effectiveRateRange": 0.040555791109296635, "effectiveRateCustom": 0.040555791109296635}, "homeInspection": 0, "insuranceRange": 32996.8, "monthlyPayFTHB": 0, "principalRange": 857916.8, "titleInsurance": 0, "chosenPrincipal": 857916.8, "insuranceCustom": 0, "monthlyPayRange": 4554.770330026901, "periodicPayFTHB": 0, "principalCustom": 577720, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "35", "monthlyPayCustom": 3067.176112022916, "numberOfPayments": 12, "periodicPayRange": 4554.770330026901, "chosenPeriodicPay": 4554.770330026901, "periodicPayCustom": 3067.176112022916, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 857916.8, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 63880, "percent": "7.1872187218721875%"}, "totalMonthlyPayments": 4554.770330026901, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "mls-A2221916", "createdAt": "2025-05-22T15:34:25.464Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "x5egnezv6rrrq9q5l32j98p4", "locale": "en"}, {"id": 667, "title": "price-398000", "sheet": {"pdf": {"full": false, "user": {"id": "607f28a5cd063c35088bf735", "team": {"id": "61770dbba35ddfda0293a472", "logo": {"url": "https://indi-strapi-v2.s3.amazonaws.com/public/images/origin/Indi_Mortgage_W_Tagline_f3305b1a5f.png"}}, "email": "<EMAIL>", "phone": "**********", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg", "formats": {"squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1745278540247_0f8wg-<PERSON>_Portrait-1_low.jpg"}, "thumbnail": {"url": null}}}, "website": "https://brunosousa.cc", "lastname": "<PERSON>", "position": "IT Director", "brokerage": null, "cellPhone": "**********", "firstname": "<PERSON>", "workEmail": "<EMAIL>", "workPhone": "**********"}, "short": true, "address": null, "mlsCode": null, "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": null, "url": null}, "monthlyExpenses": true}, "cable": 0, "phone": 0, "rate5": "4", "years": 25, "amount": "398000", "rate10": "4", "rate15": "4", "rate20": "4", "saving": false, "hoaFees": 0, "loading": false, "realtor": {"email": "", "photo": null, "company": "", "website": "", "lastname": "", "position": "", "firstname": "", "workPhone": "", "middlename": null}, "downPay5": 19900, "internet": 0, "rateFTHB": 0, "appraisal": 0, "condoFees": 0, "downPay10": 39800, "downPay15": 59700, "downPay20": 79600, "frequency": "monthly", "houseType": "select", "lawyerFee": 0, "rangeRate": {"rate": null, "type": "normalTier"}, "rateRange": 0, "uploading": {"type": null, "status": false}, "utilities": 0, "insurance5": 15124, "principal5": 393224, "rateCustom": "4", "showImages": true, "askingPrice": "398000", "customYears": 0, "downPayFTHB": 19900, "estoppelFee": 0, "insurance10": 11104.2, "insurance15": 9472.4, "insurance20": 0, "monthlyPay5": 2068.437692056464, "principal10": 369304.2, "principal15": 347772.4, "principal20": 318400, "downPayRange": 0, "monthlyPay10": 1942.6147109910862, "monthlyPay15": 1829.3530924280756, "monthlyPay20": 1674.848333648959, "periodicPay5": 2068.437692056464, "chosenDownPay": {"rate": "rate5", "amount": 19900, "percent": "5%"}, "downPayCustom": 139300, "insuranceFTHB": 15124, "periodicPay10": 1942.6147109910862, "periodicPay15": 1829.3530924280756, "periodicPay20": 1674.848333648959, "principalFTHB": 393224, "effectiveRates": {"effectiveRate5": 0.039670683895646874, "effectiveRate10": 0.039670683895646874, "effectiveRate15": 0.039670683895646874, "effectiveRate20": 0.039670683895646874, "effectiveRateFTHB": 0, "effectiveRateRange": 0, "effectiveRateCustom": 0.039670683895646874}, "homeInspection": 0, "insuranceRange": 0, "monthlyPayFTHB": 0, "principalRange": 0, "titleInsurance": 0, "chosenPrincipal": 393224, "insuranceCustom": 0, "monthlyPayRange": 0, "periodicPayFTHB": 0, "principalCustom": 258700, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": "35", "monthlyPayCustom": 1360.8142710897791, "numberOfPayments": 12, "periodicPayRange": 0, "chosenPeriodicPay": 2068.437692056464, "periodicPayCustom": 1360.8142710897791, "propertyInsurance": 0, "showAdditionalInfo": true, "showBodyCashNeeded": true, "amortizationBalance": 393224, "monthlyDebtPayments": 0, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {"amount": 19900, "percent": "5%"}, "totalMonthlyPayments": 2068.437692056464, "showBodyYearlyExpenses": true, "showBodyMonthlyExpenses": true}, "slug": "price-398000-7cf56122-68f3-e22b-8493-487a010f30ea", "createdAt": "2025-05-29T21:59:01.355Z", "updatedAt": "2025-06-16T15:49:34.170Z", "publishedAt": null, "documentId": "w6nphtsfhbk7chwrgabapuq3", "locale": "en"}], "userProgresses": [{"id": 1179, "completedLessons": ["66fe04fc41b3c753cc9e345a", "66fe0c3541c1020015b7f333", "66fe0e7941c1020015b7f359", "66febaae41c1020015b7f3bf", "66fecb3741c1020015b7f433", "66fececc41c1020015b7f462", "66fecf6141c1020015b7f463", "66fed22d41c1020015b7f464", "66fed2cd41c1020015b7f465", "66fed49941c1020015b7f4a2"], "completedModules": ["66f60106ff709100157ba9fd", "66f60172ff709100157ba9ff", "66f60448ff709100157baa4b", "66fe057041b3c753cc9e345b"], "completedQuizzes": ["66f60106ff709100157ba9fd", "66f60172ff709100157ba9ff", "66f60448ff709100157baa4b", "66fe057041b3c753cc9e345b"], "lastAccessedLesson": 1, "isComplete": null, "createdAt": "2024-10-08T19:26:07.269Z", "updatedAt": "2025-06-16T15:49:34.171Z", "publishedAt": null, "documentId": "cbdkqalx0eoc05mdefki1u4h", "locale": "en"}, {"id": 1495, "completedLessons": ["6744e8bea07e050015531c2c", "67465531d20b500015580fbe", "67465dd3d20b500015580fc8", "674655a6d20b500015580fc0", "67465605d20b500015580fc1"], "completedModules": [], "completedQuizzes": [], "lastAccessedLesson": 19, "isComplete": null, "createdAt": "2024-11-25T21:16:15.345Z", "updatedAt": "2025-06-16T15:49:34.171Z", "publishedAt": null, "documentId": "oseyb4dkgymcae7tzfa5p64l", "locale": "en"}, {"id": 1520, "completedLessons": [], "completedModules": [], "completedQuizzes": [], "lastAccessedLesson": {}, "isComplete": null, "createdAt": "2025-01-21T21:06:03.035Z", "updatedAt": "2025-06-16T15:49:34.171Z", "publishedAt": null, "documentId": "cqq9dsupv4r11ex2epf51338", "locale": "en"}], "trainingScores": [{"id": 1145, "score": "66", "knowledgeCheck": [{"score": 100, "answers": [{"question": "Fintrac is Optional?  ", "correctOption": "False", "selectedOption": "False"}], "moduleId": "66f60106ff709100157ba9fd", "moduleName": "Background"}, {"score": 60, "answers": [{"question": "What is the fundamental principle of KYC? ", "correctOption": "To maintain a clear understanding of the client's identity and gather required information  ", "selectedOption": "To maintain a clear understanding of the client's identity and gather required information  "}, {"question": "What are the three main pieces of information required under KYC for each transaction?", "correctOption": "Identity verification, PEP/HIO declaration, sanctions screening", "selectedOption": "Identity verification, PEP/HIO declaration, sanctions screening"}, {"question": "Which of the following methods of identity verification is allowed by Indi Mortgage?", "correctOption": "Government Issued Photo ID verified through a third-party tool", "selectedOption": "Dual Process Method"}, {"question": "When must agents determine if a client is a Politically Exposed Person (PEP) or Head of an International Organization (HIO)?  ", "correctOption": "When entering into a business relationship", "selectedOption": "When opening an account"}, {"question": "What is the retention period for PEP and HIO-related records?", "correctOption": "5 years from the date they were created or obtained", "selectedOption": "5 years from the date they were created or obtained"}], "moduleId": "66f60172ff709100157ba9ff", "moduleName": "KYC | Know Your Client"}, {"score": 60, "answers": [{"question": "What is a Suspicious Transaction Report (STR)?", "correctOption": "A report submitted to FINTRAC when there are reasonable grounds to suspect a transaction is related to money laundering or terrorist financing", "selectedOption": "A report submitted to FINTRAC when there are reasonable grounds to suspect a transaction is related to money laundering or terrorist financing"}, {"question": "What must you do once you identify property linked to terrorism?", "correctOption": "Immediately submit a Terrorist Property Report (TPR) to FINTRAC, RCMP, and CSIS", "selectedOption": "Immediately submit a Terrorist Property Report (TPR) to FINTRAC, RCMP, and CSIS"}, {"question": "Which threshold of suspicion is required to submit a Suspicious Transaction Report?", "correctOption": "Reasonable grounds to suspect", "selectedOption": "Reasonable grounds to believe "}, {"question": "What is the difference between 'Reasonable Grounds to Suspect' and 'Reasonable Grounds to Believe'?", "correctOption": "Reasonable grounds to suspect is a lower threshold than reasonable grounds to believe", "selectedOption": "Reasonable grounds to suspect is a lower threshold than reasonable grounds to believe"}, {"question": "What type of report must be submitted if there are no transactions but terrorist-linked property is involved?", "correctOption": "Terrorist Property Report", "selectedOption": "Property Fraud Report"}, {"question": "What must you include in a Suspicious Transaction Report when explaining your suspicion?", "correctOption": "A detailed explanation of the facts, context, and indicators that led to your suspicion", "selectedOption": "A list of unrelated transactions"}, {"question": "What is an indicator that may prompt a Suspicious Transaction Report submission?", "correctOption": "Unexplained complexity of accounts or transactions", "selectedOption": "Unexplained complexity of accounts or transactions"}, {"question": "When must you submit a Suspicious Transaction Report in cases of suspected terrorist financing?", "correctOption": "As soon as practicable after establishing reasonable grounds to suspect", "selectedOption": "After a 30-day review period"}, {"question": "What action should be taken if you are unable to verify beneficial ownership?", "correctOption": "Verify the identity of the entity's CEO or equivalent and apply enhanced measures", "selectedOption": "Verify the identity of the entity's CEO or equivalent and apply enhanced measures"}, {"question": "Why is enhanced monitoring required for high-risk clients?", "correctOption": "To flag and review suspicious activities more frequently", "selectedOption": "To flag and review suspicious activities more frequently"}], "moduleId": "66f60448ff709100157baa4b", "moduleName": " Reporting Requirements "}, {"score": 80, "answers": [{"question": "What type of authority does FINTRAC have regarding penalties for noncompliance with regulations?", "correctOption": "The authority to issue administrative monetary penalties", "selectedOption": "The authority to issue administrative monetary penalties"}, {"question": "What is the range of penalties for a minor violation?", "correctOption": "$1 to $1,000 per violation", "selectedOption": "$1 to $100,000 per violation"}, {"question": "For an entity, what is the maximum penalty for a very serious violation?", "correctOption": "$500,000", "selectedOption": "$500,000"}, {"question": "What happens if a business fails to comply with PCMLTFA requirements?", "correctOption": "FINTRAC makes public all administrative monetary penalties imposed", "selectedOption": "FINTRAC makes public all administrative monetary penalties imposed"}, {"question": "Which of the following factors is considered when categorizing the seriousness of a violation?", "correctOption": "The entity's compliance history", "selectedOption": "The entity's compliance history"}], "moduleId": "66fe057041b3c753cc9e345b", "moduleName": "Administrative Penalties"}], "isFinished": true, "createdAt": "2024-10-08T19:27:10.316Z", "updatedAt": "2024-10-08T19:57:18.723Z", "publishedAt": null, "documentId": "zs3nq3e5oz8gko9jczb58xnx", "locale": "en"}], "clientGiftingRequests": [], "assistants": [], "leader": {"id": 202, "username": "indimortgage", "email": "<EMAIL>", "provider": "local", "password": "$2a$10$yrb7IDqq70XYa4UXGSUN..vWPLL9xLhxVq50plT10NS3SR0LSLRIq", "confirmed": true, "blocked": true, "confirmationToken": null, "resetPasswordToken": null, "license": null, "phone": "***********", "bio": "\"Shouldn't I just go to my bank?\"\n\nAbsolutely. In fact, you should check with every bank in the country. You should inquire at every bank available, all the bank subsidiaries, all the trust companies, the insurance companies, the financial corporations and all the private lenders around!\n\nThis is exactly the service we provide here at Indi Mortgage. We are independent mortgage brokers who do the shopping for you. Best of all, our service is absolutely FREE!\n\nAlways Use A Mortgage Broker!\n\nOur focus is simple. We want to provide you with the experience, knowledge, and level of service needed to ensure all your mortgage needs are handled properly. As part of our service, we will shop your interest rate free of charge and personally assist you in every aspect of obtaining your financing.\n\nOur dedicated staff of professionals will help take the guess work out of getting you a mortgage. We are electronically linked to over 50 major banks and financial institutions and can have you approved at the touch of a button. Our on-going relationships with these lenders allow us to get your financing done quickly and at a lower rate than the bank would offer you directly.\n\nOur promise to you is that we will meet or beat anyone's offer! We want to earn your business and make you a lifelong client.", "address": "223 14 Street NW", "firstname": "<PERSON><PERSON>", "lastname": "Mortgage", "position": "", "bioTitle": "There is a better way to get mortgage!", "applicationLink": "https://access-on-demand.mtg-app.com/signup", "brokerage": "Indi Mortgage ", "mapEmbedSrc": "", "facebook": "https://www.facebook.com/indimortgage/", "instagram": "https://www.instagram.com/indimortgagecompany/?hl=en", "twitter": "https://twitter.com/IndiMortgage", "linkedin": "https://www.linkedin.com/company/axiom-mortgage-solutions-and-the-financial-guides", "youtube": null, "whatsapp": null, "hasLogo2": null, "fax": null, "hasCustomBanner": false, "titles": null, "photoOnPrintable": true, "website": "website", "ext": null, "qrCodes": null, "qrCodeOnPrintable": false, "isOnboarding": false, "homePhone": null, "cellPhone": null, "emergencyContact": null, "emergencyPhone": null, "birthdate": null, "sin": null, "startDate": null, "dietRestriction": null, "additionalNotes": null, "middlename": null, "city": "Calgary", "postalCode": "T2N 1Z6", "tollfree": "***********", "workEmail": "<EMAIL>", "websiteOptIn": true, "ownDomain": true, "providedDomain": false, "websiteDomainName": "indimortgage.ca", "websiteDomainRegistrar": null, "tollfreeExt": null, "gender": null, "appointmentScheduleLink": null, "province": "Alberta", "tshirtSize": null, "additionalDomainNames": null, "secondaryWebsite": null, "onboardingStartDate": null, "onboardingEndDate": null, "loginCount": "0", "legalName": null, "preferredName": null, "googleTag": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=G-EPRZMD3RQN\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n\n  gtag('config', 'G-EPRZMD3RQN');\n</script>", "facebookPixelTag": null, "googleWebsiteVerification": null, "googleTagManagerInHead": null, "googleTagManagerInBody": null, "thirdPartyScriptTag": null, "emptyPrintableFooter": false, "googleReviewsLink": null, "facebookHandler": null, "instagramHandler": null, "linkedinHandler": null, "twitterHandler": null, "youtubeHandler": null, "notListed": null, "personalAddress": null, "personalCity": null, "personalProvince": null, "personalPostalCode": null, "personalSuiteUnit": null, "suiteUnit": null, "licensed": true, "chatWidgetCode": "<div \n  data-chat-widget \n  data-widget-id=\"668459c21123b1beec9a9942\" \n  data-location-id=\"f4jYGy8kW29EuVV75lcr\"  > \n </div> \n<script \n  src=\"https://widgets.leadconnectorhq.com/loader.js\"  \n  data-resources-url=\"https://widgets.leadconnectorhq.com/chat-widget/loader.js\" \n data-widget-id=\"668459c21123b1beec9a9942\"  > \n </script>", "reviewWidgetCode": "<script type='text/javascript' src='https://api.leadconnectorhq.com/js/reviews_widget.js'></script><iframe class='lc_reviews_widget' src='https://services.leadconnectorhq.com/reputation/widgets/review_widget/f4jYGy8kW29EuVV75lcr' frameborder='0' scrolling='no' style='min-width: 100%;width: 100%;'></iframe>", "emailNotifications": true, "circularPhotoUrl": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/A_logo_6a23f6ba5e_02f7b7e2c3-A_logo_6a23f6ba5e.png", "isStaffMember": false, "tiktok": null, "tiktokHandler": null, "pinterest": null, "pinterestHandler": null, "threads": null, "threadsHandler": null, "bluesky": null, "blueskyHandler": null, "websiteGitBranch": null, "isComplianceStaff": null, "createdAt": "2023-04-19T21:35:44.469Z", "updatedAt": "2025-04-22T22:55:03.061Z", "documentId": "de7rqmhyaz50ncae7t1xej2i", "locale": null, "publishedAt": "2025-06-27T21:47:05.978Z"}, "taskRequest": null}