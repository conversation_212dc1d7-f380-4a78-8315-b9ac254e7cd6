import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const STRAPI_BASE_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1339';

export async function GET(request: NextRequest) {
  try {
    // Get JWT token from cookies for authentication
    const cookieStore = await cookies();
    const jwt = cookieStore.get('jwt')?.value;

    // Make request to Strapi backend
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add Authorization header if JWT token exists
    if (jwt) {
      headers.Authorization = `Bearer ${jwt}`;
    }

    const response = await fetch(`${STRAPI_BASE_URL}/api/client-gift?populate=*`, {
      method: 'GET',
      headers,
      // Add timeout for safety
      signal: AbortSignal.timeout(10000)
    });

    if (!response.ok) {
      return NextResponse.json(
        { 
          error: `Strapi API error: ${response.status} ${response.statusText}`,
          status: response.status 
        }, 
        { status: response.status }
      );
    }

    const data = await response.json();

    // Return the Strapi response as-is
    return NextResponse.json(data);

  } catch (error) {
    console.error('Client Gift API: Error:', error);
    
    let errorMessage = 'Failed to fetch client gift data';
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Handle timeout errors
      if (error.name === 'AbortError') {
        errorMessage = 'Request timeout - Strapi backend may be unavailable';
        statusCode = 504;
      }
    }

    return NextResponse.json(
      { 
        error: errorMessage,
        message: error instanceof Error ? error.message : 'Unknown error',
        strapiUrl: `${STRAPI_BASE_URL}/api/client-gift?populate=*`
      }, 
      { status: statusCode }
    );
  }
}
