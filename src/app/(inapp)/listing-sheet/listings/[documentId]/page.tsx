import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/shared/ui/button";
import { ListingSheetForm } from "@/features/listing-sheet/components/listing-sheet-form";

export const metadata: Metadata = {
  title: "Edit Listing Sheet | Indi Central",
  description: "Edit your property listing sheet with mortgage calculations",
};

interface EditListingSheetPageProps {
  params: Promise<{
    documentId: string;
  }>;
}

export default async function EditListingSheetPage({ params }: EditListingSheetPageProps) {
  const { documentId } = await params;
  console.log("EditListingSheetPage rendered with documentId:", documentId);
  
  return (
    <div className="container w-full max-w-4xl mx-auto py-6">
      {/* Header */}
      <div className="flex flex-col justify-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild className="w-fit">
          <Link href="/listing-sheet">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Listing Sheets
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Edit Listing Sheet
          </h1>
          <p className="text-muted-foreground">
            Update your listing sheet with mortgage calculations
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Document ID: {documentId}
          </p>
        </div>
      </div>

      {/* Listing Sheet Form */}
      <ListingSheetForm mode="edit" slug={documentId} />
    </div>
  );
}