import { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@/shared/ui/button";
import { ListingSheetForm } from "@/features/listing-sheet/components/listing-sheet-form";

export const metadata: Metadata = {
  title: "New Listing Sheet | Indi Central",
  description: "Create a new property listing sheet with mortgage calculations",
};

export default function NewListingSheetPage() {
  return (
    <div className="container w-full max-w-4xl mx-auto py-6">
      {/* Header */}
      <div className="flex flex-col justify-center gap-4 mb-6">
        <Button variant="outline" size="sm" asChild className="w-fit">
          <Link href="/listing-sheet">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Listing Sheets
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            New Listing Sheet
          </h1>
          <p className="text-muted-foreground">
            Create a professional listing sheet with mortgage calculations
          </p>
        </div>
      </div>

      {/* Listing Sheet Form */}
      <ListingSheetForm mode="create" />
    </div>
  );
}
