"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { AlertTriangle, RefreshCw, Home } from "lucide-react";

interface ClientGiftErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * Error page for client gift
 */
export default function ClientGiftError({ error, reset }: ClientGiftErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Client Gift Error:", error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <Card className="max-w-2xl mx-auto text-center">
          <CardContent className="pt-12 pb-8">
            <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-6" />
            
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h1>
            
            <p className="text-gray-600 mb-6">
              We encountered an error while loading the client gift page. 
              This could be a temporary issue.
            </p>

            {/* Error details for development */}
            {process.env.NODE_ENV === "development" && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-left">
                <h3 className="text-sm font-medium text-red-800 mb-2">Error Details:</h3>
                <p className="text-sm text-red-700 font-mono">
                  {error.message}
                </p>
                {error.digest && (
                  <p className="text-xs text-red-600 mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={reset}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.location.href = "/dashboard"}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                Return to Dashboard
              </Button>
            </div>

            <div className="mt-8 text-sm text-gray-500">
              <p>
                If this problem persists, please contact support or try again later.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
