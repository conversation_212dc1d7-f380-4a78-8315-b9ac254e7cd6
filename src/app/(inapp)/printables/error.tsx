"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";

interface PrintablesErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function PrintablesError({ error, reset }: PrintablesErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Printables error:", error);
  }, [error]);

  return (
    <div className="container mx-auto py-6">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-center text-destructive">
            Something went wrong
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <svg
              className="mx-auto h-16 w-16 text-muted-foreground mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <p className="text-muted-foreground mb-4">
              We encountered an error while loading the printables. Please try again.
            </p>
            {process.env.NODE_ENV === "development" && (
              <details className="text-left">
                <summary className="cursor-pointer text-sm text-muted-foreground mb-2">
                  Error details (development only)
                </summary>
                <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                  {error.message}
                </pre>
              </details>
            )}
          </div>
          <div className="flex gap-2">
            <Button onClick={reset} className="flex-1">
              Try again
            </Button>
            <Button variant="outline" className="flex-1" onClick={() => window.history.back()}>
              Go back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
