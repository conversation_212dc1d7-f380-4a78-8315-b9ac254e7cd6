import { Metadata } from "next";
import { PrintablesCategoryClientWrapper } from "@/features/printables";
import { formatProvinceTitle } from "@/features/printables/lib/api-client";

interface PrintablesCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: PrintablesCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = formatProvinceTitle(category);

  return {
    title: `${categoryTitle} | Printables | Indi Central`,
    description: `Printable documents for ${categoryTitle.toLowerCase()}`,
  };
}

export default async function PrintablesCategoryPage({ params }: PrintablesCategoryPageProps) {
  const { category } = await params;
  return <PrintablesCategoryClientWrapper category={category} />;
}
