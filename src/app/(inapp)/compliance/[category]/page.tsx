import { Metadata } from "next";
import { ComplianceCategoryItems } from "@/features/compliance";
import { ComplianceApiClient } from "@/features/compliance/lib/api-client";

interface ComplianceCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: ComplianceCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = ComplianceApiClient.formatProvinceTitle(category);

  return {
    title: `${categoryTitle} Compliance | Indi Central`,
    description: `Compliance documents for ${categoryTitle}`,
  };
}

export default async function ComplianceCategoryPage({ params }: ComplianceCategoryPageProps) {
  const { category } = await params;
  return <ComplianceCategoryItems category={category} />;
}
