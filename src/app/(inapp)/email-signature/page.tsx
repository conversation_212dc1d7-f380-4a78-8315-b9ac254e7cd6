"use client";

import React, { useMemo, useState, useEffect, useRef } from "react";
import { useAuth } from "@/shared/hooks/use-auth";
import { SignatureProvider, useSignature } from "@/features/email-signature/context/signature-context";
import SignatureForm from "@/features/email-signature/components/signature-form";
import SignatureWeb from "@/features/email-signature/components/signature-web";
import SignatureWebWithPhoto from "@/features/email-signature/components/signature-web-photo";
import SignatureOutlook from "@/features/email-signature/components/signature-outlook";
import SignatureWithPhotoOutlook from "@/features/email-signature/components/signature-with-photo-outlook";
import { Card, CardContent, CardFooter } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Switch } from "@/shared/ui/switch";
import { Label } from "@/shared/ui/label";
import { Input } from "@/shared/ui/input";
import formatUrl from "@/shared/lib/format-url";

function EmailSignatureContent() {
  const { user, isLoading } = useAuth();
  const [context] = useSignature();

  const [photo, setPhoto] = useState(false);
  const [webClient, setWebClient] = useState(true);
  const [outlookClient, setOutlookClient] = useState(false);
  const [applemailClient, setApplemailClient] = useState(false);
  const [logo, setLogo] = useState(false);

  const [customStyle, setCustomStyle] = useState({ styleEnabled: false, styles: null });
  const [extraButton, setExtraButton] = useState({ enabled: false, label: "", link: "" });
  const [secondContact, setSecondContact] = useState({ enabled: false, name: '', position: '', phone: '', email: '' });
  const [signatureGenerated, setSignatureGenerated] = useState(false);
  const [showSignaturePreview, setShowSignaturePreview] = useState(false);
  const [formSubmitRef, setFormSubmitRef] = useState<(() => void) | null>(null);
  const signatureRef = useRef<HTMLDivElement>(null);

  const handlePhoto = () => {
    setPhoto(!photo);
  };

  const handleLogo = () => {
    setLogo(!logo);
  };

  const handleCustomStyle = () => {
    setCustomStyle({ ...customStyle, styleEnabled: !customStyle.styleEnabled, styles: user?.team?.customStyle });
  };

  const updateExtraButton = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    const name = e.target.name;

    if (name === 'label') {
      setExtraButton({ ...extraButton, label: value });
    }
    if (name === 'link') {
      value = formatUrl(value);
      setExtraButton({ ...extraButton, link: value });
    }
  };

  const handleExtraButton = (e: React.MouseEvent) => {
    e.preventDefault();
    setExtraButton({ ...extraButton, enabled: !extraButton.enabled });
  };

  const handleGenerateSignature = () => {
    // Trigger the form submission
    if (formSubmitRef && typeof formSubmitRef === 'function') {
      formSubmitRef();
    }
    // Show the signature preview
    setShowSignaturePreview(true);
    setSignatureGenerated(true);
  };

  const copySignature = async () => {
    if (signatureRef.current) {
      try {
        await navigator.clipboard.writeText(signatureRef.current.innerHTML);
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy signature:', err);
      }
    }
  };

  const copyHtmlCode = async () => {
    if (signatureRef.current) {
      try {
        await navigator.clipboard.writeText(signatureRef.current.outerHTML);
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy HTML code:', err);
      }
    }
  };

  useEffect(() => {
    if (context.secondaryName || context.secondaryPosition || context.secondaryPhone || context.secondaryEmail) {
      setSecondContact({
        enabled: false,
        name: context.secondaryName || '',
        position: context.secondaryPosition || '',
        phone: context.secondaryPhone || '',
        email: context.secondaryEmail || '',
      });
    }
  }, [context.secondaryName, context.secondaryPosition, context.secondaryPhone, context.secondaryEmail]);

  useEffect(() => {
    if (user?.team?.customStyle) {
      setCustomStyle({ styleEnabled: false, styles: user.team.customStyle });
    }
  }, [user?.team?.customStyle]);

  const handleEmailClient = (clientType: string) => {
    if (clientType === 'outlook' && outlookClient) {
      setWebClient(true);
    }
    if (clientType === 'applemail' && applemailClient) {
      setWebClient(true);
    }
    if (clientType === 'outlook') {
      setOutlookClient(!outlookClient);
      if (!outlookClient) {
        setApplemailClient(false);
      }
      if (!outlookClient && !applemailClient) {
        setWebClient(false);
      }
    }
    if (clientType === 'applemail') {
      setApplemailClient(!applemailClient);
      if (!applemailClient) {
        setOutlookClient(false);
      }
      if (!outlookClient && !applemailClient) {
        setWebClient(false);
      }
    }
  };

  const showSignature = () => {
    if (webClient && photo) {
      return <SignatureWebWithPhoto user={user} logo={logo} extraButton={extraButton} secondContact={secondContact} customStyle={customStyle && customStyle.styleEnabled ? customStyle : null} onGenerate={handleGenerateSignature} context={context} />;
    }
    if (webClient && !photo) {
      return <SignatureWeb user={user} logo={logo} extraButton={extraButton} secondContact={secondContact} customStyle={customStyle && customStyle.styleEnabled ? customStyle : null} onGenerate={handleGenerateSignature} context={context} />;
    }
    if (outlookClient && photo) {
      return <SignatureWithPhotoOutlook user={user} logo={logo} extraButton={extraButton} secondContact={secondContact} customStyle={customStyle && customStyle.styleEnabled ? customStyle : null} onGenerate={handleGenerateSignature} context={context} />;
    }
    if (outlookClient && !photo) {
      return <SignatureOutlook user={user} logo={logo} extraButton={extraButton} secondContact={secondContact} customStyle={customStyle && customStyle.styleEnabled ? customStyle : null} onGenerate={handleGenerateSignature} context={context} />;
    }
  };

  if (isLoading) return <div className="p-6">Loading...</div>;
  if (!user) return <div className="p-6">You must be logged in.</div>;

  return (
    <div className="p-4 md:p-6">
        <h1 className="text-2xl font-semibold mb-2">Indi Signature Generator</h1>
        <p>
          The Indi Email Signature Generator allows you to create your branded email signature in a HTML format. It
          means that you can have your website address and social media links clickables.
        </p>
        <p className="mt-2 font-semibold">
          Warning: Any information changed on your Email Signature will be replaced on your Profile and update it as well.
        </p>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardContent className="pt-6">
              <SignatureForm
                user={user}
                onSubmitRefReady={setFormSubmitRef}
              />
            </CardContent>
          </Card>
          <div>
            <Card>
              <CardContent className="pt-6">
                {showSignaturePreview ? (
                  <div ref={signatureRef}>
                    {showSignature()}
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <p className="text-lg">Click on Generate Signature to create your email signature</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button 
                  onClick={handleGenerateSignature}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  Generate Signature
                </Button>
                {showSignaturePreview && (
                  <>
                    <Button 
                      onClick={copySignature}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Copy Signature
                    </Button>
                    <Button 
                      onClick={copyHtmlCode}
                      className="bg-gray-600 hover:bg-gray-700 text-white"
                    >
                      Copy HTML Code
                    </Button>
                  </>
                )}
              </CardFooter>
            </Card>
            <Card className="mt-6">
              <CardContent className="pt-6 space-y-4">
                <h4 className="text-lg font-medium">Signature Options</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between gap-4">
                    <Label htmlFor="photo">Show Photo</Label>
                    <Switch id="photo" checked={photo} onCheckedChange={handlePhoto} />
                  </div>
                  {user.team?.cobranded ? (
                    <div className="flex items-center justify-between gap-4">
                      <Label htmlFor="logo">Show Logo</Label>
                      <Switch id="logo" checked={logo} onCheckedChange={handleLogo} />
                    </div>
                  ) : null}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between gap-4">
                    <Label htmlFor="outlook">Using Outlook (Desktop App Only)</Label>
                    <Switch id="outlook" checked={outlookClient} onCheckedChange={() => handleEmailClient('outlook')} />
                  </div>
                  {user.team?.customStyle?.enabled ? (
                    <div className="flex items-center justify-between gap-4">
                      <Label htmlFor="brandStyle">Apply Brand Style</Label>
                      <Switch id="brandStyle" checked={customStyle.styleEnabled} onCheckedChange={handleCustomStyle} />
                    </div>
                  ) : null}
                </div>

                <div>
                  <h4 className="text-lg font-medium mt-2">Extra Button</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="extraLabel">Label</Label>
                      <Input id="extraLabel" name="label" value={extraButton.label || ''} onChange={updateExtraButton} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="extraLink">Link</Label>
                      <Input id="extraLink" name="link" value={extraButton.link || ''} onChange={updateExtraButton} />
                    </div>
                  </div>
                  <div className="flex justify-end mt-2">
                    <button className="bg-cyan-700 text-white rounded px-3 py-2" onClick={handleExtraButton}>
                      Add Button
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
  );
}

export default function EmailSignaturePage() {
  const initialCtx = useMemo(() => ({
    username: "",
    email: "",
    titles: "",
    address: "",
    license: "",
    phone: "",
    photo: {},
    applicationLink: "",
    firstname: "",
    lastname: "",
    position: "",
    facebook: "",
    instagram: "",
    linkedin: "",
    twitter: "",
    whatsapp: "",
    fax: "",
    team: { logo: { url: "" } },
    website: "",
  }), []);

  return (
    <SignatureProvider initial={initialCtx}>
      <EmailSignatureContent />
    </SignatureProvider>
  );
}

