import { Metadata } from "next";
import { ResourcesCategoryItems } from "@/features/resources";
import { ResourcesApiClient } from "@/features/resources/lib/api-client";

interface ResourcesCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: ResourcesCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = ResourcesApiClient.formatProvinceTitle(category);

  return {
    title: `${categoryTitle} Resources | Indi Central`,
    description: `Resource documents for ${categoryTitle}`,
  };
}

export default async function ResourcesCategoryPage({ params }: ResourcesCategoryPageProps) {
  const { category } = await params;
  return <ResourcesCategoryItems category={category} />;
}
