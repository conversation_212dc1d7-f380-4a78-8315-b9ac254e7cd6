import { redirect } from "next/navigation";
import { checkAuthAction, getDirectoryDataAction } from "@/shared/actions/auth";
import { CompanyDirectoryPage } from "@/features/company-directory";

interface CompanyDirectoryPageProps {
  searchParams: Promise<{
    page?: string;
    province?: string;
    search?: string;
    isStaffMember?: string;
  }>;
}

export default async function CompanyDirectoryRoute({ searchParams }: CompanyDirectoryPageProps) {
  // Check authentication
  const { isAuthenticated } = await checkAuthAction();
  
  if (!isAuthenticated) {
    redirect("/login");
  }

  // Parse search parameters
  const params = await searchParams;
  const page = parseInt(params.page || '1', 10);
  const province = params.province;
  const search = params.search;
  const isStaffMember = params.isStaffMember === 'true';

  // Fetch directory data server-side
  const { users, count, error } = await getDirectoryDataAction({
    start: (page - 1) * 12,
    province,
    search,
    isStaffMember
  });

  // If there's an error, we still render the page but with empty data
  // The error will be handled by the component
  const safeUsers = error ? [] : users;
  const safeCount = error ? 0 : count;

  return (
    <CompanyDirectoryPage
      initialUsers={safeUsers}
      initialCount={safeCount}
      initialPage={page}
      initialFilters={{
        province,
        search,
        isStaffMember,
        page,
        limit: 12
      }}
    />
  );
}

// Metadata for the page
export const metadata = {
  title: "Company Directory - IndiCentral",
  description: "Find and connect with team members across the organization.",
};
