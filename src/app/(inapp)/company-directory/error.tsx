"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { AlertCircle, RefreshCw, Home } from "lucide-react";

interface CompanyDirectoryErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function CompanyDirectoryError({
  error,
  reset,
}: CompanyDirectoryErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Company Directory Error:", error);
  }, [error]);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Company Directory</h1>
        <p className="text-muted-foreground">
          Find and connect with team members across the organization.
        </p>
      </div>

      {/* Error Card */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertCircle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">Something went wrong</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error.message || "An unexpected error occurred while loading the company directory."}
            </AlertDescription>
          </Alert>

          <div className="text-center text-sm text-muted-foreground">
            <p>This could be due to:</p>
            <ul className="mt-2 space-y-1 text-left">
              <li>• Network connectivity issues</li>
              <li>• Server maintenance</li>
              <li>• Authentication problems</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button onClick={reset} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Go Home
            </Button>
          </div>

          {process.env.NODE_ENV === "development" && error.digest && (
            <div className="mt-4 p-3 bg-muted rounded-md">
              <p className="text-xs text-muted-foreground">
                Error ID: {error.digest}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
