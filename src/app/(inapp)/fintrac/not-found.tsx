import { <PERSON><PERSON> } from "@/shared/ui/button";
import { FileX, Home } from "lucide-react";
import Link from "next/link";

export default function FintracNotFound() {
  return (
    <div className="container mx-auto py-12">
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <FileX className="h-16 w-16 text-muted-foreground" />
        </div>
        
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">Fintrac Document Not Found</h1>
          <p className="text-muted-foreground max-w-md mx-auto">
            The fintrac document you're looking for doesn't exist or has been moved.
          </p>
        </div>

        <div className="flex justify-center gap-4">
          <Button asChild>
            <Link href="/fintrac">
              Back to Fintrac
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/dashboard" className="flex items-center gap-2">
              <Home className="h-4 w-4" />
              Dashboard
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
