import { Metadata } from "next";
import { DocumentViewer } from "@/shared/components";

interface FintracDocumentPageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ 
    title?: string;
    file?: string;
    ext?: string;
    category?: string;
  }>;
}

export async function generateMetadata({ params, searchParams }: FintracDocumentPageProps): Promise<Metadata> {
  const { slug } = await params;
  const searchParamsData = await searchParams;
  const title = searchParamsData.title || slug.replace(/-/g, ' ');
  
  return {
    title: `${title} | Fintrac | Indi Central`,
    description: `View and download the fintrac document: ${title}`,
  };
}

export default async function FintracDocumentPage({ params, searchParams }: FintracDocumentPageProps) {
  const { slug } = await params;
  const { title, file, ext, category } = await searchParams;
  
  if (!file || !title) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-destructive mb-4">Invalid Document</h1>
          <p className="text-muted-foreground">
            Missing document information. Please navigate to this page from the fintrac list.
          </p>
        </div>
      </div>
    );
  }

  const backUrl = category ? `/fintrac/${category}` : "/fintrac";

  return (
    <div className="container mx-auto py-6">
      <DocumentViewer
        fileUrl={file}
        title={title}
        fileExt={ext}
        backUrl={backUrl}
        backLabel="Back to Fintrac"
      />
    </div>
  );
}
