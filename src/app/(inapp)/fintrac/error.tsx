"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/shared/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";

export default function FintracError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Fintrac page error:", error);
  }, [error]);

  return (
    <div className="container mx-auto py-12">
      <div className="text-center space-y-6">
        <div className="flex justify-center">
          <AlertCircle className="h-16 w-16 text-destructive" />
        </div>
        
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">Something went wrong!</h1>
          <p className="text-muted-foreground max-w-md mx-auto">
            We encountered an error while loading the fintrac documents. Please try again.
          </p>
        </div>

        <div className="flex justify-center gap-4">
          <Button onClick={reset} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Try again
          </Button>
          <Button variant="outline" onClick={() => window.location.href = "/dashboard"}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}
