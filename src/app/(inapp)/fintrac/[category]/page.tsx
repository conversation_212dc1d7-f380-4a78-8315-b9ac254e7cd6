import { Metadata } from "next";
import { FintracCategoryItems } from "@/features/fintrac";
import { FintracApiClient } from "@/features/fintrac/lib/api-client";

interface FintracCategoryPageProps {
  params: Promise<{ category: string }>;
}

export async function generateMetadata({ params }: FintracCategoryPageProps): Promise<Metadata> {
  const { category } = await params;
  const categoryTitle = FintracApiClient.formatProvinceTitle(category);
  
  return {
    title: `${categoryTitle} Fintrac | Indi Central`,
    description: `Fintrac documents for ${categoryTitle}`,
  };
}

export default async function FintracCategoryPage({ params }: FintracCategoryPageProps) {
  const { category } = await params;
  return <FintracCategoryItems category={category} />;
}
