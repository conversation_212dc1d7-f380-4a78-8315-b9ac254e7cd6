"use client";

import { useState, useEffect, useCallback, useMemo } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";

export interface UseCategoryDataOptions<T> {
  data: T[];
  categoryExtractor: (item: T) => string;
  itemTransformer: (item: T) => CategoryItem;
  searchFields?: (keyof T)[];
  filterFields?: (keyof T)[];
}

export interface UseCategoryDataResult<T> {
  categories: CategoryItem[];
  filteredItems: T[];
  searchQuery: string;
  selectedCategory: string | null;
  isLoading: boolean;
  error: string | null;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string | null) => void;
  clearFilters: () => void;
  retry: () => void;
}

export function useCategoryData<T>({
  data,
  categoryExtractor,
  itemTransformer,
  searchFields = [],
  filterFields = [],
}: UseCategoryDataOptions<T>): UseCategoryDataResult<T> {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Extract unique categories from data
  const categories = useMemo(() => {
    const categoryMap = new Map<string, T[]>();
    
    data.forEach((item) => {
      const category = categoryExtractor(item);
      if (!categoryMap.has(category)) {
        categoryMap.set(category, []);
      }
      categoryMap.get(category)!.push(item);
    });

    return Array.from(categoryMap.entries()).map(([category, items]) => {
      const firstItem = items[0];
      return itemTransformer(firstItem);
    });
  }, [data, categoryExtractor, itemTransformer]);

  // Filter items based on search and category
  const filteredItems = useMemo(() => {
    let filtered = data;

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter((item) => 
        categoryExtractor(item) === selectedCategory
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((item) => {
        // Search in specified fields
        const searchableFields = searchFields.length > 0 ? searchFields : Object.keys(item as Record<string, unknown>);
        
        return searchableFields.some((field) => {
          const value = (item as Record<string, unknown>)[field as string];
          if (typeof value === "string") {
            return value.toLowerCase().includes(query);
          }
          if (typeof value === "number") {
            return value.toString().includes(query);
          }
          return false;
        });
      });
    }

    return filtered;
  }, [data, selectedCategory, searchQuery, categoryExtractor, searchFields]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchQuery("");
    setSelectedCategory(null);
  }, []);

  // Retry function (can be overridden by implementing features)
  const retry = useCallback(() => {
    setError(null);
    // This would typically trigger a refetch in the implementing feature
  }, []);

  // Reset filters when data changes
  useEffect(() => {
    if (data.length > 0) {
      setError(null);
    }
  }, [data]);

  return {
    categories,
    filteredItems,
    searchQuery,
    selectedCategory,
    isLoading,
    error,
    setSearchQuery,
    setSelectedCategory,
    clearFilters,
    retry,
  };
}

// Specialized hook for province-based categories (like printables, compliance, resources)
export function useProvinceBasedCategories<T>({
  data,
  itemTransformer,
  searchFields = [],
  provinceExtractor,
}: Omit<UseCategoryDataOptions<T>, "categoryExtractor"> & {
  provinceExtractor: (item: T) => string;
}) {
  const categoryExtractor = useCallback((item: T) => {
    return provinceExtractor(item);
  }, [provinceExtractor]);

  return useCategoryData({
    data,
    categoryExtractor,
    itemTransformer,
    searchFields,
  });
}

// Specialized hook for media-based categories (like brand-materials, custom-shop)
export function useMediaBasedCategories<T>({
  data,
  itemTransformer,
  searchFields = [],
  mediaCategoryExtractor,
}: Omit<UseCategoryDataOptions<T>, "categoryExtractor"> & {
  mediaCategoryExtractor: (item: T) => string;
}) {
  const categoryExtractor = useCallback((item: T) => {
    return mediaCategoryExtractor(item);
  }, [mediaCategoryExtractor]);

  return useCategoryData({
    data,
    categoryExtractor,
    itemTransformer,
    searchFields,
  });
}
