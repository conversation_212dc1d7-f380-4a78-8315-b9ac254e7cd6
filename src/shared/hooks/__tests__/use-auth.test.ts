import { renderHook, act, waitFor } from "@testing-library/react";
import { useAuth, useUserData } from "../use-auth";

// Mock environment
jest.mock("@/shared/lib/env", () => ({
  env: {
    API_URL: "http://localhost:1339",
  },
}));

// Mock auth utilities
jest.mock("@/shared/lib/auth", () => ({
  setCookie: jest.fn(),
  getCookie: jest.fn(),
  getAuthHeaders: jest.fn(() => ({
    Authorization: "Bearer mock-jwt-token",
    "Content-Type": "application/json",
  })),
}));

// Mock next/navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    pathname: "/",
  }),
}));

const { setCookie, getCookie } = require("@/shared/lib/auth");

describe("useAuth", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should login successfully with valid credentials", async () => {
    const mockResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        jwt: "mock-jwt-token",
        cookie_token: "mock-cookie-token",
        user: {
          id: "1",
          email: "<EMAIL>",
          firstname: "John",
          lastname: "Doe",
        },
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useAuth());

    let loginResult: boolean;
    await act(async () => {
      loginResult = await result.current.login("<EMAIL>", "password");
    });

    expect(loginResult!).toBe(true);
    expect(global.fetch).toHaveBeenCalledWith(
      "http://localhost:1339/api/auth/local",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          identifier: "<EMAIL>",
          password: "password",
        }),
      }
    );
  });

  it("should handle login failure with proper error message", async () => {
    const mockResponse = {
      ok: false,
      json: jest.fn().mockResolvedValue({
        message: [
          {
            messages: [
              {
                message: "Invalid credentials",
              },
            ],
          },
        ],
      }),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useAuth());

    let loginResult: boolean;
    await act(async () => {
      loginResult = await result.current.login("<EMAIL>", "wrongpass");
    });

    expect(loginResult!).toBe(false);
    expect(result.current.error).toBe("Invalid credentials");
  });

  it("should handle network errors during login", async () => {
    (global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() => useAuth());

    let loginResult: boolean;
    await act(async () => {
      loginResult = await result.current.login("<EMAIL>", "password");
    });

    expect(loginResult!).toBe(false);
    expect(result.current.error).toBe("Network error");
  });
});

describe("useUserData", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    global.fetch = jest.fn();
    getCookie.mockReturnValue("123");
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it("should fetch user data successfully", async () => {
    const mockUserResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        id: "123",
        email: "<EMAIL>",
        firstname: "John",
        lastname: "Doe",
      }),
    };

    const mockNotificationsResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([]),
    };

    const mockOnePagesResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([]),
    };

    (global.fetch as jest.Mock)
      .mockResolvedValueOnce(mockUserResponse)
      .mockResolvedValueOnce(mockNotificationsResponse)
      .mockResolvedValueOnce(mockOnePagesResponse);

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    expect(fetchResult.userInfo).toEqual({
      id: "123",
      name: "John Doe",
      email: "<EMAIL>",
      role: "user",
      avatar: undefined,
      company: undefined,
    });

    expect(global.fetch).toHaveBeenCalledWith(
      "http://localhost:1339/api/users-permissions/users-light/123",
      {
        headers: {
          Authorization: "Bearer mock-jwt-token",
          "Content-Type": "application/json",
        },
      }
    );
  });

  it("should handle API errors gracefully", async () => {
    const mockUserResponse = {
      ok: false,
      json: jest.fn(),
    };

    (global.fetch as jest.Mock).mockResolvedValue(mockUserResponse);

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    expect(fetchResult.userInfo).toBe(null);
    expect(result.current.error).toBe("Failed to fetch user data");
  });

  it("should handle missing user ID", async () => {
    getCookie.mockReturnValue(null);

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    expect(fetchResult.userInfo).toBe(null);
    expect(result.current.error).toBe("No user ID found");
    expect(global.fetch).not.toHaveBeenCalled();
  });

  it("should normalize complex API response data", async () => {
    const mockComplexUserResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        _id: { $oid: "507f1f77bcf86cd799439011" },
        id: "123",
        email: "<EMAIL>",
        firstname: "John",
        lastname: "Doe",
        role: "615729ce1026f27e0f34ea5f",
        photo: {
          url: "/uploads/avatar.jpg",
        },
        team: {
          name: "Development Team",
        },
      }),
    };

    const mockNotificationsResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([]),
    };

    const mockOnePagesResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([
        {
          id: "1",
          Title: "Test Page",
          slug: "test-page",
        },
      ]),
    };

    (global.fetch as jest.Mock)
      .mockResolvedValueOnce(mockComplexUserResponse)
      .mockResolvedValueOnce(mockNotificationsResponse)
      .mockResolvedValueOnce(mockOnePagesResponse);

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    expect(fetchResult.userInfo).toEqual({
      id: "123",
      name: "John Doe",
      email: "<EMAIL>",
      role: "user",
      avatar: "/uploads/avatar.jpg",
      company: "Development Team",
    });

    expect(fetchResult.onePages).toEqual([
      {
        id: "1",
        _id: undefined,
        Title: "Test Page",
        slug: "test-page",
      },
    ]);
  });

  it("should handle malformed onePages data", async () => {
    const mockUserResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        id: "123",
        email: "<EMAIL>",
      }),
    };

    const mockNotificationsResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([]),
    };

    const mockOnePagesResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue([
        { id: "1", Title: "Valid Page", slug: "valid-page" },
        { id: "2" }, // Missing required fields
        null, // Null item
        { id: "3", Title: "Another Valid", slug: "another-valid" },
      ]),
    };

    (global.fetch as jest.Mock)
      .mockResolvedValueOnce(mockUserResponse)
      .mockResolvedValueOnce(mockNotificationsResponse)
      .mockResolvedValueOnce(mockOnePagesResponse);

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    // Should filter out invalid items
    expect(fetchResult.onePages).toHaveLength(2);
    expect(fetchResult.onePages[0].Title).toBe("Valid Page");
    expect(fetchResult.onePages[1].Title).toBe("Another Valid");
  });

  it("should handle network errors during data fetch", async () => {
    (global.fetch as jest.Mock).mockRejectedValue(new Error("Network error"));

    const { result } = renderHook(() => useUserData());

    let fetchResult: any;
    await act(async () => {
      fetchResult = await result.current.fetchUserData();
    });

    expect(fetchResult.userInfo).toBe(null);
    expect(result.current.error).toBe("Network error");
  });
});
