"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Skeleton } from "@/shared/ui/skeleton";
import { cn } from "@/lib/utils";

export interface CategoryItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: {
    url: string;
    alternativeText?: string;
    formats?: {
      thumbnail?: { url: string };
    };
  };
  icon?: React.ReactNode;
  metadata?: Record<string, any>;
}

export interface CategoryLandingPageProps {
  title: string;
  subtitle?: string;
  description?: string;
  categories: CategoryItem[];
  isLoading?: boolean;
  error?: string | null;
  onCategorySelect: (category: CategoryItem) => void;
  buttonLabel?: string;
  layout?: "grid" | "list";
  className?: string;
  emptyStateMessage?: string;
  retryAction?: () => void;
}

const CategoryLandingPage: React.FC<CategoryLandingPageProps> = ({
  title,
  subtitle,
  description,
  categories,
  isLoading = false,
  error = null,
  onCategorySelect,
  buttonLabel = "View Items",
  layout = "grid",
  className,
  emptyStateMessage = "No categories found",
  retryAction,
}) => {
  const handleCategoryClick = React.useCallback((category: CategoryItem) => {
    onCategorySelect(category);
  }, [onCategorySelect]);

  const handleKeyDown = React.useCallback((event: React.KeyboardEvent, category: CategoryItem) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleCategoryClick(category);
    }
  }, [handleCategoryClick]);

  const handleRetry = React.useCallback(() => {
    if (retryAction) {
      retryAction();
    }
  }, [retryAction]);



  // Loading skeleton
  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          {subtitle && <Skeleton className="h-6 w-1/2" />}
          {description && <Skeleton className="h-4 w-2/3" />}
        </div>

        <div className={cn(
          layout === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        )}>
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="aspect-video w-full rounded-lg" />
              <div className="space-y-2 p-4">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
              <Skeleton className="h-9 w-full mx-4 mb-4" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-12", className)}>
        <div className="text-center">
          <svg
            className="mx-auto h-16 w-16 text-muted-foreground mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 className="text-lg font-medium text-foreground mb-2">
            Something went wrong
          </h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          {retryAction && (
            <Button onClick={handleRetry} variant="outline">
              <svg
                className="h-4 w-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Try again
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Empty state
  if (categories.length === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center py-12">
          <svg
            className="mx-auto h-16 w-16 text-muted-foreground mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <h3 className="text-lg font-medium text-foreground mb-2">
            {emptyStateMessage}
          </h3>
          {retryAction && (
            <Button onClick={handleRetry} variant="outline">
              Refresh
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header Section */}
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {subtitle && (
            <p className="text-xl text-muted-foreground mt-2">{subtitle}</p>
          )}
          {description && (
            <p className="text-muted-foreground mt-2">{description}</p>
          )}
        </div>
      </div>

      {/* Categories Grid/List */}
      <div className={cn(
        layout === "grid" 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          : "space-y-4"
      )}>
        {categories.map((category) => (
          <Card
            key={category.id}
            className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] p-0 overflow-hidden flex flex-col justify-between"
            onClick={() => handleCategoryClick(category)}
            onKeyDown={(e) => handleKeyDown(e, category)}
            tabIndex={0}
            role="button"
            aria-label={`View ${category.title} items`}
          >
            <CardContent className="p-0">
              {/* Thumbnail or Icon */}
              <div className="relative aspect-video overflow-hidden rounded-t-lg bg-muted">
                {category.thumbnail ? (
                  <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
                    {category.icon || (
                      <svg
                        className="h-16 w-16 text-primary/60"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    )}
                  </div>
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center">
                    {category.icon || (
                      <svg
                        className="h-16 w-16 text-primary/60"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1.5}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    )}
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-lg leading-tight mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                  {category.title}
                </h3>
                {category.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {category.description}
                  </p>
                )}
              </div>
            </CardContent>

            {/* Footer with Button */}
            <CardFooter className="p-4 pt-0">
              <Button
                className="w-full"
                variant="default"
                onClick={() => handleCategoryClick(category)}
              >
                {buttonLabel}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CategoryLandingPage;
