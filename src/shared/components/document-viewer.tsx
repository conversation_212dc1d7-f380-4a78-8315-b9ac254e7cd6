"use client";

import React, { useState, useEffect, useRef } from "react";
import dynamic from "next/dynamic";
import { saveAs } from "file-saver";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { Download, FileText, ExternalLink, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { shouldProxyUrl, getProxyUrl } from "@/shared/lib/s3-proxy";
import { getFileExtension, shouldUseDocViewer as shouldUseDocViewerUtil, canDisplayInline as canDisplayInlineUtil, getFileTypeBadge, validateFileExtension } from "@/shared/lib/file-utils";

// Import DocViewer with no SSR to prevent window not defined errors
const DocViewer = dynamic(
  () =>
    import('react-doc-viewer').then((mod) => {
      const { DocViewerRenderers } = mod;
      return {
        default: (props: any) => <mod.default {...props} pluginRenderers={DocViewerRenderers} />
      };
    }),
  { ssr: false }
);

export interface DocumentViewerProps {
  fileUrl: string;
  title: string;
  fileExt?: string;
  fileName?: string;
  fileMime?: string;
  className?: string;
  backUrl?: string;
  backLabel?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  fileUrl,
  title,
  fileExt,
  fileName,
  fileMime,
  className,
  backUrl,
  backLabel = "Back to Documents",
}) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [iframeHeight, setIframeHeight] = useState<number>(800);
  const [fileValidation, setFileValidation] = useState<{ isValid: boolean; detectedExt: string; warnings: string[] } | null>(null);
  const viewerRef = useRef<HTMLIFrameElement>(null);

  // Create file info object for utility functions
  const fileInfo = {
    url: fileUrl,
    ext: fileExt,
    name: fileName,
    mime: fileMime,
  };

  // Get the actual file extension using our utility
  const actualFileExt = getFileExtension(fileInfo);
  const fileExtWithDot = actualFileExt ? `.${actualFileExt}` : '';

  // Calculate iframe height based on window size
  const calculateIframeHeight = () => {
    if (typeof window !== "undefined") {
      return Math.max(600, (window.innerHeight - 200) * 1.41);
    }
    return 800;
  };

  useEffect(() => {
    setIsClient(true);
    setIsLoading(false);

    // Validate file extension on component mount
    const validation = validateFileExtension(fileInfo);
    setFileValidation(validation);

    // Log warnings for debugging
    if (validation.warnings.length > 0) {
      console.warn('File extension validation warnings:', validation.warnings);
    }

    const handleResize = () => {
      setIframeHeight(calculateIframeHeight());
    };

    setIframeHeight(calculateIframeHeight());
    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => {
        window.removeEventListener("resize", handleResize);
      };
    }
  }, [fileUrl, fileExt, fileName, fileMime]);

  const handleDownload = async () => {
    if (fileUrl && title) {
      try {
        // Use s3-proxy helper for reliable cross-origin downloads
        const filename = `${title.replace(/[^a-z0-9\s]/gi, '-').toLowerCase()}${fileExtWithDot}`;

        // Check if URL needs proxying and use appropriate fetch URL
        const needsProxy = shouldProxyUrl(fileUrl);
        const fetchUrl = needsProxy ? getProxyUrl(fileUrl, 'stream') : fileUrl;

        // Fetch the file and save it using file-saver
        const response = await fetch(fetchUrl);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        saveAs(blob, filename);
      } catch (error) {
        console.error("Download failed:", error);
        // Fallback to opening in new tab if download fails
        window.open(fileUrl, "_blank");
      }
    }
  };

  const handleExternalView = () => {
    if (fileUrl) {
      window.open(fileUrl, "_blank");
    }
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // Use the utility functions with the actual file extension
  const fileTypeBadge = getFileTypeBadge(fileInfo);
  const canDisplayInline = canDisplayInlineUtil(fileInfo);
  const shouldUseDocViewer = shouldUseDocViewerUtil(fileInfo);

  const showDocument = () => {
    if (!isClient || !fileUrl) return null;
    if (isLoading) {
      return (
        <div className="space-y-4">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      );
    }

    if (shouldUseDocViewer) {
      return (
        <div style={{ width: "100%", maxWidth: "1000px", height: `${iframeHeight}px` }}>
          <DocViewer
            documents={[{ uri: fileUrl, fileName: `${title}${fileExtWithDot}` }]}
            style={{ height: "100%", width: "100%" }}
            config={{
              header: {
                disableHeader: false,
                disableFileName: false,
                retainURLParams: true,
              },
            }}
          />
        </div>
      );
    } else {
      return (
        <iframe
          ref={viewerRef}
          src={fileUrl}
          width="100%"
          height={`${iframeHeight}px`}
          title={`Document Viewer - ${title}`}
          className="border-0 rounded-lg"
          onLoad={handleIframeLoad}
          style={{ maxWidth: "1000px" }}
        />
      );
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Back Button */}
      {backUrl && (
        <div className="flex items-center">
          <Button
            variant="outline"
            onClick={() => router.push(backUrl)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {backLabel}
          </Button>
        </div>
      )}

      {/* Document Title */}
      <div className="space-y-4">
        <h1 className="flex text-3xl font-bold tracking-tight">
          <FileText className="h-8 w-8 mr-2" />
          {title}
        </h1>
        
        {/* File Validation Warnings */}
        {fileValidation && fileValidation.warnings.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">
                  File Extension Warnings
                </h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <ul className="list-disc pl-5 space-y-1">
                    {fileValidation.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Document Viewer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Viewer
            </span>
            <div className="flex items-center gap-2">
              <Badge variant={fileTypeBadge.variant}>{fileTypeBadge.label}</Badge>
              <div className="flex gap-2">
                <Button onClick={handleExternalView} size="sm" variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open
                </Button>
                <Button onClick={handleDownload} size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {canDisplayInline ? (
            <div className="border rounded-lg overflow-hidden relative">
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80">
                  <Skeleton className="h-96 w-full" />
                </div>
              )}
              {showDocument()}
            </div>
          ) : (
            <div className="border rounded-lg p-8 text-center space-y-4">
              <FileText className="h-16 w-16 mx-auto text-muted-foreground" />
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Document Preview Not Available</h3>
                <p className="text-muted-foreground">
                  This document type cannot be previewed inline. Please download or open in a new tab to view.
                </p>
                {fileValidation && !fileValidation.isValid && (
                  <p className="text-sm text-red-600">
                    Detected file type: {fileValidation.detectedExt || 'Unknown'}
                  </p>
                )}
              </div>
              <div className="flex gap-2 justify-center">
                <Button onClick={handleExternalView} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in New Tab
                </Button>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentViewer;
