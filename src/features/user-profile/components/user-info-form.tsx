"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Skeleton } from "@/shared/ui/skeleton";
import { Alert } from "@/shared/ui/alert";
import {
  userInfoSchema,
  type UserInfoFormData,
  TSHIRT_SIZE_OPTIONS,
} from "../lib/validation";
import type { UserInfo } from "../types";

interface UserInfoFormProps {
  data?: UserInfo;
  onSubmit: (data: UserInfoFormData) => Promise<void>;
  isLoading: boolean;
  error?: string | null;
}

export function UserInfoForm({
  data,
  onSubmit,
  isLoading,
  error,
}: UserInfoFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    reset,
  } = useForm<UserInfoFormData>({
    resolver: zodResolver(userInfoSchema),
    defaultValues: {
      firstname: "",
      middlename: "",
      lastname: "",
      legalName: "",
      preferredName: "",
      titles: "",
      position: "",
      license: "",
      tshirtSize: undefined,
      bio: "",
      additionalNotes: "",
    },
  });

  // Reset form when data changes
  useEffect(() => {
    if (data) {
      reset({
        firstname: data.firstname || "",
        middlename: data.middlename || "",
        lastname: data.lastname || "",
        legalName: data.legalName || "",
        preferredName: data.preferredName || "",
        titles: data.titles || "",
        position: data.position || "",
        license: data.license || "",
        tshirtSize: data.tshirtSize,
        bio: data.bio || "",
        additionalNotes: data.additionalNotes || "",
      });
    }
  }, [data, reset]);

  const bioValue = watch("bio") || "";
  const notesValue = watch("additionalNotes") || "";

  const handleFormSubmit = async (formData: UserInfoFormData) => {
    try {
      await onSubmit(formData);
    } catch (err) {
      // Error handling is done by parent component
      console.error("Form submission error:", err);
    }
  };

  if (!data && isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>
          Update your personal details. This information will be reflected on
          your Indi website.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstname">
                First Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="firstname"
                {...register("firstname")}
                placeholder="John"
                disabled={isSubmitting}
              />
              {errors.firstname && (
                <p className="text-sm text-red-500">
                  {errors.firstname.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="middlename">Middle Name</Label>
              <Input
                id="middlename"
                {...register("middlename")}
                placeholder="Michael"
                disabled={isSubmitting}
              />
              {errors.middlename && (
                <p className="text-sm text-red-500">
                  {errors.middlename.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="lastname">
                Last Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="lastname"
                {...register("lastname")}
                placeholder="Doe"
                disabled={isSubmitting}
              />
              {errors.lastname && (
                <p className="text-sm text-red-500">
                  {errors.lastname.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="legalName">Legal Name</Label>
              <Input
                id="legalName"
                {...register("legalName")}
                placeholder="John Michael Doe"
                disabled={isSubmitting}
              />
              {errors.legalName && (
                <p className="text-sm text-red-500">
                  {errors.legalName.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="preferredName">Preferred Name</Label>
              <Input
                id="preferredName"
                {...register("preferredName")}
                placeholder="Johnny"
                disabled={isSubmitting}
              />
              {errors.preferredName && (
                <p className="text-sm text-red-500">
                  {errors.preferredName.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="titles">Title After Name (e.g. AMP, BCC)</Label>
              <Input
                id="titles"
                {...register("titles")}
                placeholder="AMP, BCC, BCO"
                disabled={isSubmitting}
              />
              {errors.titles && (
                <p className="text-sm text-red-500">{errors.titles.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                {...register("position")}
                placeholder="Mortgage Broker, BCS"
                disabled={isSubmitting}
              />
              {errors.position && (
                <p className="text-sm text-red-500">
                  {errors.position.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="license">License Number</Label>
              <Input
                id="license"
                {...register("license")}
                placeholder="#AXM003333"
                disabled={isSubmitting}
              />
              {errors.license && (
                <p className="text-sm text-red-500">{errors.license.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tshirtSize">T-Shirt Size</Label>
            <select
              id="tshirtSize"
              {...register("tshirtSize")}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={isSubmitting}
            >
              <option value="">Select a size</option>
              {TSHIRT_SIZE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.tshirtSize && (
              <p className="text-sm text-red-500">
                {errors.tshirtSize.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">
              More About Me (Bio) <span className="text-red-500">*</span>
            </Label>
            <textarea
              id="bio"
              {...register("bio")}
              rows={6}
              maxLength={800}
              className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Tell us about yourself..."
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center">
              {errors.bio && (
                <p className="text-sm text-red-500">{errors.bio.message}</p>
              )}
              <p className="text-xs text-muted-foreground ml-auto">
                {bioValue.length}/800
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="additionalNotes">Additional Notes</Label>
            <textarea
              id="additionalNotes"
              {...register("additionalNotes")}
              rows={6}
              maxLength={800}
              className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Any additional information..."
              disabled={isSubmitting}
            />
            <div className="flex justify-between items-center">
              {errors.additionalNotes && (
                <p className="text-sm text-red-500">
                  {errors.additionalNotes.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground ml-auto">
                {notesValue.length}/800
              </p>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full md:w-auto"
            >
              {isSubmitting ? "Saving..." : "Save Information"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
