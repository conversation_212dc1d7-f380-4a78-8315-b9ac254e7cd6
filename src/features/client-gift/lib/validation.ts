import { z } from "zod";

// Phone number validation regex - matches the legacy pattern
const phoneRegex = /^[\d\s\-\(\)\.]+$/;

// Phone data schema for masked input
export const phoneDataSchema = z.object({
  masked: z.string().min(1, "Phone number is required"),
  raw: z.string().min(10, "Phone number must be at least 10 digits"),
});

// Payment method schema
export const paymentMethodSchema = z.object({
  payrollDeduction: z.boolean(),
  creditCard: z.boolean(),
}).refine(
  (data) => data.payrollDeduction || data.creditCard,
  {
    message: "Please select a payment method",
    path: ["paymentMethod"],
  }
);

// Base client gift form schema
export const clientGiftFormSchema = z.object({
  // Gift Selection - Required
  selectedGift: z.string().min(1, "Please select a gift"),
  subOption: z.string().min(1, "Please select a gift option"),
  
  // Client Information - Required fields
  clientFirstname: z.string().min(1, "First name is required"),
  clientLastname: z.string().min(1, "Last name is required"),
  companyOrLender: z.string().min(1, "Company/Lender is required"),
  clientPhone: phoneDataSchema,
  
  // Client Information - Optional fields
  clientMiddlename: z.string().optional(),
  clientEmail: z.string().email("Invalid email address").optional().or(z.literal("")),
  
  // Payment Method - Required
  paymentMethod: paymentMethodSchema,
  
  // Gift Details - Conditional and optional
  giftIdeaDescription: z.string().optional(),
  receiverDetails: z.string().optional(),
  giftCardMessage: z.string().optional(),
  
  // Shipping - Optional
  shippingAddress: z.string().optional(),
  desiredShippingDate: z.string().optional(),
}).superRefine((data, ctx) => {
  // Conditional validation: if subOption is "idea", giftIdeaDescription is required
  if (data.subOption === "idea" && (!data.giftIdeaDescription || data.giftIdeaDescription.trim() === "")) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Gift idea description is required when you have an idea for a gift",
      path: ["giftIdeaDescription"],
    });
  }
});

// Gift selection step schema (for step 1 validation)
export const giftSelectionSchema = z.object({
  selectedGift: z.string().min(1, "Please select a gift"),
  subOption: z.string().min(1, "Please select a gift option"),
});

// Client details step schema (for step 2 validation)
export const clientDetailsSchema = z.object({
  clientFirstname: z.string().min(1, "First name is required"),
  clientLastname: z.string().min(1, "Last name is required"),
  companyOrLender: z.string().min(1, "Company/Lender is required"),
  clientPhone: phoneDataSchema,
  clientMiddlename: z.string().optional(),
  clientEmail: z.string().email("Invalid email address").optional().or(z.literal("")),
  paymentMethod: paymentMethodSchema,
  giftIdeaDescription: z.string().optional(),
  receiverDetails: z.string().optional(),
  giftCardMessage: z.string().optional(),
  shippingAddress: z.string().optional(),
  desiredShippingDate: z.string().optional(),
}).superRefine((data, ctx) => {
  // This will be checked at the parent level, but we include it here for completeness
  // The actual subOption check will be done in the main form validation
});

// Type inference
export type ClientGiftFormData = z.infer<typeof clientGiftFormSchema>;
export type GiftSelectionData = z.infer<typeof giftSelectionSchema>;
export type ClientDetailsData = z.infer<typeof clientDetailsSchema>;
export type PaymentMethodData = z.infer<typeof paymentMethodSchema>;
export type PhoneData = z.infer<typeof phoneDataSchema>;

// Validation helper functions
export const validateGiftSelection = (data: any): { isValid: boolean; errors: string[] } => {
  try {
    giftSelectionSchema.parse(data);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => err.message),
      };
    }
    return { isValid: false, errors: ["Validation error"] };
  }
};

export const validateClientDetails = (data: any, subOption?: string): { isValid: boolean; errors: string[] } => {
  try {
    // Add subOption to data for conditional validation
    const dataWithSubOption = { ...data, subOption };
    clientGiftFormSchema.parse(dataWithSubOption);
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => err.message),
      };
    }
    return { isValid: false, errors: ["Validation error"] };
  }
};

// Helper function to convert camelCase to readable format (matching legacy)
export const camelToCapital = (str: string): string => {
  if (!str) return "";
  return str
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

// Required fields list (matching legacy)
export const REQUIRED_FIELDS = [
  "selectedGift",
  "clientFirstname", 
  "clientLastname",
  "companyOrLender",
  "paymentMethod",
  "clientPhone"
] as const;

export type RequiredField = typeof REQUIRED_FIELDS[number];
