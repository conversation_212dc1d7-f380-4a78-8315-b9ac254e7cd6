import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";
import type { User } from "@/shared/types/auth";
import type {
  ClientGiftPageData,
  ClientGiftFormData,
  ClientGiftSubmissionData,
  ClientGiftApiResponse,
  ClientGiftSubmissionResponse,
  PhoneData
} from "../types";

export class ClientGiftApiClient {
  /**
   * Fetch client gift page data
   */
  static async getClientGiftPage(authHeaders?: { Authorization: string }): Promise<ClientGiftApiResponse> {
    try {
      // The API returns a Strapi response structure: { data: {...}, meta: {} }
      // Note: apiClient automatically adds /api prefix, so we don't need it here
      
      let responseData: any;
      let responseSuccess: boolean;
      let responseError: string | undefined;

      if (authHeaders) {
        // Server-side: Use fetch with provided auth headers
        const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1339'}/api/client-gift?populate=*`, {
          headers: {
            'Content-Type': 'application/json',
            ...authHeaders
          }
        });

        console.log('ClientGiftApiClient server-side response status:', response.status);

        if (!response.ok) {
          responseSuccess = false;
          responseError = `HTTP error! status: ${response.status}`;
        } else {
          responseSuccess = true;
          responseData = await response.json();
        }
      } else {
        // Client-side: Use apiClient which handles auth automatically
        const response = await apiClient.get<{ data: any; meta?: any }>('/client-gift?populate=*');
        
        console.log('ClientGiftApiClient client-side response:', response);
        
        responseSuccess = response.success;
        responseData = response.data;
        responseError = response.error;
      }

      if (!responseSuccess) {
        console.error('ClientGiftApiClient error:', responseError);
        return {
          success: false,
          error: responseError || 'Failed to fetch client gift data'
        };
      }

      if (!responseData) {
        console.error('ClientGiftApiClient no data received');
        return {
          success: false,
          error: 'No data received from API'
        };
      }

      // Extract the actual data from the Strapi response structure
      const strapiData = responseData.data;

      if (!strapiData) {
        console.error('ClientGiftApiClient no data in response.data.data');
        throw new Error('No data found in API response');
      }

      console.log('ClientGiftApiClient strapiData:', strapiData);

      // Transform the Strapi response to match our expected interface
      const clientGiftData: ClientGiftPageData = {
        id: strapiData.id?.toString() || strapiData.documentId || '1',
        title: strapiData.title || 'Client Gift Program',
        description: strapiData.description || 'Send thoughtful gifts to your clients.',
        banner: strapiData.banner || { url: '' },
        svgBanner: strapiData.svgBanner || false,
        eventStatus: strapiData.eventStatus || 'active',
        startDate: strapiData.startDate || new Date().toISOString().split('T')[0],
        endDate: strapiData.endDate || new Date(new Date().getFullYear() + 1, 0, 1).toISOString().split('T')[0],
        giftOption: (strapiData.giftOption || []).map((option: any) => ({
          id: option.id,
          title: option.title || 'Untitled Gift',
          description: option.description || '',
          isDonation: option.isDonation || false,
          image: option.image || { url: '' }, // Provide default image if not present
          subOptions: option.subOptions || []
        })),
        chooseForMeDescription: strapiData.chooseForMeDescription || 'Our team will select an appropriate gift based on your preferences.'
      };

      console.log('ClientGiftApiClient transformed data:', clientGiftData);

      return {
        success: true,
        data: clientGiftData
      };
    } catch (error) {
      console.error('Error fetching client gift page:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch client gift data'
      };
    }
  }

  /**
   * Submit client gift request
   */
  static async submitClientGiftRequest(
    formData: ClientGiftFormData,
    user: User
  ): Promise<ClientGiftSubmissionResponse> {
    try {
      const userId = getCookie("userId");
      const jwt = getCookie("jwt");

      if (!userId || !jwt) {
        throw new Error("Authentication required");
      }

      // Transform form data to match API expectations (following legacy format)
      const submissionData: ClientGiftSubmissionData = {
        user: userId,
        client: `${formData.clientFirstname || ''} ${formData.clientMiddlename || ''} ${formData.clientLastname || ''}`.trim(),
        clientEmail: formData.clientEmail || '',
        clientFirstname: formData.clientFirstname || '',
        clientLastname: formData.clientLastname || '',
        clientMiddlename: formData.clientMiddlename || '',
        clientPhone: formData.clientPhone?.raw || '',
        companyOrLender: formData.companyOrLender || '',
        desiredShippingDate: formData.desiredShippingDate || '',
        requester: user ? `${user.firstname || ''} ${user.lastname || ''}`.trim() : 'Unknown User',
        selectedGift: formData.selectedGift || '',
        shippingAddress: formData.shippingAddress || '',
        subOption: this.transformSubOption(formData.subOption),
        paymentMethod: this.getPaymentMethodString(formData.paymentMethod),
        giftIdeaDescription: formData.giftIdeaDescription || '',
        receiverDetails: formData.receiverDetails || '',
        giftCardMessage: formData.giftCardMessage || ''
      };

      const response = await apiClient.post<any>('/client-gifting-requests', submissionData);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to submit gift request');
      }
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('Error submitting client gift request:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to submit gift request'
      };
    }
  }

  /**
   * Transform sub-option value to match legacy format
   */
  private static transformSubOption(subOption: string): string {
    switch (subOption) {
      case 'choose':
        return 'Choose Something For Me';
      case 'idea':
        return 'I Have An Idea For A Gift';
      default:
        return subOption || '';
    }
  }

  /**
   * Get payment method string from payment method object
   */
  private static getPaymentMethodString(paymentMethod: any): string {
    if (paymentMethod?.payrollDeduction) {
      return 'payrollDeduction';
    } else if (paymentMethod?.creditCard) {
      return 'creditCard';
    }
    return '';
  }
}

/**
 * Helper function to format phone number for API submission
 * Extracts raw phone number from PhoneData object
 */
export const getRawPhone = (formData: Partial<ClientGiftFormData>): string => {
  if (formData.clientPhone && typeof formData.clientPhone === 'object' && 'raw' in formData.clientPhone) {
    return formData.clientPhone.raw;
  }
  return '';
};

/**
 * Helper function to validate phone number format
 */
export const validatePhoneNumber = (phone: PhoneData | string): boolean => {
  if (typeof phone === 'string') {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length >= 10;
  }
  
  if (phone && typeof phone === 'object' && 'raw' in phone) {
    return phone.raw.length >= 10;
  }
  
  return false;
};

/**
 * Helper function to format phone number for display
 */
export const formatPhoneForDisplay = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length >= 10) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
  }
  return phone;
};

/**
 * Helper function to create PhoneData object from string input
 */
export const createPhoneDataFromInput = (input: string): PhoneData => {
  const raw = input.replace(/\D/g, '');
  const masked = formatPhoneForDisplay(input);

  return {
    masked,
    raw
  };
};
