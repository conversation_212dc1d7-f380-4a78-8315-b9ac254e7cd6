"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { ArrowLeft } from "lucide-react";
import { cn } from "@/shared/lib/utils";
import type { StepIndicatorProps } from "../types";

/**
 * Step indicator component for the client gift flow
 * Shows progress between gift selection and details form steps
 */
export function StepIndicator({ currentStep, totalSteps = 2, onBack }: StepIndicatorProps) {
  const steps = [
    { number: 1, label: "Select Gift" },
    { number: 2, label: "Details" }
  ];

  return (
    <div className="w-full">
      {/* Back button for step 2 */}
      {currentStep === 2 && onBack && (
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Gift Selection
          </Button>
        </div>
      )}

      {/* Step indicator */}
      <div className="flex items-center justify-center mb-8 pb-6 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          {steps.map((step, index) => (
            <React.Fragment key={step.number}>
              {/* Step circle and label */}
              <div className="flex items-center space-x-3">
                <div
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold transition-colors",
                    currentStep === step.number
                      ? "bg-blue-600 text-white" // Active step
                      : currentStep > step.number
                      ? "bg-green-600 text-white" // Completed step
                      : "bg-gray-200 text-gray-500" // Inactive step
                  )}
                >
                  {step.number}
                </div>
                <span
                  className={cn(
                    "text-base font-medium transition-colors",
                    currentStep === step.number
                      ? "text-blue-600" // Active step
                      : currentStep > step.number
                      ? "text-green-600" // Completed step
                      : "text-gray-500" // Inactive step
                  )}
                >
                  {step.label}
                </span>
              </div>

              {/* Divider line between steps */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "w-12 h-0.5 transition-colors",
                    currentStep > step.number
                      ? "bg-green-600" // Completed
                      : "bg-gray-200" // Not completed
                  )}
                />
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Alternative compact step indicator for mobile
 */
export function CompactStepIndicator({ currentStep, totalSteps = 2 }: Omit<StepIndicatorProps, "onBack">) {
  return (
    <div className="flex items-center justify-center mb-6 md:hidden">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-500">Step</span>
        <span className="text-sm font-medium text-blue-600">
          {currentStep} of {totalSteps}
        </span>
      </div>
    </div>
  );
}

/**
 * Progress bar style step indicator
 */
export function ProgressStepIndicator({ currentStep, totalSteps = 2 }: Omit<StepIndicatorProps, "onBack">) {
  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <div className="w-full mb-8">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">
          Step {currentStep} of {totalSteps}
        </span>
        <span className="text-sm text-gray-500">
          {Math.round(progressPercentage)}% Complete
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
    </div>
  );
}
