"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select";
import { Label } from "@/shared/ui/label";
import { Check<PERSON>ir<PERSON>, ArrowRight } from "lucide-react";
import { cn } from "@/shared/lib/utils";
import type { GiftSelectionProps, GiftOption } from "../types";

/**
 * Gift selection component for step 1 of the client gift flow
 * Displays gift options as cards with sub-option selection
 */
export function GiftSelection({
  giftOptions,
  selectedGift,
  selectedSubOption,
  onGiftSelect,
  onSubOptionChange,
  onNext,
  disabled = false
}: GiftSelectionProps) {
  
  const handleGiftCardClick = (giftId: string) => {
    if (disabled) return;
    onGiftSelect(giftId);
  };

  const handleSubOptionChange = (value: string) => {
    if (disabled) return;
    onSubOptionChange(value);
  };

  const isNextDisabled = !selectedGift || !selectedSubOption || disabled;

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold mb-6">Select your item:</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {giftOptions.map((option) => (
            <GiftCard
              key={option.title}
              option={option}
              isSelected={selectedGift === option.title}
              selectedSubOption={selectedSubOption}
              onSelect={() => handleGiftCardClick(option.title)}
              onSubOptionChange={handleSubOptionChange}
              onNext={onNext}
              disabled={disabled}
              isNextDisabled={isNextDisabled}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

interface GiftCardProps {
  option: GiftOption;
  isSelected: boolean;
  selectedSubOption: string;
  onSelect: () => void;
  onSubOptionChange: (value: string) => void;
  onNext: () => void;
  disabled: boolean;
  isNextDisabled: boolean;
}

function GiftCard({
  option,
  isSelected,
  selectedSubOption,
  onSelect,
  onSubOptionChange,
  onNext,
  disabled,
  isNextDisabled
}: GiftCardProps) {
  
  return (
    <Card 
      className={cn(
        "relative cursor-pointer transition-all duration-200 hover:shadow-lg",
        isSelected && "ring-2 ring-blue-600 bg-blue-50",
        disabled && "opacity-50 cursor-not-allowed"
      )}
      onClick={onSelect}
    >
      {/* Selection indicator */}
      {isSelected && (
        <div className="absolute -top-2 -right-2 bg-blue-600 rounded-full p-1 z-10">
          <CheckCircle className="h-6 w-6 text-white" />
        </div>
      )}

      <CardContent className="p-4">
        {/* Gift image */}
        <div className="mb-4">
          <img
            src={option.image.url}
            alt={option.title}
            className="w-full h-48 object-cover rounded-md"
          />
        </div>

        {/* Gift title and description */}
        <div className="mb-4">
          <h4 className="text-lg font-semibold mb-2">{option.title}</h4>
          {option.description && (
            <div 
              className="text-sm text-gray-600 prose prose-sm"
              dangerouslySetInnerHTML={{ __html: option.description }}
            />
          )}
        </div>

        {/* Sub-options dropdown */}
        <div className="mb-4" onClick={(e) => e.stopPropagation()}>
          <Label htmlFor={`subOption-${option.title}`} className="text-sm font-medium mb-2 block">
            Gift Options:
          </Label>
          
          {!isSelected && (
            <p className="text-xs text-gray-500 mb-2">
              Select this item before selecting a gift option.
            </p>
          )}
          
          <Select
            disabled={!isSelected || disabled}
            value={isSelected ? selectedSubOption : ""}
            onValueChange={onSubOptionChange}
          >
            <SelectTrigger className={cn(
              "w-full",
              !isSelected && "bg-gray-100"
            )}>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="idea">I have an idea for a gift</SelectItem>
              <SelectItem value="choose">Choose something for me</SelectItem>
              {option.subOptions?.map((subOption) => (
                <SelectItem key={subOption.title} value={subOption.title}>
                  {subOption.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Action buttons */}
        <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
          {isSelected ? (
            <Button
              onClick={onNext}
              disabled={isNextDisabled}
              className="w-full flex items-center justify-center gap-2"
            >
              Next Step
              <ArrowRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={onSelect}
              disabled={disabled}
              className="w-full flex items-center justify-center gap-2"
            >
              Select This Item
              <ArrowRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
