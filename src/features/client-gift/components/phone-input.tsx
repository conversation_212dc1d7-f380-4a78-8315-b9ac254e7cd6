"use client";

import React, { forwardRef, useEffect, useState } from "react";
import { Input } from "@/shared/ui/input";
import { cn } from "@/shared/lib/utils";
import type { PhoneData } from "../types";

interface PhoneInputProps extends Omit<React.ComponentProps<"input">, "onChange" | "value"> {
  value?: PhoneData | string;
  onChange?: (phoneData: PhoneData) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: boolean;
}

/**
 * Phone input component with masking functionality
 * Formats phone numbers as XXX-XXX-XXXX and provides both masked and raw values
 */
export const PhoneInput = forwardRef<HTMLInputElement, PhoneInputProps>(
  ({ value, onChange, onBlur, error, className, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState("");

    // Initialize display value from prop
    useEffect(() => {
      if (typeof value === "string") {
        setDisplayValue(formatPhoneNumber(value));
      } else if (value && typeof value === "object" && "masked" in value) {
        setDisplayValue(value.masked);
      } else {
        setDisplayValue("");
      }
    }, [value]);

    const formatPhoneNumber = (input: string): string => {
      // Remove all non-digit characters
      const cleaned = input.replace(/\D/g, "");
      
      // Apply formatting based on length
      if (cleaned.length >= 6) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      } else if (cleaned.length >= 3) {
        return `${cleaned.slice(0, 3)}-${cleaned.slice(3)}`;
      } else {
        return cleaned;
      }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      const formatted = formatPhoneNumber(inputValue);
      const raw = inputValue.replace(/\D/g, "");

      setDisplayValue(formatted);

      // Call onChange with PhoneData object
      if (onChange) {
        onChange({
          masked: formatted,
          raw: raw
        });
      }
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      if (onBlur) {
        onBlur(e);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Allow: backspace, delete, tab, escape, enter
      if ([8, 9, 27, 13, 46].indexOf(e.keyCode) !== -1 ||
          // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
          (e.keyCode === 65 && e.ctrlKey === true) ||
          (e.keyCode === 67 && e.ctrlKey === true) ||
          (e.keyCode === 86 && e.ctrlKey === true) ||
          (e.keyCode === 88 && e.ctrlKey === true) ||
          // Allow: home, end, left, right
          (e.keyCode >= 35 && e.keyCode <= 39)) {
        return;
      }
      
      // Ensure that it is a number and stop the keypress
      if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
        e.preventDefault();
      }
      
      // Limit to 10 digits
      const currentRaw = displayValue.replace(/\D/g, "");
      if (currentRaw.length >= 10 && ![8, 9, 27, 13, 46].includes(e.keyCode)) {
        e.preventDefault();
      }
    };

    return (
      <Input
        {...props}
        ref={ref}
        type="tel"
        value={displayValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={cn(
          error && "border-red-500 focus-visible:border-red-500",
          className
        )}
        placeholder="************"
        maxLength={12} // XXX-XXX-XXXX format
      />
    );
  }
);

PhoneInput.displayName = "PhoneInput";

/**
 * Utility function to validate phone number
 */
export const isValidPhoneNumber = (phone: PhoneData | string): boolean => {
  if (typeof phone === "string") {
    const cleaned = phone.replace(/\D/g, "");
    return cleaned.length >= 10;
  }
  
  if (phone && typeof phone === "object" && "raw" in phone) {
    return phone.raw.length >= 10;
  }
  
  return false;
};

/**
 * Utility function to extract raw phone number
 */
export const getRawPhoneNumber = (phone: PhoneData | string): string => {
  if (typeof phone === "string") {
    return phone.replace(/\D/g, "");
  }
  
  if (phone && typeof phone === "object" && "raw" in phone) {
    return phone.raw;
  }
  
  return "";
};

/**
 * Utility function to create PhoneData from string
 */
export const createPhoneData = (input: string): PhoneData => {
  const raw = input.replace(/\D/g, "");
  const formatted = raw.length >= 6 
    ? `${raw.slice(0, 3)}-${raw.slice(3, 6)}-${raw.slice(6, 10)}`
    : raw.length >= 3 
    ? `${raw.slice(0, 3)}-${raw.slice(3)}`
    : raw;
  
  return {
    masked: formatted,
    raw: raw
  };
};
