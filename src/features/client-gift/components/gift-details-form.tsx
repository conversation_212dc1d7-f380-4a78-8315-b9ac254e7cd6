"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Textarea } from "@/shared/ui/textarea";
import { Label } from "@/shared/ui/label";
import { Switch } from "@/shared/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { CheckCircle, User, Wallet, Truck, Gift } from "lucide-react";
import { cn } from "@/shared/lib/utils";
import { PhoneInput } from "./phone-input";
import { clientGiftFormSchema, type ClientGiftFormData } from "../lib/validation";
import type { GiftDetailsFormProps } from "../types";

/**
 * Gift details form component for step 2 of the client gift flow
 * Handles recipient information, payment method, and shipping details
 */
export function GiftDetailsForm({
  formData,
  onSubmit,
  onBack,
  isSubmitting,
  validationErrors,
  selectedGift,
  selectedGiftImage,
  subOption,
  chooseForMeDescription
}: GiftDetailsFormProps) {
  
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<ClientGiftFormData>({
    resolver: zodResolver(clientGiftFormSchema),
    defaultValues: {
      selectedGift: selectedGift || "",
      subOption: subOption || "",
      clientFirstname: formData.clientFirstname || "",
      clientMiddlename: formData.clientMiddlename || "",
      clientLastname: formData.clientLastname || "",
      clientEmail: formData.clientEmail || "",
      companyOrLender: formData.companyOrLender || "",
      paymentMethod: formData.paymentMethod || { payrollDeduction: false, creditCard: false },
      giftIdeaDescription: formData.giftIdeaDescription || "",
      receiverDetails: formData.receiverDetails || "",
      giftCardMessage: formData.giftCardMessage || "",
      shippingAddress: formData.shippingAddress || "",
      clientPhone: formData.clientPhone || { masked: "", raw: "" }
    }
  });

  const watchedPaymentMethod = watch("paymentMethod");
  const watchedSubOption = watch("subOption");

  const handlePaymentMethodChange = (method: "payrollDeduction" | "creditCard", checked: boolean) => {
    if (method === "payrollDeduction") {
      setValue("paymentMethod", { payrollDeduction: checked, creditCard: false });
    } else {
      setValue("paymentMethod", { payrollDeduction: false, creditCard: checked });
    }
  };

  const handlePhoneChange = (phoneData: any) => {
    setValue("clientPhone", phoneData);
  };

  return (
    <div className="space-y-6">
      {/* Validation errors */}
      {validationErrors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <h4 className="text-red-800 font-medium mb-2">The following fields are required:</h4>
            <ul className="list-disc list-inside text-red-700 text-sm space-y-1">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Gift Summary Sidebar */}
        <div className="lg:col-span-1">
          <Card className="bg-gray-50">
            <CardContent className="pt-6">
              {selectedGiftImage && (
                <img
                  src={selectedGiftImage}
                  alt={selectedGift || "Selected gift"}
                  className="w-full rounded-md mb-4"
                />
              )}
              <div>
                <h3 className="font-semibold text-lg mb-2">Your Gift Option:</h3>
                <p className="font-medium mb-2">{selectedGift}</p>
                <p className="font-medium text-sm text-gray-600">
                  {subOption === "choose" ? "Choose Something For Me" : "I Have An Idea For A Gift"}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Form */}
        <div className="lg:col-span-3">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            
            {/* Gift Idea Description (conditional) */}
            {watchedSubOption === "idea" && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gift className="h-5 w-5" />
                    Describe Your Gift Idea
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Label htmlFor="giftIdeaDescription">
                      Description <span className="text-red-500">*</span>
                    </Label>
                    <Textarea
                      id="giftIdeaDescription"
                      {...register("giftIdeaDescription")}
                      placeholder="Please describe your gift idea..."
                      className="min-h-32"
                    />
                    {errors.giftIdeaDescription && (
                      <p className="text-sm text-red-500">{errors.giftIdeaDescription.message}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Choose For Me Description */}
            {watchedSubOption === "choose" && chooseForMeDescription && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-6">
                  <div 
                    className="prose prose-sm text-blue-800"
                    dangerouslySetInnerHTML={{ __html: chooseForMeDescription }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Payment Method */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5" />
                  Select Payment Method <span className="text-red-500">*</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <Label htmlFor="payrollDeduction" className="flex-1">
                      Deducted From Payroll
                    </Label>
                    <Switch
                      id="payrollDeduction"
                      checked={watchedPaymentMethod?.payrollDeduction || false}
                      onCheckedChange={(checked) => handlePaymentMethodChange("payrollDeduction", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <Label htmlFor="creditCard" className="flex-1">
                      Paid By Credit Card With a 3.5% Admin Fee
                    </Label>
                    <Switch
                      id="creditCard"
                      checked={watchedPaymentMethod?.creditCard || false}
                      onCheckedChange={(checked) => handlePaymentMethodChange("creditCard", checked)}
                    />
                  </div>
                </div>
                {errors.paymentMethod && (
                  <p className="text-sm text-red-500 mt-2">{errors.paymentMethod.message}</p>
                )}
              </CardContent>
            </Card>

            {/* Recipient Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Gift Recipient Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="clientFirstname">
                      First Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="clientFirstname"
                      {...register("clientFirstname")}
                      placeholder="First Name"
                    />
                    {errors.clientFirstname && (
                      <p className="text-sm text-red-500">{errors.clientFirstname.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="clientLastname">
                      Last Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="clientLastname"
                      {...register("clientLastname")}
                      placeholder="Last Name"
                    />
                    {errors.clientLastname && (
                      <p className="text-sm text-red-500">{errors.clientLastname.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="companyOrLender">
                      Company/Lender <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="companyOrLender"
                      {...register("companyOrLender")}
                      placeholder="Company Name"
                    />
                    {errors.companyOrLender && (
                      <p className="text-sm text-red-500">{errors.companyOrLender.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="clientEmail">Email</Label>
                    <Input
                      id="clientEmail"
                      type="email"
                      {...register("clientEmail")}
                      placeholder="<EMAIL>"
                    />
                    {errors.clientEmail && (
                      <p className="text-sm text-red-500">{errors.clientEmail.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="clientPhone">
                      Phone Number <span className="text-red-500">*</span>
                    </Label>
                    <PhoneInput
                      value={formData.clientPhone}
                      onChange={handlePhoneChange}
                      error={!!errors.clientPhone}
                    />
                    {errors.clientPhone && (
                      <p className="text-sm text-red-500">{errors.clientPhone.message}</p>
                    )}
                  </div>
                </div>

                <div className="mt-6 space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="receiverDetails">More About Who Is Receiving The Gift</Label>
                    <p className="text-sm text-gray-600">
                      Tell us a bit more about the receiver, for example if we know they love baseball or jazz music,
                      it can help with selecting better gifts.
                    </p>
                    <Textarea
                      id="receiverDetails"
                      {...register("receiverDetails")}
                      placeholder="Tell us about the gift recipient..."
                      className="min-h-32"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="giftCardMessage">Gift Card Message</Label>
                    <p className="text-sm text-gray-600">
                      If you'd like to include a message with your gift, please enter it here.
                    </p>
                    <Textarea
                      id="giftCardMessage"
                      {...register("giftCardMessage")}
                      placeholder="Your message here..."
                      className="min-h-24"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shipping Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Shipping Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress">Shipping Address</Label>
                  <Textarea
                    id="shippingAddress"
                    {...register("shippingAddress")}
                    placeholder="Enter the shipping address..."
                    className="min-h-32"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2 px-8"
              >
                {isSubmitting ? "Submitting..." : "Submit"}
                <CheckCircle className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
