"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Card, CardContent } from "@/shared/ui/card";
import { CheckCircle, PlusCircle, Calendar } from "lucide-react";
import { cn } from "@/shared/lib/utils";
import { StepIndicator } from "./step-indicator";
import { GiftSelection } from "./gift-selection";
import { GiftDetailsForm } from "./gift-details-form";
import { ClientGiftApiClient } from "../lib/api-client";
import { validateGiftSelection, camelToCapital, REQUIRED_FIELDS } from "../lib/validation";
import type { 
  ClientGiftPageProps, 
  ClientGiftState, 
  ClientGiftFormData,
  PhoneData 
} from "../types";

/**
 * Main client gift page component
 * Orchestrates the entire client gift flow with step management
 */
export function ClientGiftPage({ page: gift, user }: ClientGiftPageProps) {
  const [state, setState] = useState<ClientGiftState>({
    currentStep: 1,
    selectedGift: null,
    selectedGiftImage: null,
    formData: {},
    isSubmitting: false,
    isSubmitted: false,
    validationErrors: [],
    completionMessage: ""
  });

  // Initialize form with user data if available
  useEffect(() => {
    if (user) {
      setState(prev => ({
        ...prev,
        formData: {
          ...prev.formData,
          clientFirstname: user.firstname || "",
          clientLastname: user.lastname || "",
          clientEmail: user.email || "",
          clientPhone: user.phone ? { 
            masked: formatPhoneForDisplay(user.phone), 
            raw: user.phone.replace(/\D/g, '') 
          } : { masked: "", raw: "" }
        }
      }));
    }
  }, [user]);

  const formatPhoneForDisplay = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length >= 10) {
      return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
    return phone;
  };

  const handleGiftSelect = (giftId: string) => {
    const selectedOption = gift.giftOption.find(option => option.title === giftId);
    const giftImage = selectedOption?.image.url || null;
    
    setState(prev => ({
      ...prev,
      selectedGift: giftId,
      selectedGiftImage: giftImage,
      formData: {
        ...prev.formData,
        selectedGift: giftId,
        subOption: "" // Reset sub-option when changing gift
      }
    }));
  };

  const handleSubOptionChange = (option: string) => {
    setState(prev => ({
      ...prev,
      formData: {
        ...prev.formData,
        subOption: option
      }
    }));
  };

  const handleNextStep = () => {
    // Validate gift selection before proceeding
    const { isValid, errors } = validateGiftSelection({
      selectedGift: state.selectedGift,
      subOption: state.formData.subOption
    });

    if (!isValid) {
      setState(prev => ({
        ...prev,
        validationErrors: errors
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      currentStep: 2,
      validationErrors: []
    }));

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handlePreviousStep = () => {
    setState(prev => ({
      ...prev,
      currentStep: 1,
      validationErrors: []
    }));
  };

  const validateForm = (formData: ClientGiftFormData): string[] => {
    const missingFields: string[] = [];

    // Check required fields
    REQUIRED_FIELDS.forEach((field) => {
      if (field === 'paymentMethod') {
        if (!formData.paymentMethod ||
            (!formData.paymentMethod.payrollDeduction && !formData.paymentMethod.creditCard)) {
          missingFields.push(field);
        }
      } else if (field === 'clientPhone') {
        if (!formData.clientPhone ||
            !formData.clientPhone.raw ||
            formData.clientPhone.raw.length < 10) {
          missingFields.push(field);
        }
      } else {
        if (!formData[field as keyof ClientGiftFormData]) {
          missingFields.push(field);
        }
      }
    });

    // Check conditional validation for gift idea description
    if (formData.subOption === 'idea' &&
        (!formData.giftIdeaDescription || formData.giftIdeaDescription.trim() === '')) {
      missingFields.push('giftIdeaDescription');
    }

    return missingFields.map(field => camelToCapital(field));
  };

  const handleFormSubmit = async (formData: ClientGiftFormData) => {
    // Validate form
    const validationErrors = validateForm(formData);
    if (validationErrors.length > 0) {
      setState(prev => ({
        ...prev,
        validationErrors
      }));
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    setState(prev => ({
      ...prev,
      isSubmitting: true,
      validationErrors: []
    }));

    try {
      const response = await ClientGiftApiClient.submitClientGiftRequest(formData, user);
      
      if (response.success) {
        setState(prev => ({
          ...prev,
          isSubmitting: false,
          isSubmitted: true,
          completionMessage: "Thank you. Your gift request has been submitted successfully!"
        }));
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        throw new Error(response.error || "Failed to submit gift request");
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setState(prev => ({
        ...prev,
        isSubmitting: false,
        validationErrors: [`Error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }));
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handleReset = () => {
    setState({
      currentStep: 1,
      selectedGift: null,
      selectedGiftImage: null,
      formData: {},
      isSubmitting: false,
      isSubmitted: false,
      validationErrors: [],
      completionMessage: ""
    });
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  if (state.isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Banner */}
        <div 
          className="relative h-64 bg-cover bg-center"
          style={{ backgroundImage: `url(${gift.banner.url})` }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-50" />
          <div className="relative z-10 flex items-center justify-center h-full">
            <h1 className="text-4xl font-bold text-white text-center">{gift.title}</h1>
          </div>
        </div>

        {/* Success Content */}
        <div className="container mx-auto px-4 py-12">
          <Card className="max-w-2xl mx-auto text-center">
            <CardContent className="pt-12 pb-8">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-6" />
              <h3 className="text-2xl font-semibold mb-6">{state.completionMessage}</h3>
              <Button
                onClick={handleReset}
                className="flex items-center gap-2 mx-auto"
              >
                <PlusCircle className="h-4 w-4" />
                Send Another Gift
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Banner */}
      <div 
        className="relative h-64 bg-cover bg-center"
        style={{ backgroundImage: `url(${gift.banner.url})` }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-50" />
        <div className="relative z-10 flex items-center justify-center h-full">
          <h1 className="text-4xl font-bold text-white text-center">{gift.title}</h1>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Date Information */}
        {gift.startDate && (
          <div className="mb-8">
            <div className="flex items-center justify-center gap-2 text-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
              <span className="font-medium">Requests up to:</span>
              <span>
                {gift.endDate && gift.endDate !== gift.startDate && (
                  <>
                    {formatDate(gift.endDate)}, 
                  </>
                )}
                {new Date(gift.startDate).getFullYear()}
              </span>
            </div>
          </div>
        )}

        {/* Description (only show on step 1) */}
        {state.currentStep === 1 && (
          <div className="mb-8 max-w-4xl mx-auto">
            <div 
              className="prose prose-lg mx-auto text-center"
              dangerouslySetInnerHTML={{ __html: gift.description }}
            />
          </div>
        )}

        {/* Step Indicator */}
        <StepIndicator
          currentStep={state.currentStep}
          totalSteps={2}
          onBack={handlePreviousStep}
        />

        {/* Form Content */}
        <div className="max-w-6xl mx-auto">
          {state.currentStep === 1 ? (
            <GiftSelection
              giftOptions={gift.giftOption}
              selectedGift={state.selectedGift}
              selectedSubOption={state.formData.subOption || ""}
              onGiftSelect={handleGiftSelect}
              onSubOptionChange={handleSubOptionChange}
              onNext={handleNextStep}
              disabled={state.isSubmitting}
            />
          ) : (
            <GiftDetailsForm
              formData={state.formData}
              onSubmit={handleFormSubmit}
              onBack={handlePreviousStep}
              isSubmitting={state.isSubmitting}
              validationErrors={state.validationErrors}
              selectedGift={state.selectedGift}
              selectedGiftImage={state.selectedGiftImage}
              subOption={state.formData.subOption || ""}
              chooseForMeDescription={gift.chooseForMeDescription}
            />
          )}
        </div>
      </div>
    </div>
  );
}
