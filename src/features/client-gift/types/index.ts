// Client Gift Feature Types

import type { User } from "@/shared/types/auth";

export interface GiftOption {
  id?: number;
  title: string;
  description?: string;
  isDonation?: boolean;
  image?: {
    url: string;
  };
  subOptions?: GiftSubOption[];
}

export interface GiftSubOption {
  title: string;
  value?: string;
}

export interface ClientGiftPageData {
  id: string;
  title: string;
  description: string;
  banner: {
    url: string;
  };
  svgBanner?: boolean;
  eventStatus: string;
  startDate?: string;
  endDate?: string;
  giftOption: GiftOption[];
  chooseForMeDescription?: string;
}

export interface PaymentMethod {
  payrollDeduction: boolean;
  creditCard: boolean;
}

export interface PhoneData {
  masked: string;
  raw: string;
}

export interface ClientGiftFormData {
  // Gift Selection
  selectedGift: string;
  subOption: string;
  
  // Client Information
  clientFirstname: string;
  clientMiddlename?: string;
  clientLastname: string;
  clientEmail?: string;
  clientPhone: PhoneData;
  companyOrLender: string;
  
  // Payment
  paymentMethod: PaymentMethod;
  
  // Gift Details
  giftIdeaDescription?: string;
  receiverDetails?: string;
  giftCardMessage?: string;
  
  // Shipping
  shippingAddress?: string;
  desiredShippingDate?: string;
}

export interface ClientGiftSubmissionData {
  user: string;
  client: string;
  clientEmail: string;
  clientFirstname: string;
  clientMiddlename: string;
  clientLastname: string;
  clientPhone: string;
  companyOrLender: string;
  desiredShippingDate: string;
  requester: string;
  selectedGift: string;
  shippingAddress: string;
  subOption: string;
  paymentMethod: string;
  giftIdeaDescription: string;
  receiverDetails: string;
  giftCardMessage: string;
}

export interface ClientGiftApiResponse {
  success: boolean;
  data?: ClientGiftPageData;
  error?: string;
}

export interface ClientGiftSubmissionResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// Component Props
export interface GiftSelectionProps {
  giftOptions: GiftOption[];
  selectedGift: string | null;
  selectedSubOption: string;
  onGiftSelect: (giftId: string) => void;
  onSubOptionChange: (option: string) => void;
  onNext: () => void;
  disabled?: boolean;
}

export interface GiftDetailsFormProps {
  formData: Partial<ClientGiftFormData>;
  onSubmit: (data: ClientGiftFormData) => void;
  onBack: () => void;
  isSubmitting: boolean;
  validationErrors: string[];
  selectedGift: string | null;
  selectedGiftImage: string | null;
  subOption: string;
  chooseForMeDescription?: string;
}

export interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  onBack?: () => void;
}

export interface ClientGiftPageProps {
  page: ClientGiftPageData;
  user: User;
}

// Form validation types
export type ClientGiftFormErrors = {
  [K in keyof ClientGiftFormData]?: string;
};

// Step management
export type ClientGiftStep = 1 | 2;

export interface ClientGiftState {
  currentStep: ClientGiftStep;
  selectedGift: string | null;
  selectedGiftImage: string | null;
  formData: Partial<ClientGiftFormData>;
  isSubmitting: boolean;
  isSubmitted: boolean;
  validationErrors: string[];
  completionMessage: string;
}
