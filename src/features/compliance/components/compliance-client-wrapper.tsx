"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryLandingPage from "@/shared/components/category-landing-page";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { useCompliance } from "../hooks/use-compliance";

const ComplianceClientWrapper: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = useCompliance();

  const handleCategorySelect = React.useCallback((category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/compliance/${category.id}`);
  }, [router]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  return (
    <CategoryLandingPage
      title="Compliance"
      subtitle="Compliance documents organized by province"
      description="Access compliance documents organized by province. Each province contains relevant compliance materials and regulatory information for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No compliance categories found"
      retryAction={handleRetry}
    />
  );
};

export default ComplianceClientWrapper;
