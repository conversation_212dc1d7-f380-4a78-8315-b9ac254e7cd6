"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { CategoryLandingPage, CategoryItem } from "@/shared/components";
import { useCompliance } from "../hooks/use-compliance";

const ComplianceLandingPage: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = useCompliance();

  const handleCategorySelect = (category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/compliance/${category.id}`);
  };

  return (
    <CategoryLandingPage
      title="Compliance"
      subtitle="Compliance documents applicable for all provinces"
      description="Access compliance documents organized by province. Each province contains relevant compliance forms and documents for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No compliance categories found"
      retryAction={retry}
    />
  );
};

export default ComplianceLandingPage;
