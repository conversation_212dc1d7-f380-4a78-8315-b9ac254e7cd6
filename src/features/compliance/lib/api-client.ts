import { DocsApiClient, Document, DocumentType } from "@/shared/lib/docs-api-client";

// Re-export shared types for convenience
export type {
  Document as ComplianceDocument,
  ProvinceFile,
  DocumentsResponse as ComplianceResponse,
  DocumentResponse as ComplianceDocumentResponse,
} from "@/shared/lib/docs-api-client";

/**
 * API client for compliance using shared DocsApiClient
 */
export class ComplianceApiClient {
  private static readonly DOCUMENT_TYPE: DocumentType = "compliance";

  /**
   * Get all compliance documents
   */
  static async getCompliance(params?: {
    page?: number;
    pageSize?: number;
  }) {
    return DocsApiClient.getDocuments(ComplianceApiClient.DOCUMENT_TYPE, params);
  }

  /**
   * Get a specific compliance document by documentId
   */
  static async getComplianceDocument(documentId: string) {
    return DocsApiClient.getDocument(ComplianceApiClient.DOCUMENT_TYPE, documentId);
  }

  /**
   * Get compliance documents by province
   */
  static async getComplianceByProvince(province: string) {
    return DocsApiClient.getDocumentsByProvince(ComplianceApiClient.DOCUMENT_TYPE, province);
  }

  /**
   * Get all available provinces for compliance documents
   */
  static async getComplianceProvinces() {
    return DocsApiClient.getProvinces(ComplianceApiClient.DOCUMENT_TYPE);
  }

  /**
   * Transform compliance documents to categories for the landing page
   */
  static transformDocumentsToCategories(documents: Document[]) {
    return DocsApiClient.transformDocumentsToCategories(documents);
  }

  /**
   * Format province name to readable title
   */
  static formatProvinceTitle(province: string | null | undefined) {
    return DocsApiClient.formatProvinceTitle(province);
  }
}

// Export convenience functions
export const {
  getCompliance,
  getComplianceDocument,
  getComplianceByProvince,
  getComplianceProvinces,
  transformDocumentsToCategories,
  formatProvinceTitle,
} = ComplianceApiClient;
