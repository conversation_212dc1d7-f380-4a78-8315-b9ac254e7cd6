"use client";

import { useState, useEffect, useCallback } from "react";
import { ComplianceApiClient, ComplianceDocument } from "../lib/api-client";

export interface UseComplianceCategoryResult {
  items: ComplianceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useComplianceCategory(category: string): UseComplianceCategoryResult {
  const [items, setItems] = useState<ComplianceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComplianceByCategory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ComplianceApiClient.getComplianceByProvince(category);

      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      console.error("Error in useComplianceCategory:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch compliance documents for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, [category]);

  const retry = useCallback(() => {
    fetchComplianceByCategory();
  }, [fetchComplianceByCategory]);

  useEffect(() => {
    if (category) {
      fetchComplianceByCategory();
    }
  }, [category, fetchComplianceByCategory]);

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
