"use client";

import { useState, useEffect, useCallback } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { ComplianceApiClient } from "../lib/api-client";

export interface UseComplianceResult {
  categories: CategoryItem[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useCompliance(): UseComplianceResult {
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCompliance = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ComplianceApiClient.getCompliance();

      if (response.error) {
        setError(response.error);
        setCategories([]);
        return;
      }

      // Transform documents to categories
      const transformedCategories = ComplianceApiClient.transformDocumentsToCategories(response.data);

      // Convert to CategoryItem format
      const categoryItems: CategoryItem[] = transformedCategories.map((cat) => ({
        id: cat.id,
        title: cat.title,
        description: cat.description,
        count: cat.documentCount,
      }));

      setCategories(categoryItems);
    } catch (err) {
      console.error("Error in useCompliance:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch compliance documents");
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    fetchCompliance();
  }, [fetchCompliance]);

  useEffect(() => {
    fetchCompliance();
  }, [fetchCompliance]);

  return {
    categories,
    isLoading,
    error,
    retry,
  };
}
