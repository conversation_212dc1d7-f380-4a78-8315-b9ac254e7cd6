"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryLandingPage from "@/shared/components/category-landing-page";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { usePrintables } from "../hooks/use-printables";

const PrintablesClientWrapper: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = usePrintables();

  const handleCategorySelect = React.useCallback((category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/printables/${category.id}`);
  }, [router]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  return (
    <CategoryLandingPage
      title="Printables"
      subtitle="Printable documents organized by province"
      description="Access printable documents organized by province. Each province contains relevant forms and documents for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No printable categories found"
      retryAction={handleRetry}
    />
  );
};

export default PrintablesClientWrapper;
