import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import PrintablesLandingPage from "../printables-landing-page";
import { usePrintables } from "../../hooks/use-printables";

// Mock dependencies
jest.mock("next/navigation");
jest.mock("../../hooks/use-printables");

const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUsePrintables = usePrintables as jest.MockedFunction<typeof usePrintables>;

describe("PrintablesLandingPage", () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);
  });

  it("should render loading state", () => {
    mockUsePrintables.mockReturnValue({
      categories: [],
      documents: [],
      isLoading: true,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    // In loading state, the CategoryLandingPage shows skeletons but not the title
    // Check for skeleton elements by their CSS class
    const skeletonElements = document.querySelectorAll('[data-slot="skeleton"]');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it("should render categories when loaded", () => {
    const mockCategories = [
      {
        id: "alberta",
        title: "Alberta",
        description: "2 documents available",
      },
      {
        id: "ontario",
        title: "Ontario", 
        description: "3 documents available",
      },
    ];

    mockUsePrintables.mockReturnValue({
      categories: mockCategories,
      documents: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    expect(screen.getByText("Alberta")).toBeInTheDocument();
    expect(screen.getByText("Ontario")).toBeInTheDocument();
    expect(screen.getByText("2 documents available")).toBeInTheDocument();
    expect(screen.getByText("3 documents available")).toBeInTheDocument();
  });

  it("should render error state", () => {
    const mockRetry = jest.fn();
    mockUsePrintables.mockReturnValue({
      categories: [],
      documents: [],
      isLoading: false,
      error: "Failed to fetch printables",
      retry: mockRetry,
    });

    render(<PrintablesLandingPage />);

    expect(screen.getByText("Failed to fetch printables")).toBeInTheDocument();
  });

  it("should render empty state", () => {
    mockUsePrintables.mockReturnValue({
      categories: [],
      documents: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    expect(screen.getByText("No printable categories found")).toBeInTheDocument();
  });

  it("should navigate to category when selected", async () => {
    const mockCategories = [
      {
        id: "alberta",
        title: "Alberta",
        description: "2 documents available",
      },
    ];

    mockUsePrintables.mockReturnValue({
      categories: mockCategories,
      documents: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    const viewDocumentsButton = screen.getByText("View Documents");
    fireEvent.click(viewDocumentsButton);

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith("/printables/alberta");
    });
  });

  it("should call retry when retry button is clicked", () => {
    const mockRetry = jest.fn();
    mockUsePrintables.mockReturnValue({
      categories: [],
      documents: [],
      isLoading: false,
      error: "Failed to fetch printables",
      retry: mockRetry,
    });

    render(<PrintablesLandingPage />);

    const retryButton = screen.getByRole("button", { name: /try again/i });
    fireEvent.click(retryButton);

    expect(mockRetry).toHaveBeenCalledTimes(1);
  });

  it("should display correct title and description when categories exist", () => {
    const mockCategories = [
      {
        id: "alberta",
        title: "Alberta",
        description: "1 document available",
      },
    ];

    mockUsePrintables.mockReturnValue({
      categories: mockCategories,
      documents: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    expect(screen.getByText("Printables")).toBeInTheDocument();
    expect(screen.getByText("Printable documents applicable for all provinces")).toBeInTheDocument();
    expect(screen.getByText("Access printable documents organized by province. Each province contains relevant forms and documents for your area.")).toBeInTheDocument();
  });

  it("should use correct button label", () => {
    const mockCategories = [
      {
        id: "alberta",
        title: "Alberta",
        description: "2 documents available",
      },
    ];

    mockUsePrintables.mockReturnValue({
      categories: mockCategories,
      documents: [],
      isLoading: false,
      error: null,
      retry: jest.fn(),
    });

    render(<PrintablesLandingPage />);

    expect(screen.getByText("View Documents")).toBeInTheDocument();
  });
});
