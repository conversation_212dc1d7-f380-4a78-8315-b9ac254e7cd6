"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/shared/ui/card";
import { Label } from "@/shared/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Skeleton } from "@/shared/ui/skeleton";
import { RadioGroup, RadioGroupItem } from "@/shared/ui/radio-group";
import { AlertCircle, Image, QrCode, FileX } from "lucide-react";
import { useQRCodes } from "../hooks/use-qr-codes";
import { usePrintablePreferences } from "../hooks/use-printable-preferences";

export type PrintableOption = "empty" | "photo" | "qrcode";

export interface PrintableOptionsState {
  option: PrintableOption;
  selectedQrId: string | null;
}

interface PrintableOptionsSelectorProps {
  onSelectionChange: (state: PrintableOptionsState) => void;
  className?: string;
}

const PrintableOptionsSelector: React.FC<PrintableOptionsSelectorProps> = ({
  onSelectionChange,
  className,
}) => {
  const { qrCodes, isLoading: qrLoading, error: qrError } = useQRCodes();
  const { preferences, isLoading: prefsLoading, updatePreferences } = usePrintablePreferences();
  
  const [selectedOption, setSelectedOption] = useState<PrintableOption>("empty");
  const [selectedQrId, setSelectedQrId] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debug logging
  console.log("PrintableOptionsSelector render:", {
    qrCodes,
    qrCodesLength: qrCodes?.length,
    qrLoading,
    qrError,
    preferences,
    prefsLoading,
    selectedOption,
    selectedQrId,
    isInitialized,
    isUpdating
  });

  // Initialize state from preferences only once when component first loads
  useEffect(() => {
    console.log("PrintableOptionsSelector: useEffect for initialization", {
      preferences,
      prefsLoading,
      isUpdating,
      isInitialized,
      currentSelectedOption: selectedOption,
      willInitialize: preferences && !prefsLoading && !isUpdating && !isInitialized
    });

    // Only initialize once when preferences are loaded and we haven't initialized yet
    if (preferences && !prefsLoading && !isUpdating && !isInitialized) {
      console.log("PrintableOptionsSelector: Initializing from preferences");

      if (preferences.emptyPrintableFooter) {
        console.log("PrintableOptionsSelector: Initializing to 'empty'");
        setSelectedOption("empty");
        setSelectedQrId(null);
      } else if (preferences.photoOnPrintable) {
        console.log("PrintableOptionsSelector: Initializing to 'photo'");
        setSelectedOption("photo");
        setSelectedQrId(null);
      } else if (preferences.qrCodeOnPrintable) {
        console.log("PrintableOptionsSelector: Initializing to 'qrcode'");
        setSelectedOption("qrcode");
        // Find the last used QR code or use the first one
        const lastUsedQr = qrCodes.find(qr => qr.isLastUsed);
        const defaultQr = lastUsedQr || qrCodes[0];
        if (defaultQr) {
          setSelectedQrId(defaultQr.id);
        }
      }

      setIsInitialized(true);
    }
  }, [preferences, prefsLoading, qrCodes, isUpdating, isInitialized, selectedOption]);

  // Notify parent of changes
  useEffect(() => {
    onSelectionChange({
      option: selectedOption,
      selectedQrId: selectedQrId,
    });
  }, [selectedOption, selectedQrId, onSelectionChange]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const handleOptionChange = async (option: string) => {
    console.log("PrintableOptionsSelector: handleOptionChange called", {
      option,
      currentOption: selectedOption,
      isUpdating,
      prefsLoading
    });

    if (isUpdating || prefsLoading) return;

    // Type guard to ensure option is a valid PrintableOption
    if (option !== "empty" && option !== "photo" && option !== "qrcode") {
      console.error("Invalid option:", option);
      return;
    }

    const printableOption = option as PrintableOption;

    // Don't update if the option is already selected
    if (printableOption === selectedOption) {
      console.log("PrintableOptionsSelector: Option already selected, skipping");
      return;
    }

    const previousOption = selectedOption;
    const previousQrId = selectedQrId;

    setIsUpdating(true);
    console.log("PrintableOptionsSelector: Setting selectedOption to", printableOption);
    setSelectedOption(printableOption);

    // Clear QR selection if not selecting QR code option
    if (printableOption !== "qrcode") {
      setSelectedQrId(null);
    } else if (printableOption === "qrcode" && qrCodes.length > 0) {
      // Auto-select first QR code if none selected
      if (!selectedQrId) {
        const lastUsedQr = qrCodes.find(qr => qr.isLastUsed);
        const defaultQr = lastUsedQr || qrCodes[0];
        setSelectedQrId(defaultQr.qrImage);
      }
    }

    // Update preferences
    const newPreferences = {
      emptyPrintableFooter: printableOption === "empty",
      photoOnPrintable: printableOption === "photo",
      qrCodeOnPrintable: printableOption === "qrcode",
    };

    console.log("PrintableOptionsSelector: Updating preferences", newPreferences);

    try {
      await updatePreferences(newPreferences);
      console.log("PrintableOptionsSelector: Preferences updated successfully");
    } catch (error) {
      console.error("Failed to update preferences:", error);
      // Revert the selection if the update failed
      console.log("PrintableOptionsSelector: Reverting selection due to error");
      setSelectedOption(previousOption);
      setSelectedQrId(previousQrId);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleQrSelection = (qrImage: string) => {
    setSelectedQrId(qrImage);
  };

  const isLoading = qrLoading || prefsLoading || isUpdating;

  if (qrError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load QR codes: {qrError}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <QrCode className="h-5 w-5" />
          Printable Footer Options
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Choose what to display in the footer of your printable documents
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : (
          <>
            {/* Empty Footer Option */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <FileX className="h-5 w-5 text-muted-foreground" />
                <div>
                  <Label htmlFor="empty-option" className="text-base font-medium">
                    No Photo or QR Code (Empty)
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Leave the footer empty
                  </p>
                </div>
              </div>
              <RadioGroup
                value={selectedOption}
                onValueChange={handleOptionChange}
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                <RadioGroupItem value="empty" id="empty-option" />
              </RadioGroup>
            </div>

            {/* Profile Photo Option */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Image className="h-5 w-5 text-muted-foreground" />
                <div>
                  <Label htmlFor="photo-option" className="text-base font-medium">
                    My Profile Photo
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Show your profile photo in the footer
                  </p>
                </div>
              </div>
              <RadioGroup
                value={selectedOption}
                onValueChange={handleOptionChange}
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                <RadioGroupItem value="photo" id="photo-option" />
              </RadioGroup>
            </div>

            {/* QR Code Option - Only show if user has QR codes */}
            {(() => {
              console.log("QR Code conditional rendering check:", {
                qrCodesLength: qrCodes?.length,
                shouldShow: qrCodes?.length > 0,
                qrCodes
              });
              return qrCodes.length > 0 && (
                <div className="space-y-4">
                  <div className="flex flex-col gap-4 w-full p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <QrCode className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <Label htmlFor="qrcode-option" className="text-base font-medium">
                            A QR Code
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            Show a QR code in the footer
                          </p>
                        </div>
                      </div>
                      <RadioGroup
                        value={selectedOption}
                        onValueChange={handleOptionChange}
                        disabled={isLoading}
                        className="flex items-center space-x-2"
                      >
                        <RadioGroupItem value="qrcode" id="qrcode-option" />
                      </RadioGroup>
                    </div>
                    

                    {/* QR Code Selection Dropdown */}
                  {selectedOption === "qrcode" && (
                    <div className="w-full">
                      <Label htmlFor="qr-select" className="text-sm font-medium">
                        Select QR Code:
                      </Label>
                      <Select
                        value={selectedQrId || ""}
                        onValueChange={handleQrSelection}
                        disabled={isLoading}
                      >
                        <SelectTrigger id="qr-select">
                          <SelectValue placeholder="Choose a QR code" />
                        </SelectTrigger>
                        <SelectContent>
                          {qrCodes.map((qr) => (
                            <SelectItem key={qr.id} value={qr.qrImage}>
                              {qr.url}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  </div>                  
                </div>
              );
            })()}

            {/* No QR Codes Message */}
            {(() => {
              console.log("No QR Codes message check:", {
                qrCodesLength: qrCodes?.length,
                selectedOption,
                shouldShow: qrCodes?.length === 0 && selectedOption === "qrcode"
              });
              return qrCodes.length === 0 && selectedOption === "qrcode" && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    You don&apos;t have any QR codes yet. Create some QR codes first to use this option.
                  </AlertDescription>
                </Alert>
              );
            })()}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PrintableOptionsSelector;
