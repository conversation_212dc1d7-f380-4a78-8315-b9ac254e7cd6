"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Skeleton } from "@/shared/ui/skeleton";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { AlertCircle, FileText, ArrowLeft } from "lucide-react";
import { usePrintableDocument } from "../hooks/use-printable-document";
import { type ContactPosition } from "../lib/pdf-utils";
import PdfViewer from "./pdf-viewer";
import { useRouter } from "next/navigation";

interface PrintablesDocumentClientWrapperProps {
  documentId: string;
  title?: string;
  file?: string;
  ext?: string;
  filetype?: string;
  position?: string;
  qrImageUrl?: string;
  category?: string;
}

const PrintablesDocumentClientWrapper: React.FC<PrintablesDocumentClientWrapperProps> = ({
  documentId,
  title: urlTitle,
  file: urlFile,
  position,
  qrImageUrl,
  category
}) => {
  const { document, isLoading, error, retry } = usePrintableDocument(documentId);
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        {/* Back to Printables List Button */}
        <div className="flex items-center">
          <Button
            variant="outline"
            onClick={() => router.push(category ? `/printables/${category}` : "/printables")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Printables List
          </Button>
        </div>

        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-4">
                <Skeleton className="h-6 w-1/3" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
              <div className="space-y-4">
                <Skeleton className="h-6 w-1/3" />
                <div className="space-y-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        {/* Back to Printables List Button */}
        <div className="flex items-center mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(category ? `/printables/${category}` : "/printables")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Printables List
          </Button>
        </div>

        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center text-destructive">
              Error Loading Document
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={retry} variant="outline">
                Try again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="container mx-auto py-6">
        {/* Back to Printables List Button */}
        <div className="flex items-center mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(category ? `/printables/${category}` : "/printables")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Printables List
          </Button>
        </div>

        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Document Not Found</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                The requested document could not be found.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Get the first province file for display purposes
  const firstProvinceFile = document.provinceFile?.[0];
  // Use title from URL params if available, otherwise from document data
  const documentTitle = urlTitle || document.title || "Untitled Document";
  // Use file URL from URL params if available, otherwise from document data
  const documentFile = urlFile || firstProvinceFile?.file?.url;

  // Determine contact position, default to footerLeft if not specified
  const contactPos: ContactPosition = (position as ContactPosition) || "footerLeft";

  // If no PDF file is available, show error
  if (!documentFile) {
    return (
      <div className="container mx-auto py-6">
        {/* Back to Printables List Button */}
        <div className="flex items-center mb-6">
          <Button
            variant="outline"
            onClick={() => router.push(category ? `/printables/${category}` : "/printables")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Printables List
          </Button>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No PDF file is available for this document.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Back to Printables List Button */}
      <div className="flex items-center">
        <Button
          variant="outline"
          onClick={() => router.push(category ? `/printables/${category}` : "/printables")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Printables List
        </Button>
      </div>

      <div className="space-y-4">      
        <h1 className="flex text-3xl font-bold tracking-tight"><FileText className="h-8 w-8 mr-2" />{documentTitle}</h1>        
      </div>

      {/* PDF Viewer with customization options */}
      <PdfViewer
        pdfUrl={documentFile}
        title={documentTitle}
        contactPosition={contactPos}
        qrImageUrl={qrImageUrl}
      />
    </div>
  );
};

export default PrintablesDocumentClientWrapper;
