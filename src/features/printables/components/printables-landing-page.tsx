"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { CategoryLandingPage, CategoryItem } from "@/shared/components";
import { usePrintables } from "../hooks/use-printables";

const PrintablesLandingPage: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = usePrintables();

  const handleCategorySelect = (category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/printables/${category.id}`);
  };

  return (
    <CategoryLandingPage
      title="Printables"
      subtitle="Printable documents applicable for all provinces"
      description="Access printable documents organized by province. Each province contains relevant forms and documents for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No printable categories found"
      retryAction={retry}
    />
  );
};

export default PrintablesLandingPage;
