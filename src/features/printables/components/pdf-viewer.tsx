"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/shared/ui/card";
import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Skeleton } from "@/shared/ui/skeleton";
import { Download, AlertCircle, Loader2 } from "lucide-react";
import { useAuthContext } from "@/shared/contexts/auth-context";

import { modifyPdf, downloadPdf, type UserForPdf, type ContactPosition } from "../lib/pdf-utils";

interface PdfViewerProps {
  pdfUrl: string;
  title: string;
  contactPosition: ContactPosition;
  qrImageUrl?: string | null;
  className?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({
  pdfUrl,
  title,
  contactPosition,
  qrImageUrl,
  className,
}) => {
  const { userAuth } = useAuthContext();
  
  const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [iframeHeight, setIframeHeight] = useState<number>(800);
  
  const viewerRef = useRef<HTMLIFrameElement>(null);

  // Calculate iframe height based on window size
  const calculateIframeHeight = () => {
    if (typeof window !== "undefined") {
      return Math.max(600, (window.innerHeight - 200) * 1.41);
    }
    return 800;
  };



  // Process PDF with user information
  const processPdf = useCallback(async () => {
    if (!userAuth.userInfo || !pdfUrl) return;

    setIsProcessing(true);
    setError(null);
    setValidationErrors([]);

    try {
      // Determine preferences based on qrImageUrl parameter
      const hasQrCode = Boolean(qrImageUrl && qrImageUrl !== "null");
      const usePhoto = !hasQrCode; // Use photo if no QR code is provided
      const useEmpty = false; // We'll handle empty case in the PDF utils

      const userForPdf: UserForPdf = {
        firstname: userAuth.userInfo?.firstname,
        lastname: userAuth.userInfo?.lastname,
        titles: (userAuth.userInfo as any)?.titles, // User type doesn't have titles, but UserForPdf expects it
        position: userAuth.userInfo?.position,
        workEmail: userAuth.userInfo?.workEmail,
        email: userAuth.userInfo?.email,
        workPhone: userAuth.userInfo?.workPhone,
        phone: userAuth.userInfo?.phone,
        website: userAuth.userInfo?.website,
        photo: userAuth.userInfo?.photo,
        circularPhoto: (userAuth.userInfo as any)?.circularPhoto, // User type doesn't have circularPhoto, but UserForPdf expects it
        photoOnPrintable: usePhoto,
        qrCodeOnPrintable: hasQrCode,
        emptyPrintableFooter: useEmpty,
      };

      const result = await modifyPdf(pdfUrl, userForPdf, contactPosition, qrImageUrl);

      if (result) {
        setPdfBlobUrl(result.blobUrl);
      }
    } catch (err) {
      console.error("Error processing PDF:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to process PDF";

      if (errorMessage.includes("Validation failed:")) {
        const validationMessage = errorMessage.replace("Validation failed: ", "");
        setValidationErrors(validationMessage.split(", "));
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsProcessing(false);
    }
  }, [userAuth.userInfo, pdfUrl, contactPosition, qrImageUrl]);



  // Download the processed PDF
  const handleDownload = useCallback(() => {
    if (pdfBlobUrl) {
      const filename = `${title.replace(/\s+/g, "-").toLowerCase()}.pdf`;
      downloadPdf(pdfBlobUrl, filename);
    }
  }, [pdfBlobUrl, title]);

  // Process PDF when component mounts or dependencies change
  useEffect(() => {
    if (userAuth.userInfo && pdfUrl) {
      processPdf();
    }
  }, [processPdf, userAuth.userInfo, pdfUrl]);

  // Update iframe height on window resize
  useEffect(() => {
    const updateHeight = () => {
      setIframeHeight(calculateIframeHeight());
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);



  if (!userAuth.userInfo) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please log in to view and customize printable documents.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>


      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">Please fix the following issues:</p>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="text-sm">{error}</li>
                ))}
              </ul>
              <p className="text-sm">
                You can update your profile information in your account settings.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* PDF Viewer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            
            <div className="flex items-center gap-2">
              
              {pdfBlobUrl && (
                <Button onClick={handleDownload} size="lg">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isProcessing ? (
            <div className="flex items-center justify-center p-8">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Processing PDF...</span>
              </div>
            </div>
          ) : pdfBlobUrl ? (
            <div className="border rounded-lg overflow-hidden">
              <iframe
                ref={viewerRef}
                src={pdfBlobUrl}
                width="100%"
                height={iframeHeight}
                title={`PDF Viewer - ${title}`}
                className="border-0"
              />
            </div>
          ) : (
            <Skeleton className="w-full h-96" />
          )}
        </CardContent>
        <CardFooter>
        {pdfBlobUrl && (
                <Button onClick={handleDownload} size="lg">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default PdfViewer;
