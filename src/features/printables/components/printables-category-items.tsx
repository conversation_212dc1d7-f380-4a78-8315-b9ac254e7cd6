"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList, { CategoryItem } from "@/shared/components/category-items-list";
import { usePrintablesCategory } from "../hooks/use-printables-category";

interface PrintablesCategoryItemsProps {
  category: string;
}

const PrintablesCategoryItems: React.FC<PrintablesCategoryItemsProps> = ({ category }) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = usePrintablesCategory(category);

  const handleItemSelect = (item: CategoryItem) => {
    // Navigate to the document detail page
    const slug = item.title.replace(/\s+/g, "-").toLowerCase();
    const fileUrl = item.file?.url || "";
    const fileExt = item.file?.ext?.replace(".", "") || "";
    router.push(`/printables/document/${slug}?title=${slug}&file=${fileUrl}&ext=${fileExt}`);
  };

  const formatCategoryTitle = (categoryName: string) => {
    return categoryName.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
  };

  // Transform PrintableDocument items to CategoryItem format
  const transformedItems: CategoryItem[] = items.map((item) => {
    // Find the province file for this category
    const provinceFile = item.provinceFile.find((pf) => pf.province === category);

    return {
      id: item.id.toString(), // Convert number to string
      title: item.title,
      description: `Printable document for ${formatCategoryTitle(category).toLowerCase()}`,
      thumbnail: provinceFile?.thumbnail ? {
        url: provinceFile.thumbnail.url,
        alternativeText: provinceFile.thumbnail.alternativeText,
        formats: provinceFile.thumbnail.formats ? {
          thumbnail: provinceFile.thumbnail.formats.squared ? {
            url: provinceFile.thumbnail.formats.squared.url
          } : undefined
        } : undefined
      } : undefined,
      file: provinceFile?.file ? {
        url: provinceFile.file.url,
        ext: provinceFile.file.ext,
        size: provinceFile.file.size
      } : undefined,
      metadata: {
        documentType: item.documentType,
        contactPosition: item.contactPosition,
        province: category,
        documentId: item.documentId,
        publishedAt: item.publishedAt,
      },
      category: category,
    };
  });

  return (
    <CategoryItemsList
      title={`${formatCategoryTitle(category)} Printable Documents`}
      subtitle={`Printable documents applicable for ${category}`}
      items={transformedItems}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Printable"
      emptyStateMessage={`No printable documents found for ${category}`}
      retryAction={retry}
      showSearchAndFilter={true}
      onSearch={(query) => {
        // Implement search functionality
        console.log("Search query:", query);
      }}
      onFilterChange={(filter) => {
        // Implement filter functionality
        console.log("Filter changed:", filter);
      }}
      availableFilters={[category]}
    />
  );
};

export default PrintablesCategoryItems;
