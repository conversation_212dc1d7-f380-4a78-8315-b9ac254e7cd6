"use client";

import React, { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList, { type CategoryItem } from "@/shared/components/category-items-list";
import { PrintableDocument, formatProvinceTitle } from "../lib/api-client";
import { usePrintablesCategory } from "../hooks/use-printables-category";
import PrintableOptionsSelector, { type PrintableOptionsState } from "./printable-options-selector";

interface PrintablesCategoryClientWrapperProps {
  category: string;
}

const PrintablesCategoryClientWrapper: React.FC<PrintablesCategoryClientWrapperProps> = ({ category }) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = usePrintablesCategory(category);

  // State for printable options
  const [printableOptions, setPrintableOptions] = useState<PrintableOptionsState>({
    option: "empty",
    selectedQrId: null,
  });

  // Handle printable options change
  const handleOptionsChange = useCallback((options: PrintableOptionsState) => {
    setPrintableOptions(options);
  }, []);

  const handleItemSelect = React.useCallback((item: CategoryItem) => {
    // Navigate to the document detail page with all necessary parameters
    // The item is a CategoryItem, but we stored the original PrintableDocument in metadata
    const originalDocument = item.metadata?.originalDocument as PrintableDocument;
    const slug = item.metadata?.slug || originalDocument?.slug;
    
    if (!originalDocument) {
      console.error('Original document data not found for item:', item);
      return;
    }
    
    if (!slug) {
      console.error('Document slug not found for item:', originalDocument);
      return;
    }
    
    // Safely find the province file with proper null checks
    const provinceFile = originalDocument.provinceFile && Array.isArray(originalDocument.provinceFile) 
      ? originalDocument.provinceFile.find((pf) => pf.province === category)
      : undefined;
    
    if (provinceFile?.file) {
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        file: provinceFile.file.url,
        ext: provinceFile.file.ext.replace('.', ''),
        filetype: provinceFile.file.ext,
        position: originalDocument.contactPosition || 'unknown',
        // Pass the selected QR code image URL from the options
        qrImageUrl: printableOptions.selectedQrId || 'null',
        // Pass the current category for back navigation
        category: category
      });
      
      router.push(`/printables/document/${slug}?${queryParams.toString()}`);
    } else {
      // Fallback if no file is available
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        position: originalDocument.contactPosition || 'unknown',
        qrImageUrl: printableOptions.selectedQrId || 'null',
        // Pass the current category for back navigation
        category: category
      });
      
      // Add file info if available from the first provinceFile entry
      if (originalDocument.provinceFile && Array.isArray(originalDocument.provinceFile) && originalDocument.provinceFile.length > 0) {
        const firstProvinceFile = originalDocument.provinceFile[0];
        if (firstProvinceFile?.file) {
          queryParams.append('file', firstProvinceFile.file.url);
          queryParams.append('ext', firstProvinceFile.file.ext.replace('.', ''));
          queryParams.append('filetype', firstProvinceFile.file.ext);
        }
      }
      
      router.push(`/printables/document/${slug}?${queryParams.toString()}`);
    }
  }, [router, category, printableOptions]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  const handleSearch = React.useCallback((query: string) => {
    // Implement search functionality
    console.log("Search query:", query);
  }, []);

  const handleFilterChange = React.useCallback((filter: string) => {
    // Implement filter functionality
    console.log("Filter changed:", filter);
  }, []);

  const formattedTitle = formatProvinceTitle(category);

  // Transform items to include thumbnail and file information from the selected province
  const transformedItems = items.map((item) => {
    // Safely find the provinceFile entry for the current category with null checks
    const provinceFile = item.provinceFile && Array.isArray(item.provinceFile) 
      ? item.provinceFile.find((pf) => pf.province === category)
      : undefined;
    
    return {
      id: item.id.toString(),
      title: item.title,
      description: `Document ID: ${item.documentId}`,
      thumbnail: provinceFile?.thumbnail ? {
        url: provinceFile.thumbnail.url,
        alternativeText: provinceFile.thumbnail.alternativeText,
        formats: {
          thumbnail: {
            url: provinceFile.thumbnail.formats?.small?.url || provinceFile.thumbnail.url
          }
        }
      } : undefined,
      file: provinceFile?.file ? {
        url: provinceFile.file.url,
        ext: provinceFile.file.ext,
        size: provinceFile.file.size
      } : undefined,
      metadata: {
        slug: item.slug,
        documentId: item.documentId,
        province: category,
        createdAt: item.createdAt,
        originalDocument: item, // Store the original PrintableDocument
      },
      category: formattedTitle,
    };
  });

  return (
    <div className="grid grid-cols-1 lg:grid-cols-8 xl:grid-cols-4 gap-6 space-y-6">      

      {/* Printable Options Selector */}
      <PrintableOptionsSelector
        onSelectionChange={handleOptionsChange}
        className="max-w-2xl lg:col-span-3 xl:col-span-1"
      />

      <div className="col-span-3 lg:col-span-5 xl:col-span-3">
      {/* Category Items List */}
      <CategoryItemsList
        title={formattedTitle}
        subtitle={`Printable documents for ${formattedTitle.toLowerCase()}`}
        items={transformedItems}
        isLoading={isLoading}
        error={error}
        onItemSelect={handleItemSelect}
        buttonLabel="View Document"
        emptyStateMessage={`No printable documents found for ${formattedTitle.toLowerCase()}`}
        retryAction={handleRetry}
        showSearchAndFilter={true}
        onSearch={handleSearch}
        onFilterChange={handleFilterChange}
        availableFilters={[formattedTitle]}
      />
      </div>
    </div>
  );
};

export default PrintablesCategoryClientWrapper;
