/**
 * Printables Feature - Public API
 *
 * This file exports all public components, hooks, and utilities
 * from the printables feature following the feature-sliced pattern.
 */

// Components
export { default as PrintablesLandingPage } from "./components/printables-landing-page";
export { default as PrintablesCategoryItems } from "./components/printables-category-items";
export { default as PrintablesClientWrapper } from "./components/printables-client-wrapper";
export { default as PrintablesCategoryClientWrapper } from "./components/printables-category-client-wrapper";
export { default as PrintablesDocumentClientWrapper } from "./components/printables-document-client-wrapper";
export { default as PdfViewer } from "./components/pdf-viewer";

// Hooks
export { usePrintables } from "./hooks/use-printables";
export { usePrintablesCategory } from "./hooks/use-printables-category";
export { usePrintableDocument } from "./hooks/use-printable-document";
export { usePrintablePreferences } from "./hooks/use-printable-preferences";

// Types
export type * from "./types";

// Utilities
export * from "./lib/pdf-utils";

// API Client
export { 
  getPrintables, 
  getPrintablesByProvince, 
  getPrintableProvinces, 
  getPrintableDocument,
  transformDocumentsToCategories,
  formatProvinceTitle
} from "./lib/api-client";
