import { apiClient } from "@/shared/lib/api";

export interface ProvinceFile {
  id: number;
  province: string;
  thumbnail: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    formats: {
      small?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        size: number;
        width: number;
      };
      medium?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        size: number;
        width: number;
      };
      squared?: {
        ext: string;
        url: string;
        hash: string;
        mime: string;
        name: string;
        size: number;
        width: number;
        height: number;
      };
    };
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: Record<string, unknown>;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
  file: {
    id: number;
    name: string;
    alternativeText: string;
    caption: string;
    formats: Record<string, unknown>[];
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: Record<string, unknown>;
    createdAt: string;
    updatedAt: string;
    documentId: string;
    publishedAt: string;
  };
}

export interface PrintableDocument {
  id: number;
  title: string;
  slug: string;
  documentType: string;
  contactPosition: string;
  isHidden: boolean;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  documentId: string;
  provinceFile: ProvinceFile[];
}

export interface PrintablesResponse {
  data: PrintableDocument[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface PrintableDocumentResponse {
  data: PrintableDocument;
}

/**
 * API client for printables operations
 */
export class PrintablesApiClient {
  private static readonly BASE_PATH = "/docs";

  /**
   * Get all printable documents
   * Filters by documentType=printable and populates provinceFile data
   */
  static async getPrintables(params?: {
    page?: number;
    pageSize?: number;
  }): Promise<{ data: PrintableDocument[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      
      // Filter by document type
      queryParams.append("filters[documentType][$eq]", "printable");
      
      // Filter by hidden status
      queryParams.append("filters[isHidden][$eq]", "false");
      
      // Populate provinceFile data
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");
      
      // Pagination
      if (params?.page) {
        queryParams.append("pagination[page]", params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append("pagination[pageSize]", params.pageSize.toString());
      }

      const url = `${PrintablesApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<PrintablesResponse>(url);

      if (response.success && response.data) {
        return { data: response.data.data || [] };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch printables",
      };
    } catch (error) {
      console.error("Error fetching printables:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch printables",
      };
    }
  }

  /**
   * Get a specific printable document by documentId
   */
  static async getPrintableDocument(
    documentId: string
  ): Promise<{ data?: PrintableDocument; error?: string }> {
    try {
      if (!documentId || documentId.trim() === '') {
        return { error: "Invalid document ID" };
      }

      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentId][$eq]", documentId);
      queryParams.append("filters[documentType][$eq]", "printable");
      queryParams.append("populate[0]", "provinceFile");
      queryParams.append("populate[1]", "provinceFile.file");
      queryParams.append("populate[2]", "provinceFile.thumbnail");

      const response = await apiClient.get<PrintablesResponse>(
        `${PrintablesApiClient.BASE_PATH}?${queryParams.toString()}`
      );

      if (response.success && response.data && response.data.data.length > 0) {
        return { data: response.data.data[0] };
      }

      return { error: "Printable document not found" };
    } catch (error) {
      console.error("Error fetching printable document:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch printable document",
      };
    }
  }

  /**
   * Get printables by specific province
   */
  static async getPrintablesByProvince(
    province: string
  ): Promise<{ data: PrintableDocument[]; error?: string }> {
    try {
      // Validate province parameter
      if (!province || province.trim() === '') {
        return {
          data: [],
          error: "Invalid province parameter",
        };
      }

      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentType][$eq]", "printable");
      queryParams.append("filters[isHidden][$eq]", "false");
      
      // If requesting "allProvinces", we need to handle it specially since null values should be included
      if (province === "allProvinces") {
        // For allProvinces, we'll fetch all printables and filter on the client side
        // to include documents with null/empty province values
        queryParams.append("populate[0]", "provinceFile");
        queryParams.append("populate[1]", "provinceFile.file");
        queryParams.append("populate[2]", "provinceFile.thumbnail");
      } else {
        // For specific provinces, filter by the province
        queryParams.append("filters[provinceFile][province][$eq]", province);
        queryParams.append("populate[0]", "provinceFile");
        queryParams.append("populate[1]", "provinceFile.file");
        queryParams.append("populate[2]", "provinceFile.thumbnail");
      }

      const url = `${PrintablesApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<PrintablesResponse>(url);

      if (response.success && response.data) {
        let filteredData = response.data.data;
        
        // If requesting "allProvinces", include documents with null/empty province values
        if (province === "allProvinces") {
          filteredData = response.data.data.filter((doc) => {
            return doc.provinceFile && Array.isArray(doc.provinceFile) && 
                   doc.provinceFile.some((pf) => {
                     const pfProvince = pf.province;
                     // Include if province is "allProvinces" or null/empty
                     return pfProvince === "allProvinces" || !pfProvince || pfProvince.trim() === '';
                   });
          });
        } else {
          // For specific provinces, filter to ensure they have the requested province
          filteredData = response.data.data.filter((doc) => {
            return doc.provinceFile && Array.isArray(doc.provinceFile) && 
                   doc.provinceFile.some((pf) => pf.province === province);
          });
        }

        return { data: filteredData };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch printables by province",
      };
    } catch (error) {
      console.error("Error fetching printables by province:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch printables by province",
      };
    }
  }

  /**
   * Get all available provinces for printables
   */
  static async getPrintableProvinces(): Promise<{ data: string[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("filters[documentType][$eq]", "printable");
      queryParams.append("filters[isHidden][$eq]", "false");
      queryParams.append("populate[0]", "provinceFile");

      const url = `${PrintablesApiClient.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<PrintablesResponse>(url);

      if (response.success && response.data) {
        // Extract unique provinces from the documents
        const provinces = new Set<string>();
        response.data.data.forEach((doc) => {
          if (doc.provinceFile && Array.isArray(doc.provinceFile)) {
            doc.provinceFile.forEach((provinceFile) => {
              // If province is null, undefined, or empty, treat it as "allProvinces"
              let province = provinceFile.province;
              if (!province || province.trim() === '') {
                province = "allProvinces";
              }
              provinces.add(province);
            });
          }
        });

        return { data: Array.from(provinces) };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch printable provinces",
      };
    } catch (error) {
      console.error("Error fetching printable provinces:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch printable provinces",
      };
    }
  }

  /**
   * Transform documents to categories for the landing page
   * Groups by province from provinceFile array
   */
  static transformDocumentsToCategories(
    documents: PrintableDocument[]
  ): Array<{
    id: string;
    title: string;
    description: string;
    documentCount: number;
    province: string;
  }> {
    const provinceMap = new Map<string, PrintableDocument[]>();
    
    documents.forEach((doc) => {
      if (doc.provinceFile && Array.isArray(doc.provinceFile)) {
        doc.provinceFile.forEach((provinceFile) => {
          // If province is null, undefined, or empty, treat it as "allProvinces"
          let province = provinceFile.province;
          if (!province || province.trim() === '') {
            province = "allProvinces";
          }
          
          if (!provinceMap.has(province)) {
            provinceMap.set(province, []);
          }
          provinceMap.get(province)!.push(doc);
        });
      }
    });

    return Array.from(provinceMap.entries()).map(([province, docs]) => ({
      id: province,
      title: PrintablesApiClient.formatProvinceTitle(province),
      description: `${docs.length} document${docs.length !== 1 ? "s" : ""} available`,
      documentCount: docs.length,
      province,
    }));
  }

  /**
   * Format province name to readable title
   */
  static formatProvinceTitle(province: string | null | undefined): string {
    // Handle null, undefined, or empty values
    if (!province || province.trim() === '') {
      return "Unknown Province";
    }

    switch (province) {
      case "allProvinces":
        return "All Provinces";
      case "alberta":
        return "Alberta";
      case "manitoba":
        return "Manitoba";
      case "ontario":
        return "Ontario";
      case "quebec":
        return "Quebec";
      case "britishColumbia":
        return "British Columbia";
      case "saskatchewan":
        return "Saskatchewan";
      case "novaScotia":
        return "Nova Scotia";
      case "newBrunswick":
        return "New Brunswick";
      case "newfoundland":
        return "Newfoundland";
      case "pei":
        return "Prince Edward Island";
      case "northwestTerritories":
        return "Northwest Territories";
      case "nunavut":
        return "Nunavut";
      case "yukon":
        return "Yukon";
      default:
        return province.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
    }
  }
}

// Export convenience functions
export const {
  getPrintables,
  getPrintableDocument,
  getPrintablesByProvince,
  getPrintableProvinces,
  transformDocumentsToCategories,
  formatProvinceTitle,
} = PrintablesApiClient;
