import {
  getPrintables,
  getPrintableDocument,
  getPrintablesByProvince,
  getPrintableProvinces,
  transformDocumentsToCategories,
  formatProvinceTitle,
} from "../api-client";
import { apiClient } from "@/shared/lib/api";

// Mock the API client
jest.mock("@/shared/lib/api", () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient.get as jest.MockedFunction<typeof apiClient.get>;

describe("Printables API Client", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getPrintables", () => {
    it("should fetch printables successfully", async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [
            {
              id: 1,
              title: "Test Document",
              slug: "test-document",
              documentType: "printable",
              contactPosition: "footerLeft",
              isHidden: false,
              createdAt: "2023-01-01",
              updatedAt: "2023-01-01",
              publishedAt: "2023-01-01",
              documentId: "doc-1",
              provinceFile: [],
            },
          ],
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintables();

      expect(result.data).toEqual(mockResponse.data.data);
      expect(result.error).toBeUndefined();
      expect(mockApiClient).toHaveBeenCalledWith(
        expect.stringContaining("/docs?")
      );
    });

    it("should handle API error", async () => {
      const mockResponse = {
        success: false,
        error: "API Error",
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintables();

      expect(result.data).toEqual([]);
      expect(result.error).toBe("API Error");
    });

    it("should handle network error", async () => {
      const networkError = new Error("Network error");
      mockApiClient.mockRejectedValue(networkError);

      const result = await getPrintables();

      expect(result.data).toEqual([]);
      expect(result.error).toBe("Network error");
    });

    it("should include pagination parameters", async () => {
      mockApiClient.mockResolvedValue({ success: true, data: { data: [] } });

      await getPrintables({ page: 2, pageSize: 10 });

      expect(mockApiClient).toHaveBeenCalledWith(
        expect.stringContaining("pagination%5Bpage%5D=2")
      );
      expect(mockApiClient).toHaveBeenCalledWith(
        expect.stringContaining("pagination%5BpageSize%5D=10")
      );
    });
  });

  describe("getPrintableDocument", () => {
    it("should fetch document successfully", async () => {
      const mockDocument = {
        id: 1,
        title: "Test Document",
        documentId: "doc-1",
        provinceFile: [],
      };

      const mockResponse = {
        success: true,
        data: {
          data: [mockDocument],
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintableDocument("doc-1");

      expect(result.data).toEqual(mockDocument);
      expect(result.error).toBeUndefined();
    });

    it("should handle invalid document ID", async () => {
      const result = await getPrintableDocument("");

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Invalid document ID");
      expect(mockApiClient).not.toHaveBeenCalled();
    });

    it("should handle document not found", async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [],
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintableDocument("doc-1");

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Printable document not found");
    });
  });

  describe("getPrintablesByProvince", () => {
    it("should fetch printables by province successfully", async () => {
      const mockDocuments = [
        {
          id: 1,
          title: "Alberta Document",
          provinceFile: [{ province: "alberta" }],
        },
      ];

      const mockResponse = {
        success: true,
        data: {
          data: mockDocuments,
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintablesByProvince("alberta");

      expect(result.data).toEqual(mockDocuments);
      expect(result.error).toBeUndefined();
    });

    it("should handle invalid province parameter", async () => {
      const result = await getPrintablesByProvince("");

      expect(result.data).toEqual([]);
      expect(result.error).toBe("Invalid province parameter");
      expect(mockApiClient).not.toHaveBeenCalled();
    });
  });

  describe("getPrintableProvinces", () => {
    it("should extract unique provinces", async () => {
      const mockDocuments = [
        {
          id: 1,
          provinceFile: [
            { province: "alberta" },
            { province: "ontario" },
          ],
        },
        {
          id: 2,
          provinceFile: [
            { province: "alberta" },
            { province: "bc" },
          ],
        },
      ];

      const mockResponse = {
        success: true,
        data: {
          data: mockDocuments,
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintableProvinces();

      expect(result.data).toEqual(expect.arrayContaining(["alberta", "ontario", "bc"]));
      expect(result.error).toBeUndefined();
    });

    it("should handle empty province as allProvinces", async () => {
      const mockDocuments = [
        {
          id: 1,
          provinceFile: [
            { province: "" },
            { province: null },
            { province: "alberta" },
          ],
        },
      ];

      const mockResponse = {
        success: true,
        data: {
          data: mockDocuments,
        },
      };

      mockApiClient.mockResolvedValue(mockResponse);

      const result = await getPrintableProvinces();

      expect(result.data).toEqual(expect.arrayContaining(["allProvinces", "alberta"]));
    });
  });

  describe("transformDocumentsToCategories", () => {
    it("should transform documents to categories", () => {
      const mockDocuments = [
        {
          id: 1,
          title: "Alberta Doc 1",
          provinceFile: [{ province: "alberta" }],
        },
        {
          id: 2,
          title: "Alberta Doc 2",
          provinceFile: [{ province: "alberta" }],
        },
        {
          id: 3,
          title: "Ontario Doc",
          provinceFile: [{ province: "ontario" }],
        },
      ] as any[];

      const result = transformDocumentsToCategories(mockDocuments);

      expect(result).toHaveLength(2);
      expect(result).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: "alberta",
            title: "Alberta",
            documentCount: 2,
            province: "alberta",
          }),
          expect.objectContaining({
            id: "ontario",
            title: "Ontario",
            documentCount: 1,
            province: "ontario",
          }),
        ])
      );
    });

    it("should handle empty province as allProvinces", () => {
      const mockDocuments = [
        {
          id: 1,
          title: "All Provinces Doc",
          provinceFile: [{ province: "" }],
        },
      ] as any[];

      const result = transformDocumentsToCategories(mockDocuments);

      expect(result).toEqual([
        expect.objectContaining({
          id: "allProvinces",
          title: "All Provinces",
          province: "allProvinces",
        }),
      ]);
    });
  });

  describe("formatProvinceTitle", () => {
    it("should format province titles correctly", () => {
      expect(formatProvinceTitle("alberta")).toBe("Alberta");
      expect(formatProvinceTitle("ontario")).toBe("Ontario");
      expect(formatProvinceTitle("britishColumbia")).toBe("British Columbia");
      expect(formatProvinceTitle("newBrunswick")).toBe("New Brunswick");
      expect(formatProvinceTitle("allProvinces")).toBe("All Provinces");
      expect(formatProvinceTitle("unknown")).toBe("Unknown");
    });
  });
});
