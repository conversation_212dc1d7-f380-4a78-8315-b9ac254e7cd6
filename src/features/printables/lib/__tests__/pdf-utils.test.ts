import {
  formatPhone,
  transformSiteURL,
  validateRequiredData,
  getFileExtension,
  UserForPdf,
  ContactPosition,
} from "../pdf-utils";

describe("PDF Utils", () => {
  describe("formatPhone", () => {
    it("should format 10-digit phone numbers", () => {
      expect(formatPhone("1234567890")).toBe("************");
      expect(formatPhone("(*************")).toBe("************");
      expect(formatPhone("************")).toBe("************");
    });

    it("should handle phone numbers with more than 10 digits", () => {
      expect(formatPhone("11234567890")).toBe("************");
    });

    it("should return original string for invalid phone numbers", () => {
      expect(formatPhone("123")).toBe("123");
      expect(formatPhone("abc")).toBe("abc");
    });

    it("should handle empty or undefined input", () => {
      expect(formatPhone("")).toBe("");
      expect(formatPhone(undefined)).toBe("");
    });
  });

  describe("transformSiteURL", () => {
    it("should remove protocol and www", () => {
      expect(transformSiteURL("https://www.example.com")).toBe("example.com");
      expect(transformSiteURL("http://www.example.com")).toBe("example.com");
      expect(transformSiteURL("https://example.com")).toBe("example.com");
      expect(transformSiteURL("http://example.com")).toBe("example.com");
      expect(transformSiteURL("www.example.com")).toBe("example.com");
    });

    it("should handle URLs without protocol or www", () => {
      expect(transformSiteURL("example.com")).toBe("example.com");
      expect(transformSiteURL("subdomain.example.com")).toBe("subdomain.example.com");
    });
  });

  describe("getFileExtension", () => {
    it("should extract file extensions from URLs", () => {
      expect(getFileExtension("https://example.com/file.pdf")).toBe(".pdf");
      expect(getFileExtension("https://example.com/image.jpg")).toBe(".jpg");
      expect(getFileExtension("https://example.com/document.docx")).toBe(".docx");
    });

    it("should handle URLs with query parameters", () => {
      expect(getFileExtension("https://example.com/file.pdf?version=1")).toBe(".pdf");
      expect(getFileExtension("https://example.com/file.jpg#section")).toBe(".jpg");
    });

    it("should return default extension for URLs without extension", () => {
      expect(getFileExtension("https://example.com/file")).toBe(".jpg");
      expect(getFileExtension("https://example.com/file", ".png")).toBe(".png");
    });

    it("should handle empty or invalid URLs", () => {
      expect(getFileExtension("")).toBe(".jpg");
      expect(getFileExtension("", ".png")).toBe(".png");
    });
  });

  describe("validateRequiredData", () => {
    const mockUser: UserForPdf = {
      firstname: "John",
      lastname: "Doe",
      email: "<EMAIL>",
      photo: {
        url: "https://example.com/photo.jpg",
      },
      photoOnPrintable: false,
      qrCodeOnPrintable: false,
      emptyPrintableFooter: true,
    };

    it("should return no errors for valid user data", () => {
      const errors = validateRequiredData(mockUser, "footerLeft");
      expect(errors).toEqual([]);
    });

    it("should require first and last name", () => {
      const userWithoutName: UserForPdf = {
        ...mockUser,
        firstname: "",
        lastname: "",
      };

      const errors = validateRequiredData(userWithoutName, "footerLeft");
      expect(errors).toContain("Your first and last name are required for the document footer");
    });

    it("should require photo when photo option is enabled", () => {
      const userWithPhotoOption: UserForPdf = {
        ...mockUser,
        photoOnPrintable: true,
        photo: undefined,
      };

      const errors = validateRequiredData(userWithPhotoOption, "footerLeft");
      expect(errors).toContain("You've selected to show your photo, but no photo is available in your profile");
    });

    it("should require photo URL when photo option is enabled", () => {
      const userWithEmptyPhoto: UserForPdf = {
        ...mockUser,
        photoOnPrintable: true,
        photo: {},
      };

      const errors = validateRequiredData(userWithEmptyPhoto, "footerLeft");
      expect(errors).toContain("You've selected to show your photo, but no photo is available in your profile");
    });

    it("should not require photo for noContactInfo position", () => {
      const userWithPhotoOption: UserForPdf = {
        ...mockUser,
        photoOnPrintable: true,
        photo: undefined,
      };

      const errors = validateRequiredData(userWithPhotoOption, "noContactInfo");
      expect(errors).not.toContain("You've selected to show your photo, but no photo is available in your profile");
    });

    it("should accept photo with formats but no URL", () => {
      const userWithFormats: UserForPdf = {
        ...mockUser,
        photoOnPrintable: true,
        photo: {
          formats: {
            small: { url: "https://example.com/small.jpg" },
          },
        },
      };

      const errors = validateRequiredData(userWithFormats, "footerLeft");
      expect(errors).not.toContain("You've selected to show your photo, but no photo is available in your profile");
    });

    it("should return multiple errors when multiple validations fail", () => {
      const invalidUser: UserForPdf = {
        firstname: "",
        lastname: "",
        photoOnPrintable: true,
        photo: undefined,
      };

      const errors = validateRequiredData(invalidUser, "footerLeft");
      expect(errors).toHaveLength(2);
      expect(errors).toContain("Your first and last name are required for the document footer");
      expect(errors).toContain("You've selected to show your photo, but no photo is available in your profile");
    });
  });
});
