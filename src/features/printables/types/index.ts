// Re-export types from the API client
export type {
  PrintableDocument,
  PrintablesResponse,
  PrintableDocumentResponse,
  ProvinceFile,
} from "../lib/api-client";

// Re-export types from hooks
export type {
  UsePrintablesResult,
} from "../hooks/use-printables";

export type {
  UsePrintablesCategoryResult,
} from "../hooks/use-printables-category";

export type {
  UsePrintableDocumentResult,
} from "../hooks/use-printable-document";

export type {
  PrintablePreferences,
  UsePrintablePreferencesResult,
} from "../hooks/use-printable-preferences";

export type {
  QRCode,
  UseQRCodesReturn,
} from "../hooks/use-qr-codes";

// Re-export types from PDF utilities
export type {
  UserForPdf,
  ContactPosition,
} from "../lib/pdf-utils";

// Re-export types from components
export type {
  PrintableOption,
  PrintableOptionsState,
} from "../components/printable-options-selector";
