"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { env } from "@/shared/lib/env";
import { getCookie } from "@/shared/lib/auth";

export interface QRCode {
  id: string;
  url: string;
  qrImage: string;
  isLastUsed?: boolean;
}

export interface UseQRCodesReturn {
  qrCodes: QRCode[];
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useQRCodes(): UseQRCodesReturn {
  const { userAuth } = useAuthContext();
  const [qrCodes, setQrCodes] = useState<QRCode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchQRCodes = useCallback(async () => {
    const token = getCookie("jwt");
    
    if (!userAuth.userInfo?.id || !token) {
      console.log("useQRCodes: Missing user info or token", { 
        userId: userAuth.userInfo?.id, 
        hasToken: !!token 
      });
      setQrCodes([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("useQRCodes: Fetching QR codes for user:", userAuth.userInfo.id);
      
      // Use the endpoint that returns full user data including qrCodes
      // Based on the API response you provided, this endpoint returns the complete user object
      const response = await fetch(`${env.API_URL}/api/users/${userAuth.userInfo.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch QR codes: ${response.status}`);
      }

      const userData = await response.json();
      console.log("useQRCodes: Raw user data response:", userData);
      
      // Extract user data from the response (handle both .data and direct response)
      const actualUserData = userData?.data || userData;
      console.log("useQRCodes: Actual user data:", actualUserData);
      
      const userQrCodes = actualUserData?.qrCodes || [];
      console.log("useQRCodes: Extracted QR codes:", userQrCodes);
      
      setQrCodes(userQrCodes);
    } catch (err) {
      console.error("useQRCodes: Error fetching QR codes:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch QR codes");
      setQrCodes([]);
    } finally {
      setIsLoading(false);
    }
  }, [userAuth.userInfo?.id]);

  const refetch = () => {
    fetchQRCodes();
  };

  useEffect(() => {
    console.log("useQRCodes: useEffect triggered", {
      userId: userAuth.userInfo?.id,
      hasToken: !!getCookie("jwt"),
      initialized: userAuth.initialized
    });
    fetchQRCodes();
  }, [userAuth.userInfo?.id, userAuth.initialized, fetchQRCodes]);

  return {
    qrCodes,
    isLoading,
    error,
    refetch,
  };
}
