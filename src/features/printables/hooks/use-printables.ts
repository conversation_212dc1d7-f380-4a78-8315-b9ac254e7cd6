"use client";

import { useState, useEffect } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { PrintableDocument, getPrintables, transformDocumentsToCategories } from "../lib/api-client";

export interface UsePrintablesResult {
  categories: CategoryItem[];
  documents: PrintableDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function usePrintables(): UsePrintablesResult {
  const [documents, setDocuments] = useState<PrintableDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPrintables = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await getPrintables();
      
      if (response.error) {
        setError(response.error);
        setDocuments([]);
      } else {
        setDocuments(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch printables");
      setDocuments([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPrintables();
  }, []);

  const retry = () => {
    fetchPrintables();
  };

  // Transform documents to categories using the API client utility
  const categories = transformDocumentsToCategories(documents).map((category) => ({
    id: category.id,
    title: category.title,
    description: category.description,
    metadata: { province: category.province, documentCount: category.documentCount },
  }));

  return {
    categories,
    documents,
    isLoading,
    error,
    retry,
  };
}
