import { renderHook, waitFor } from "@testing-library/react";
import { useQRCodes } from "../use-qr-codes";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getCookie } from "@/shared/lib/auth";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetCookie = getCookie as jest.MockedFunction<typeof getCookie>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe("useQRCodes", () => {
  const mockUserAuth = {
    userInfo: {
      id: "user-1",
      name: "Test User",
      email: "<EMAIL>",
      role: "user",
    },
    isAuth: true,
    initialized: true,
    notifications: [],
    onePages: [],
    error: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuthContext.mockReturnValue({
      userAuth: mockUserAuth,
      setUserAuth: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
      refreshUserData: jest.fn(),
    });
    mockGetCookie.mockReturnValue("mock-jwt-token");
  });

  it("should initialize with empty QR codes", () => {
    mockFetch.mockImplementation(() => new Promise(() => {})); // Never resolves

    const { result } = renderHook(() => useQRCodes());

    expect(result.current.qrCodes).toEqual([]);
    expect(result.current.isLoading).toBe(true); // Should be loading initially
    expect(result.current.error).toBe(null);
  });

  it("should fetch QR codes successfully", async () => {
    const mockQRCodes = [
      {
        id: "qr-1",
        url: "https://example.com",
        qrImage: "data:image/png;base64,mock-qr-image",
        isLastUsed: true,
      },
      {
        id: "qr-2",
        url: "https://example2.com",
        qrImage: "data:image/png;base64,mock-qr-image-2",
        isLastUsed: false,
      },
    ];

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        data: {
          qrCodes: mockQRCodes,
        },
      }),
    } as Response);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.qrCodes).toEqual(mockQRCodes);
    });

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(mockFetch).toHaveBeenCalledWith(
      "http://localhost:1339/api/users/user-1",
      expect.objectContaining({
        headers: {
          Authorization: "Bearer mock-jwt-token",
          "Content-Type": "application/json",
        },
      })
    );
  });

  it("should handle direct response format", async () => {
    const mockQRCodes = [
      {
        id: "qr-1",
        url: "https://example.com",
        qrImage: "data:image/png;base64,mock-qr-image",
      },
    ];

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        qrCodes: mockQRCodes,
      }),
    } as Response);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.qrCodes).toEqual(mockQRCodes);
    });

    expect(result.current.error).toBe(null);
  });

  it("should handle missing QR codes in response", async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({}),
    } as Response);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.qrCodes).toEqual([]);
    });

    expect(result.current.error).toBe(null);
  });

  it("should handle API error", async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
      statusText: "Not Found",
    } as Response);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.error).toBe("Failed to fetch QR codes: 404");
    });

    expect(result.current.qrCodes).toEqual([]);
  });

  it("should handle network error", async () => {
    const networkError = new Error("Network error");
    mockFetch.mockRejectedValueOnce(networkError);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.error).toBe("Network error");
    });

    expect(result.current.qrCodes).toEqual([]);
  });

  it("should not fetch when user is not authenticated", () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: { ...mockUserAuth, userInfo: null, isAuth: false },
      setUserAuth: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
      refreshUserData: jest.fn(),
    });

    const { result } = renderHook(() => useQRCodes());

    expect(result.current.qrCodes).toEqual([]);
    expect(mockFetch).not.toHaveBeenCalled();
  });

  it("should not fetch when no JWT token", () => {
    mockGetCookie.mockReturnValue(null);

    const { result } = renderHook(() => useQRCodes());

    expect(result.current.qrCodes).toEqual([]);
    expect(mockFetch).not.toHaveBeenCalled();
  });

  it("should refetch data", async () => {
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ qrCodes: [] }),
    } as Response);

    const { result } = renderHook(() => useQRCodes());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear the mock to verify refetch calls
    mockFetch.mockClear();

    // Call refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });
});
