import { renderHook, waitFor } from "@testing-library/react";
import { usePrintableDocument } from "../use-printable-document";
import { getPrintableDocument } from "../../lib/api-client";

// Mock the API client
jest.mock("../../lib/api-client", () => ({
  getPrintableDocument: jest.fn(),
}));

const mockGetPrintableDocument = getPrintableDocument as jest.MockedFunction<typeof getPrintableDocument>;

describe("usePrintableDocument", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should initialize with loading state", () => {
    mockGetPrintableDocument.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    const { result } = renderHook(() => usePrintableDocument("doc-1"));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.document).toBeUndefined();
    expect(result.current.error).toBe(null);
  });

  it("should fetch document successfully", async () => {
    const mockDocument = {
      id: 1,
      title: "Test Document",
      slug: "test-document",
      documentType: "printable",
      contactPosition: "footerLeft",
      isHidden: false,
      createdAt: "2023-01-01",
      updatedAt: "2023-01-01",
      publishedAt: "2023-01-01",
      documentId: "doc-1",
      provinceFile: [],
    };

    mockGetPrintableDocument.mockResolvedValue({ data: mockDocument });

    const { result } = renderHook(() => usePrintableDocument("doc-1"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.document).toEqual(mockDocument);
    expect(result.current.error).toBe(null);
    expect(mockGetPrintableDocument).toHaveBeenCalledWith("doc-1");
  });

  it("should handle API error", async () => {
    const errorMessage = "Document not found";
    mockGetPrintableDocument.mockResolvedValue({ error: errorMessage });

    const { result } = renderHook(() => usePrintableDocument("doc-1"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.document).toBeUndefined();
  });

  it("should handle network error", async () => {
    const networkError = new Error("Network error");
    mockGetPrintableDocument.mockRejectedValue(networkError);

    const { result } = renderHook(() => usePrintableDocument("doc-1"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe("Network error");
    expect(result.current.document).toBeUndefined();
  });

  it("should handle invalid document ID", () => {
    const { result } = renderHook(() => usePrintableDocument(""));

    // When documentId is empty, useEffect doesn't run, so loading stays true
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.document).toBeUndefined();
    expect(mockGetPrintableDocument).not.toHaveBeenCalled();
  });

  it("should handle undefined document ID", async () => {
    const { result } = renderHook(() => usePrintableDocument("undefined"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe("Invalid document ID");
    expect(result.current.document).toBeUndefined();
    expect(mockGetPrintableDocument).not.toHaveBeenCalled();
  });

  it("should refetch when document ID changes", async () => {
    mockGetPrintableDocument.mockResolvedValue({ data: {} as any });

    const { result, rerender } = renderHook(
      ({ documentId }) => usePrintableDocument(documentId),
      { initialProps: { documentId: "doc-1" } }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockGetPrintableDocument).toHaveBeenCalledWith("doc-1");

    // Change document ID
    rerender({ documentId: "doc-2" });

    await waitFor(() => {
      expect(mockGetPrintableDocument).toHaveBeenCalledWith("doc-2");
    });

    expect(mockGetPrintableDocument).toHaveBeenCalledTimes(2);
  });

  it("should retry fetching data", async () => {
    mockGetPrintableDocument.mockResolvedValue({ data: {} as any });

    const { result } = renderHook(() => usePrintableDocument("doc-1"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear the mock to verify retry calls
    mockGetPrintableDocument.mockClear();

    // Call retry
    result.current.retry();

    await waitFor(() => {
      expect(mockGetPrintableDocument).toHaveBeenCalledWith("doc-1");
    });
  });
});
