import { renderHook, waitFor, act } from "@testing-library/react";
import { usePrintablePreferences } from "../use-printable-preferences";
import { useAuthContext } from "@/shared/contexts/auth-context";
import { getCookie } from "@/shared/lib/auth";
import { apiClient } from "@/shared/lib/api";

// Mock dependencies
jest.mock("@/shared/contexts/auth-context");
jest.mock("@/shared/lib/auth");
jest.mock("@/shared/lib/api");

const mockUseAuthContext = useAuthContext as jest.MockedFunction<typeof useAuthContext>;
const mockGetCookie = getCookie as jest.MockedFunction<typeof getCookie>;
const mockApiClient = apiClient.put as jest.MockedFunction<typeof apiClient.put>;

describe("usePrintablePreferences", () => {
  const mockRefreshUserData = jest.fn();
  const mockUserAuth = {
    userInfo: {
      id: "user-1",
      name: "Test User",
      email: "<EMAIL>",
      role: "user",
    },
    isAuth: true,
    initialized: true,
    notifications: [],
    onePages: [],
    error: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuthContext.mockReturnValue({
      userAuth: mockUserAuth,
      setUserAuth: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
      refreshUserData: mockRefreshUserData,
    });
    mockGetCookie.mockReturnValue("mock-jwt-token");
    // Mock getCookie for userId
    (getCookie as jest.MockedFunction<typeof getCookie>)
      .mockImplementation((key: string) => {
        if (key === "userId") return "user-1";
        if (key === "jwt") return "mock-jwt-token";
        return null;
      });
  });

  it("should initialize with default preferences", () => {
    const { result } = renderHook(() => usePrintablePreferences());

    expect(result.current.preferences).toEqual({
      photoOnPrintable: false,
      qrCodeOnPrintable: false,
      emptyPrintableFooter: true,
    });
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBe(null);
  });

  it("should update preferences successfully", async () => {
    mockApiClient.mockResolvedValueOnce({
      success: true,
      data: {},
    });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      await result.current.updatePreferences({ photoOnPrintable: true });
    });

    expect(result.current.error).toBe(null);
    expect(mockApiClient).toHaveBeenCalledWith(
      "/users/user-1",
      {
        photoOnPrintable: true,
        qrCodeOnPrintable: false,
        emptyPrintableFooter: false,
      }
    );
    expect(mockRefreshUserData).toHaveBeenCalledTimes(1);
  });

  it("should handle update error", async () => {
    mockApiClient.mockResolvedValueOnce({
      success: false,
      error: "Bad Request",
    });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      try {
        await result.current.updatePreferences({ photoOnPrintable: true });
      } catch (err) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe("Bad Request");
    expect(mockRefreshUserData).not.toHaveBeenCalled();
  });

  it("should handle network error", async () => {
    const networkError = new Error("Network error");
    mockApiClient.mockRejectedValueOnce(networkError);

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      try {
        await result.current.updatePreferences({ photoOnPrintable: true });
      } catch (err) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe("Network error");
  });

  it("should set photo on printable", async () => {
    mockApiClient.mockResolvedValueOnce({
      success: true,
      data: {},
    });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      await result.current.setPhotoOnPrintable();
    });

    expect(mockApiClient).toHaveBeenCalledWith("/users/user-1", {
      photoOnPrintable: true,
      qrCodeOnPrintable: false,
      emptyPrintableFooter: false,
    });
    expect(mockRefreshUserData).toHaveBeenCalledTimes(1);
  });

  it("should set QR code on printable", async () => {
    mockApiClient.mockResolvedValueOnce({
      success: true,
      data: {},
    });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      await result.current.setQrCodeOnPrintable();
    });

    expect(mockApiClient).toHaveBeenCalledWith("/users/user-1", {
      photoOnPrintable: false,
      qrCodeOnPrintable: true,
      emptyPrintableFooter: false,
    });
    expect(mockRefreshUserData).toHaveBeenCalledTimes(1);
  });

  it("should set empty footer", async () => {
    mockApiClient.mockResolvedValueOnce({
      success: true,
      data: {},
    });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      await result.current.setEmptyFooter();
    });

    expect(mockApiClient).toHaveBeenCalledWith("/users/user-1", {
      photoOnPrintable: false,
      qrCodeOnPrintable: false,
      emptyPrintableFooter: true,
    });
    expect(mockRefreshUserData).toHaveBeenCalledTimes(1);
  });

  it("should not update when user is not authenticated", async () => {
    mockUseAuthContext.mockReturnValue({
      userAuth: { ...mockUserAuth, userInfo: null, isAuth: false },
      setUserAuth: jest.fn(),
      login: jest.fn(),
      logout: jest.fn(),
      refreshUserData: mockRefreshUserData,
    });

    // Also mock no userId in cookies
    (getCookie as jest.MockedFunction<typeof getCookie>)
      .mockImplementation((key: string) => {
        if (key === "userId") return null; // No userId
        if (key === "jwt") return "mock-jwt-token";
        return null;
      });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      try {
        await result.current.updatePreferences({ photoOnPrintable: true });
      } catch (err) {
        // Expected to throw
      }
    });

    expect(mockApiClient).not.toHaveBeenCalled();
  });

  it("should not update when no JWT token", async () => {
    (getCookie as jest.MockedFunction<typeof getCookie>)
      .mockImplementation((key: string) => {
        if (key === "userId") return null; // No userId
        if (key === "jwt") return "mock-jwt-token";
        return null;
      });

    const { result } = renderHook(() => usePrintablePreferences());

    await act(async () => {
      try {
        await result.current.updatePreferences({ photoOnPrintable: true });
      } catch (err) {
        // Expected to throw
      }
    });

    expect(mockApiClient).not.toHaveBeenCalled();
  });
});
