import { renderHook, waitFor } from "@testing-library/react";
import { usePrintablesCategory } from "../use-printables-category";
import { getPrintablesByProvince } from "../../lib/api-client";

// Mock the API client
jest.mock("../../lib/api-client", () => ({
  getPrintablesByProvince: jest.fn(),
}));

const mockGetPrintablesByProvince = getPrintablesByProvince as jest.MockedFunction<typeof getPrintablesByProvince>;

describe("usePrintablesCategory", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should initialize with loading state", () => {
    mockGetPrintablesByProvince.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    const { result } = renderHook(() => usePrintablesCategory("alberta"));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.items).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it("should fetch printables by category successfully", async () => {
    const mockItems = [
      {
        id: 1,
        title: "Alberta Document",
        slug: "alberta-document",
        documentType: "printable",
        contactPosition: "footerLeft",
        isHidden: false,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        publishedAt: "2023-01-01",
        documentId: "doc-1",
        provinceFile: [],
      },
    ];

    mockGetPrintablesByProvince.mockResolvedValue({ data: mockItems });

    const { result } = renderHook(() => usePrintablesCategory("alberta"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.items).toEqual(mockItems);
    expect(result.current.error).toBe(null);
    expect(mockGetPrintablesByProvince).toHaveBeenCalledWith("alberta");
  });

  it("should handle API error", async () => {
    const errorMessage = "Failed to fetch printables for this category";
    mockGetPrintablesByProvince.mockResolvedValue({ data: [], error: errorMessage });

    const { result } = renderHook(() => usePrintablesCategory("alberta"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.items).toEqual([]);
  });

  it("should handle network error", async () => {
    const networkError = new Error("Network error");
    mockGetPrintablesByProvince.mockRejectedValue(networkError);

    const { result } = renderHook(() => usePrintablesCategory("alberta"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe("Network error");
    expect(result.current.items).toEqual([]);
  });

  it("should not fetch when category is empty", () => {
    const { result } = renderHook(() => usePrintablesCategory(""));

    expect(result.current.isLoading).toBe(true);
    expect(mockGetPrintablesByProvince).not.toHaveBeenCalled();
  });

  it("should refetch when category changes", async () => {
    mockGetPrintablesByProvince.mockResolvedValue({ data: [] });

    const { result, rerender } = renderHook(
      ({ category }) => usePrintablesCategory(category),
      { initialProps: { category: "alberta" } }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(mockGetPrintablesByProvince).toHaveBeenCalledWith("alberta");

    // Change category
    rerender({ category: "ontario" });

    await waitFor(() => {
      expect(mockGetPrintablesByProvince).toHaveBeenCalledWith("ontario");
    });

    expect(mockGetPrintablesByProvince).toHaveBeenCalledTimes(2);
  });

  it("should retry fetching data", async () => {
    mockGetPrintablesByProvince.mockResolvedValue({ data: [] });

    const { result } = renderHook(() => usePrintablesCategory("alberta"));

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear the mock to verify retry calls
    mockGetPrintablesByProvince.mockClear();

    // Call retry
    result.current.retry();

    await waitFor(() => {
      expect(mockGetPrintablesByProvince).toHaveBeenCalledWith("alberta");
    });
  });
});
