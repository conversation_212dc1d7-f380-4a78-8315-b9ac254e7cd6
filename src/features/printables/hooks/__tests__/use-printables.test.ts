import { renderHook, waitFor } from "@testing-library/react";
import { usePrintables } from "../use-printables";
import { getPrintables, transformDocumentsToCategories } from "../../lib/api-client";

// Mock the API client
jest.mock("../../lib/api-client", () => ({
  getPrintables: jest.fn(),
  transformDocumentsToCategories: jest.fn(),
}));

const mockGetPrintables = getPrintables as jest.MockedFunction<typeof getPrintables>;
const mockTransformDocumentsToCategories = transformDocumentsToCategories as jest.MockedFunction<typeof transformDocumentsToCategories>;

describe("usePrintables", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should initialize with loading state", () => {
    mockGetPrintables.mockImplementation(() => new Promise(() => {})); // Never resolves
    mockTransformDocumentsToCategories.mockReturnValue([]);

    const { result } = renderHook(() => usePrintables());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.categories).toEqual([]);
    expect(result.current.documents).toEqual([]);
    expect(result.current.error).toBe(null);
  });

  it("should fetch printables successfully", async () => {
    const mockDocuments = [
      {
        id: 1,
        title: "Test Document",
        slug: "test-document",
        documentType: "printable",
        contactPosition: "footerLeft",
        isHidden: false,
        createdAt: "2023-01-01",
        updatedAt: "2023-01-01",
        publishedAt: "2023-01-01",
        documentId: "doc-1",
        provinceFile: [],
      },
    ];

    const mockCategories = [
      {
        id: "alberta",
        title: "Alberta",
        description: "1 document available",
        documentCount: 1,
        province: "alberta",
      },
    ];

    mockGetPrintables.mockResolvedValue({ data: mockDocuments });
    mockTransformDocumentsToCategories.mockReturnValue(mockCategories);

    const { result } = renderHook(() => usePrintables());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.documents).toEqual(mockDocuments);
    expect(result.current.categories).toEqual([
      {
        id: "alberta",
        title: "Alberta",
        description: "1 document available",
        metadata: { province: "alberta", documentCount: 1 },
      },
    ]);
    expect(result.current.error).toBe(null);
    expect(mockGetPrintables).toHaveBeenCalledTimes(1);
    expect(mockTransformDocumentsToCategories).toHaveBeenCalledWith(mockDocuments);
  });

  it("should handle API error", async () => {
    const errorMessage = "Failed to fetch printables";
    mockGetPrintables.mockResolvedValue({ data: [], error: errorMessage });
    mockTransformDocumentsToCategories.mockReturnValue([]);

    const { result } = renderHook(() => usePrintables());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.documents).toEqual([]);
    expect(result.current.categories).toEqual([]);
    expect(mockTransformDocumentsToCategories).toHaveBeenCalledWith([]);
  });

  it("should handle network error", async () => {
    const networkError = new Error("Network error");
    mockGetPrintables.mockRejectedValue(networkError);
    mockTransformDocumentsToCategories.mockReturnValue([]);

    const { result } = renderHook(() => usePrintables());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBe("Network error");
    expect(result.current.documents).toEqual([]);
    expect(result.current.categories).toEqual([]);
    expect(mockTransformDocumentsToCategories).toHaveBeenCalledWith([]);
  });

  it("should retry fetching data", async () => {
    mockGetPrintables.mockResolvedValue({ data: [] });
    mockTransformDocumentsToCategories.mockReturnValue([]);

    const { result } = renderHook(() => usePrintables());

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear the mock to verify retry calls
    mockGetPrintables.mockClear();

    // Call retry
    result.current.retry();

    await waitFor(() => {
      expect(mockGetPrintables).toHaveBeenCalledTimes(1);
    });
  });
});
