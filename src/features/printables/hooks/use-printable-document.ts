"use client";

import { useState, useEffect, useCallback } from "react";
import { PrintableDocument, getPrintableDocument } from "../lib/api-client";

export interface UsePrintableDocumentResult {
  document?: PrintableDocument;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function usePrintableDocument(documentId: string): UsePrintableDocumentResult {
  const [document, setDocument] = useState<PrintableDocument | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      if (!documentId || documentId === 'undefined') {
        setError("Invalid document ID");
        setDocument(undefined);
        return;
      }

      const response = await getPrintableDocument(documentId);

      if (response.error) {
        setError(response.error);
        setDocument(undefined);
      } else {
        setDocument(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch printable document");
      setDocument(undefined);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId, fetchDocument]);

  const retry = () => {
    fetchDocument();
  };

  return {
    document,
    isLoading,
    error,
    retry,
  };
}
