"use client";

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/shared/hooks/use-auth';
import { socialMediaApi } from '../lib/api-client';
import { getProvinceContent, validatePost, createContentError, isPostAvailableForProvince } from '../lib/utils';
import type {
  UseSocialMediaContentResult,
  SocialMediaContent,
  LightweightPost,
  SocialMediaPost
} from '../types';

export const useSocialMediaContent = (): UseSocialMediaContentResult => {
  const [content, setContent] = useState<SocialMediaContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const contentPageRef = useRef<HTMLDivElement>(null);

  const handleSetContent = async (post: LightweightPost, isExtra = false) => {
    console.log('handleSetContent called with post:', post);
    console.log('Post documentId:', post.documentId);
    console.log('Post month:', post.month);
    console.log('Post isExtra:', post.isExtra);
    console.log('Post Province:', post.Province);
    
    // Validate post object
    if (!validatePost(post)) {
      const errorMessage = 'Invalid post data provided';
      console.error(errorMessage, post);
      setError(errorMessage);
      return;
    }

    setIsLoading(true);
    setError(null);

    // Get user province outside try block so it's available in catch
    const userProvince = user?.province || user?.team?.province;

    try {
      // Scroll to top of the content area
      if (contentPageRef.current) {
        contentPageRef.current.scrollIntoView({ behavior: 'smooth' });
      } else {
        // Fallback to scrolling to top of window
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // First check if the lightweight post has province data for filtering
      if (!post.Province || !Array.isArray(post.Province) || post.Province.length === 0) {
        throw new Error('Post data is missing province information');
      }

      // Check if this post is available for the user's province using the lightweight data
      console.log('Checking province availability for user:', userProvince);
      console.log('Post province data:', post.Province);
      
      const isAvailable = isPostAvailableForProvince(post, userProvince);
      console.log('Post available for province:', isAvailable);
      
      if (!isAvailable) {
        throw new Error(`No content found for your province (${userProvince || 'unknown'})`);
      }

      // Now fetch the full post data for images and other content
      console.log('Post is available for province, fetching full content...');
      const response = await socialMediaApi.getSocialMediaPost(post.id);

      if (!response.data) {
        throw new Error('No content found for selected post');
      }

      const fullPost = response.data;
      console.log('Full post data:', fullPost);

      // Get province-specific content from the full post
      // If the full post doesn't have province data, fall back to the lightweight post
      const postWithProvince = fullPost.Province ? fullPost : post;
      const provinceContent = getProvinceContent(postWithProvince as SocialMediaPost, userProvince);

      if (provinceContent) {
        console.log('Setting content with province data:', provinceContent);
        
        // Validate the content structure before setting it
        const contentToSet = {
          ...provinceContent,
          isExtra: fullPost.isExtra || isExtra
        };
        
        console.log('Content to set:', contentToSet);
        console.log('Content images type:', typeof contentToSet.images);
        console.log('Content images is array:', Array.isArray(contentToSet.images));
        console.log('Content images length:', contentToSet.images?.length);
        
        // Ensure images is always an array
        if (!Array.isArray(contentToSet.images)) {
          console.warn('Images is not an array, converting to empty array');
          contentToSet.images = [];
        }
        
        setContent(contentToSet);
      } else {
        console.log('No province content found, throwing error');
        throw new Error(`No content found for your province (${userProvince || 'unknown'})`);
      }
    } catch (err) {
      console.error('Error fetching post content:', err);
      
      const errorMessage = createContentError(err, userProvince);
      setError(errorMessage);
      
      // More detailed error logging
      if (err && typeof err === 'object' && 'response' in err) {
        const errorResponse = err as any;
        if (errorResponse.response) {
          console.error('Error response data:', errorResponse.response.data);
          console.error('Error response status:', errorResponse.response.status);
          console.error('Error response headers:', errorResponse.response.headers);
        } else if (errorResponse.request) {
          console.error('Error request:', errorResponse.request);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-load latest monthly post when component mounts
  useEffect(() => {
    // This will be handled by the parent component that has access to the posts
  }, []);

  return {
    content,
    isLoading,
    error,
    setContent: handleSetContent,
    contentPageRef
  };
};
