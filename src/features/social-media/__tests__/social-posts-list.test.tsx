import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SocialPostsList } from '../components/social-posts-list';
import type { ImageData, CaptionData } from '../types';

// Mock the toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock the S3 proxy
jest.mock('@/shared/lib/s3-proxy', () => ({
  fetchImageWithProxy: jest.fn(() => Promise.resolve({ arrayBuffer: new ArrayBuffer(8) })),
}));

// Mock file-saver
jest.mock('file-saver', () => ({
  saveAs: jest.fn(),
}));

// Mock JSZip
jest.mock('jszip', () => {
  return jest.fn().mockImplementation(() => ({
    file: jest.fn(),
    generateAsync: jest.fn(() => Promise.resolve(new Blob())),
  }));
});

const mockImages: ImageData[] = [
  {
    id: 'img1',
    name: 'A1-image.jpg',
    url: 'https://example.com/image1.jpg',
    alternativeText: 'Test image 1',
    hash: 'hash1',
    ext: 'jpg',
    mime: 'image/jpeg',
    size: 1000,
    provider: 'local',
    createdAt: '2025-01-01',
    updatedAt: '2025-01-01',
  },
  {
    id: 'img2',
    name: 'B2-image.jpg',
    url: 'https://example.com/image2.jpg',
    alternativeText: 'Test image 2',
    hash: 'hash2',
    ext: 'jpg',
    mime: 'image/jpeg',
    size: 2000,
    provider: 'local',
    createdAt: '2025-01-01',
    updatedAt: '2025-01-01',
  },
];

const mockCaptions: CaptionData[] = [
  {
    id: 'cap1',
    title: 'A1 August Long',
    postDate: '2025-08-04',
    text: 'Happy Civic Holiday to our incredible community across Canada!',
    backgroundInfo: 'Civic Holiday celebration',
  },
  {
    id: 'cap2',
    title: 'B2 Did You Know?',
    postDate: '2025-08-06',
    text: 'Did you know we have ancient civilizations to thank for property taxes?',
    backgroundInfo: 'Property tax history',
  },
];

describe('SocialPostsList', () => {
  const defaultProps = {
    images: mockImages,
    captions: mockCaptions,
    calendar: null,
    month: '2025-07-01',
    isExtra: false,
  };

  beforeEach(() => {
    // Mock window.URL.createObjectURL
    Object.defineProperty(window, 'URL', {
      value: {
        createObjectURL: jest.fn(() => 'mock-url'),
        revokeObjectURL: jest.fn(),
      },
      writable: true,
    });

    // Mock navigator.clipboard
    Object.defineProperty(navigator, 'clipboard', {
      value: {
        writeText: jest.fn(() => Promise.resolve()),
      },
      writable: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<SocialPostsList {...defaultProps} />);
    expect(screen.getByText('July 2025')).toBeInTheDocument();
  });

  it('renders extra content when isExtra is true', () => {
    render(<SocialPostsList {...defaultProps} isExtra={true} />);
    expect(screen.getByText('Extra Content')).toBeInTheDocument();
    expect(screen.getByText('Extra Post')).toBeInTheDocument();
  });

  it('displays images in a grid', () => {
    render(<SocialPostsList {...defaultProps} />);
    
    // Check if images are rendered
    expect(screen.getByAltText('Test image 1')).toBeInTheDocument();
    expect(screen.getByAltText('Test image 2')).toBeInTheDocument();
  });

  it('displays image filenames without extensions', () => {
    render(<SocialPostsList {...defaultProps} />);
    
    expect(screen.getByText('A1-image')).toBeInTheDocument();
    expect(screen.getByText('B2-image')).toBeInTheDocument();
  });

  it('shows captions section with proper metadata', () => {
    render(<SocialPostsList {...defaultProps} />);
    
    expect(screen.getByText('Caption Text')).toBeInTheDocument();
    expect(screen.getByText('A1 August Long')).toBeInTheDocument();
    expect(screen.getByText('B2 Did You Know?')).toBeInTheDocument();
    expect(screen.getByText('Post date: 2025-08-04')).toBeInTheDocument();
    expect(screen.getByText('Post date: 2025-08-06')).toBeInTheDocument();
    expect(screen.getByText('Background info: Civic Holiday celebration')).toBeInTheDocument();
    expect(screen.getByText('Background info: Property tax history')).toBeInTheDocument();
  });

  it('handles copy caption functionality', async () => {
    const mockClipboard = jest.fn(() => Promise.resolve());
    Object.defineProperty(navigator, 'clipboard', {
      value: { writeText: mockClipboard },
      writable: true,
    });

    render(<SocialPostsList {...defaultProps} />);
    
    const copyButtons = screen.getAllByText('Copy Caption');
    fireEvent.click(copyButtons[0]);
    
    await waitFor(() => {
      expect(mockClipboard).toHaveBeenCalledWith('Happy Civic Holiday to our incredible community across Canada!');
    });
  });

  it('shows download all button', () => {
    render(<SocialPostsList {...defaultProps} />);
    expect(screen.getByText('Download All Images')).toBeInTheDocument();
  });

  it('shows calendar view when calendar is provided', () => {
    render(<SocialPostsList {...defaultProps} calendar="https://example.com/calendar.pdf" />);
    expect(screen.getByText('Social Calendar')).toBeInTheDocument();
  });

  it('handles PDF calendar files', () => {
    render(<SocialPostsList {...defaultProps} calendar="https://example.com/calendar.pdf" />);
    
    const iframe = screen.getByTitle('calendar');
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://example.com/calendar.pdf');
  });

  it('handles image calendar files', () => {
    render(<SocialPostsList {...defaultProps} calendar="https://example.com/calendar.png" />);
    
    const img = screen.getByTitle('calendar');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', 'https://example.com/calendar.png');
  });

  it('shows fallback for unsupported calendar file types', () => {
    render(<SocialPostsList {...defaultProps} calendar="https://example.com/calendar.txt" />);
    
    expect(screen.getByText('Unsupported calendar file type: txt')).toBeInTheDocument();
    expect(screen.getByText('Open Calendar File')).toBeInTheDocument();
  });

  it('handles empty images array', () => {
    render(<SocialPostsList {...defaultProps} images={[]} />);
    expect(screen.getByText('No images available for this content.')).toBeInTheDocument();
  });

  it('handles null month gracefully', () => {
    render(<SocialPostsList {...defaultProps} month={null} />);
    expect(screen.getByText('Unknown Date')).toBeInTheDocument();
  });

  it('handles missing captions gracefully', () => {
    render(<SocialPostsList {...defaultProps} captions={null} />);
    
    // Should still show images
    expect(screen.getByAltText('Test image 1')).toBeInTheDocument();
    expect(screen.getByAltText('Test image 2')).toBeInTheDocument();
    
    // Should not show captions section
    expect(screen.queryByText('Caption Text')).not.toBeInTheDocument();
  });
});
