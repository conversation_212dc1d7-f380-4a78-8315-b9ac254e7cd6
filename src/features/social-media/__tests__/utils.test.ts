import {
  validatePost,
  filterPostsByType,
  sortPostsByMonth,
  formatMonth,
  isPostAvailableForProvince,
  getProvinceContent,
  createContentError,
} from '../lib/utils';
import type { LightweightPost, SocialMediaPost, ProvinceData, CaptionData } from '../types';

describe('Social Media Utils', () => {
  describe('validatePost', () => {
    const validPost: LightweightPost = {
      id: 1,
      documentId: 'doc1',
      month: '2025-07-01',
      isExtra: false,
      Province: [{ all: true }],
    };

    it('validates a valid post', () => {
      expect(validatePost(validPost)).toBe(true);
    });

    it('rejects post without id', () => {
      const invalidPost = { ...validPost, id: undefined };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post with non-numeric id', () => {
      const invalidPost = { ...validPost, id: 'invalid' };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post without documentId', () => {
      const invalidPost = { ...validPost, documentId: undefined };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('rejects post with non-string documentId', () => {
      const invalidPost = { ...validPost, documentId: 123 };
      expect(validatePost(invalidPost)).toBe(false);
    });

    it('allows extra posts with null month', () => {
      const extraPost = { ...validPost, isExtra: true, month: null };
      expect(validatePost(extraPost)).toBe(true);
    });

    it('rejects non-extra posts with null month', () => {
      const invalidPost = { ...validPost, isExtra: false, month: null };
      expect(validatePost(invalidPost)).toBe(false);
    });
  });

  describe('filterPostsByType', () => {
    const posts: LightweightPost[] = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 2,
        documentId: 'doc2',
        month: '2025-06-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 3,
        documentId: 'doc3',
        month: null,
        isExtra: true,
        extraTitle: 'Extra Post',
        Province: [{ all: true }],
      },
      {
        id: 4,
        documentId: 'doc4',
        month: '2025-05-01',
        isExtra: null, // This should be treated as monthly
        Province: [{ all: true }],
      },
    ];

    it('filters monthly posts correctly', () => {
      const monthlyPosts = filterPostsByType(posts, 'monthly');
      expect(monthlyPosts).toHaveLength(3); // Including the one with isExtra: null
      expect(monthlyPosts.every(post => !post.isExtra || post.isExtra === null)).toBe(true);
    });

    it('filters extra posts correctly', () => {
      const extraPosts = filterPostsByType(posts, 'extra');
      expect(extraPosts).toHaveLength(1);
      expect(extraPosts.every(post => post.isExtra === true)).toBe(true);
    });

    it('handles empty posts array', () => {
      expect(filterPostsByType([], 'monthly')).toHaveLength(0);
      expect(filterPostsByType([], 'extra')).toHaveLength(0);
    });
  });

  describe('sortPostsByMonth', () => {
    const posts: LightweightPost[] = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 2,
        documentId: 'doc2',
        month: '2025-06-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 3,
        documentId: 'doc3',
        month: '2025-08-01',
        isExtra: false,
        Province: [{ all: true }],
      },
      {
        id: 4,
        documentId: 'doc4',
        month: null,
        isExtra: true,
        extraTitle: 'Extra Post',
        Province: [{ all: true }],
      },
    ];

    it('sorts posts by month in descending order', () => {
      const sortedPosts = sortPostsByMonth(posts);
      expect(sortedPosts[0].month).toBe('2025-08-01');
      expect(sortedPosts[1].month).toBe('2025-07-01');
      expect(sortedPosts[2].month).toBe('2025-06-01');
    });

    it('puts posts with null month at the end', () => {
      const sortedPosts = sortPostsByMonth(posts);
      const lastPost = sortedPosts[sortedPosts.length - 1];
      expect(lastPost.month).toBeNull();
    });

    it('handles posts with same month', () => {
      const postsWithSameMonth = [
        { ...posts[0], month: '2025-07-01' },
        { ...posts[1], month: '2025-07-01' },
      ];
      const sortedPosts = sortPostsByMonth(postsWithSameMonth);
      expect(sortedPosts).toHaveLength(2);
    });
  });

  describe('formatMonth', () => {
    it('formats valid date strings', () => {
      expect(formatMonth('2025-07-01')).toBe('July 2025');
      expect(formatMonth('2024-12-25')).toBe('December 2024');
      expect(formatMonth('2023-01-01')).toBe('January 2023');
    });

    it('handles null month', () => {
      expect(formatMonth(null)).toBe('Unknown Date');
    });

    it('handles invalid date strings', () => {
      expect(formatMonth('invalid-date')).toBe('invalid-date');
    });

    it('handles different date formats', () => {
      expect(formatMonth('2025/07/01')).toBe('July 2025');
      expect(formatMonth('2025.07.01')).toBe('July 2025');
    });
  });

  describe('isPostAvailableForProvince', () => {
    const userProvince = 'ontario';

    it('returns true for posts available in all provinces', () => {
      const post = {
        Province: [{ all: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('returns true for posts available in user province', () => {
      const post = {
        Province: [{ ontario: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('returns false for posts not available in user province', () => {
      const post = {
        Province: [{ quebec: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles multiple provinces', () => {
      const post = {
        Province: [{ ontario: true }, { quebec: true }],
      };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(true);
    });

    it('handles empty Province array', () => {
      const post = { Province: [] };
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles missing Province property', () => {
      const post = {};
      expect(isPostAvailableForProvince(post, userProvince)).toBe(false);
    });

    it('handles camelCase province names', () => {
      const post = {
        Province: [{ britishColumbia: true }],
      };
      expect(isPostAvailableForProvince(post, 'britishcolumbia')).toBe(true);
    });
  });

  describe('getProvinceContent', () => {
    const mockProvinceData: ProvinceData[] = [
      {
        ontario: true,
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01',
          },
        ],
        captionTexts: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday!',
            backgroundInfo: 'Civic Holiday celebration',
          },
        ],
      },
    ];

    it('returns content for user province', () => {
      const result = getProvinceContent(mockProvinceData, 'ontario');
      expect(result).toBeDefined();
      expect(result?.images).toHaveLength(1);
      expect(result?.captions).toHaveLength(1);
    });

    it('returns null for unavailable province', () => {
      const result = getProvinceContent(mockProvinceData, 'quebec');
      expect(result).toBeNull();
    });

    it('handles posts with all provinces access', () => {
      const allProvincesData: ProvinceData[] = [
        {
          all: true,
          images: mockProvinceData[0].images,
          captionTexts: mockProvinceData[0].captionTexts,
        },
      ];
      const result = getProvinceContent(allProvincesData, 'ontario');
      expect(result).toBeDefined();
    });

    it('processes captionTexts correctly', () => {
      const result = getProvinceContent(mockProvinceData, 'ontario');
      expect(result?.captions?.[0]).toEqual({
        id: 'cap1',
        title: 'A1 August Long',
        postDate: '2025-08-04',
        text: 'Happy Civic Holiday!',
        backgroundInfo: 'Civic Holiday celebration',
      });
    });

    it('handles missing captionTexts', () => {
      const dataWithoutCaptions: ProvinceData[] = [
        {
          ontario: true,
          images: mockProvinceData[0].images,
          captionTexts: [],
        },
      ];
      const result = getProvinceContent(dataWithoutCaptions, 'ontario');
      expect(result?.captions).toHaveLength(0);
    });

    it('handles missing images', () => {
      const dataWithoutImages: ProvinceData[] = [
        {
          ontario: true,
          images: [],
          captionTexts: mockProvinceData[0].captionTexts,
        },
      ];
      const result = getProvinceContent(dataWithoutImages, 'ontario');
      expect(result?.images).toHaveLength(0);
    });
  });

  describe('createContentError', () => {
    it('creates error with default message', () => {
      const error = createContentError();
      expect(error.message).toBe('No content found for your province');
    });

    it('creates error with custom message', () => {
      const customMessage = 'Custom error message';
      const error = createContentError(customMessage);
      expect(error.message).toBe(customMessage);
    });

    it('creates error with custom name', () => {
      const error = createContentError('Custom error', 'CustomError');
      expect(error.name).toBe('CustomError');
    });
  });
});
