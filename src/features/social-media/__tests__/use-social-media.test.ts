import { renderHook, waitFor } from '@testing-library/react';
import { useSocialMedia } from '../hooks/use-social-media';
import type { LightweightPost } from '../types';

// Mock the API client
const mockGetLightweightPosts = jest.fn();

jest.mock('../lib/api-client', () => ({
  socialMediaApi: {
    getLightweightPosts: mockGetLightweightPosts,
  },
}));

// Mock the auth hook
const mockUseAuth = jest.fn();

jest.mock('@/shared/hooks/use-auth', () => ({
  useAuth: () => mockUseAuth(),
}));

describe('useSocialMedia', () => {
  const mockPosts: LightweightPost[] = [
    {
      id: 1,
      documentId: 'doc1',
      month: '2025-07-01',
      isExtra: false,
      Province: [{ ontario: true }],
    },
    {
      id: 2,
      documentId: 'doc2',
      month: '2025-06-01',
      isExtra: false,
      Province: [{ all: true }],
    },
    {
      id: 3,
      documentId: 'doc3',
      month: null,
      isExtra: true,
      extraTitle: 'Spring Forward Reminder(s)',
      Province: [{ ontario: true }],
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: { team: { province: 'ontario' } },
      isAuthenticated: true,
    });
  });

  it('fetches posts on mount when user is authenticated', async () => {
    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(mockGetLightweightPosts).toHaveBeenCalled();
    });

    expect(result.current.monthlyPosts).toHaveLength(2);
    expect(result.current.extraPosts).toHaveLength(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('filters posts by user province', async () => {
    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.monthlyPosts).toHaveLength(2);
    });

    // Should only show posts available for Ontario
    expect(result.current.monthlyPosts[0].Province[0]).toHaveProperty('ontario', true);
    expect(result.current.monthlyPosts[1].Province[0]).toHaveProperty('all', true);
  });

  it('separates monthly and extra posts correctly', async () => {
    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.monthlyPosts).toHaveLength(2);
      expect(result.current.extraPosts).toHaveLength(1);
    });

    // Monthly posts should have isExtra: false
    expect(result.current.monthlyPosts.every(post => !post.isExtra)).toBe(true);
    
    // Extra posts should have isExtra: true
    expect(result.current.extraPosts.every(post => post.isExtra)).toBe(true);
  });

  it('handles API errors gracefully', async () => {
    const errorMessage = 'Failed to fetch posts';
    mockGetLightweightPosts.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.error).toBeTruthy();
    });

    expect(result.current.error).toContain(errorMessage);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.monthlyPosts).toHaveLength(0);
    expect(result.current.extraPosts).toHaveLength(0);
  });

  it('shows loading state while fetching', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise(resolve => {
      resolvePromise = resolve;
    });
    
    mockGetLightweightPosts.mockReturnValue(promise);

    const { result } = renderHook(() => useSocialMedia());

    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBeNull();

    resolvePromise!({ data: mockPosts });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('handles user without province gracefully', async () => {
    mockUseAuth.mockReturnValue({
      user: { team: {} }, // No province
      isAuthenticated: true,
    });

    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(mockGetLightweightPosts).toHaveBeenCalled();
    });

    // Should still fetch posts even without province
    expect(result.current.monthlyPosts).toHaveLength(2);
    expect(result.current.extraPosts).toHaveLength(1);
  });

  it('handles user without team gracefully', async () => {
    mockUseAuth.mockReturnValue({
      user: {}, // No team
      isAuthenticated: true,
    });

    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(mockGetLightweightPosts).toHaveBeenCalled();
    });

    // Should still fetch posts even without team
    expect(result.current.monthlyPosts).toHaveLength(2);
    expect(result.current.extraPosts).toHaveLength(1);
  });

  it('refetches posts when refetch is called', async () => {
    mockGetLightweightPosts.mockResolvedValue({
      data: mockPosts,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.monthlyPosts).toHaveLength(2);
    });

    // Call refetch
    result.current.refetch();

    await waitFor(() => {
      expect(mockGetLightweightPosts).toHaveBeenCalledTimes(2);
    });
  });

  it('does not fetch posts when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isAuthenticated: false,
    });

    renderHook(() => useSocialMedia());

    expect(mockGetLightweightPosts).not.toHaveBeenCalled();
  });

  it('handles empty posts response', async () => {
    mockGetLightweightPosts.mockResolvedValue({
      data: [],
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.monthlyPosts).toHaveLength(0);
      expect(result.current.extraPosts).toHaveLength(0);
    });
  });

  it('handles posts with missing Province data', async () => {
    const postsWithoutProvince = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [], // Empty Province array
      },
    ];

    mockApiClient.getLightweightPosts.mockResolvedValue({
      data: postsWithoutProvince,
    });

    const { result } = renderHook(() => useSocialMedia());

    await waitFor(() => {
      expect(result.current.monthlyPosts).toHaveLength(0);
    });

    // Posts without Province data should be filtered out
    expect(result.current.monthlyPosts).toHaveLength(0);
  });
});
