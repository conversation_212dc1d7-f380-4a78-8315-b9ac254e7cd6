import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SocialMediaList } from '../components/social-media-list';
import type { LightweightPost } from '../types';

const mockMonthlyPosts: LightweightPost[] = [
  {
    id: 1,
    documentId: 'doc1',
    month: '2025-07-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 2,
    documentId: 'doc2',
    month: '2025-06-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 3,
    documentId: 'doc3',
    month: '2025-05-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 4,
    documentId: 'doc4',
    month: '2025-04-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 5,
    documentId: 'doc5',
    month: '2025-03-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 6,
    documentId: 'doc6',
    month: '2025-02-01',
    isExtra: false,
    Province: [{ all: true }],
  },
  {
    id: 7,
    documentId: 'doc7',
    month: '2025-01-01',
    isExtra: false,
    Province: [{ all: true }],
  },
];

const mockExtraPosts: LightweightPost[] = [
  {
    id: 8,
    documentId: 'doc8',
    month: '2025-07-01',
    isExtra: true,
    extraTitle: 'Spring Forward Reminder(s)',
    Province: [{ all: true }],
  },
  {
    id: 9,
    documentId: 'doc9',
    month: null,
    isExtra: true,
    extraTitle: 'Bank Of Canada Announcements',
    Province: [{ all: true }],
  },
];

describe('SocialMediaList', () => {
  const defaultProps = {
    monthlyPosts: mockMonthlyPosts,
    extraPosts: mockExtraPosts,
    onPostSelect: jest.fn(),
    showExtras: true,
    onToggleExtras: jest.fn(),
    isMonthsMobOpen: false,
    onToggleMonthsMob: jest.fn(),
    selectedMonth: '2025-07-01',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<SocialMediaList {...defaultProps} />);
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
  });

  it('shows extra posts section', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
    expect(screen.getByText('Spring Forward Reminder(s)')).toBeInTheDocument();
    expect(screen.getByText('Bank Of Canada Announcements')).toBeInTheDocument();
  });

  it('shows monthly posts grouped by year', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    // Use getAllByText since there are multiple 2025 elements (one for extras, one for months)
    const yearElements = screen.getAllByText('2025');
    expect(yearElements.length).toBeGreaterThan(0);
    
    // Check for month names - they should be rendered by YearGroupSelector
    // The formatMonth function should convert '2025-07-01' to 'July 2025', etc.
    expect(screen.getByText('July 2025')).toBeInTheDocument();
    expect(screen.getByText('June 2025')).toBeInTheDocument();
    expect(screen.getByText('May 2025')).toBeInTheDocument();
  });

  it('shows separator line between extras and years', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    // Should have a separator (hr element)
    const separator = document.querySelector('hr');
    expect(separator).toBeInTheDocument();
  });

  it('handles toggle extras functionality', () => {
    const mockOnToggleExtras = jest.fn();
    render(<SocialMediaList {...defaultProps} onToggleExtras={mockOnToggleExtras} />);
    
    const toggleButton = screen.getByText('Extra Posts').closest('button');
    expect(toggleButton).toBeInTheDocument();
    
    if (toggleButton) {
      fireEvent.click(toggleButton);
      expect(mockOnToggleExtras).toHaveBeenCalled();
    }
  });

  it('calls onPostSelect when a post is clicked', () => {
    const mockOnPostSelect = jest.fn();
    render(<SocialMediaList {...defaultProps} onPostSelect={mockOnPostSelect} />);
    
    const extraPostButton = screen.getByText('Spring Forward Reminder(s)');
    fireEvent.click(extraPostButton);
    
    expect(mockOnPostSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 8,
        extraTitle: 'Spring Forward Reminder(s)',
      }),
      true
    );
  });

  it('calls onPostSelect when a month is clicked', () => {
    const mockOnPostSelect = jest.fn();
    render(<SocialMediaList {...defaultProps} onPostSelect={mockOnPostSelect} />);
    
    // Use a more specific selector for the month button
    const monthButton = screen.getByText((content, element) => {
      return content === 'July 2025' && element?.closest('button') !== null;
    });
    fireEvent.click(monthButton);
    
    expect(mockOnPostSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 1,
        month: '2025-07-01',
      }),
      false
    );
  });

  it('shows mobile month selector when isMonthsMobOpen is true', () => {
    render(<SocialMediaList {...defaultProps} isMonthsMobOpen={true} />);
    
    expect(screen.getByText('Select a Month')).toBeInTheDocument();
    // Use getAllByText since there are multiple Extra Posts elements
    const extraPostsElements = screen.getAllByText('Extra Posts');
    expect(extraPostsElements.length).toBeGreaterThan(0);
    
    // Use getAllByText since there are multiple 2025 elements
    const yearElements = screen.getAllByText('2025');
    expect(yearElements.length).toBeGreaterThan(0);
  });

  it('handles mobile toggle functionality', () => {
    const mockOnToggleMonthsMob = jest.fn();
    render(<SocialMediaList {...defaultProps} onToggleMonthsMob={mockOnToggleMonthsMob} />);
    
    const mobileToggleButton = screen.getByText('Select a Month');
    fireEvent.click(mobileToggleButton);
    
    expect(mockOnToggleMonthsMob).toHaveBeenCalled();
  });

  it('shows loading skeleton when no monthly posts', () => {
    render(<SocialMediaList {...defaultProps} monthlyPosts={[]} />);
    
    // Should show skeleton loading state
    const skeletons = document.querySelectorAll('[class*="animate-pulse"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('handles empty extra posts gracefully', () => {
    render(<SocialMediaList {...defaultProps} extraPosts={[]} />);
    
    // Should still show Extra Posts section but collapsed
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
    expect(screen.queryByText('Spring Forward Reminder(s)')).not.toBeInTheDocument();
  });

  it('handles empty monthly posts gracefully', () => {
    render(<SocialMediaList {...defaultProps} monthlyPosts={[]} />);
    
    // Should show loading skeleton
    const skeletons = document.querySelectorAll('[class*="animate-pulse"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('passes selectedMonth to YearGroupSelector', () => {
    render(<SocialMediaList {...defaultProps} selectedMonth="2025-07-01" />);
    
    // The selectedMonth should be passed down to highlight the selected month
    // Use a more specific selector for the month text
    expect(screen.getByText((content, element) => {
      return content === 'July 2025' && element?.closest('button') !== null;
    })).toBeInTheDocument();
  });

  it('shows star icons for extra posts', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    // Should show star icons for extra posts
    // Look for star icons in the DOM
    const starIcons = document.querySelectorAll('svg[class*="lucide-star"]');
    expect(starIcons.length).toBeGreaterThan(0);
  });

  it('shows folder icons for years', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    // Should show folder icons for years
    // Look for folder icons in the DOM
    const folderIcons = document.querySelectorAll('svg[class*="lucide-folder-open"]');
    expect(folderIcons.length).toBeGreaterThan(0);
  });

  it('handles posts with null month in extra posts', () => {
    render(<SocialMediaList {...defaultProps} />);
    
    // Posts with null month should still be displayed
    expect(screen.getByText('Bank Of Canada Announcements')).toBeInTheDocument();
  });

  it('maintains state between desktop and mobile views', () => {
    const { rerender } = render(<SocialMediaList {...defaultProps} />);
    
    // Desktop view
    const extraPostsElements = screen.getAllByText('Extra Posts');
    expect(extraPostsElements.length).toBeGreaterThan(0);
    
    const yearElements = screen.getAllByText('2025');
    expect(yearElements.length).toBeGreaterThan(0);
    
    // Switch to mobile view
    rerender(<SocialMediaList {...defaultProps} isMonthsMobOpen={true} />);
    
    expect(screen.getByText('Select a Month')).toBeInTheDocument();
    
    const mobileExtraPostsElements = screen.getAllByText('Extra Posts');
    expect(mobileExtraPostsElements.length).toBeGreaterThan(0);
    
    const mobileYearElements = screen.getAllByText('2025');
    expect(mobileYearElements.length).toBeGreaterThan(0);
  });
});
