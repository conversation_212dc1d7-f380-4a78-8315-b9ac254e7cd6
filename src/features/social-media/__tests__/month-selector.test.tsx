import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MonthSelector } from '../components/month-selector';
import type { LightweightPost } from '../types';

const mockPosts: LightweightPost[] = [
  {
    id: 1,
    documentId: 'doc1',
    month: '2025-07-01',
    isExtra: true,
    extraTitle: 'Spring Forward Reminder(s)',
    Province: [{ all: true }],
  },
  {
    id: 2,
    documentId: 'doc2',
    month: '2025-06-01',
    isExtra: true,
    extraTitle: 'Bank Of Canada Announcements',
    Province: [{ all: true }],
  },
  {
    id: 3,
    documentId: 'doc3',
    month: null,
    isExtra: true,
    extraTitle: 'Pride 2023',
    Province: [{ all: true }],
  },
];

describe('MonthSelector', () => {
  const defaultProps = {
    posts: mockPosts,
    onPostSelect: jest.fn(),
    isExtra: true,
    showExtras: true,
    onToggleExtras: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<MonthSelector {...defaultProps} />);
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
  });

  it('shows extra posts section when isExtra is true', () => {
    render(<MonthSelector {...defaultProps} />);
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
  });

  it('returns null when isExtra is false', () => {
    render(<MonthSelector {...defaultProps} isExtra={false} />);
    expect(screen.queryByText('Extra Posts')).not.toBeInTheDocument();
  });

  it('groups posts by year correctly', () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Should show 2025 as the current year
    expect(screen.getByText('2025')).toBeInTheDocument();
  });

  it('shows year headers with folder icons', () => {
    render(<MonthSelector {...defaultProps} />);
    
    const yearHeaders = screen.getAllByText(/2025/);
    expect(yearHeaders).toHaveLength(1);
  });

  it('expands current year by default', () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Current year (2025) should be expanded and show posts
    expect(screen.getByText('Spring Forward Reminder(s)')).toBeInTheDocument();
    expect(screen.getByText('Bank Of Canada Announcements')).toBeInTheDocument();
  });

  it('handles posts with null month gracefully', () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Posts with null month should still be displayed
    expect(screen.getByText('Pride 2023')).toBeInTheDocument();
  });

  it('toggles year expansion when clicked', () => {
    render(<MonthSelector {...defaultProps} />);
    
    const yearButton = screen.getByText('2025').closest('button');
    expect(yearButton).toBeInTheDocument();
    
    if (yearButton) {
      fireEvent.click(yearButton);
      // Year should collapse
      expect(screen.queryByText('Spring Forward Reminder(s)')).not.toBeInTheDocument();
    }
  });

  it('calls onPostSelect when a post is clicked', () => {
    const mockOnPostSelect = jest.fn();
    render(<MonthSelector {...defaultProps} onPostSelect={mockOnPostSelect} />);
    
    const postButton = screen.getByText('Spring Forward Reminder(s)');
    fireEvent.click(postButton);
    
    expect(mockOnPostSelect).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 1,
        extraTitle: 'Spring Forward Reminder(s)',
      }),
      true
    );
  });

  it('shows chevron icons for expand/collapse', () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Should show chevron up when expanded
    const chevronUp = document.querySelector('svg[class*="lucide-chevron-up"]');
    expect(chevronUp).toBeInTheDocument();
  });

  it('handles empty posts array', () => {
    render(<MonthSelector {...defaultProps} posts={[]} />);
    
    // Should still show the Extra Posts header
    expect(screen.getByText('Extra Posts')).toBeInTheDocument();
    // But no year groups
    expect(screen.queryByText('2025')).not.toBeInTheDocument();
  });

  it('sorts posts within years by title', () => {
    render(<MonthSelector {...defaultProps} />);
    
    // Posts should be sorted alphabetically within the year
    const postElements = screen.getAllByText(/Spring Forward|Bank Of Canada|Pride/);
    expect(postElements[0]).toHaveTextContent('Bank Of Canada Announcements');
    expect(postElements[1]).toHaveTextContent('Pride 2023');
    expect(postElements[2]).toHaveTextContent('Spring Forward Reminder(s)');
  });

  it('handles toggle extras functionality', () => {
    const mockOnToggleExtras = jest.fn();
    render(<MonthSelector {...defaultProps} onToggleExtras={mockOnToggleExtras} />);
    
    const toggleButton = screen.getByText('Extra Posts').closest('button');
    expect(toggleButton).toBeInTheDocument();
    
    if (toggleButton) {
      fireEvent.click(toggleButton);
      expect(mockOnToggleExtras).toHaveBeenCalled();
    }
  });
});
