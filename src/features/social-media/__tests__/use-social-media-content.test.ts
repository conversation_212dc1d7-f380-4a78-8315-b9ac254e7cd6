import { renderHook, waitFor } from '@testing-library/react';
import { useSocialMediaContent } from '../hooks/use-social-media-content';
import type { LightweightPost, SocialMediaPost } from '../types';

// Mock the API client
jest.mock('../lib/api-client', () => ({
  socialMediaApi: {
    getSocialMediaPost: jest.fn(),
  },
}));

const mockApiClient = {
  getSocialMediaPost: jest.fn(),
};

// Mock the auth hook
const mockUseAuth = jest.fn();

jest.mock('@/shared/hooks/use-auth', () => ({
  useAuth: () => mockUseAuth(),
}));

describe('useSocialMediaContent', () => {
  const mockLightweightPost: LightweightPost = {
    id: 1,
    documentId: 'doc1',
    month: '2025-07-01',
    isExtra: false,
    Province: [{ ontario: true }],
  };

  const mockFullPost: SocialMediaPost = {
    id: 1,
    documentId: 'doc1',
    month: '2025-07-01',
    isExtra: false,
    Province: [
      {
        ontario: true,
        images: [
          {
            id: 'img1',
            name: 'A1-image.jpg',
            url: 'https://example.com/image1.jpg',
            alternativeText: 'Test image 1',
            hash: 'hash1',
            ext: 'jpg',
            mime: 'image/jpeg',
            size: 1000,
            provider: 'local',
            createdAt: '2025-01-01',
            updatedAt: '2025-01-01',
          },
        ],
        captionTexts: [
          {
            id: 'cap1',
            title: 'A1 August Long',
            postDate: '2025-08-04',
            text: 'Happy Civic Holiday to our incredible community across Canada!',
            backgroundInfo: 'Civic Holiday celebration',
          },
        ],
      },
    ],
    calendar: 'https://example.com/calendar.pdf',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseAuth.mockReturnValue({
      user: { team: { province: 'ontario' } },
      isAuthenticated: true,
    });
  });

  it('initializes with default state', () => {
    const { result } = renderHook(() => useSocialMediaContent());

    expect(result.current.content).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(typeof result.current.setContent).toBe('function');
    expect(result.current.contentPageRef.current).toBeNull();
  });

  it('fetches full post content when setContent is called', async () => {
    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: mockFullPost,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(mockApiClient.getSocialMediaPost).toHaveBeenCalledWith(1);
    });

    expect(result.current.content).toEqual(mockFullPost);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('filters content by user province', async () => {
    const postWithMultipleProvinces: SocialMediaPost = {
      ...mockFullPost,
      Province: [
        { ontario: true, images: mockFullPost.Province[0].images, captionTexts: mockFullPost.Province[0].captionTexts },
        { quebec: true, images: [], captionTexts: [] },
      ],
    };

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: postWithMultipleProvinces,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(result.current.content).toBeTruthy();
    });

    // Should only show Ontario content
    expect(result.current.content?.Province).toHaveLength(1);
    expect(result.current.content?.Province[0]).toHaveProperty('ontario', true);
  });

  it('handles API errors gracefully', async () => {
    const errorMessage = 'Failed to fetch post';
    mockApiClient.getSocialMediaPost.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(result.current.error).toBeTruthy();
    });

    expect(result.current.error).toContain(errorMessage);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.content).toBeNull();
  });

  it('shows loading state while fetching', async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise(resolve => {
      resolvePromise = resolve;
    });
    
    mockApiClient.getSocialMediaPost.mockReturnValue(promise);

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBeNull();

    resolvePromise!({ data: mockFullPost });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
  });

  it('handles user without province gracefully', async () => {
    mockUseAuth.mockReturnValue({
      user: { team: {} }, // No province
      isAuthenticated: true,
    });

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: mockFullPost,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
    });

    // Should still fetch content even without province
    expect(result.current.content).toEqual(mockFullPost);
  });

  it('handles user without team gracefully', async () => {
    mockUseAuth.mockReturnValue({
      user: {}, // No team
      isAuthenticated: true,
    });

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: mockFullPost,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(mockApiClient.getSocialMediaPost).toHaveBeenCalled();
    });

    // Should still fetch content even without team
    expect(result.current.content).toEqual(mockFullPost);
  });

  it('handles posts with missing Province data', async () => {
    const postWithoutProvince: SocialMediaPost = {
      ...mockFullPost,
      Province: [],
    };

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: postWithoutProvince,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(result.current.error).toBeTruthy();
    });

    // Should show error for posts without Province data
    expect(result.current.error).toContain('No content found for your province');
  });

  it('handles posts with missing images gracefully', async () => {
    const postWithoutImages: SocialMediaPost = {
      ...mockFullPost,
      Province: [
        {
          ontario: true,
          images: [],
          captionTexts: mockFullPost.Province[0].captionTexts,
        },
      ],
    };

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: postWithoutImages,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(result.current.content).toBeTruthy();
    });

    // Should handle posts without images
    expect(result.current.content?.Province[0].images).toHaveLength(0);
  });

  it('handles posts with missing captions gracefully', async () => {
    const postWithoutCaptions: SocialMediaPost = {
      ...mockFullPost,
      Province: [
        {
          ontario: true,
          images: mockFullPost.Province[0].images,
          captionTexts: [],
        },
      ],
    };

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: postWithoutCaptions,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(mockLightweightPost);

    await waitFor(() => {
      expect(result.current.content).toBeTruthy();
    });

    // Should handle posts without captions
    expect(result.current.content?.Province[0].captionTexts).toHaveLength(0);
  });

  it('validates post data before setting content', async () => {
    const invalidPost = {
      ...mockLightweightPost,
      id: 'invalid', // Invalid ID type
    };

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(invalidPost);

    await waitFor(() => {
      expect(result.current.error).toBeTruthy();
    });

    // Should show validation error
    expect(result.current.error).toContain('Invalid post data provided');
  });

  it('handles extra posts correctly', async () => {
    const extraPost: LightweightPost = {
      ...mockLightweightPost,
      isExtra: true,
      extraTitle: 'Spring Forward Reminder(s)',
    };

    const fullExtraPost: SocialMediaPost = {
      ...mockFullPost,
      isExtra: true,
      extraTitle: 'Spring Forward Reminder(s)',
    };

    mockApiClient.getSocialMediaPost.mockResolvedValue({
      data: fullExtraPost,
    });

    const { result } = renderHook(() => useSocialMediaContent());

    result.current.setContent(extraPost);

    await waitFor(() => {
      expect(result.current.content).toBeTruthy();
    });

    expect(result.current.content?.isExtra).toBe(true);
    expect(result.current.content?.extraTitle).toBe('Spring Forward Reminder(s)');
  });

  it('clears content when setContent is called with null', () => {
    const { result } = renderHook(() => useSocialMediaContent());

    // Set some content first
    result.current.setContent(mockLightweightPost);
    
    // Then clear it
    result.current.setContent(null);

    expect(result.current.content).toBeNull();
    expect(result.current.error).toBeNull();
    expect(result.current.isLoading).toBe(false);
  });
});
