import { socialMediaApi } from '../lib/api-client';

// Mock the shared API client
jest.mock('@/shared/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = {
  get: jest.fn(),
};

describe('Social Media API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getLightweightPosts', () => {
    it('fetches lightweight posts successfully', async () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            documentId: 'doc1',
            month: '2025-07-01',
            isExtra: false,
            Province: [{ all: true }],
          },
        ],
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/lightweight');
      expect(result).toEqual(mockResponse);
    });

    it('handles API errors gracefully', async () => {
      const errorMessage = 'Failed to fetch posts';
      mockApiClient.get.mockRejectedValue(new Error(errorMessage));

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow(errorMessage);
      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/lightweight');
    });

    it('handles empty response', async () => {
      const mockResponse = { data: [] };
      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(result).toEqual(mockResponse);
      expect(result.data).toHaveLength(0);
    });
  });

  describe('getSocialMediaPost', () => {
    it('fetches full post data successfully', async () => {
      const mockResponse = {
        data: {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [
            {
              ontario: true,
              images: [
                {
                  id: 'img1',
                  name: 'A1-image.jpg',
                  url: 'https://example.com/image1.jpg',
                  alternativeText: 'Test image 1',
                  hash: 'hash1',
                  ext: 'jpg',
                  mime: 'image/jpeg',
                  size: 1000,
                  provider: 'local',
                  createdAt: '2025-01-01',
                  updatedAt: '2025-01-01',
                },
              ],
              captionTexts: [
                {
                  id: 'cap1',
                  title: 'A1 August Long',
                  postDate: '2025-08-04',
                  text: 'Happy Civic Holiday!',
                  backgroundInfo: 'Civic Holiday celebration',
                },
              ],
            },
          ],
          calendar: 'https://example.com/calendar.pdf',
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(1);

      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/1');
      expect(result).toEqual(mockResponse);
    });

    it('handles API errors gracefully', async () => {
      const errorMessage = 'Failed to fetch post';
      mockApiClient.get.mockRejectedValue(new Error(errorMessage));

      await expect(socialMediaApi.getSocialMediaPost(1)).rejects.toThrow(errorMessage);
      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/1');
    });

    it('handles different post IDs', async () => {
      const mockResponse = { data: { id: 123, documentId: 'doc123' } };
      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(123);

      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/123');
      expect(result.data.id).toBe(123);
    });

    it('handles posts with missing data gracefully', async () => {
      const mockResponse = {
        data: {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [], // Empty Province array
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getSocialMediaPost(1);

      expect(result.data.Province).toHaveLength(0);
    });
  });

  describe('API endpoint consistency', () => {
    it('uses correct endpoint for lightweight posts', async () => {
      mockApiClient.get.mockResolvedValue({ data: [] });
      await socialMediaApi.getLightweightPosts();
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/lightweight');
    });

    it('uses correct endpoint for full post data', async () => {
      mockApiClient.get.mockResolvedValue({ data: {} });
      await socialMediaApi.getSocialMediaPost(456);
      
      expect(mockApiClient.get).toHaveBeenCalledWith('/social-medias/456');
    });
  });

  describe('Response handling', () => {
    it('preserves response structure', async () => {
      const mockResponse = {
        data: {
          id: 1,
          documentId: 'doc1',
          month: '2025-07-01',
          isExtra: false,
          Province: [{ all: true }],
        },
        meta: {
          pagination: {
            page: 1,
            pageSize: 25,
            pageCount: 1,
            total: 1,
          },
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(result).toEqual(mockResponse);
      expect(result.data).toBeDefined();
      expect(result.meta).toBeDefined();
    });

    it('handles malformed responses gracefully', async () => {
      const malformedResponse = { data: null };
      mockApiClient.get.mockResolvedValue(malformedResponse);

      const result = await socialMediaApi.getLightweightPosts();

      expect(result).toEqual(malformedResponse);
      expect(result.data).toBeNull();
    });
  });

  describe('Error scenarios', () => {
    it('handles network errors', async () => {
      const networkError = new Error('Network Error');
      mockApiClient.get.mockRejectedValue(networkError);

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow('Network Error');
    });

    it('handles HTTP error responses', async () => {
      const httpError = new Error('HTTP 404 Not Found');
      mockApiClient.get.mockRejectedValue(httpError);

      await expect(socialMediaApi.getSocialMediaPost(999)).rejects.toThrow('HTTP 404 Not Found');
    });

    it('handles timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      mockApiClient.get.mockRejectedValue(timeoutError);

      await expect(socialMediaApi.getLightweightPosts()).rejects.toThrow('Request timeout');
    });
  });

  describe('Data validation', () => {
    it('accepts valid post IDs', async () => {
      const validIds = [1, 123, 999999];
      
      for (const id of validIds) {
        mockApiClient.get.mockResolvedValue({ data: { id } });
        const result = await socialMediaApi.getSocialMediaPost(id);
        expect(result.data.id).toBe(id);
      }
    });

    it('handles edge case post IDs', async () => {
      const edgeCaseIds = [0, -1, Number.MAX_SAFE_INTEGER];
      
      for (const id of edgeCaseIds) {
        mockApiClient.get.mockResolvedValue({ data: { id } });
        const result = await socialMediaApi.getSocialMediaPost(id);
        expect(result.data.id).toBe(id);
      }
    });
  });
});
