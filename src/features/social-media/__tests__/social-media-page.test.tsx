import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SocialMediaPage } from '../components/social-media-page';

// Mock the hooks
const mockUseSocialMedia = jest.fn();
const mockUseSocialMediaContent = jest.fn();

jest.mock('../hooks/use-social-media', () => ({
  useSocialMedia: () => mockUseSocialMedia(),
}));

jest.mock('../hooks/use-social-media-content', () => ({
  useSocialMediaContent: () => mockUseSocialMediaContent(),
}));

// Mock the shared hooks
jest.mock('@/shared/hooks/use-auth', () => ({
  useAuth: () => ({
    user: { team: { province: 'ontario' } },
  }),
}));

describe('SocialMediaPage', () => {
  const defaultSocialMediaMock = {
    monthlyPosts: [],
    extraPosts: [],
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  };

  const defaultContentMock = {
    content: null,
    isLoading: false,
    error: null,
    setContent: jest.fn(),
    contentPageRef: { current: null },
  };

  beforeEach(() => {
    mockUseSocialMedia.mockReturnValue(defaultSocialMediaMock);
    mockUseSocialMediaContent.mockReturnValue(defaultContentMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<SocialMediaPage />);
    expect(screen.getByText('Social media content')).toBeInTheDocument();
  });

  it('shows loading state when loading', () => {
    mockUseSocialMedia.mockReturnValue({
      ...defaultSocialMediaMock,
      isLoading: true,
    });

    render(<SocialMediaPage />);
    expect(screen.getByText('Social media content')).toBeInTheDocument();
    // Should show loading skeletons
    expect(screen.getAllByTestId('skeleton')).toHaveLength(6);
  });

  it('shows error state when there is an error', () => {
    mockUseSocialMedia.mockReturnValue({
      ...defaultSocialMediaMock,
      error: 'Failed to fetch posts',
    });

    render(<SocialMediaPage />);
    expect(screen.getByText('Failed to fetch posts')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('shows content when posts are available', () => {
    const mockPosts = [
      {
        id: 1,
        documentId: 'doc1',
        month: '2025-07-01',
        isExtra: false,
        Province: [{ all: true }],
      },
    ];

    mockUseSocialMedia.mockReturnValue({
      ...defaultSocialMediaMock,
      monthlyPosts: mockPosts,
      extraPosts: [],
    });

    render(<SocialMediaPage />);
    expect(screen.getByText('Social media content')).toBeInTheDocument();
    expect(screen.getByText('Select a month from the sidebar to view content.')).toBeInTheDocument();
  });

  it('handles retry when error occurs', async () => {
    const mockRefetch = jest.fn();
    mockUseSocialMedia.mockReturnValue({
      ...defaultSocialMediaMock,
      error: 'Failed to fetch posts',
      refetch: mockRefetch,
    });

    render(<SocialMediaPage />);
    
    const retryButton = screen.getByText('Try Again');
    fireEvent.click(retryButton);
    
    await waitFor(() => {
      expect(mockRefetch).toHaveBeenCalled();
    });
  });
});
