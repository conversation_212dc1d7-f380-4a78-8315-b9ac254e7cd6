# Social Media Feature

This feature provides social media content management functionality, allowing users to browse and download monthly social media posts and extra content based on their province.

## Features

- **Province-based Content Filtering**: Content is filtered based on the user's team province
- **Monthly Posts**: Regular monthly social media content organized by month
- **Extra Posts**: Additional social media content marked as extras
- **Responsive Design**: Mobile-friendly interface with collapsible navigation
- **Image Management**: View, download, and copy captions for social media images
- **Calendar Integration**: View associated calendar content when available

## Architecture

The feature follows the established feature-sliced architecture pattern:

```
src/features/social-media/
├── components/          # React components
├── hooks/              # Custom React hooks
├── lib/                # Utilities and API client
├── types/              # TypeScript type definitions
├── __tests__/          # Test files
├── index.ts            # Public API exports
└── README.md           # This file
```

## Components

### SocialMediaPage
Main page component that orchestrates the entire feature. Handles state management and combines the sidebar navigation with content display.

### SocialMediaList
Sidebar navigation component that displays monthly posts and extra posts. Includes mobile-responsive design with collapsible navigation.

### MonthSelector
Individual month/extra post selector component used within the sidebar.

### SocialPostsList
Content display component that shows images, captions, and calendar content. Includes download functionality and responsive image grid.

## Hooks

### useSocialMedia
Fetches and manages the list of available social media posts, filtering them by user province.

### useSocialMediaContent
Manages the selected content state, handles content fetching, and provides scroll-to-top functionality.

## API Integration

The feature integrates with the Strapi backend through the `SocialMediaApiClient`:

- `getLightweightPosts()`: Fetches lightweight post data for navigation
- `getSocialMediaPost(id)`: Fetches full post content by ID
- `getSocialMediaPosts(filters)`: Fetches posts with optional filters

## Data Flow

1. **Initial Load**: `useSocialMedia` hook fetches available posts
2. **Province Filtering**: Posts are filtered based on user's team province
3. **Content Selection**: User selects a post from the sidebar
4. **Content Fetching**: `useSocialMediaContent` hook fetches full post data
5. **Content Display**: Selected content is displayed in the main area

## Province Logic

The feature includes sophisticated province-based content filtering:

- Supports both direct province data and nested reference structures
- Handles "all provinces" flags for universal content
- Case-insensitive province matching
- Filters out non-boolean properties and special fields

## Responsive Design

- **Desktop**: Sidebar navigation with content area
- **Mobile**: Collapsible month selector with overlay navigation
- **Images**: Responsive grid layout with hover actions
- **Calendar**: Responsive iframe sizing based on screen width

## Error Handling

- Network error handling with retry functionality
- Province-specific error messages
- Graceful fallbacks for missing content
- Loading states and skeleton placeholders

## Usage

```tsx
import { SocialMediaPage } from "@/features/social-media";

export default function SocialMedia() {
  return <SocialMediaPage />;
}
```

## Dependencies

- **UI Components**: Uses shadcn/ui components (Button, Card, Alert, etc.)
- **Icons**: Lucide React icons
- **State Management**: React hooks for local state
- **API**: Custom API client extending the shared API infrastructure
- **Styling**: Tailwind CSS for responsive design

## Testing

The feature includes test files in the `__tests__/` directory. Run tests with:

```bash
npm test src/features/social-media
```

## Future Enhancements

- ZIP download functionality for multiple images
- Advanced filtering and search capabilities
- Content scheduling and preview features
- Analytics and usage tracking
- Bulk content management tools
