"use client";

import React, { useMemo, useState, useEffect } from 'react';
import { Button } from '@/shared/ui/button';
import { formatMonth } from '../lib/utils';
import type { MonthSelectorProps, LightweightPost } from '../types';
import { FolderOpen, Star, ChevronDown, ChevronUp } from 'lucide-react';

export const MonthSelector: React.FC<MonthSelectorProps> = ({
  posts,
  onPostSelect,
  isExtra = false,
  showExtras,
  onToggleExtras
}) => {
  console.log('MonthSelector props:', { posts: posts?.length, isExtra, showExtras });
  console.log('MonthSelector posts sample:', posts?.[0]);
  if (isExtra) {
    // Group extra posts by year
    const postsByYear = useMemo(() => {
      const groups: Record<string, LightweightPost[]> = {};
      
      posts.forEach(post => {
        // Extract year from month field or use createdAt if month is not available
        let year: string;
        if (post.month) {
          year = new Date(post.month).getFullYear().toString();
        } else {
          // Fallback to current year if month is not available
          year = new Date().getFullYear().toString();
        }
        
        if (!groups[year]) {
          groups[year] = [];
        }
        groups[year].push(post);
      });
      
      // Sort years in descending order (newest first)
      return Object.entries(groups)
        .sort(([a], [b]) => parseInt(b) - parseInt(a))
        .reduce((acc, [year, yearPosts]) => {
          // Sort posts within each year by title for better organization
          yearPosts.sort((a, b) => {
            const titleA = (a.extraTitle || '').toLowerCase();
            const titleB = (b.extraTitle || '').toLowerCase();
            return titleA.localeCompare(titleB);
          });
          acc[year] = yearPosts;
          return acc;
        }, {} as Record<string, LightweightPost[]>);
    }, [posts]);

    // State to track which years are expanded for extra posts
    const [expandedExtraYears, setExpandedExtraYears] = useState<Set<string>>(new Set());
    
    // Get current year for default expansion
    const currentYear = new Date().getFullYear().toString();
    
    // Initialize with current year expanded only once when component mounts
    useEffect(() => {
      if (expandedExtraYears.size === 0 && Object.keys(postsByYear).length > 0) {
        setExpandedExtraYears(new Set([currentYear]));
      }
    }, []); // Empty dependency array - only run once on mount

    const toggleExtraYear = (year: string) => {
      setExpandedExtraYears((prev: Set<string>) => {
        const newSet = new Set(prev);
        if (newSet.has(year)) {
          newSet.delete(year);
        } else {
          newSet.add(year);
        }
        return newSet;
      });
    };

    return (
      <div className="mb-2">
        <Button
          variant="ghost"
          className="w-full justify-between text-left font-normal"
          onClick={onToggleExtras}
        >
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-yellow-500" />
            <span>Extra Posts</span>
          </div>
          {showExtras ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
        
        {showExtras && (
          <div className="ml-4 mt-2 space-y-2">
            {Object.entries(postsByYear).reverse().map(([year, yearPosts]) => {
              const isYearExpanded = expandedExtraYears.has(year);
              const isCurrentYear = year === currentYear;
              
              return (
                <div key={year} className="space-y-1">
                  {/* Year Header with Collapse/Expand */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-between text-left font-normal text-sm hover:text-primary hover:bg-primary/10"
                    onClick={() => toggleExtraYear(year)}
                  >
                    <div className="flex items-center gap-2">
                      <FolderOpen className="h-3 w-3 text-primary" />
                      <span className={isCurrentYear ? "font-semibold" : ""}>
                        {year}
                      </span>
                    </div>
                    {isYearExpanded ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </Button>
                  
                  {/* Posts for this year (collapsible) */}
                  {isYearExpanded && (
                    <div className="ml-4 space-y-1">
                      {yearPosts.map((post) => (
                        <div key={post.documentId}>
                          <div
                            className="w-full p-2 text-left font-normal text-sm hover:text-primary hover:bg-primary/10 rounded-md cursor-pointer transition-colors"
                            onClick={() => onPostSelect(post, true)}
                          >
                            <div className="flex items-start gap-2">
                              <Star className="h-4 w-4 mt-0.5 text-yellow-500 flex-shrink-0" />
                              <span className="break-words leading-relaxed">
                                {post.extraTitle || 'Extra Post'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }

  // This component now only handles extra posts
  // Monthly posts are handled by YearGroupSelector
  return null;
};
