"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/shared/ui/button';
import { Skeleton } from '@/shared/ui/skeleton';
import { MonthSelector } from './month-selector';
import { YearGroupSelector } from './year-group-selector';
import type { SocialMediaListProps } from '../types';
import { ChevronDown, ChevronUp } from 'lucide-react';

export const SocialMediaList: React.FC<SocialMediaListProps> = ({
  monthlyPosts,
  extraPosts,
  onPostSelect,
  showExtras,
  onToggleExtras,
  isMonthsMobOpen,
  onToggleMonthsMob,
  selectedMonth
}) => {
  console.log('SocialMediaList props:', {
    monthlyPosts: monthlyPosts?.length,
    extraPosts: extraPosts?.length,
    showExtras,
    isMonthsMobOpen
  });
  
  if (!monthlyPosts || monthlyPosts.length === 0) {
    console.log('No monthly posts, showing skeleton');
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="space-y-2">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Desktop Sidebar */}
      <div className="hidden md:block bg-white rounded-lg border p-4 space-y-4">
        {/* Extra Posts Section */}
        <MonthSelector
          posts={extraPosts}
          onPostSelect={onPostSelect}
          isExtra={true}
          showExtras={showExtras}
          onToggleExtras={onToggleExtras}
        />
        
        {/* Separator */}
        <hr className="border-gray-200" />
        
        {/* Monthly Posts Grouped by Year */}
        <YearGroupSelector
          posts={monthlyPosts}
          onPostSelect={onPostSelect}
          selectedMonth={selectedMonth}
        />
      </div>

      {/* Mobile Month Selector */}
      <div className="md:hidden">
        <Button
          variant="outline"
          className="w-full justify-between"
          onClick={onToggleMonthsMob}
        >
          Select a Month
          {isMonthsMobOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
        
        {isMonthsMobOpen && (
          <div className="mt-2 bg-white border rounded-lg p-4 space-y-4">
            {/* Extra Posts */}
            {extraPosts.length > 0 && (
              <div className="mb-3">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between text-left font-normal"
                  onClick={onToggleExtras}
                >
                  <span className="flex items-center gap-2">
                    <span className="text-yellow-500">★</span>
                    Extra Posts
                  </span>
                  {showExtras ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
                
                {showExtras && (
                  <div className="ml-4 mt-2 space-y-1">
                    {extraPosts.map((post) => (
                      <Button
                        key={post.documentId}
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-left font-normal text-sm"
                        onClick={() => onPostSelect(post, true)}
                      >
                        <span className="text-yellow-500 mr-2">★</span>
                        <small>{post.extraTitle || 'Extra Post'}</small>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {/* Separator */}
            {extraPosts.length > 0 && <hr className="border-gray-200" />}
            
            {/* Monthly Posts Grouped by Year */}
            <YearGroupSelector
              posts={monthlyPosts}
              onPostSelect={onPostSelect}
              selectedMonth={selectedMonth}
            />
          </div>
        )}
      </div>
    </div>
  );
};
