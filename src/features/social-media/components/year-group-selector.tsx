"use client";

import React, { useState, useMemo } from 'react';
import { Button } from '@/shared/ui/button';
import { formatMonth } from '../lib/utils';
import type { LightweightPost } from '../types';
import { FolderOpen, ChevronDown, ChevronUp } from 'lucide-react';

interface YearGroupSelectorProps {
  posts: LightweightPost[];
  onPostSelect: (post: LightweightPost, isExtra?: boolean) => Promise<void>;
  selectedMonth?: string; // Add selected month prop for highlighting
}

export const YearGroupSelector: React.FC<YearGroupSelectorProps> = ({
  posts,
  onPostSelect,
  selectedMonth
}) => {
  // Get current year first
  const currentYear = new Date().getFullYear().toString();
  
  // Group posts by year
  const postsByYear = useMemo(() => {
    const groups: Record<string, LightweightPost[]> = {};
    
    posts.forEach(post => {
      // Skip posts without month (extra posts)
      if (!post.month) return;
      
      const year = new Date(post.month).getFullYear().toString();
      if (!groups[year]) {
        groups[year] = [];
      }
      groups[year].push(post);
    });
    
    // Sort years with current year first, then previous years in descending order
    const sortedYears = Object.keys(groups).sort((a, b) => {
      const yearA = parseInt(a);
      const yearB = parseInt(b);
      const currentYearInt = parseInt(currentYear);
      
      // If one is the current year, it should come first
      if (yearA === currentYearInt) return -1;
      if (yearB === currentYearInt) return 1;
      
      // Otherwise, sort in descending order (newer years first)
      return yearB - yearA;
    });
    
    return sortedYears.reduce((acc, year) => {
      // Sort posts within each year by month (newest first)
      const yearPosts = groups[year];
      yearPosts.sort((a, b) => {
        // At this point, month is guaranteed to be non-null due to the filter above
        return new Date(b.month!).getTime() - new Date(a.month!).getTime();
      });
      acc[year] = yearPosts;
      return acc;
    }, {} as Record<string, LightweightPost[]>);
  }, [posts, currentYear]);
  
  // State to track which years are expanded
  const [expandedYears, setExpandedYears] = useState<Set<string>>(new Set([currentYear]));

  const toggleYear = (year: string) => {
    setExpandedYears(prev => {
      const newSet = new Set(prev);
      if (newSet.has(year)) {
        newSet.delete(year);
      } else {
        newSet.add(year);
      }
      return newSet;
    });
  };

  if (!posts || posts.length === 0) {
    return null;
  }

  // Sort the entries to ensure proper rendering order
  const sortedEntries = Object.entries(postsByYear).sort(([yearA], [yearB]) => {
    const yearAInt = parseInt(yearA);
    const yearBInt = parseInt(yearB);
    const currentYearInt = parseInt(currentYear);
    
    // If one is the current year, it should come first
    if (yearAInt === currentYearInt) return -1;
    if (yearBInt === currentYearInt) return 1;
    
    // Otherwise, sort in descending order (newer years first)
    return yearBInt - yearAInt;
  });

  return (
    <>
      {sortedEntries.map(([year, yearPosts]) => {
        const isExpanded = expandedYears.has(year);
        const isCurrentYear = year === currentYear;
        
        return (
          <div key={year} className="mb-2">
            <Button
              variant="ghost"
              className="w-full justify-between text-left font-normal hover:text-primary hover:bg-primary/10"
              onClick={() => toggleYear(year)}
            >
              <div className="flex items-center gap-2">
                <FolderOpen className="h-4 w-4 text-primary" />
                <span className={isCurrentYear ? "font-semibold" : ""}>
                  {year}
                </span>
              </div>
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            
            {isExpanded && (
              <div className="ml-4 mt-2 space-y-1">
                {yearPosts.map((post) => {
                  const isSelected = selectedMonth === post.month;
                  return (
                    <div key={post.documentId}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`w-full justify-start text-left font-normal text-sm ${
                          isSelected 
                            ? 'text-primary bg-primary/10' 
                            : 'hover:text-primary hover:bg-primary/10'
                        }`}
                        onClick={() => onPostSelect(post, false)}
                      >
                        <FolderOpen className={`h-4 w-4 mr-2 ${
                          isSelected ? 'text-primary' : 'text-gray-400'
                        }`} />
                        {formatMonth(post.month)}
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
      })}
    </>
  );
};
