"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { Button } from '@/shared/ui/button';
import { Card, CardContent } from '@/shared/ui/card';
import { Badge } from '@/shared/ui/badge';
import { Skeleton } from '@/shared/ui/skeleton';
import { Download, Calendar, Copy, ExternalLink } from 'lucide-react';
import type { SocialPostsListProps, ImageData } from '../types';
import { fetchImageWithProxy } from '@/shared/lib/s3-proxy';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';
import { toast } from 'sonner';

const BATCH_SIZE = 5; // Process 5 images at a time

// Process a batch of images for ZIP download
const processBatch = async (batch: ImageData[], zip: JSZip) => {
  const results = await Promise.all(
    batch.map(async (image) => {
      const filename = image.name;
      const originalUrl = image.url;
      
      if (!originalUrl) {
        console.error(`Missing original URL for image: ${filename}`);
        return { success: false, filename, error: new Error('Missing original URL') };
      }
      
      try {
        const { arrayBuffer } = await fetchImageWithProxy(originalUrl);
        zip.file(filename, arrayBuffer);
        return { success: true, filename };
      } catch (err) {
        console.error(`Error downloading ${filename} from ${originalUrl}:`, err);
        return { success: false, filename, error: err };
      }
    })
  );
  return results;
};

const SocialPostsList: React.FC<SocialPostsListProps> = ({
  images,
  captions,
  calendar,
  month,
  isExtra
}) => {
  // Debug: Log the props to see what we're receiving
  console.log('SocialPostsList props:', { images, captions, calendar, month, isExtra });
  console.log('Captions type:', typeof captions);
  console.log('Captions is array:', Array.isArray(captions));
  if (Array.isArray(captions)) {
    console.log('Captions length:', captions.length);
    console.log('First caption:', captions[0]);
    console.log('First caption type:', typeof captions[0]);
  }
  console.log('Images sample:', images?.[0]);
  console.log('First image caption:', images?.[0]?.caption);
  console.log('First image alternativeText:', images?.[0]?.alternativeText);
  const [isDownloading, setIsDownloading] = useState(false);
  const [singleImageDownloads, setSingleImageDownloads] = useState<Record<string, boolean>>({});
  const [iframeSize, setIframeSize] = useState<{ width: number; height: number } | null>(null);
  const [imagesReady, setImagesReady] = useState(false);
  const [loadedImages, setLoadedImages] = useState(new Set<string>());

  // Memoize sorted images
  const sortedImages = useMemo(() => {
    if (!images || !Array.isArray(images)) {
      console.warn('Images prop is not an array:', images);
      return [];
    }
    
    // Validate that images have the required properties
    const validImages = images.filter((img: any) => {
      if (!img || typeof img !== 'object') {
        console.warn('Invalid image object:', img);
        return false;
      }
      if (!img.url || !img.name) {
        console.warn('Image missing required properties (url or name):', img);
        return false;
      }
      return true;
    });
    
    if (validImages.length !== images.length) {
      console.warn(`Filtered out ${images.length - validImages.length} invalid images`);
    }
    
    return validImages.sort((a, b) => {
      // Sort by image name (assuming format like "01-image.jpg")
      const aNum = parseInt(a.name.slice(1, 3)) || 0;
      const bNum = parseInt(b.name.slice(1, 3)) || 0;
      return aNum - bNum;
    });
  }, [images]);

  // Track image loading
  const handleImageLoad = (imageId: string) => {
    setLoadedImages((prev) => {
      const newSet = new Set(prev);
      newSet.add(imageId);
      return newSet;
    });
  };

  // Reset loading state when images change
  useEffect(() => {
    if (sortedImages.length > 0) {
      setImagesReady(false);
      setLoadedImages(new Set());
    }
  }, [sortedImages]);

  // Check if all images are loaded
  useEffect(() => {
    if (!sortedImages.length) return;

    if (loadedImages.size === sortedImages.length) {
      setImagesReady(true);
    }
  }, [loadedImages, sortedImages]);

  // Handle responsive iframe sizing
  const handleResponsiveWidth = () => {
    if (typeof window === 'undefined') return;

    const windowWidth = window.document.body.getBoundingClientRect().width;
    const sizes = {
      xs: { width: 400, height: 400 },
      sm: { width: 600, height: 480 },
      md: { width: 720, height: 540 },
      lg: { width: 960, height: 600 }
    };

    if (windowWidth < 480) setIframeSize(sizes.xs);
    else if (windowWidth < 768) setIframeSize(sizes.sm);
    else if (windowWidth < 992) setIframeSize(sizes.md);
    else setIframeSize(sizes.lg);
  };

  // Handle window resize
  useEffect(() => {
    handleResponsiveWidth();

    const handleResize = () => {
      handleResponsiveWidth();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Download single image
  const downloadSingleImage = async (image: ImageData) => {
    const imageUrl = image.url;
    const filename = image.name;

    if (!imageUrl || !filename) {
      console.error('Missing image URL or filename for download.', image);
      alert('Could not download image: missing data.');
      return;
    }

    // Set loading state for this specific image
    setSingleImageDownloads(prev => ({ ...prev, [image.id]: true }));

    try {
      // Use S3 proxy for external images
      const { arrayBuffer } = await fetchImageWithProxy(imageUrl);
      // Save using file-saver
      saveAs(new Blob([arrayBuffer]), filename);
    } catch (error) {
      console.error(`Error downloading single image ${filename}:`, error);
      alert(`Failed to download ${filename}. Please check the console for details.`);
    } finally {
      // Remove loading state for this specific image
      setSingleImageDownloads(prev => {
        const newState = { ...prev };
        delete newState[image.id];
        return newState;
      });
    }
  };

  // Download all images as ZIP
  const downloadAllImages = async () => {
    if (!imagesReady || !sortedImages || sortedImages.length === 0) {
      console.log('Images not ready or empty array');
      return;
    }

    setIsDownloading(true);
    const zip = new JSZip();
    const safeMonth = month ? month.replace(/[^a-zA-Z0-9-]/g, '_') : 'posts';
    const zipFilename = `social-media-${safeMonth}.zip`;
    const errors: Array<{ filename: string; error: string }> = [];

    try {
      for (let i = 0; i < sortedImages.length; i += BATCH_SIZE) {
        const batch = sortedImages.slice(i, i + BATCH_SIZE);
        const results = await processBatch(batch, zip);

        results.forEach((result) => {
          if (!result.success) {
            errors.push({ 
              filename: result.filename, 
              error: result.error instanceof Error ? result.error.message : 'Unknown error' 
            });
          }
        });
      }

      const content = await zip.generateAsync({ type: 'blob' });
      saveAs(content, zipFilename);

      if (errors.length > 0) {
        console.warn('Some files failed to download:', errors);
        const errorFiles = errors.map((e) => e.filename).join(', ');
        alert(
          `Download completed, but ${errors.length} image(s) failed to download: ${errorFiles}. Check console for details.`
        );
      } else {
        console.log('Download completed successfully');
      }
    } catch (error) {
      console.error('Download error:', error);
      alert('Failed to download images. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  // Copy caption to clipboard
  const copyCaption = async (caption: string) => {
    try {
      await navigator.clipboard.writeText(caption);
      toast.success('Caption copied to clipboard!');
    } catch (error) {
      console.error('Error copying caption:', error);
      toast.error('Failed to copy caption');
    }
  };

  if (!images || images.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No images available for this content.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {isExtra ? 'Extra Content' : formatMonth(month)}
          </h2>
          {isExtra && (
            <Badge variant="secondary" className="mt-2">
              Extra Post
            </Badge>
          )}
        </div>
        
        <div className="flex gap-2">
          {calendar && (
            <Button variant="outline" size="sm" asChild>
              <a href={calendar} target="_blank" rel="noopener noreferrer">
                <Calendar className="h-4 w-4 mr-2" />
                View Calendar
              </a>
            </Button>
          )}
          
          <Button 
            onClick={downloadAllImages} 
            disabled={isDownloading || !imagesReady}
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            {isDownloading ? 'Preparing ZIP...' : !imagesReady ? 'Loading Images...' : 'Download All Images'}
          </Button>
        </div>
      </div>

      {/* Processing indicator for ZIP download */}
      {isDownloading && (
        <div className="text-center py-4">
          <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            Preparing ZIP download...
          </div>
        </div>
      )}

      {/* Images Grid */}
      {sortedImages.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            No valid images found. Please check the data structure.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Expected: Array of objects with 'url' and 'name' properties
          </p>
          <p className="text-sm text-muted-foreground">
            Received: {Array.isArray(images) ? `${images.length} items` : typeof images}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {sortedImages.map((image, index) => (
          <Card key={image.id} className="overflow-hidden py-0">
            <CardContent className="p-0">
              <div className="relative">
                {!imagesReady && (
                  <Skeleton className="aspect-square w-full" />
                )}
                
                <img
                  src={image.url}
                  alt={image.alternativeText || image.name}
                  className={`w-full aspect-square object-cover transition-opacity duration-300 ${
                    imagesReady ? 'opacity-100' : 'opacity-0'
                  }`}
                  onLoad={() => handleImageLoad(image.id)}
                />
                
                {/* Image overlay with actions */}
                <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => downloadSingleImage(image)}
                      disabled={singleImageDownloads[image.id]}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="secondary"
                      asChild
                    >
                      <a href={image.url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Image Filename */}
              <div className="p-4 pt-3">
                <p className="text-sm font-medium text-gray-700">
                  {image.name.replace(/\.[^/.]+$/, '')} {/* Remove file extension */}
                </p>
              </div>
              

                          </CardContent>
          </Card>
        ))}

<Button 
            onClick={downloadAllImages} 
            disabled={isDownloading || !imagesReady}
            size="sm"
          >
            <Download className="h-4 w-4 mr-2" />
            {isDownloading ? 'Preparing ZIP...' : !imagesReady ? 'Loading Images...' : 'Download All Images'}
          </Button>
        </div>
      )}
      


      {/* Captions Section - Show all available captions */}
      {captions && captions.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Caption Text</h2>
          {captions.map((caption) => (
            <Card key={caption.id} className="p-6">
              <CardContent className="p-0">
                <div className="space-y-4">
                  {/* Title */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{caption.title}</h3>
                  </div>
                  
                  {/* Post Date */}
                  {caption.postDate && (
                    <p className="text-sm text-muted-foreground">
                      <strong>Post date:</strong> {caption.postDate}
                    </p>
                  )}
                  
                  {/* Background Info */}
                  {caption.backgroundInfo && (
                    <p className="text-sm text-muted-foreground">
                      <strong>Background info:</strong> {caption.backgroundInfo}
                    </p>
                  )}
                  
                  {/* Caption Text */}
                  <div>
                    <h4 className="text-md font-medium mb-2">Caption idea:</h4>
                    <div className="text-sm text-muted-foreground leading-relaxed">
                      {caption.text}
                    </div>
                  </div>
                  
                  {/* Copy Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyCaption(caption.text)}
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" /> Copy Caption
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Calendar View */}
      {calendar && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold">Social Calendar</h2>
        <Card>
          <CardContent className="p-4">            
            <div className="flex justify-center">
              {(() => {
                const fileType = calendar.split('.').pop()?.toLowerCase();
                
                                 if (fileType === 'pdf' && iframeSize) {
                   return (
                     <iframe
                       width={iframeSize.width}
                       height={iframeSize.height}
                       title="calendar"
                       src={calendar}
                       className="border-0 rounded-lg"
                     />
                   );
                 }
                
                if (['png', 'jpg', 'jpeg'].includes(fileType || '')) {
                  return (
                    <img
                      title="calendar"
                      src={calendar}
                      alt="calendar"
                      className="w-full h-auto rounded-lg"
                      style={{ display: 'table' }}
                    />
                  );
                }
                
                return (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">
                      Unsupported calendar file type: {fileType || 'unknown'}
                    </p>
                    <a 
                      href={calendar} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-primary hover:underline mt-2 inline-block"
                    >
                      Open Calendar File
                    </a>
                  </div>
                );
              })()}
            </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

// Helper function to format month (duplicate from utils for component use)
const formatMonth = (month: string | null): string => {
  if (!month) return 'Unknown Date';
  
  try {
    const date = new Date(month);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  } catch (error) {
    return month;
  }
};

export default SocialPostsList;
