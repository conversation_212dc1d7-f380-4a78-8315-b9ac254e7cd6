import { apiClient } from '@/shared/lib/api';
import type { 
  SocialMediaResponse, 
  SocialMediaPostResponse, 
  LightweightPostsResponse,
  SocialMediaFilters 
} from '../types';

export class SocialMediaApiClient {
  /**
   * Get lightweight posts for sidebar navigation
   */
  static async getLightweightPosts(): Promise<LightweightPostsResponse> {
    const response = await apiClient.get<LightweightPostsResponse>('/social-medias/lightweight');
    
    console.log('API Response raw:', response);
    console.log('API Response success:', response.success);
    console.log('API Response data:', response.data);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch lightweight posts');
    }
    
    // Handle nested data structure from backend
    // Backend returns: { data: { data: [...] } }
    const posts = response.data?.data || [];
    console.log('Extracted posts:', posts);
    console.log('Posts length:', posts.length);
    
    return {
      data: posts
    };
  }

  /**
   * Get all social media posts with filters
   */
  static async getSocialMediaPosts(filters: SocialMediaFilters = {}): Promise<SocialMediaResponse> {
    const queryParams = new URLSearchParams();
    
    if (filters.month) {
      queryParams.append('month', filters.month);
    }
    
    if (filters._sort) {
      queryParams.append('_sort', filters._sort);
    }
    
    if (filters._limit) {
      queryParams.append('_limit', filters._limit.toString());
    }
    
    const endpoint = `/social-medias${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await apiClient.get<SocialMediaResponse>(endpoint);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch social media posts');
    }
    
    // Handle nested data structure from backend
    const posts = response.data?.data || [];
    
    return {
      data: posts
    };
  }

    /**
   * Get a specific social media post by ID
   */
  static async getSocialMediaPost(id: number): Promise<SocialMediaPostResponse> {
    // Use the numeric ID directly with the Strapi V5 endpoint    
    const response = await apiClient.get<SocialMediaPostResponse>(`/social-medias/${id}`);
    
    if (!response.success || !response.data) {
      throw new Error(response.error || 'Failed to fetch social media posts');
    }

    // Handle nested data structure from backend
    const post = response.data?.data || response.data;
    
    console.log('Found post by ID:', post);
    console.log('Post Province data:', post.Province);
    
    return {
      data: post
    };
  }
}

// Export singleton instance for backward compatibility
export const socialMediaApi = SocialMediaApiClient;
