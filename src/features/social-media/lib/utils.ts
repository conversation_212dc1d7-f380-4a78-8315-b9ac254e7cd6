import type { LightweightPost, SocialMediaPost, ProvinceData, SocialMediaContent, CaptionData } from '../types';

/**
 * Check if a post is available for a specific province
 */
export const isPostAvailableForProvince = (post: LightweightPost | SocialMediaPost, userProvince?: string): boolean => {
  if (!post.Province || !Array.isArray(post.Province) || post.Province.length === 0) {
    console.log('Post has no Province data:', post.id);
    return false;
  }

  const userProvinceLower = userProvince ? userProvince.toLowerCase() : '';
  console.log(`Checking post ${post.id} for province: "${userProvinceLower}" (length: ${userProvinceLower.length})`);
  console.log(`Post ${post.id} Province data:`, post.Province);

  // Check if any province in the post has 'all' set to true or matches the user's province
  const isAvailable = post.Province.some((prov) => {
    // Handle the case where province data is in a ref object
    const provinceData = prov.ref || prov;
    console.log(`Province object for post ${post.id}:`, provinceData);

    // Check for 'all' flag first
    if (provinceData.all === true) {
      console.log(`Post ${post.id} has 'all' flag set to true`);
      return true;
    }

    // Check each property in the province object for a match
    return Object.keys(provinceData).some((key) => {
      // Skip non-boolean properties or special properties
      if (
        typeof provinceData[key] !== 'boolean' ||
        key === '__v' ||
        key === '_id' ||
        key === 'id' ||
        key === 'images' ||
        key === 'kind' ||
        key === '[[Prototype]]'
      ) {
        return false;
      }

      // Compare lowercase key with user province
      console.log(`Checking key: "${key}" (${typeof key}) against userProvince: "${userProvinceLower}" (${typeof userProvinceLower})`);
      console.log(`Key value: ${provinceData[key]} (${typeof provinceData[key]})`);
      
      const matches = key.toLowerCase() === userProvinceLower && provinceData[key] === true;
      if (matches) {
        console.log(`Post ${post.id} matches province ${key} for user ${userProvinceLower}`);
      }
      return matches;
    });
  });

  console.log(`Post ${post.id} available: ${isAvailable}`);
  return isAvailable;
};

/**
 * Get province-specific content from a post
 */
export const getProvinceContent = (post: SocialMediaPost, userProvince?: string): SocialMediaContent | null => {
  console.log('getProvinceContent called with post:', post);
  console.log('User province:', userProvince);
  
  // Convert userProvince to lowercase for case-insensitive comparison
  const userProvinceLower = userProvince ? userProvince.toLowerCase() : '';
  console.log('User province lowercase:', userProvinceLower);
  console.log('User province original:', userProvince);
  console.log('Available province keys:', post.Province.map(p => Object.keys(p).filter(k => typeof p[k] === 'boolean' && k !== 'all')));

  // Find a province that matches either 'all' flag or the user's province (case-insensitive)
  console.log('Looking for province match in:', post.Province);
  console.log('User province to match:', userProvinceLower);
  
  const provinceData = post.Province.find((prov) => {
    console.log('Checking province object:', prov);
    
    // Handle the case where province data is in a ref object
    const provinceRef = prov.ref || prov;
    console.log('Province ref data:', provinceRef);

    // Check for 'all' flag first
    if (provinceRef.all === true) {
      console.log('Found province with all flag set to true');
      return true;
    }

    // Check each property in the province object for a match
    const hasMatch = Object.keys(provinceRef).some((key) => {
      // Skip non-boolean properties or special properties
      if (
        typeof provinceRef[key] !== 'boolean' ||
        key === '__v' ||
        key === '_id' ||
        key === 'id' ||
        key === 'images' ||
        key === 'kind' ||
        key === '[[Prototype]]'
      ) {
        return false;
      }

      // Handle different province name formats
      let keyToMatch = key;
      
      // Convert camelCase to lowercase (e.g., "britishColumbia" -> "britishcolumbia")
      if (key.includes('britishColumbia')) {
        keyToMatch = 'britishcolumbia';
      } else if (key.includes('novaScotia')) {
        keyToMatch = 'novascotia';
      } else {
        keyToMatch = key.toLowerCase();
      }

      // Compare with user province (case-insensitive)
      const matches = keyToMatch === userProvinceLower && provinceRef[key] === true;
      if (matches) {
        console.log(`Found matching province: ${key} = ${provinceRef[key]}`);
      }
      return matches;
    });
    
    console.log('Province object has match:', hasMatch);
    return hasMatch;
  });

  if (!provinceData) {
    console.log('No matching province data found');
    console.log('Available provinces with content:');
    post.Province.forEach((prov, index) => {
      const availableProvinces = Object.keys(prov).filter(key => 
        typeof prov[key] === 'boolean' && 
        key !== 'all' && 
        prov[key] === true
      );
      console.log(`Province ${index}:`, availableProvinces);
    });
    return null;
  }

  console.log('Found matching province data:', provinceData);

  // Images might be in the provinceData directly or in the ref object
  const images = provinceData.images || (provinceData.ref && provinceData.ref.images);
  console.log('Images found:', images);

  if (!images) {
    console.error('No images found in province data:', provinceData);
    return null;
  }

  // The backend is returning the correct ImageData format, so we can use images directly
  console.log('Images structure:', images);
  console.log('First image sample:', images[0]);
  
  // Debug captionTexts
  console.log('Post captionTexts:', post.captionTexts);
  console.log('Post captionTexts type:', typeof post.captionTexts);
  console.log('Post captionTexts is array:', Array.isArray(post.captionTexts));

  // Ensure captions is always an array of CaptionData or null
  let processedCaptions: CaptionData[] | null = null;
  
  // Try to get captions from different possible sources
  if (post.captionTexts && Array.isArray(post.captionTexts)) {
    console.log('Processing captionTexts array:', post.captionTexts);
    processedCaptions = post.captionTexts.map((caption: any, index) => {
      console.log(`Processing caption ${index}:`, caption);
      if (typeof caption === 'string') {
        // Handle legacy string captions
        console.log(`Caption ${index} is string:`, caption);
        return {
          id: `caption-${index}`,
          title: `Caption ${index + 1}`,
          text: caption,
          postDate: post.month || undefined
        };
      } else if (caption && typeof caption === 'object') {
        // Handle structured caption objects
        console.log(`Caption ${index} is object:`, caption);
        return {
          id: caption.id || `caption-${index}`,
          title: caption.title || `Caption ${index + 1}`,
          text: caption.text || caption.caption || JSON.stringify(caption),
          postDate: caption.postDate || post.month || undefined,
          backgroundInfo: caption.backgroundInfo
        };
      }
      // Fallback for unknown caption types
      const fallbackText = String(caption);
      console.log(`Caption ${index} fallback:`, fallbackText);
      return {
        id: `caption-${index}`,
        title: `Caption ${index + 1}`,
        text: fallbackText,
        postDate: post.month || undefined
      };
    });
  } else if (images && Array.isArray(images) && images.length > 0) {
    // If no captionTexts, create captions from image objects
    processedCaptions = images.map((img, index) => ({
      id: img.id || `image-caption-${index}`,
      title: img.name || `Image ${index + 1}`,
      text: img.caption || img.alternativeText || '',
      postDate: post.month || undefined
    }));
  }
  
  console.log('Final processed captions:', processedCaptions);

  return {
    images: images,
    captions: processedCaptions,
    calendar: post.calendar?.url || null,
    month: post.month || null,
    isExtra: post.isExtra || false
  };
};

/**
 * Sort posts by month in descending order (latest first)
 */
export const sortPostsByMonth = (posts: LightweightPost[]): LightweightPost[] => {
  return [...posts].sort((a, b) => {
    // Handle null months (extra posts) - put them at the end
    if (!a.month && !b.month) return 0;
    if (!a.month) return 1;
    if (!b.month) return -1;
    
    // Assuming month format is 'YYYY-MM-DD' or similar sortable format
    return new Date(b.month).getTime() - new Date(a.month).getTime();
  });
};

/**
 * Filter posts by type (monthly vs extra)
 * Treats null isExtra as false (monthly posts)
 */
export const filterPostsByType = (posts: LightweightPost[], isExtra: boolean): LightweightPost[] => {
  if (isExtra) {
    // For extra posts, only include those with isExtra === true
    return posts.filter(post => post.isExtra === true);
  } else {
    // For monthly posts, include those with isExtra === false or null
    return posts.filter(post => post.isExtra === false || post.isExtra === null);
  }
};

/**
 * Format month for display
 */
export const formatMonth = (month: string | null): string => {
  if (!month) return 'Unknown Date';
  
  try {
    const date = new Date(month);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  } catch (error) {
    console.error('Error formatting month:', error);
    return month; // Return original if parsing fails
  }
};

/**
 * Validate post object
 */
export const validatePost = (post: any): post is LightweightPost => {
  console.log('Validating post:', post);
  console.log('Post exists:', !!post);
  console.log('id type:', typeof post?.id, 'value:', post?.id);
  console.log('documentId type:', typeof post?.documentId, 'value:', post?.documentId);
  console.log('month type:', typeof post?.month, 'value:', post?.month);
  console.log('isExtra type:', typeof post?.isExtra, 'value:', post?.isExtra);
  console.log('Province is array:', Array.isArray(post?.Province));
  
  const isValid = post && 
         typeof post.id === 'number' && 
         typeof post.documentId === 'string' && 
         (post.isExtra === true ? true : typeof post.month === 'string') && 
         (typeof post.isExtra === 'boolean' || post.isExtra === null) &&
         Array.isArray(post.Province);
  
  console.log('Post validation result:', isValid);
  return isValid;
};

/**
 * Create error message for failed content loading
 */
export const createContentError = (error: any, userProvince?: string): string => {
  if (error.response?.status === 401) {
    return 'Authentication failed. Please log in again.';
  }
  
  if (error.response?.status === 404) {
    return 'Content not found.';
  }
  
  if (error.message?.includes('province')) {
    return `No content found for your province (${userProvince || 'unknown'})`;
  }
  
  return error.message || 'Failed to load content. Please try again.';
};
