/**
 * Social Media Feature - Public API
 *
 * This file exports all public components, hooks, and utilities
 * from the social media feature following the feature-sliced pattern.
 */

// Components
export { SocialMediaPage } from "./components/social-media-page";
export { SocialMediaList } from "./components/social-media-list";
export { MonthSelector } from "./components/month-selector";
export { default as SocialPostsList } from "./components/social-posts-list";

// Hooks
export { useSocialMedia } from "./hooks/use-social-media";
export { useSocialMediaContent } from "./hooks/use-social-media-content";

// Types
export type {
  SocialMediaPost,
  ProvinceData,
  ProvinceRef,
  ImageData,
  ImageFormat,
  LightweightPost,
  SocialMediaResponse,
  SocialMediaPostResponse,
  LightweightPostsResponse,
  UseSocialMediaResult,
  UseSocialMediaContentResult,
  SocialMediaContent,
  SocialMediaListProps,
  SocialPostsListProps,
  MonthSelectorProps,
  SocialMediaError,
  SocialMediaFilters,
} from "./types";

// API Client
export { SocialMediaApiClient, socialMediaApi } from "./lib/api-client";

// Utilities
export {
  isPostAvailableForProvince,
  getProvinceContent,
  sortPostsByMonth,
  filterPostsByType,
  formatMonth,
  validatePost,
  createContentError,
} from "./lib/utils";
