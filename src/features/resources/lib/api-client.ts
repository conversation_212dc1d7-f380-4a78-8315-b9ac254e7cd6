import { DocsApiClient, Document, DocumentType } from "@/shared/lib/docs-api-client";

// Re-export shared types for convenience
export type {
  Document as ResourceDocument,
  ProvinceFile,
  DocumentsResponse as ResourcesResponse,
  DocumentResponse as ResourceDocumentResponse,
} from "@/shared/lib/docs-api-client";

/**
 * API client for resources using shared DocsApiClient
 */
export class ResourcesApiClient {
  private static readonly DOCUMENT_TYPE: DocumentType = "resource";

  /**
   * Get all resource documents
   */
  static async getResources(params?: {
    page?: number;
    pageSize?: number;
  }) {
    return DocsApiClient.getDocuments(ResourcesApiClient.DOCUMENT_TYPE, params);
  }

  /**
   * Get a specific resource document by documentId
   */
  static async getResourceDocument(documentId: string) {
    return DocsApiClient.getDocument(ResourcesApiClient.DOCUMENT_TYPE, documentId);
  }

  /**
   * Get resource documents by province
   */
  static async getResourcesByProvince(province: string) {
    return DocsApiClient.getDocumentsByProvince(ResourcesApiClient.DOCUMENT_TYPE, province);
  }

  /**
   * Get all available provinces for resources
   */
  static async getResourceProvinces() {
    return DocsApiClient.getProvinces(ResourcesApiClient.DOCUMENT_TYPE);
  }

  /**
   * Transform resource documents to categories for the landing page
   */
  static transformDocumentsToCategories(documents: Document[]) {
    return DocsApiClient.transformDocumentsToCategories(documents);
  }

  /**
   * Format province name to readable title
   */
  static formatProvinceTitle(province: string | null | undefined) {
    return DocsApiClient.formatProvinceTitle(province);
  }
}

// Export convenience functions
export const {
  getResources,
  getResourceDocument,
  getResourcesByProvince,
  getResourceProvinces,
  transformDocumentsToCategories,
  formatProvinceTitle,
} = ResourcesApiClient;
