"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON><PERSON>, CardTitle } from "@/shared/ui/card";
import { Button } from "@/shared/ui/button";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import { Download, FileText, ExternalLink } from "lucide-react";

interface DocumentViewerProps {
  fileUrl: string;
  title: string;
  fileType?: string;
  className?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  fileUrl,
  title,
  fileType,
  className,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [iframeHeight, setIframeHeight] = useState<number>(800);
  const viewerRef = useRef<HTMLIFrameElement>(null);

  // Calculate iframe height based on window size
  const calculateIframeHeight = () => {
    if (typeof window !== "undefined") {
      return Math.max(600, (window.innerHeight - 200) * 1.41);
    }
    return 800;
  };

  // Handle download
  const handleDownload = () => {
    const filename = `${title.replace(/\s+/g, "-").toLowerCase()}${fileType || ""}`;
    const link = document.createElement("a");
    link.href = fileUrl;
    link.download = filename;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Handle external view
  const handleExternalView = () => {
    window.open(fileUrl, "_blank");
  };

  // Update iframe height on window resize
  useEffect(() => {
    const updateHeight = () => {
      setIframeHeight(calculateIframeHeight());
    };

    updateHeight();
    window.addEventListener("resize", updateHeight);
    return () => window.removeEventListener("resize", updateHeight);
  }, []);

  // Handle iframe load
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // Determine if file can be displayed inline
  const canDisplayInline = () => {
    if (!fileType) return true; // Default to trying inline display
    
    const inlineTypes = ['.pdf', '.txt', '.html', '.htm'];
    const officeTypes = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'];
    
    return inlineTypes.includes(fileType.toLowerCase()) || 
           officeTypes.includes(fileType.toLowerCase());
  };

  // Get file type badge variant
  const getFileTypeBadge = () => {
    if (!fileType) return { variant: "secondary" as const, label: "Document" };
    
    const type = fileType.toLowerCase();
    if (type === '.pdf') return { variant: "default" as const, label: "PDF" };
    if (['.doc', '.docx'].includes(type)) return { variant: "outline" as const, label: "Word" };
    if (['.xls', '.xlsx'].includes(type)) return { variant: "outline" as const, label: "Excel" };
    if (['.ppt', '.pptx'].includes(type)) return { variant: "outline" as const, label: "PowerPoint" };
    
    return { variant: "secondary" as const, label: type.replace('.', '').toUpperCase() };
  };

  const fileTypeBadge = getFileTypeBadge();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Document Viewer */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {title}
            </span>
            <div className="flex items-center gap-2">
              <Badge variant={fileTypeBadge.variant}>{fileTypeBadge.label}</Badge>
              <div className="flex gap-2">
                <Button onClick={handleExternalView} size="sm" variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open
                </Button>
                <Button onClick={handleDownload} size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {canDisplayInline() ? (
            <div className="border rounded-lg overflow-hidden relative">
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10">
                  <Skeleton className="w-full h-96" />
                </div>
              )}
              <iframe
                ref={viewerRef}
                src={fileUrl}
                width="100%"
                height={iframeHeight}
                title={`Document Viewer - ${title}`}
                className="border-0"
                onLoad={handleIframeLoad}
              />
            </div>
          ) : (
            <div className="border rounded-lg p-8 text-center space-y-4">
              <FileText className="h-16 w-16 mx-auto text-muted-foreground" />
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Document Preview Not Available</h3>
                <p className="text-muted-foreground">
                  This document type cannot be previewed inline. Please download or open in a new tab to view.
                </p>
              </div>
              <div className="flex gap-2 justify-center">
                <Button onClick={handleExternalView} variant="outline">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in New Tab
                </Button>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download Document
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentViewer;
