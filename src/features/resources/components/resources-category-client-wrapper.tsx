"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList from "@/shared/components/category-items-list";
import { CategoryItem } from "@/shared/components/category-items-list";
import { useResourcesCategory } from "../hooks/use-resources-category";
import { formatProvinceTitle } from "../lib/api-client";

interface ResourcesCategoryClientWrapperProps {
  category: string;
}

const ResourcesCategoryClientWrapper: React.FC<ResourcesCategoryClientWrapperProps> = ({
  category,
}) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = useResourcesCategory(category);

  const handleItemSelect = React.useCallback((item: CategoryItem) => {
    // Find the original document to get the documentId
    const originalDocument = items.find((doc) => doc.id.toString() === item.id);
    
    if (!originalDocument) {
      console.error("Could not find original document for item:", item);
      return;
    }

    // Create slug from title
    const slug = originalDocument.title.replace(/\s+/g, "-").toLowerCase();
    
    // Find the province file for this category
    const provinceFile = originalDocument.provinceFile.find(
      (pf) => pf.province === category
    );
    
    if (provinceFile?.file) {
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
        file: provinceFile.file.url,
        ext: provinceFile.file.ext.replace('.', ''),
        filetype: provinceFile.file.ext,
      });
      
      router.push(`/resources/document/${slug}?${queryParams.toString()}`);
    } else {
      // Fallback if no file is available
      const queryParams = new URLSearchParams({
        title: originalDocument.title,
        documentId: originalDocument.documentId,
      });
      
      router.push(`/resources/document/${slug}?${queryParams.toString()}`);
    }
  }, [items, category, router]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  const handleSearch = React.useCallback((query: string) => {
    // Implement search functionality if needed
    console.log("Search query:", query);
  }, []);

  const handleFilterChange = React.useCallback((filter: string) => {
    // Implement filter functionality if needed
    console.log("Filter changed:", filter);
  }, []);

  // Transform items for the CategoryItemsList component
  const transformedItems: CategoryItem[] = items.map((doc) => {
    // Find the province file for this category
    const provinceFile = doc.provinceFile.find((pf) => pf.province === category);
    
    return {
      id: doc.id.toString(),
      title: doc.title,
      description: `Resource document for ${formatProvinceTitle(category).toLowerCase()}`,
      thumbnail: provinceFile?.thumbnail ? {
        url: provinceFile.thumbnail.url,
        alternativeText: provinceFile.thumbnail.alternativeText,
        formats: provinceFile.thumbnail.formats ? {
          thumbnail: provinceFile.thumbnail.formats.squared ? {
            url: provinceFile.thumbnail.formats.squared.url
          } : undefined
        } : undefined
      } : undefined,
      file: provinceFile?.file ? {
        url: provinceFile.file.url,
        ext: provinceFile.file.ext,
        size: provinceFile.file.size
      } : undefined,
      metadata: {
        documentType: doc.documentType,
        province: category,
        fileType: provinceFile?.file?.ext || "unknown",
        fileSize: provinceFile?.file?.size || 0,
      },
      category: category,
    };
  });

  const formattedTitle = formatProvinceTitle(category);

  return (
    <CategoryItemsList
      title={`${formattedTitle} Resource Documents`}
      subtitle={`Resource documents for ${formattedTitle.toLowerCase()}`}
      items={transformedItems}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Document"
      emptyStateMessage={`No resource documents found for ${formattedTitle.toLowerCase()}`}
      retryAction={handleRetry}
      showSearchAndFilter={true}
      onSearch={handleSearch}
      onFilterChange={handleFilterChange}
      availableFilters={[formattedTitle]}
    />
  );
};

export default ResourcesCategoryClientWrapper;
