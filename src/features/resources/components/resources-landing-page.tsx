"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { CategoryLandingPage, CategoryItem } from "@/shared/components";
import { useResources } from "../hooks/use-resources";

const ResourcesLandingPage: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = useResources();

  const handleCategorySelect = (category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/resources/${category.id}`);
  };

  return (
    <CategoryLandingPage
      title="Resources"
      subtitle="Resource documents applicable for all provinces"
      description="Access resource documents organized by province. Each province contains relevant resources and documents for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No resource categories found"
      retryAction={retry}
    />
  );
};

export default ResourcesLandingPage;
