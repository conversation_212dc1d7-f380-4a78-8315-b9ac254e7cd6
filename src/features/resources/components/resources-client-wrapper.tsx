"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryLandingPage from "@/shared/components/category-landing-page";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { useResources } from "../hooks/use-resources";

const ResourcesClientWrapper: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = useResources();

  const handleCategorySelect = React.useCallback((category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/resources/${category.id}`);
  }, [router]);

  const handleRetry = React.useCallback(() => {
    retry();
  }, [retry]);

  return (
    <CategoryLandingPage
      title="Resources"
      subtitle="Resource documents organized by province"
      description="Access resource documents organized by province. Each province contains relevant resources and materials for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No resource categories found"
      retryAction={handleRetry}
    />
  );
};

export default ResourcesClientWrapper;
