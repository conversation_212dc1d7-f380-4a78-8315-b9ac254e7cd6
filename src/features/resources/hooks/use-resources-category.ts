"use client";

import { useState, useEffect, useCallback } from "react";
import { ResourcesApiClient, ResourceDocument } from "../lib/api-client";

export interface UseResourcesCategoryResult {
  items: ResourceDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useResourcesCategory(category: string): UseResourcesCategoryResult {
  const [items, setItems] = useState<ResourceDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResourcesByCategory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ResourcesApiClient.getResourcesByProvince(category);

      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      console.error("Error in useResourcesCategory:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch resources for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, [category]);

  const retry = useCallback(() => {
    fetchResourcesByCategory();
  }, [fetchResourcesByCategory]);

  useEffect(() => {
    if (category) {
      fetchResourcesByCategory();
    }
  }, [category, fetchResourcesByCategory]);

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
