"use client";

import { useState, useEffect, useCallback } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { ResourcesApiClient } from "../lib/api-client";

export interface UseResourcesResult {
  categories: CategoryItem[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useResources(): UseResourcesResult {
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResources = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await ResourcesApiClient.getResources();

      if (response.error) {
        setError(response.error);
        setCategories([]);
        return;
      }

      // Transform documents to categories
      const transformedCategories = ResourcesApiClient.transformDocumentsToCategories(response.data);

      // Convert to CategoryItem format
      const categoryItems: CategoryItem[] = transformedCategories.map((cat) => ({
        id: cat.id,
        title: cat.title,
        description: cat.description,
        count: cat.documentCount,
      }));

      setCategories(categoryItems);
    } catch (err) {
      console.error("Error in useResources:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch resources");
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    fetchResources();
  }, [fetchResources]);

  useEffect(() => {
    fetchResources();
  }, [fetchResources]);

  return {
    categories,
    isLoading,
    error,
    retry,
  };
}
