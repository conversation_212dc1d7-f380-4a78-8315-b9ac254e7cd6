"use client";

import React from "react";
import { useRouter } from "next/navigation";
import CategoryItemsList, { CategoryItem } from "@/shared/components/category-items-list";
import { useFintracCategory } from "../hooks/use-fintrac-category";
import { getFileExtension, validateFileExtension } from "@/shared/lib/file-utils";

interface FintracCategoryItemsProps {
  category: string;
}

const FintracCategoryItems: React.FC<FintracCategoryItemsProps> = ({ category }) => {
  const router = useRouter();
  const { items, isLoading, error, retry } = useFintracCategory(category);

  const handleItemSelect = (item: CategoryItem) => {
    try {
      // Navigate to the document detail page
      const slug = item.title.replace(/\s+/g, "-").toLowerCase();
      const fileUrl = item.file?.url || "";

      if (!fileUrl) {
        console.warn("No file URL found for item:", item);
        return;
      }

      // Use our file utility to get the correct extension with all available properties
      const fileInfo = {
        ext: item.file?.ext,
        url: item.file?.url,
        name: item.file?.name,
        mime: item.file?.mime
      };

      const fileExt = getFileExtension(fileInfo);
      
      // Validate the file extension for debugging
      const validation = validateFileExtension(fileInfo);
      if (validation.warnings.length > 0) {
        console.warn(`File extension validation warnings for "${item.title}":`, validation.warnings);
      }

      console.log(`Navigating to document: ${item.title}`, {
        slug,
        fileUrl,
        detectedExt: fileExt,
        originalExt: item.file?.ext,
        validation
      });

      router.push(`/fintrac/document/${slug}?title=${encodeURIComponent(item.title)}&file=${encodeURIComponent(fileUrl)}&ext=${fileExt}&category=${category}`);
    } catch (error) {
      console.error("Error navigating to document:", error);
      // Fallback: try to navigate with basic info
      const slug = item.title.replace(/\s+/g, "-").toLowerCase();
      const fileUrl = item.file?.url || "";
      if (fileUrl) {
        router.push(`/fintrac/document/${slug}?title=${encodeURIComponent(item.title)}&file=${encodeURIComponent(fileUrl)}&category=${category}`);
      }
    }
  };

  const formatCategoryTitle = (categoryName: string) => {
    return categoryName.replace(/([A-Z])/g, " $1").replace(/^./, (str) => str.toUpperCase());
  };

  // Transform FintracDocument items to CategoryItem format
  const transformedItems: CategoryItem[] = items.map((item) => {
    // Find the province file for this category
    // Handle province: null as a match for "allProvinces"
    const provinceFile = item.provinceFile.find((pf) => 
      pf.province === category || (pf.province === null && category === 'allProvinces')
    );
    
    // Use the found province file
    const fileToUse = provinceFile?.file;
    const thumbnailToUse = provinceFile?.thumbnail;

    // Create file info for validation
    const fileInfo = fileToUse ? {
      ext: fileToUse.ext,
      url: fileToUse.url,
      name: fileToUse.name,
      mime: fileToUse.mime
    } : undefined;

    // Validate file extension if file info exists
    if (fileInfo) {
      const validation = validateFileExtension(fileInfo);
      if (validation.warnings.length > 0) {
        console.warn(`File validation warnings for "${item.title}":`, validation.warnings);
      }
    }

    // Log the transformation for debugging
    console.log(`Transforming Fintrac item "${item.title}":`, {
      originalProvince: provinceFile?.province,
      category,
      hasProvinceFile: !!provinceFile,
      fileToUse: fileToUse ? {
        url: fileToUse.url,
        ext: fileToUse.ext,
        name: fileToUse.name
      } : null
    });

    return {
      id: item.id.toString(), // Convert number to string
      title: item.title,
      description: `Fintrac document for ${formatCategoryTitle(category).toLowerCase()}`,
      thumbnail: thumbnailToUse ? {
        url: thumbnailToUse.url,
        alternativeText: thumbnailToUse.alternativeText,
        formats: thumbnailToUse.formats ? {
          thumbnail: thumbnailToUse.formats.squared ? {
            url: thumbnailToUse.formats.squared.url
          } : undefined
        } : undefined
      } : undefined,
      file: fileToUse ? {
        url: fileToUse.url,
        ext: fileToUse.ext,
        name: fileToUse.name,
        mime: fileToUse.mime,
        size: fileToUse.size
      } : undefined,
      metadata: {
        documentType: item.documentType,
        province: category,
        documentId: item.documentId,
        publishedAt: item.publishedAt,
      },
      category: category,
    };
  });

  return (
    <CategoryItemsList
      title={`${formatCategoryTitle(category)} Fintrac Documents`}
      subtitle={`Fintrac documents applicable for ${category}`}
      items={transformedItems}
      isLoading={isLoading}
      error={error}
      onItemSelect={handleItemSelect}
      buttonLabel="View Document"
      emptyStateMessage={`No fintrac documents found for ${category}`}
      retryAction={retry}
      showSearchAndFilter={true}
      onSearch={(query) => {
        // Implement search functionality
        console.log("Search query:", query);
      }}
      onFilterChange={(filter) => {
        // Implement filter functionality
        console.log("Filter changed:", filter);
      }}
      availableFilters={[category]}
    />
  );
};

export default FintracCategoryItems;
