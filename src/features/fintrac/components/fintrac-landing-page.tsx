"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { CategoryLandingPage, CategoryItem } from "@/shared/components";
import { useFintrac } from "../hooks/use-fintrac";

const FintracLandingPage: React.FC = () => {
  const router = useRouter();
  const { categories, isLoading, error, retry } = useFintrac();

  const handleCategorySelect = (category: CategoryItem) => {
    // Navigate to the category items page
    router.push(`/fintrac/${category.id}`);
  };

  return (
    <CategoryLandingPage
      title="Fintrac"
      subtitle="Fintrac documents applicable for all provinces"
      description="Access fintrac documents organized by province. Each province contains relevant fintrac forms and documents for your area."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      emptyStateMessage="No fintrac categories found"
      retryAction={retry}
    />
  );
};

export default FintracLandingPage;
