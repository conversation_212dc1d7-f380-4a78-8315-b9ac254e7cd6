"use client";

import { useState, useEffect, useCallback } from "react";
import { FintracApiClient, FintracDocument } from "../lib/api-client";

export interface UseFintracDocumentResult {
  document?: FintracDocument;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useFintracDocument(documentId: string): UseFintracDocumentResult {
  const [document, setDocument] = useState<FintracDocument | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (!documentId || documentId === 'undefined') {
        setError("Invalid document ID");
        setDocument(undefined);
        return;
      }
      
      const response = await FintracApiClient.getFintracDocument(documentId);
      
      if (response.error) {
        setError(response.error);
        setDocument(undefined);
      } else {
        setDocument(response.data);
      }
    } catch (err) {
      console.error("Error in useFintracDocument:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch fintrac document");
      setDocument(undefined);
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  const retry = useCallback(() => {
    fetchDocument();
  }, [fetchDocument]);

  useEffect(() => {
    if (documentId) {
      fetchDocument();
    }
  }, [documentId, fetchDocument]);

  return {
    document,
    isLoading,
    error,
    retry,
  };
}
