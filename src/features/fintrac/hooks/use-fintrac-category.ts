"use client";

import { useState, useEffect, useCallback } from "react";
import { FintracApiClient, FintracDocument } from "../lib/api-client";

export interface UseFintracCategoryResult {
  items: FintracDocument[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useFintracCategory(category: string): UseFintracCategoryResult {
  const [items, setItems] = useState<FintracDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFintracByCategory = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await FintracApiClient.getFintracByProvince(category);
      
      if (response.error) {
        setError(response.error);
        setItems([]);
      } else {
        setItems(response.data);
      }
    } catch (err) {
      console.error("Error in useFintracCategory:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch fintrac documents for this category");
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, [category]);

  const retry = useCallback(() => {
    fetchFintracByCategory();
  }, [fetchFintracByCategory]);

  useEffect(() => {
    if (category) {
      fetchFintracByCategory();
    }
  }, [category, fetchFintracByCategory]);

  return {
    items,
    isLoading,
    error,
    retry,
  };
}
