"use client";

import { useState, useEffect, useCallback } from "react";
import { CategoryItem } from "@/shared/components/category-landing-page";
import { FintracApiClient } from "../lib/api-client";

export interface UseFintracResult {
  categories: CategoryItem[];
  isLoading: boolean;
  error: string | null;
  retry: () => void;
}

export function useFintrac(): UseFintracResult {
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFintrac = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await FintracApiClient.getFintrac();

      if (response.error) {
        setError(response.error);
        setCategories([]);
        return;
      }

      // Transform documents to categories
      const transformedCategories = FintracApiClient.transformDocumentsToCategories(response.data);
      
      // Convert to CategoryItem format
      const categoryItems: CategoryItem[] = transformedCategories.map((cat) => ({
        id: cat.id,
        title: cat.title,
        description: cat.description,
        count: cat.documentCount,
      }));
      
      setCategories(categoryItems);
    } catch (err) {
      console.error("Error in useFintrac:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch fintrac documents");
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const retry = useCallback(() => {
    fetchFintrac();
  }, [fetchFintrac]);

  useEffect(() => {
    fetchFintrac();
  }, [fetchFintrac]);

  return {
    categories,
    isLoading,
    error,
    retry,
  };
}
