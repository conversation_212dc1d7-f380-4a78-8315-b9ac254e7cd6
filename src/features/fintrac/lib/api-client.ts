import { DocsApiClient, Document, DocumentType } from "@/shared/lib/docs-api-client";

// Re-export shared types for convenience
export type {
  Document as FintracDocument,
  ProvinceFile,
  DocumentsResponse as FintracResponse,
  DocumentResponse as FintracDocumentResponse,
} from "@/shared/lib/docs-api-client";

/**
 * API client for fintrac using shared DocsApiClient
 */
export class FintracApiClient {
  private static readonly DOCUMENT_TYPE: DocumentType = "fintrac";

  /**
   * Get all fintrac documents
   */
  static async getFintrac(params?: {
    page?: number;
    pageSize?: number;
  }) {
    return DocsApiClient.getDocuments(FintracApiClient.DOCUMENT_TYPE, params);
  }

  /**
   * Get a specific fintrac document by documentId
   */
  static async getFintracDocument(documentId: string) {
    return DocsApiClient.getDocument(FintracApiClient.DOCUMENT_TYPE, documentId);
  }

  /**
   * Get fintrac documents by province
   */
  static async getFintracByProvince(province: string) {
    return DocsApiClient.getDocumentsByProvince(FintracApiClient.DOCUMENT_TYPE, province);
  }

  /**
   * Get all available provinces for fintrac documents
   */
  static async getFintracProvinces() {
    return DocsApiClient.getProvinces(FintracApiClient.DOCUMENT_TYPE);
  }

  /**
   * Transform fintrac documents to categories for the landing page
   */
  static transformDocumentsToCategories(documents: Document[]) {
    return DocsApiClient.transformDocumentsToCategories(documents);
  }

  /**
   * Format province name to readable title
   */
  static formatProvinceTitle(province: string | null | undefined) {
    return DocsApiClient.formatProvinceTitle(province);
  }
}

// Export convenience functions
export const {
  getFintrac,
  getFintracDocument,
  getFintracByProvince,
  getFintracProvinces,
  transformDocumentsToCategories,
  formatProvinceTitle,
} = FintracApiClient;
