"use client";

import { useApi } from "@/shared/hooks/use-api";
import { apiClient } from "@/shared/lib/api";
import { StrapiEntity } from "@/shared/types/api";

interface ExampleData extends StrapiEntity {
  attributes: {
    title: string;
    content: string;
  };
}

export function ExampleComponent() {
  const { data, loading, error, refetch } = useApi(() =>
    apiClient.getStrapiCollection<ExampleData>("examples")
  );

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error: {error}
        <button
          onClick={refetch}
          className="ml-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h2 className="text-2xl font-bold mb-4">Example Feature</h2>
      {data?.data && data.data.length > 0 ? (
        <ul className="space-y-2">
          {data.data.map((item) => (
            <li key={item.id} className="p-3 border rounded">
              <h3 className="font-semibold">{item.attributes.title}</h3>
              <p className="text-gray-600">{item.attributes.content}</p>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500">No data available</p>
      )}
    </div>
  );
}
