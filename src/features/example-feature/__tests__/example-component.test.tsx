import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ExampleComponent } from "../components/example-component";
import * as apiHooks from "@/shared/hooks/use-api";

// Mock the useApi hook
jest.mock("@/shared/hooks/use-api");

const mockUseApi = apiHooks.useApi as jest.MockedFunction<
  typeof apiHooks.useApi
>;

describe("ExampleComponent", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders loading state", () => {
    mockUseApi.mockReturnValue({
      data: null,
      loading: true,
      error: null,
      refetch: jest.fn(),
    });

    render(<ExampleComponent />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("renders error state with retry button", async () => {
    const mockRefetch = jest.fn();
    mockUseApi.mockReturnValue({
      data: null,
      loading: false,
      error: "Failed to fetch data",
      refetch: mockRefetch,
    });

    const user = userEvent.setup();
    render(<ExampleComponent />);

    expect(screen.getByText("Error: Failed to fetch data")).toBeInTheDocument();

    const retryButton = screen.getByText("Retry");
    expect(retryButton).toBeInTheDocument();

    await user.click(retryButton);
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });

  it("renders data when available", () => {
    const mockData = {
      data: [
        {
          id: 1,
          attributes: {
            title: "Test Title 1",
            content: "Test Content 1",
          },
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01",
        },
        {
          id: 2,
          attributes: {
            title: "Test Title 2",
            content: "Test Content 2",
          },
          createdAt: "2023-01-01",
          updatedAt: "2023-01-01",
        },
      ],
    };

    mockUseApi.mockReturnValue({
      data: mockData,
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<ExampleComponent />);

    expect(screen.getByText("Example Feature")).toBeInTheDocument();
    expect(screen.getByText("Test Title 1")).toBeInTheDocument();
    expect(screen.getByText("Test Content 1")).toBeInTheDocument();
    expect(screen.getByText("Test Title 2")).toBeInTheDocument();
    expect(screen.getByText("Test Content 2")).toBeInTheDocument();
  });

  it("renders no data message when data array is empty", () => {
    mockUseApi.mockReturnValue({
      data: { data: [] },
      loading: false,
      error: null,
      refetch: jest.fn(),
    });

    render(<ExampleComponent />);
    expect(screen.getByText("No data available")).toBeInTheDocument();
  });
});
