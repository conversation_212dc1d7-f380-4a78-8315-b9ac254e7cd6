import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import EmailSignaturePage from '../../../app/(inapp)/email-signature/page';

// Mock the useAuth hook
jest.mock('@/shared/hooks/use-auth', () => ({
  useAuth: jest.fn(() => ({
    user: {
      id: '1',
      firstname: '<PERSON>',
      lastname: '<PERSON><PERSON>',
      email: '<EMAIL>',
      position: 'Real Estate Agent',
      phone: '************',
      website: 'https://example.com',
      team: {
        tagline: 'Your trusted real estate team',
        logo: { url: 'https://example.com/logo.png' },
        cobranded: true,
        customStyle: {
          enabled: true,
          highlightColorHex: '#ff0000',
        },
      },
    },
    isLoading: false,
  })),
}));

// Mock format-url
jest.mock('@/shared/lib/format-url', () => ({
  __esModule: true,
  default: jest.fn((url: string) => url.startsWith('http') ? url : `https://${url}`),
}));

// Mock auth cookies
jest.mock('@/shared/lib/auth', () => ({
  getCookie: jest.fn((name: string) => {
    if (name === 'jwt') return 'mock-jwt-token';
    if (name === 'userId') return 'mock-user-id';
    return null;
  }),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('EmailSignaturePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ circularPhotoUrl: 'https://example.com/photo.jpg' }),
    });
  });

  it('renders the email signature page', () => {
    render(<EmailSignaturePage />);

    expect(screen.getByText('Indi Signature Generator')).toBeInTheDocument();
    expect(screen.getByText(/The Indi Email Signature Generator allows you to create/)).toBeInTheDocument();
  });

  it('renders the signature form', () => {
    render(<EmailSignaturePage />);

    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Generate Signature')).toBeInTheDocument();
  });

  it('renders signature options', () => {
    render(<EmailSignaturePage />);

    expect(screen.getByText('Signature Options')).toBeInTheDocument();
    expect(screen.getByText('Show Photo')).toBeInTheDocument();
    expect(screen.getByText('Show Logo')).toBeInTheDocument();
    expect(screen.getByText('Using Outlook (Desktop App Only)')).toBeInTheDocument();
    expect(screen.getByText('Apply Brand Style')).toBeInTheDocument();
  });

  it('toggles photo option', async () => {
    render(<EmailSignaturePage />);

    const photoSwitch = screen.getByRole('switch', { name: /show photo/i });
    expect(photoSwitch).not.toBeChecked();

    fireEvent.click(photoSwitch);
    await waitFor(() => {
      expect(photoSwitch).toBeChecked();
    });
  });

  it('toggles logo option', async () => {
    render(<EmailSignaturePage />);

    const logoSwitch = screen.getByRole('switch', { name: /show logo/i });
    expect(logoSwitch).not.toBeChecked();

    fireEvent.click(logoSwitch);
    await waitFor(() => {
      expect(logoSwitch).toBeChecked();
    });
  });

  it('toggles outlook option', async () => {
    render(<EmailSignaturePage />);

    const outlookSwitch = screen.getByRole('switch', { name: /using outlook/i });
    expect(outlookSwitch).not.toBeChecked();

    fireEvent.click(outlookSwitch);
    await waitFor(() => {
      expect(outlookSwitch).toBeChecked();
    });
  });

  it('toggles brand style option', async () => {
    render(<EmailSignaturePage />);

    const brandStyleSwitch = screen.getByRole('switch', { name: /apply brand style/i });
    expect(brandStyleSwitch).not.toBeChecked();

    fireEvent.click(brandStyleSwitch);
    await waitFor(() => {
      expect(brandStyleSwitch).toBeChecked();
    });
  });

  it('shows extra button section', () => {
    render(<EmailSignaturePage />);

    expect(screen.getByText(/Extra Button/)).toBeInTheDocument();
    expect(screen.getByLabelText('Label')).toBeInTheDocument();
    expect(screen.getByLabelText('Link')).toBeInTheDocument();
    expect(screen.getByText('Add Button')).toBeInTheDocument();
  });

  it('handles extra button input changes', async () => {
    render(<EmailSignaturePage />);

    const labelInput = screen.getByLabelText('Label');
    const linkInput = screen.getByLabelText('Link');

    fireEvent.change(labelInput, { target: { value: 'Book Now' } });
    fireEvent.change(linkInput, { target: { value: 'calendly.com/user' } });

    await waitFor(() => {
      expect(labelInput).toHaveValue('Book Now');
      expect(linkInput).toHaveValue('https://calendly.com/user'); // Should be formatted
    });
  });

  it('generates signature when form is submitted', async () => {
    render(<EmailSignaturePage />);

    const generateButton = screen.getByText('Generate Signature');
    fireEvent.click(generateButton);

    await waitFor(() => {
      // Should show the signature (we can check for the copy buttons)
      expect(screen.getByText('Copy Signature')).toBeInTheDocument();
      expect(screen.getByText('Copy HTML Code')).toBeInTheDocument();
    });
  });
});
