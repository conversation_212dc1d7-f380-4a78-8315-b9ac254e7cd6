import React from 'react';
import { render, screen } from '@testing-library/react';
import { SocialIcons } from '../components/social-icons';
import { SignatureProvider } from '../context/signature-context';

// Mock the icons-links
jest.mock('../components/icons-links', () => ({
  iconsLinks: {
    facebook: {
      white: 'facebook-white.png',
      black: 'facebook-black.png',
      standard: 'facebook-standard.png',
    },
    instagram: {
      white: 'instagram-white.png',
      black: 'instagram-black.png',
      standard: 'instagram-standard.png',
    },
    linkedin: {
      white: 'linkedin-white.png',
      black: 'linkedin-black.png',
      standard: 'linkedin-standard.png',
    },
    twitter: {
      white: 'twitter-white.png',
      black: 'twitter-black.png',
      standard: 'twitter-standard.png',
    },
    youtube: {
      white: 'youtube-white.png',
      black: 'youtube-black.png',
      standard: 'youtube-standard.png',
    },
  },
}));

const renderWithProvider = (component: React.ReactElement, contextData = {}) => {
  const initialContext = {
    firstname: '',
    lastname: '',
    email: '',
    position: '',
    phone: '',
    website: '',
    team: { logo: { url: '' } },
    facebook: '',
    instagram: '',
    linkedin: '',
    twitter: '',
    youtube: '',
    ...contextData,
  };

  return render(
    <SignatureProvider initial={initialContext}>
      {component}
    </SignatureProvider>
  );
};

describe('SocialIcons', () => {
  it('renders social icons for platforms with values', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
      linkedin: 'https://linkedin.com/in/user',
    };

    renderWithProvider(<SocialIcons />, contextData);

    // Should render Facebook and LinkedIn icons
    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(2);
    expect(links[0]).toHaveAttribute('href', 'https://facebook.com/user');
    expect(links[1]).toHaveAttribute('href', 'https://linkedin.com/in/user');
  });

  it('does not render icons for platforms without values', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
      instagram: '', // Empty value
      linkedin: 'https://linkedin.com/in/user',
    };

    renderWithProvider(<SocialIcons />, contextData);

    // Should render 2 links (Facebook and LinkedIn but not Instagram)
    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(2);
    expect(links[0]).toHaveAttribute('href', 'https://facebook.com/user');
    expect(links[1]).toHaveAttribute('href', 'https://linkedin.com/in/user');
  });

  it('uses standard icons by default', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
    };

    renderWithProvider(<SocialIcons />, contextData);

    const facebookImg = screen.getByRole('presentation');
    expect(facebookImg).toHaveAttribute('src', 'facebook-standard.png');
  });

  it('uses white icons when isDark is true', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
    };

    renderWithProvider(<SocialIcons isDark={true} />, contextData);

    const facebookImg = screen.getByRole('presentation');
    expect(facebookImg).toHaveAttribute('src', 'facebook-white.png');
  });

  it('uses black icons when isBranded is true and isDark is false', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
    };

    renderWithProvider(<SocialIcons isBranded={true} isDark={false} />, contextData);

    const facebookImg = screen.getByRole('presentation');
    expect(facebookImg).toHaveAttribute('src', 'facebook-black.png');
  });

  it('uses white icons when both isBranded and isDark are true', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
    };

    renderWithProvider(<SocialIcons isBranded={true} isDark={true} />, contextData);

    const facebookImg = screen.getByRole('presentation');
    expect(facebookImg).toHaveAttribute('src', 'facebook-white.png');
  });

  it('renders all social platforms when all have values', () => {
    const contextData = {
      facebook: 'https://facebook.com/user',
      instagram: 'https://instagram.com/user',
      linkedin: 'https://linkedin.com/in/user',
      twitter: 'https://twitter.com/user',
      youtube: 'https://youtube.com/user',
    };

    renderWithProvider(<SocialIcons />, contextData);

    const links = screen.getAllByRole('link');
    expect(links).toHaveLength(5);
    expect(links[0]).toHaveAttribute('href', 'https://facebook.com/user');
    expect(links[1]).toHaveAttribute('href', 'https://instagram.com/user');
    expect(links[2]).toHaveAttribute('href', 'https://linkedin.com/in/user');
    expect(links[3]).toHaveAttribute('href', 'https://twitter.com/user');
    expect(links[4]).toHaveAttribute('href', 'https://youtube.com/user');
  });

  it('renders no icons when no social platforms have values', () => {
    const contextData = {
      facebook: '',
      instagram: '',
      linkedin: '',
      twitter: '',
      youtube: '',
    };

    renderWithProvider(<SocialIcons />, contextData);

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });
});
