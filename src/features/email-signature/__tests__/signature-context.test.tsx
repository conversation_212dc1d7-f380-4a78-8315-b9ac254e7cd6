import React from 'react';
import { render, screen, act } from '@testing-library/react';
import { SignatureProvider, useSignature } from '../context/signature-context';

// Test component that uses the signature context
const TestComponent = () => {
  const [context, setContext] = useSignature();

  return (
    <div>
      <div data-testid="firstname">{context.firstname}</div>
      <div data-testid="lastname">{context.lastname}</div>
      <div data-testid="email">{context.email}</div>
      <button
        onClick={() => setContext({ ...context, firstname: 'Updated' })}
        data-testid="update-button"
      >
        Update
      </button>
    </div>
  );
};

describe('SignatureContext', () => {
  const initialContext = {
    firstname: 'John',
    lastname: 'Doe',
    email: '<EMAIL>',
    position: 'Agent',
    phone: '555-1234',
    website: 'example.com',
    team: { logo: { url: '' } },
  };

  it('provides initial context values', () => {
    render(
      <SignatureProvider initial={initialContext}>
        <TestComponent />
      </SignatureProvider>
    );

    expect(screen.getByTestId('firstname')).toHaveTextContent('John');
    expect(screen.getByTestId('lastname')).toHaveTextContent('Doe');
    expect(screen.getByTestId('email')).toHaveTextContent('<EMAIL>');
  });

  it('updates context when setContext is called', () => {
    render(
      <SignatureProvider initial={initialContext}>
        <TestComponent />
      </SignatureProvider>
    );

    const updateButton = screen.getByTestId('update-button');
    
    act(() => {
      updateButton.click();
    });

    expect(screen.getByTestId('firstname')).toHaveTextContent('Updated');
  });

  it('throws error when useSignature is used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useSignature must be used within SignatureProvider');

    consoleSpy.mockRestore();
  });

  it('handles empty initial context', () => {
    const emptyContext = {
      firstname: '',
      lastname: '',
      email: '',
      position: '',
      phone: '',
      website: '',
      team: { logo: { url: '' } },
    };

    render(
      <SignatureProvider initial={emptyContext}>
        <TestComponent />
      </SignatureProvider>
    );

    expect(screen.getByTestId('firstname')).toHaveTextContent('');
    expect(screen.getByTestId('lastname')).toHaveTextContent('');
    expect(screen.getByTestId('email')).toHaveTextContent('');
  });
});
