"use client";

import React, { createContext, useContext, useState } from "react";

export type SignatureData = {
  firstname?: string;
  lastname?: string;
  titles?: string;
  position?: string;
  license?: string;
  email?: string;
  phone?: string;
  ext?: string;
  secondPhone?: string;
  secondExt?: string;
  tagline?: string;
  website?: string;
  applicationLink?: string;
  appointmentScheduleLink?: string;
  googleReviewsLink?: string;
  instagram?: string;
  facebook?: string;
  linkedin?: string;
  twitter?: string;
  youtube?: string;
  secondaryName?: string;
  secondaryPosition?: string;
  secondaryEmail?: string;
  secondaryPhone?: string;
  team?: Record<string, unknown>;
  brokerage?: string;
  circularPhotoUrl?: string;
  photo?: Record<string, unknown>;
  badges?: Record<string, unknown>[];
  showBadges?: { website?: boolean; emailSignature?: boolean };
};

const SignatureContext = createContext<[
  SignatureData,
  React.Dispatch<React.SetStateAction<SignatureData>>
] | null>(null);

export function useSignature() {
  const ctx = useContext(SignatureContext);
  if (!ctx) throw new Error("useSignature must be used within SignatureProvider");
  return ctx;
}

export function SignatureProvider({
  initial,
  children,
}: {
  initial: SignatureData;
  children: React.ReactNode;
}) {
  const state = useState<SignatureData>(initial);
  return (
    <SignatureContext.Provider value={state}>{children}</SignatureContext.Provider>
  );
}

export default SignatureContext;

