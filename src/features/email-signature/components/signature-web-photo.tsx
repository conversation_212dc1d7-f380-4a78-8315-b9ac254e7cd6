"use client";

import React, { useRef } from "react";

import { useColors, useIconPicker, formatPhoneDots, useCopyHtml, useHtmlModal, getLogoUrl } from "./signature-base";
import { SocialIcons } from "./social-icons";
import iconsLinks from "./icons-links";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/shared/ui/dialog";
import { Button } from "@/shared/ui/button";

export function SignatureWebWithPhoto({ user, logo, customStyle, dark, extraButton, secondContact, onGenerate, context }: Record<string, unknown>) {
  const colors = useColors(customStyle as { styleEnabled?: boolean; styles?: Record<string, unknown> });
  const icon = useIconPicker(customStyle as Record<string, unknown>, dark as boolean);
  const ref = useRef<HTMLTableElement>(null);
  const { copy } = useCopyHtml(ref);
  const { show, html, open, confirm } = useHtmlModal(ref);

  // Filter social networks like legacy
  const networks = () => {
    const socials = ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube'];
    let socialNets: Record<string, unknown> = {};

    Object.keys(context).forEach((social) => {
      socials.filter((item) => {
        if (item === social) {
          const socialValue = (context as Record<string, unknown>)[social];
          if (socialValue && (socialValue as string).length > 0) {
            socialNets = { ...socialNets, [social]: socialValue };
          }
        }
        return socialNets;
      });
    });

    return socialNets;
  };

  const socialNetworks = networks();

  const photoUrl = () => {
    if ((user as Record<string, unknown>)?.circularPhotoUrl) return (user as Record<string, unknown>).circularPhotoUrl;
    if ((context as Record<string, unknown>)?.circularPhotoUrl) return (context as Record<string, unknown>).circularPhotoUrl;
    const u = user as Record<string, unknown>;
    if ((u?.photo as Record<string, unknown>)?.formats && ((u.photo as Record<string, unknown>).formats as Record<string, unknown>)?.thumbnail && (((u.photo as Record<string, unknown>).formats as Record<string, unknown>).thumbnail as Record<string, unknown>)?.url) {
      return (((u.photo as Record<string, unknown>).formats as Record<string, unknown>).thumbnail as Record<string, unknown>).url;
    }
    return "";
  };

  return (
    <>
      <table className="ax_signature" width={560} border={0} cellPadding={0} cellSpacing={0} style={{ fontFamily: 'Arial, sans-serif !important', minWidth: 560, backgroundImage: 'url("https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/white_pixel_0d3f6a60cf.jpg")', backgroundRepeat: 'repeat' }} ref={ref}>
        <tbody style={{ backgroundColor: '#fff' }}>
          <tr>
            <td colSpan={3} style={{ backgroundColor: '#fff' }}>
              <table cellPadding={0} cellSpacing={0}>
                <tbody>
                  <tr>
                    <td width={150} style={{ borderRight: `1px solid ${colors ? colors.primary : '#2a7a94'}`, verticalAlign: 'top', paddingRight: 16 }}>
                      <p>
                        <img src={photoUrl() as string} alt={`${context.firstname} ${context.lastname}`} width={130} height={130} style={{ width: 130, height: 130, borderRadius: 65, border: '2px solid #415A71', objectFit: 'cover', marginLeft: 'auto', marginRight: 'auto', display: 'block' }} />
                      </p>
                      {logo && (context as any).team?.logo?.url ? (
                        <p style={{ minWidth: 150 }}>
                          <img src={getLogoUrl((context as any).team)} style={{ verticalAlign: 'middle', margin: '0 auto 5px auto', width: 130 }} alt="logo" width={130} />
                        </p>
                      ) : null}
                      {(context as any).applicationLink && (context as any).applicationLink.length > 0 ? (
                        <p><a href={(context as any).applicationLink} style={{ padding: '4px 8px', borderRadius: 2, margin: '0 auto 16px auto', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'table' }}>Apply Now</a></p>
                      ) : null}
                      {(context as any).appointmentScheduleLink && (context as any).appointmentScheduleLink.length > 0 ? (
                        <p><a href={(context as any).appointmentScheduleLink} style={{ padding: '4px 8px', borderRadius: 2, margin: '16px auto 16px auto', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'table' }}>Schedule a Call</a></p>
                      ) : null}
                      {(context as any).googleReviewsLink && (context as any).googleReviewsLink.length > 0 ? (
                        <p><a href={(context as any).googleReviewsLink} style={{ padding: '4px 8px', borderRadius: 2, margin: '16px auto 16px auto', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'table' }}>Leave a Review</a></p>
                      ) : null}
                      {(extraButton as Record<string, unknown>)?.enabled ? (
                        <p><a href={(extraButton as Record<string, unknown>).link as string || '#'} style={{ padding: '4px 8px', borderRadius: 2, margin: '0 auto 16px auto', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'table' }}>{(extraButton as Record<string, unknown>).label as string || 'Click Here'}</a></p>
                      ) : null}
                    </td>
                    <td width={370} style={{ verticalAlign: 'top', paddingTop: 8 }}>
                      <h1 style={{ fontFamily: 'Arial, sans-serif', fontSize: 21, lineHeight: '24px', color: colors ? colors.secondary : '#415A71', margin: '0 0 0 20px' }}>
                        {`${context.firstname || ''} ${context.lastname || ''}`}{context.titles && context.titles.length > 0 ? ', ' : ''}
                        <span style={{ fontSize: 12 }}>{context.titles && context.titles.length > 0 ? context.titles : ''}</span>
                      </h1>
                      <h4 style={{ fontFamily: 'Arial, sans-serif', fontSize: 14, lineHeight: '18px', color: colors ? colors.primary : '#2a7a94', margin: '0 0 16px 20px' }}>
                        {context.position}<span />
                      </h4>
                      {(context as any).tagline && (context as any).tagline.length > 0 ? (
                        <h4 style={{ fontFamily: 'Arial, sans-serif', fontSize: 12, lineHeight: '16px', color: '#444', margin: '0 0 16px 20px', fontWeight: 'normal' }}>{(context as any).tagline}</h4>
                      ) : null}
                      {(context as any).phone && (context as any).phone.length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={`tel:${(context as any).phone ? (context as any).phone.replace(/\D/g, '') : '#'}${(context as any).ext && (context as any).ext.length > 0 ? ',' + (context as any).ext : ''}`} style={(context as any).phone ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '8px 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('phone')} width={20} height={20} style={{ verticalAlign: 'middle' }} alt="phone" />
                            {formatPhoneDots((context as any).phone)} {(context as any).ext && (context as any).ext.length > 0 ? 'ext. ' + (context as any).ext : ''}
                          </a>
                        </p>
                      ) : null}
                      {(context as any).secondPhone && (context as any).secondPhone.length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={`tel:${(context as any).secondPhone ? (context as any).secondPhone.replace(/\D/g, '') : '#'}${(context as any).secondExt && (context as any).secondExt.length > 0 ? ',' + (context as any).secondExt : ''}`} style={(context as any).secondPhone ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('phone')} width={20} height={20} style={{ verticalAlign: 'middle' }} alt="phone" />
                            {formatPhoneDots((context as any).secondPhone)} {(context as any).secondExt && (context as any).secondExt.length > 0 ? 'ext. ' + (context as any).secondExt : ''}
                          </a>
                        </p>
                      ) : null}
                      {context.email && context.email.length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={`mailto:${context.email || '#'}`} style={context.email ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('email')} width={20} height={20} style={{ verticalAlign: 'middle', marginRight: 2 }} alt="email" />
                            {context.email}
                          </a>
                        </p>
                      ) : null}
                      {context.website && context.website.length > 0 && context.website !== '#' ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={context.website || '#'} target="_blank" style={context.website ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }} rel="noreferrer">
                            <img src={icon('website')} width={20} height={20} style={{ verticalAlign: 'middle' }} alt="website" />
                            {context.website}
                          </a>
                        </p>
                      ) : null}
                      {context.license ? (
                        <p style={{ fontSize: 12, paddingLeft: 8, lineHeight: '24px', display: 'block', textDecoration: 'none', margin: '0 0 16px 16px', color: colors ? colors.secondary : '#415A71' }}>License {context.license}</p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryName && ((secondContact as Record<string, unknown>).secondaryName as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0, color: colors ? colors.primary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '24px 0 0 16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryName as string}</strong>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryPosition && ((secondContact as Record<string, unknown>).secondaryPosition as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0, color: colors ? colors.primary : '#415A71', fontSize: 12, paddingLeft: 4, lineHeight: '18px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryPosition as string}</strong>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryPhone && ((secondContact as Record<string, unknown>).secondaryPhone as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={`tel:${(secondContact as Record<string, unknown>).secondaryPhone as string || '#'}${(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? ',' + (secondContact as Record<string, unknown>).ext : ''}`} style={(secondContact as Record<string, unknown>).secondaryPhone ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('phone')} width={20} height={20} style={{ verticalAlign: 'middle' }} alt="phone" />
                            {(secondContact as Record<string, unknown>).secondaryPhone as string} {(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? 'ext. ' + (secondContact as Record<string, unknown>).ext : ''}
                          </a>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryEmail && ((secondContact as Record<string, unknown>).secondaryEmail as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }} id="emailField">
                          <a href={`mailto:${(secondContact as Record<string, unknown>).secondaryEmail as string || '#'}`} style={(secondContact as Record<string, unknown>).secondaryEmail ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('email')} width={20} height={20} style={{ verticalAlign: 'middle', marginRight: 2 }} alt="email" />
                            {(secondContact as Record<string, unknown>).secondaryEmail as string}
                          </a>
                        </p>
                      ) : null}
                      {(user as Record<string, unknown>)?.showBadges && ((user as Record<string, unknown>).showBadges as Record<string, unknown>)?.emailSignature ? (
                        <p style={{ paddingLeft: 16 }}>
                          {((user as Record<string, unknown>).badges as any[] || []).map((b: any) => {
                            const badgeUrl = b?.image?.url;
                            if (b.enabled) {
                              return <img key={b.id} src={badgeUrl} alt={b.title} style={{ width: 105, height: 105, display: 'inline-block', margin: '0 8px 0 0' }} width={105} height={105} />;
                            }
                            return null;
                          })}
                        </p>
                      ) : null}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td colSpan={3} style={{ paddingTop: 8, backgroundColor: '#fff' }} />
          </tr>
          <tr>
            <td style={{ borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, paddingTop: 8, backgroundColor: '#fff' }}>
              {(context as any).team?.customSignatureLogo ? (
                <img src={(context as any).team.customSignatureLogo.url} alt={(context as any).brokerage} style={{ width: 200, height: 'auto' }} width={200} height={38} />
              ) : (
                <img src={iconsLinks.logo.standard} alt={(context as any).brokerage} style={{ width: 200, height: 38 }} width={200} height={38} />
              )}
            </td>
            <td style={{ verticalAlign: 'top', borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, width: 160, backgroundColor: '#fff' }}>
              {(user as Record<string, unknown>)?.team && ((user as Record<string, unknown>).team as Record<string, unknown>)?.showFSRA ? (
                <p style={{ margin: 0, paddingTop: 25, paddingLeft: 8, verticalAlign: 'middle', height: 40, fontSize: 10, fontWeight: 'bold' }}>FSRA&nbsp;12403</p>
              ) : null}
              {(context as any).team?.signatureAdditionalImages?.length > 0 ? (
                <div style={{ display: 'flex', justifyContent: 'space-between', maxWidth: 160, paddingTop: 5 }}>
                  {(context as any).team.signatureAdditionalImages.map((img: any, index: number) => {
                    const imgWidth = Math.floor((160 - 12 * ((context as any).team.signatureAdditionalImages.length - 1)) / (context as any).team.signatureAdditionalImages.length);
                    return <img key={index} src={img.url} alt={`Additional logo ${index + 1}`} style={{ height: 45, width: imgWidth, marginRight: index < (context as any).team.signatureAdditionalImages.length - 1 ? 12 : 0 }} />;
                  })}
                </div>
              ) : null}
            </td>
            <td width="154" style={{ verticalAlign: 'top', borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, backgroundColor: '#fff' }}>
              <table width="154" border={0} cellPadding={0} cellSpacing={0} style={{ textAlign: 'right', margin: '0 0 0 10px', height: '32px' }}>
                <tbody>
                  <tr>
                    <td style={{ width: 100 }} />
                    <SocialIcons isDark={dark as boolean} isBranded={!!(customStyle && (customStyle as Record<string, unknown>).styleEnabled)} />
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
      <div style={{ display: 'flex', gap: 8, marginTop: 12 }}>
        <button type="button" onClick={onGenerate as () => void} style={{ background: '#059669', color: '#fff', padding: '6px 10px', borderRadius: 4, border: 'none', cursor: 'pointer' }}>
          Generate Signature
        </button>
        <button type="button" onClick={copy} style={{ background: '#2a7a94', color: '#fff', padding: '6px 10px', borderRadius: 4, border: 'none', cursor: 'pointer' }}>
          Copy Signature
        </button>
        <button type="button" onClick={open} style={{ background: '#415A71', color: '#fff', padding: '6px 10px', borderRadius: 4, border: 'none', cursor: 'pointer' }}>
          Copy HTML Code
        </button>
      </div>

      <Dialog open={show} onOpenChange={(open) => !open && confirm(false)}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Signature HTML Code</DialogTitle>
          </DialogHeader>
          <div className="overflow-auto max-h-[60vh] bg-gray-50 p-4 rounded border">
            <pre className="text-xs whitespace-pre-wrap font-mono">{html}</pre>
          </div>
          <DialogFooter>
            <Button onClick={() => confirm(true)} className="bg-[#2a7a94] hover:bg-[#1e5a6b]">
              Copy HTML Code
            </Button>
            <Button variant="outline" onClick={() => confirm(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default SignatureWebWithPhoto;

