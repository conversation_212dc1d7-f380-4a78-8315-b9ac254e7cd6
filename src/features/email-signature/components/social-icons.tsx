"use client";

import React from "react";
import { iconsLinks } from "../components/icons-links";
import { useSignature } from "../context/signature-context";

export function SocialIcons({ isDark, isBranded }: { isDark?: boolean; isBranded?: boolean }) {
  const [context] = useSignature();

  const setIcon = (icon: keyof typeof iconsLinks) => {
    const iconData = (iconsLinks as Record<string, Record<string, string>>)[icon];
    if (!iconData) return '';

    if (isBranded) {
      if (isDark) return iconData.white || '';
      return iconData.black || '';
    } else {
      if (isDark) return iconData.white || '';
      return iconData.standard || '';
    }
  };

  const cell = (platform: "facebook" | "instagram" | "linkedin" | "twitter" | "youtube") => {
    const value = (context as Record<string, unknown>)?.[platform];
    return value ? (
      <td width={27} style={{ display: "table-cell", verticalAlign: "middle", lineHeight: "22px", height: "22px", paddingTop: 8 }}>
        <a style={{ width: 22, height: 22, display: "table", marginLeft: 4, textAlign: "right" }} href={(value as string) ?? "#"} target="_blank" rel="noreferrer">
          <img src={setIcon(platform)} width={22} height={22} alt="" />
        </a>
      </td>
    ) : (
      <td />
    );
  };

  return (
    <>
      {cell("facebook")}
      {cell("instagram")}
      {cell("linkedin")}
      {cell("twitter")}
      {cell("youtube")}
      <td width={1} style={{ display: "table-cell", verticalAlign: "middle", lineHeight: "22px", height: "22px", paddingTop: 8 }} />
    </>
  );
}

export default SocialIcons;

