"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Button } from "@/shared/ui/button";
import { emailSignatureSchema, type EmailSignatureFormData } from "../lib/validation";
import { useSignature } from "../context/signature-context";
import formatUrl from "@/shared/lib/format-url";
import { getCookie } from "@/shared/lib/auth";
import type { User } from "@/shared/types/auth";

interface SignatureFormProps {
  user: User;
  onGenerated?: (data: Record<string, unknown>) => void;
  onSubmitRefReady?: (submitFn: () => void) => void;
}

export function SignatureForm({ user, onGenerated, onSubmitRefReady }: SignatureFormProps) {
  const formRef = useRef<HTMLFormElement>(null);
  const [processing, setProcessing] = useState(false);
  const [, setContext] = useSignature();
  const [formInfo, setFormInfo] = useState(user || {});

  const { register, handleSubmit, reset, watch, setValue, formState: { errors, isSubmitting } } = useForm<EmailSignatureFormData>({
    resolver: zodResolver(emailSignatureSchema),
    defaultValues: {
      firstname: "",
      lastname: "",
      titles: "",
      position: "",
      license: "",
      email: "",
      phone: "",
      ext: "",
      secondPhone: "",
      secondExt: "",
      tagline: "",
      website: "",
      instagram: "",
      facebook: "",
      linkedin: "",
      twitter: "",
      youtube: "",
      applicationLink: "",
      appointmentScheduleLink: "",
      googleReviewsLink: "",
      secondaryName: "",
      secondaryPosition: "",
      secondaryEmail: "",
      secondaryPhone: "",
    },
  });

  // Prefill with user data
  useEffect(() => {
    if (!user) return;
    const userData = {
      firstname: user.firstname || "",
      lastname: user.lastname || "",
      titles: (user as any).titles || "",
      position: user.position || "",
      license: (user as any).license || "",
      email: user.email || "",
      phone: user.phone || "",
      ext: (user as any).ext || "",
      secondPhone: (user as any).secondPhone || "",
      secondExt: (user as any).secondExt || "",
      tagline: (user as any).team?.tagline || "",
      website: user.website || "",
      instagram: (user as any).instagram || "",
      facebook: (user as any).facebook || "",
      linkedin: (user as any).linkedin || "",
      twitter: (user as any).twitter || "",
      youtube: (user as any).youtube || "",
      applicationLink: (user as any).applicationLink || "",
      appointmentScheduleLink: (user as any).appointmentScheduleLink || "",
      googleReviewsLink: (user as any).googleReviewsLink || "",
      secondaryName: "",
      secondaryPosition: "",
      secondaryEmail: "",
      secondaryPhone: "",
    };
    reset(userData);
    setFormInfo({ ...user, ...userData });
  }, [user, reset]);

  // Handle field changes like legacy form
  const updateInfo = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    if (name === 'phone' || name === 'secondPhone') {
      setFormInfo({
        ...formInfo,
        [name]: {
          raw: value.replace(/\D/g, ''),
          input: value
        }
      });
      setValue(name as keyof EmailSignatureFormData, value);
      return;
    }

    let processedValue = value;
    if ([
      'website',
      'secondaryWebsite',
      'appointmentScheduleLink',
      'googleReviewsLink',
      'applicationLink',
      'facebook',
      'instagram',
      'linkedin',
      'twitter',
      'youtube'
    ].includes(name)) {
      processedValue = formatUrl(value);
    }

    setFormInfo({ ...formInfo, [name]: processedValue });
    setValue(name as keyof EmailSignatureFormData, processedValue);
  };

  const onSubmit = useCallback(async (data: EmailSignatureFormData) => {
    try {
      setProcessing(true);

      // Format URL fields
      const urlFields: (keyof EmailSignatureFormData)[] = [
        "website",
        "instagram",
        "facebook",
        "linkedin",
        "twitter",
        "youtube",
        "applicationLink",
        "appointmentScheduleLink",
        "googleReviewsLink",
      ];
      const formatted: Record<string, unknown> = { ...user, ...data };
      urlFields.forEach((f) => {
        formatted[f] = formatUrl((data as Record<string, unknown>)[f] as string || (user as unknown as Record<string, unknown>)[f] as string || "");
      });

      // Generate circular photo if not exists
      let circularPhotoUrl = (user as any)?.circularPhotoUrl;
      if (!circularPhotoUrl) {
        console.log('Generating new circular photo...');
        const jwt = getCookie("jwt");
        const userId = getCookie("userId");
        const apiURL = process.env.NEXT_PUBLIC_API_URL;

        if (userId && jwt && apiURL) {
          const response = await fetch(`${apiURL}/users/${userId}/generate-circular-photo`, {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${jwt}`,
            },
          });

          if (response.ok) {
            const json = await response.json();
            circularPhotoUrl = json.circularPhotoUrl;
            console.log('New circular photo generated:', circularPhotoUrl);
          }
        }
      } else {
        console.log('Using existing circular photo:', circularPhotoUrl);
      }

      const signatureData = { ...formatted, circularPhotoUrl };
      setContext(signatureData);
      onGenerated?.(signatureData);

      // Scroll to top after generating signature
      setTimeout(() => window.scrollTo(0, 0), 300);
    } catch (error) {
      console.error('Error generating signature:', error);
    } finally {
      setProcessing(false);
    }
  }, [user, onGenerated, setContext]);

  // Expose submit function to parent
  useEffect(() => {
    if (onSubmitRefReady) {
      onSubmitRefReady(() => handleSubmit(onSubmit));
    }
  }, [onSubmitRefReady, handleSubmit, onSubmit]);

  if (!user) {
    return null;
  }

  return (
    <>
      <form ref={formRef} onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstname">First Name</Label>
            <Input
              id="firstname"
              {...register("firstname")}
              placeholder="First Name"
              onChange={(e) => {
                updateInfo(e);
                register("firstname").onChange(e);
              }}
            />
            {errors.firstname && <p className="text-sm text-red-500">{errors.firstname.message}</p>}
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastname">Last Name</Label>
            <Input
              id="lastname"
              {...register("lastname")}
              placeholder="Last Name"
              onChange={(e) => {
                updateInfo(e);
                register("lastname").onChange(e);
              }}
            />
            {errors.lastname && <p className="text-sm text-red-500">{errors.lastname.message}</p>}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="titles">Title After Name (e.g. AMP, BCC)</Label>
          <Input
            id="titles"
            {...register("titles")}
            placeholder="AMP, BCC, BCO"
            onChange={(e) => {
              updateInfo(e);
              register("titles").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="position">Position</Label>
          <Input
            id="position"
            {...register("position")}
            placeholder="I.E: Mortgage Broker, BCS"
            onChange={(e) => {
              updateInfo(e);
              register("position").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="license">License Number (Optional)</Label>
          <Input
            id="license"
            {...register("license")}
            placeholder="I.E: #AXM003333"
            onChange={(e) => {
              updateInfo(e);
              register("license").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
            onChange={(e) => {
              updateInfo(e);
              register("email").onChange(e);
            }}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              type="tel"
              {...register("phone")}
              placeholder="************"
              value={formInfo && formInfo.phone ? (formInfo.phone as any).input || formInfo.phone : watch("phone") || ''}
              onChange={(e) => {
                updateInfo(e);
                register("phone").onChange(e);
              }}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="ext">Ext.</Label>
            <Input
              id="ext"
              type="tel"
              {...register("ext")}
              placeholder="123"
              onChange={(e) => {
                updateInfo(e);
                register("ext").onChange(e);
              }}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="secondPhone">Second Phone</Label>
            <Input
              id="secondPhone"
              type="tel"
              {...register("secondPhone")}
              placeholder="************"
              onChange={(e) => {
                updateInfo(e);
                register("secondPhone").onChange(e);
              }}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="secondExt">Ext.</Label>
            <Input
              id="secondExt"
              type="tel"
              {...register("secondExt")}
              placeholder="123"
              onChange={(e) => {
                updateInfo(e);
                register("secondExt").onChange(e);
              }}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="tagline">Tagline</Label>
          <Input
            id="tagline"
            {...register("tagline")}
            placeholder="Team tagline"
            onChange={(e) => {
              updateInfo(e);
              register("tagline").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="website">Website</Label>
          <Input
            id="website"
            {...register("website")}
            placeholder="I.E: https://axiommortgage.ca"
            onChange={(e) => {
              updateInfo(e);
              register("website").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="instagram">Instagram Page</Label>
          <Input
            id="instagram"
            {...register("instagram")}
            placeholder="I.E: https://instagram.com/jane-doe"
            onChange={(e) => {
              updateInfo(e);
              register("instagram").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="facebook">Facebook Page</Label>
          <Input
            id="facebook"
            {...register("facebook")}
            placeholder="I.E: https://facebook.com/jane-doe"
            onChange={(e) => {
              updateInfo(e);
              register("facebook").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="linkedin">Linkedin Page</Label>
          <Input
            id="linkedin"
            {...register("linkedin")}
            placeholder="I.E: https://linkedin.com/in/jane-doe"
            onChange={(e) => {
              updateInfo(e);
              register("linkedin").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="twitter">X (Twitter) Page</Label>
          <Input
            id="twitter"
            {...register("twitter")}
            placeholder="I.E: https://twitter.com/jane-doe"
            onChange={(e) => {
              updateInfo(e);
              register("twitter").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="youtube">Youtube Channel</Label>
          <Input
            id="youtube"
            {...register("youtube")}
            placeholder="I.E: https://youtube.com/c/jane-doe"
            onChange={(e) => {
              updateInfo(e);
              register("youtube").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="applicationLink">Mortgage Application Link</Label>
          <Input
            id="applicationLink"
            {...register("applicationLink")}
            placeholder="I.E: https://mtgapp.scarlettnetwork.com/broker-name/home"
            onChange={(e) => {
              updateInfo(e);
              register("applicationLink").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="appointmentScheduleLink">Appointment Schedule Link (I.e. Calendly)</Label>
          <Input
            id="appointmentScheduleLink"
            {...register("appointmentScheduleLink")}
            placeholder="Calendly or Other"
            onChange={(e) => {
              updateInfo(e);
              register("appointmentScheduleLink").onChange(e);
            }}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="googleReviewsLink">Google Reviews Link</Label>
          <Input
            id="googleReviewsLink"
            {...register("googleReviewsLink")}
            placeholder="Link to your Google Reviews page"
            onChange={(e) => {
              updateInfo(e);
              register("googleReviewsLink").onChange(e);
            }}
          />
        </div>

        <div className="pt-2">
          <h2 className="text-base font-medium mb-2">Second Contact Person (Optional)</h2>
          <div className="space-y-2">
            <Label htmlFor="secondaryName">Name</Label>
            <Input
              id="secondaryName"
              {...register("secondaryName")}
              onChange={(e) => {
                updateInfo(e);
                register("secondaryName").onChange(e);
              }}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="secondaryPosition">Position</Label>
            <Input
              id="secondaryPosition"
              {...register("secondaryPosition")}
              onChange={(e) => {
                updateInfo(e);
                register("secondaryPosition").onChange(e);
              }}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="secondaryEmail">Email</Label>
            <Input
              id="secondaryEmail"
              {...register("secondaryEmail")}
              onChange={(e) => {
                updateInfo(e);
                register("secondaryEmail").onChange(e);
              }}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="secondaryPhone">Phone</Label>
            <Input
              id="secondaryPhone"
              {...register("secondaryPhone")}
              onChange={(e) => {
                updateInfo(e);
                register("secondaryPhone").onChange(e);
              }}
            />
          </div>
        </div>
      </form>


    </>
  );
}

export default SignatureForm;

