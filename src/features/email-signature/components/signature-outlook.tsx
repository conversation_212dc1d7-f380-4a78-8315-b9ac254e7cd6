"use client";

import React, { useEffect, useRef, useState } from "react";

import { useColors, useIconPicker, formatPhoneDots, getLogoUrl } from "./signature-base";
import { SocialIcons } from "./social-icons";
import iconsLinks from "./icons-links";

export function SignatureOutlook({ user, logo, customStyle, dark, extraButton, secondContact, onGenerate, context }: Record<string, unknown>) {
  const colors = useColors(customStyle as { styleEnabled?: boolean; styles?: Record<string, unknown> });
  const icon = useIconPicker(customStyle as Record<string, unknown>, dark as boolean);
  const ref = useRef<HTMLTableElement>(null);
  const [rightColumnWidth, setRightColumnWidth] = useState<number | null>(null);

  // Filter social networks like legacy
  const networks = () => {
    const socials = ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube'];
    let socialNets: Record<string, unknown> = {};

    Object.keys(context).forEach((social) => {
      socials.filter((item) => {
        if (item === social) {
          const socialValue = (context as Record<string, unknown>)[social];
          if (socialValue && (socialValue as string).length > 0) {
            socialNets = { ...socialNets, [social]: socialValue };
          }
        }
        return socialNets;
      });
    });

    return socialNets;
  };

  const socialNetworks = networks();

  useEffect(() => {
    const emailField = ref.current?.querySelector("#emailField") as HTMLElement | null;
    const nameField = ref.current?.querySelector("#emailField") as HTMLElement | null;
    if (emailField && nameField) {
      const rcWidth = emailField.getBoundingClientRect().width + 50;
      setRightColumnWidth(Math.round(rcWidth));
    }
  }, [context]);

  return (
    <>
      <table ref={ref} border={0} cellPadding={0} cellSpacing={0} style={{ fontFamily: 'Arial, sans-serif !important', minWidth: 560, backgroundImage: 'url("https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/white_pixel_0d3f6a60cf.jpg")', backgroundRepeat: 'repeat' }}>
        <tbody style={{ backgroundColor: '#fff' }}>
          <tr>
            <td colSpan={3} style={{ backgroundColor: '#fff' }}>
              <table>
                <tbody>
                  <tr>
                    <td style={{ borderRight: `1px solid ${colors ? colors.primary : '#2a7a94'}`, verticalAlign: 'top', minWidth: 150, maxWidth: 260, paddingRight: 16, backgroundColor: '#fff' }}>
                      <h1 id="nameField" style={{ fontFamily: 'Arial, sans-serif', fontSize: 21, lineHeight: '24px', color: '#415A71', margin: 0 }}>
                        {`${context.firstname || ''} ${context.lastname || ''}`}{context.titles && context.titles.length > 0 ? ', ' : ''}
                        <span style={{ fontSize: 12 }}>{context.titles && context.titles.length > 0 ? context.titles : ''}</span>
                      </h1>
                      <h4 style={{ fontFamily: 'Arial, sans-serif', fontSize: 14, lineHeight: '18px', color: colors ? colors.primary : '#2a7a94', margin: '0 0 16px 0' }}>
                        {context.position}<span />
                      </h4>

                      {context.tagline && context.tagline.length > 0 ? (
                        <h4 style={{ fontFamily: 'Arial, sans-serif', fontSize: 12, lineHeight: '16px', color: '#444', margin: '0 0 16px 0', fontWeight: 'normal' }}>{context.tagline}</h4>
                      ) : null}

                      {logo && (context.team as Record<string, unknown>)?.logo && ((context.team as Record<string, unknown>).logo as Record<string, unknown>)?.url ? (
                        <p style={{ minWidth: 150 }}>
                          <img src={getLogoUrl(context.team as Record<string, unknown>)} style={{ verticalAlign: 'middle', marginBottom: 5, width: 130 }} alt="logo" width={130} />
                        </p>
                      ) : null}

                      <table>
                        <tbody>
                          {context.applicationLink && context.applicationLink.length > 0 ? (
                            <tr>
                              <td style={{ background: '#2a7a94', textAlign: 'center', height: 20, margin: '0 0 12px 0', padding: '0 8px', borderRadius: 0, borderBottomStyle: 'solid', borderBottomColor: '#fff', borderBottomWidth: 6 }}>
                                <a href={context.applicationLink} style={{ padding: '4px 8px', borderRadius: 2, margin: 0, background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'block' }}>Apply Now</a>
                              </td>
                            </tr>
                          ) : null}
                          {context.appointmentScheduleLink && context.appointmentScheduleLink.length > 0 ? (
                            <tr>
                              <td style={{ background: '#2a7a94', textAlign: 'center', height: 20, margin: '0 0 12px 0', padding: '0 8px', borderRadius: 0, borderBottomStyle: 'solid', borderBottomColor: '#fff', borderBottomWidth: 6 }}>
                                <a href={context.appointmentScheduleLink} style={{ padding: '4px 8px', borderRadius: 2, margin: 0, background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'block' }}>Schedule a Call</a>
                              </td>
                            </tr>
                          ) : null}
                          {context.googleReviewsLink && context.googleReviewsLink.length > 0 ? (
                            <tr>
                              <td style={{ background: '#2a7a94', textAlign: 'center', height: 20, margin: '0 0 12px 0', padding: '0 8px', borderRadius: 0, borderBottomStyle: 'solid', borderBottomColor: '#fff', borderBottomWidth: 6 }}>
                                <a href={context.googleReviewsLink} style={{ padding: '4px 8px', borderRadius: 2, margin: 0, background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'block' }}>Leave a Review</a>
                              </td>
                            </tr>
                          ) : null}
                          {(extraButton as Record<string, unknown>)?.enabled ? (
                            <tr>
                              <td style={{ background: '#2a7a94', textAlign: 'center', height: 20, margin: '0 0 12px 0', padding: '0 8px', borderRadius: 0, borderBottomStyle: 'solid', borderBottomColor: '#fff', borderBottomWidth: 6 }}>
                                <a href={(extraButton as Record<string, unknown>).link as string || '#'} style={{ padding: '4px 8px', borderRadius: 2, margin: 0, background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#fff', textDecoration: 'none', fontSize: 14, display: 'block' }}>{(extraButton as Record<string, unknown>).label as string || 'Click Here'}</a>
                              </td>
                            </tr>
                          ) : null}
                        </tbody>
                      </table>
                    </td>

                    <td style={{ verticalAlign: 'top', paddingTop: '0', width: `${rightColumnWidth}px`, backgroundColor: '#fff' }}>
                      <table>
                        <tbody>
                          {context.phone && context.phone.length > 0 ? (
                            <tr>
                              <td style={{ verticalAlign: 'middle', width: '20px', height: '20px', paddingLeft: '12px' }}>
                                <img src={icon('phone')} width="20" height="20" style={{ verticalAlign: 'middle', width: '20px', height: '20px' }} alt="phone icon" />
                              </td>
                              <td style={{ verticalAlign: 'top', height: '20px' }}>
                                <a href={`tel:${context.phone === null ? '#' : context.phone.replace(/\D/g, '')}${context.ext && context.ext.length > 0 ? ',' + context.ext : ''}`} style={context.phone === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '13px', paddingLeft: '0', display: 'block', textDecoration: 'none', margin: '8px 0 4px 0' }}>
                                  {context.phone === null ? '-----' : formatPhoneDots(context.phone)} {context.ext && context.ext.length > 0 ? 'ext. ' + context.ext : ''}
                                </a>
                              </td>
                            </tr>
                          ) : null}
                          {context.secondPhone && context.secondPhone.length > 0 ? (
                            <tr>
                              <td style={{ verticalAlign: 'middle', width: '20px', height: '20px', paddingLeft: '12px' }}>
                                <img src={icon('phone')} width="20" height="20" style={{ verticalAlign: 'middle', width: '20px', height: '20px' }} alt="phone icon" />
                              </td>
                              <td style={{ verticalAlign: 'top', height: '20px' }}>
                                <a href={`tel:${context.secondPhone === null ? '#' : context.secondPhone.replace(/\D/g, '')}${context.secondExt && context.secondExt.length > 0 ? ',' + context.secondExt : ''}`} style={context.secondPhone === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '13px', paddingLeft: '0', display: 'block', textDecoration: 'none', margin: '0 0 4px 0' }}>
                                  {context.secondPhone === null ? '-----' : formatPhoneDots(context.secondPhone)} {context.secondExt && context.secondExt.length > 0 ? 'ext. ' + context.secondExt : ''}
                                </a>
                              </td>
                            </tr>
                          ) : null}
                          {context.email && context.email.length > 0 ? (
                            <tr>
                              <td style={{ verticalAlign: 'middle', width: '20px', height: '20px', paddingLeft: '12px' }}>
                                <img src={icon('email')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px', width: '20px', height: '20px' }} alt="email icon" />
                              </td>
                              <td style={{ verticalAlign: 'top', height: '20px' }}>
                                <a href={`mailto:${context.email === null ? '#' : context.email}`} style={context.email === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '13px', paddingLeft: '0', display: 'block', textDecoration: 'none', margin: '0 0 4px 0' }}>
                                  {context.email === null ? '-----' : context.email}
                                </a>
                              </td>
                            </tr>
                          ) : null}
                          {context.website && context.website.length > 0 && context.website !== '#' ? (
                            <tr>
                              <td style={{ verticalAlign: 'middle', width: 20, height: 20, paddingLeft: 12 }}>
                                <img src={icon('website')} width={20} height={20} style={{ verticalAlign: 'middle', width: 20, height: 20 }} alt="website" />
                              </td>
                              <td>
                                <a href={context.website || '#'} target="_blank" style={context.website ? { color: colors ? colors.secondary : '#415A71', fontSize: 13, paddingLeft: 0, display: 'block', textDecoration: 'none', margin: '0 0 4px 0' } : { display: 'none' }} rel="noreferrer">
                                  {context.website}
                                </a>
                              </td>
                            </tr>
                          ) : null}
                        </tbody>
                      </table>

                      {context.license ? (
                        <p style={{ fontSize: 12, paddingLeft: 8, lineHeight: '24px', display: 'block', textDecoration: 'none', margin: '0 0 0 16px', color: colors ? colors.secondary : '#415A71' }}>License {context.license}</p>
                      ) : null}

                      {(secondContact as Record<string, unknown>)?.secondaryName && ((secondContact as Record<string, unknown>).secondaryName as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0, color: colors ? colors.primary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '24px 0 0 16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryName as string}</strong>
                        </p>
                      ) : null}

                      {(secondContact as Record<string, unknown>)?.secondaryPosition && ((secondContact as Record<string, unknown>).secondaryPosition as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0, color: colors ? colors.primary : '#415A71', fontSize: 12, paddingLeft: 4, lineHeight: '18px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryPosition as string}</strong>
                        </p>
                      ) : null}

                      {(secondContact as Record<string, unknown>)?.secondaryPhone && ((secondContact as Record<string, unknown>).secondaryPhone as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }}>
                          <a href={`tel:${(secondContact as Record<string, unknown>).secondaryPhone as string || '#'}${(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? ',' + (secondContact as Record<string, unknown>).ext : ''}`} style={(secondContact as Record<string, unknown>).secondaryPhone ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('phone')} width={20} height={20} style={{ verticalAlign: 'middle' }} alt="phone" />
                            {(secondContact as Record<string, unknown>).secondaryPhone as string} {(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? 'ext. ' + (secondContact as Record<string, unknown>).ext : ''}
                          </a>
                        </p>
                      ) : null}

                      {(secondContact as Record<string, unknown>)?.secondaryEmail && ((secondContact as Record<string, unknown>).secondaryEmail as string).length > 0 ? (
                        <p style={{ marginTop: 0, marginBottom: 0 }} id="emailField">
                          <a href={`mailto:${(secondContact as Record<string, unknown>).secondaryEmail as string || '#'}`} style={(secondContact as Record<string, unknown>).secondaryEmail ? { color: colors ? colors.secondary : '#415A71', fontSize: 15, paddingLeft: 4, lineHeight: '24px', height: 24, display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' } : { display: 'none' }}>
                            <img src={icon('email')} width={20} height={20} style={{ verticalAlign: 'middle', marginRight: 2 }} alt="email" />
                            {(secondContact as Record<string, unknown>).secondaryEmail as string}
                          </a>
                        </p>
                      ) : null}

                      {(user as Record<string, unknown>)?.showBadges && ((user as Record<string, unknown>).showBadges as Record<string, unknown>)?.emailSignature ? (
                        <p style={{ paddingLeft: 12 }}>
                          {((user as Record<string, unknown>).badges as any[] || []).map((b: any) => {
                            const badgeUrl = b?.image?.url;
                            if (b.enabled) {
                              return <img key={b.id} src={badgeUrl} alt={b.title} style={{ width: 90, height: 90, display: 'inline-block', margin: '0 8px 0 0' }} width={90} height={90} />;
                            }
                            return null;
                          })}
                        </p>
                      ) : null}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td colSpan={3} style={{ borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, backgroundColor: '#fff' }} />
          </tr>
          <tr>
            <td style={{ marginBottom: 0, paddingBottom: 0, display: 'table-cell', paddingTop: 8, backgroundColor: '#fff' }}>
              {(context.team as Record<string, unknown>)?.customSignatureLogo ? (
                <img src={((context.team as Record<string, unknown>).customSignatureLogo as Record<string, unknown>).url as string} alt={context.brokerage as string} style={{ width: 200, height: 'auto' }} width={200} height={38} />
              ) : (
                <img src={iconsLinks.logo.standard} alt={context.brokerage as string} style={{ width: 200, height: 38 }} width={200} height={38} />
              )}
            </td>
            <td style={{ verticalAlign: 'top', width: 160, backgroundColor: '#fff' }}>
              {(user as Record<string, unknown>)?.team && ((user as Record<string, unknown>).team as Record<string, unknown>)?.showFSRA ? (
                <>
                  <p style={{ margin: 0, paddingLeft: 8, fontSize: 10, fontWeight: 'bold' }}>FSRA&nbsp;12403</p>
                </>
              ) : null}
              {(context.team as Record<string, unknown>)?.signatureAdditionalImages && ((context.team as Record<string, unknown>).signatureAdditionalImages as any[])?.length > 0 ? (
                <div style={{ display: 'flex', justifyContent: 'space-between', maxWidth: 160, paddingTop: 5 }}>
                  {((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).map((img: any, index: number) => {
                    const imgWidth = Math.floor((160 - 12 * (((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length - 1)) / ((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length);
                    return <img key={index} src={img.url} alt={`Additional logo ${index + 1}`} style={{ height: 45, width: imgWidth, marginRight: index < ((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length - 1 ? 12 : 0 }} />;
                  })}
                </div>
              ) : null}
            </td>
            <td width="154" style={{ verticalAlign: 'top', backgroundColor: '#fff' }}>
              <table width="154" border={0} cellPadding={0} cellSpacing={0} style={{ textAlign: 'right', margin: '0 0 0 10px', height: '32px' }}>
                <tbody>
                  <tr>
                    <td style={{ width: 100 }} />
                    <SocialIcons isDark={dark as boolean} isBranded={!!(customStyle && (customStyle as Record<string, unknown>).styleEnabled)} />
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>
      <p style={{ display: 'flex', alignItems: 'center', gap: 8, marginTop: 8, color: '#415A71' }}>
        <strong>Warning: </strong>The signature will work only on <b>Outlook</b>. If you are not going to use this signature in Outlook, please toggle &apos;Using Outlook&apos; option <b>OFF</b>.
      </p>



    </>
  );
}

export default SignatureOutlook;

