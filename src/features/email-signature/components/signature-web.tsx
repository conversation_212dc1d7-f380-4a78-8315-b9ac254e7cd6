"use client";

import React, { useEffect, useRef, useState } from "react";

import { useColors, useIconPicker, formatPhoneDots, getLogoUrl } from "./signature-base";
import { SocialIcons } from "./social-icons";
import iconsLinks from "./icons-links";

export function SignatureWeb({ user, logo, customStyle, dark, extraButton, secondContact, onGenerate, context }: Record<string, unknown>) {
  const colors = useColors(customStyle as { styleEnabled?: boolean; styles?: Record<string, unknown> });
  const icon = useIconPicker(customStyle as Record<string, unknown>, dark as boolean);
  const ref = useRef<HTMLTableElement>(null);
  const [rightColumnWidth, setRightColumnWidth] = useState<number | null>(null);

  // Filter social networks like legacy
  const networks = () => {
    const socials = ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube'];
    let socialNets: Record<string, unknown> = {};

    Object.keys(context).forEach((social) => {
      socials.filter((item) => {
        if (item === social) {
          const socialValue = (context as Record<string, unknown>)[social];
          if (socialValue && (socialValue as string).length > 0) {
            socialNets = { ...socialNets, [social]: socialValue };
          }
        }
        return socialNets;
      });
    });

    return socialNets;
  };

  const socialNetworks = networks();

  useEffect(() => {
    const emailField = ref.current?.querySelector("#emailField") as HTMLElement | null;
    const nameField = ref.current?.querySelector("#emailField") as HTMLElement | null;
    if (emailField && nameField) {
      const rcWidth = emailField.getBoundingClientRect().width + 50;
      setRightColumnWidth(Math.round(rcWidth));
    }
  }, [context]);

  return (
    <>
      <table ref={ref} border={0} cellPadding={0} cellSpacing={0} style={{ fontFamily: 'Arial, sans-serif !important', minWidth: '560px', backgroundImage: 'url("https://indi-strapi.s3.us-east-1.amazonaws.com/images/origin/white_pixel_0d3f6a60cf.jpg")', backgroundRepeat: 'repeat' }}>
        <tbody style={{ backgroundColor: '#fff' }}>
          <tr>
            <td colSpan={3} style={{ backgroundColor: '#fff' }}>
              <table>
                <tbody>
                  <tr>
                    <td style={{ borderRight: `1px solid ${colors ? colors.primary : '#2a7a94'}`, verticalAlign: 'top', minWidth: '150px', maxWidth: '260px', paddingRight: '16px', backgroundColor: '#fff' }}>
                      <h1 id="nameField" style={{ fontFamily: 'Arial, sans-serif !important', fontSize: '21px', lineHeight: '24px', color: '#415A71', margin: '0' }}>
                        {`${context.firstname || ''} `}
                        {context.lastname || ''}
                        {context.titles && context.titles.length > 0 ? ', ' : ''}
                        <span style={{ fontSize: '12px' }}>{context.titles && context.titles.length > 0 ? context.titles : ''}</span>
                      </h1>
                      <h4 style={{ fontFamily: 'Arial, sans-serif !important', fontSize: '14px', lineHeight: '18px', color: colors ? colors.primary : '#2a7a94', margin: '0 0 16px 0' }}>
                        {context.position}
                        <span />
                      </h4>
                      {context.tagline && context.tagline.length > 0 ? (
                        <h4 style={{ fontFamily: 'Arial, sans-serif !important', fontSize: '12px', lineHeight: '16px', color: '#444444', margin: '0 0 16px 0', fontWeight: 'normal' }}>
                          {context.tagline}
                        </h4>
                      ) : null}
                      {logo && (context.team as Record<string, unknown>)?.logo && ((context.team as Record<string, unknown>).logo as Record<string, unknown>)?.url ? (
                        <p style={{ minWidth: '150px' }}>
                          <img src={getLogoUrl(context.team as Record<string, unknown>)} style={{ verticalAlign: 'middle', marginBottom: '5px', width: '130px' }} alt="logo" width="130" />
                        </p>
                      ) : null}
                      {context.applicationLink && context.applicationLink.length > 0 ? (
                        <p>
                          <a href={context.applicationLink} style={{ padding: '4px 8px', borderRadius: '2px', margin: '0 0 16px 0', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#ffffff', textDecoration: 'none', fontSize: '14px', display: 'table' }}>
                            Apply Now
                          </a>
                        </p>
                      ) : null}
                      {context.appointmentScheduleLink && context.appointmentScheduleLink.length > 0 ? (
                        <p>
                          <a href={context.appointmentScheduleLink} style={{ padding: '4px 8px', borderRadius: '2px', margin: '16px 0 16px 0', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#ffffff', textDecoration: 'none', fontSize: '14px', display: 'table' }}>
                            Schedule a Call
                          </a>
                        </p>
                      ) : null}
                      {context.googleReviewsLink && context.googleReviewsLink.length > 0 ? (
                        <p>
                          <a href={context.googleReviewsLink} style={{ padding: '4px 8px', borderRadius: '2px', margin: '16px 0 16px 0', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#ffffff', textDecoration: 'none', fontSize: '14px', display: 'table' }}>
                            Leave a Review
                          </a>
                        </p>
                      ) : null}
                      {(extraButton as Record<string, unknown>)?.enabled ? (
                        <p>
                          <a href={(extraButton as Record<string, unknown>).link as string || '#'} style={{ padding: '4px 8px', borderRadius: '2px', margin: '0 0 16px 0', background: colors ? colors.primary : '#2a7a94', cursor: 'pointer', color: '#ffffff', textDecoration: 'none', fontSize: '14px', display: 'table' }}>
                            {(extraButton as Record<string, unknown>).label as string || 'Click Here'}
                          </a>
                        </p>
                      ) : null}
                    </td>
                    <td style={{ verticalAlign: 'top', paddingTop: '0', width: `${rightColumnWidth}px`, backgroundColor: '#fff' }}>
                      {context.phone && context.phone.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a href={`tel:${context.phone === null ? '#' : context.phone.replace(/\D/g, '')}${context.ext && context.ext.length > 0 ? ',' + context.ext : ''}`} style={context.phone === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                            <img src={icon('phone')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="phone icon" />
                            {context.phone === null ? '-----' : formatPhoneDots(context.phone)} {context.ext && context.ext.length > 0 ? 'ext. ' + context.ext : ''}
                          </a>
                        </p>
                      ) : null}
                      {context.secondPhone && context.secondPhone.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a href={`tel:${context.secondPhone === null ? '#' : context.secondPhone.replace(/\D/g, '')}${context.secondExt && context.secondExt.length > 0 ? ',' + context.secondExt : ''}`} style={context.secondPhone === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                            <img src={icon('phone')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="phone icon" />
                            {context.secondPhone === null ? '-----' : formatPhoneDots(context.secondPhone)} {context.secondExt && context.secondExt.length > 0 ? 'ext. ' + context.secondExt : ''}
                          </a>
                        </p>
                      ) : null}
                      {context.email && context.email.length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }} id="emailField">
                          <a href={`mailto:${context.email === null ? '#' : context.email}`} style={context.email === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                            <img src={icon('email')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="email icon" />
                            {context.email === null ? '-----' : context.email}
                          </a>
                        </p>
                      ) : null}
                      {context.website && context.website.length > 0 && context.website !== '#' ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a href={context.website === null ? '#' : context.website} target="_blank" style={context.website === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }} rel="noreferrer">
                            <img src={icon('website')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="website icon" />
                            {context.website === null ? '-----' : context.website}
                          </a>
                        </p>
                      ) : null}
                      {context.license ? (
                        <p style={{ fontSize: '12px', paddingLeft: '8px', lineHeight: '24px', display: 'block', textDecoration: 'none', margin: '0 0 0 16px', color: colors ? colors.secondary : '#415A71' }}>
                          License {context.license}
                        </p>
                      ) : null}
                      {context.applicationLink && context.applicationLink.length > 0 ? (
                        <p>
                          <a
                            href={context.applicationLink}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '0 0 16px 0',
                              background: colors ? colors.primary : '#2a7a94',
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Apply Now
                          </a>
                        </p>
                      ) : null}
                      {context.appointmentScheduleLink && context.appointmentScheduleLink.length > 0 ? (
                        <p>
                          <a
                            href={context.appointmentScheduleLink}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '16px 0 16px 0',
                              background: colors ? colors.primary : '#2a7a94',
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Schedule a Call
                          </a>
                        </p>
                      ) : null}
                      {context.googleReviewsLink && context.googleReviewsLink.length > 0 ? (
                        <p>
                          <a
                            href={context.googleReviewsLink}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '16px 0 16px 0',
                              background: colors ? colors.primary : '#2a7a94',
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            Leave a Review
                          </a>
                        </p>
                      ) : null}
                      {(extraButton as Record<string, unknown>)?.enabled ? (
                        <p>
                          <a
                            href={(extraButton as Record<string, unknown>)?.link ? (extraButton as Record<string, unknown>).link as string : '#'}
                            style={{
                              padding: '4px 8px',
                              borderRadius: '2px',
                              margin: '0 0 16px 0',
                              background: colors ? colors.primary : '#2a7a94',
                              cursor: 'pointer',
                              color: '#ffffff',
                              textDecoration: 'none',
                              fontSize: '14px',
                              display: 'table'
                            }}
                          >
                            {(extraButton as Record<string, unknown>)?.label ? (extraButton as Record<string, unknown>).label as string : 'Click Here'}
                          </a>
                        </p>
                      ) : null}

                      {(secondContact as Record<string, unknown>)?.secondaryName && ((secondContact as Record<string, unknown>).secondaryName as string).length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0', color: colors ? colors.primary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', marginLeft: '16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryName as string}</strong>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryPosition && ((secondContact as Record<string, unknown>).secondaryPosition as string).length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0', color: colors ? colors.primary : '#415A71', fontSize: '12px', paddingLeft: '4px', lineHeight: '18px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                          <strong>{(secondContact as Record<string, unknown>).secondaryPosition as string}</strong>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryPhone && ((secondContact as Record<string, unknown>).secondaryPhone as string).length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }}>
                          <a href={`tel:${(secondContact as Record<string, unknown>).secondaryPhone === null ? '#' : (secondContact as Record<string, unknown>).secondaryPhone}${(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? ',' + (secondContact as Record<string, unknown>).ext : ''}`} style={(secondContact as Record<string, unknown>).secondaryPhone === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                            <img src={icon('phone')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="phone icon" />
                            {(secondContact as Record<string, unknown>).secondaryPhone === null ? '-----' : (secondContact as Record<string, unknown>).secondaryPhone as string} {(secondContact as Record<string, unknown>).ext && ((secondContact as Record<string, unknown>).ext as string).length > 0 ? 'ext. ' + (secondContact as Record<string, unknown>).ext : ''}
                          </a>
                        </p>
                      ) : null}
                      {(secondContact as Record<string, unknown>)?.secondaryEmail && ((secondContact as Record<string, unknown>).secondaryEmail as string).length > 0 ? (
                        <p style={{ marginTop: '0', marginBottom: '0' }} id="emailField">
                          <a href={`mailto:${(secondContact as Record<string, unknown>).secondaryEmail === null ? '#' : (secondContact as Record<string, unknown>).secondaryEmail}`} style={(secondContact as Record<string, unknown>).secondaryEmail === null ? { display: 'none' } : { color: colors ? colors.secondary : '#415A71', fontSize: '15px', paddingLeft: '4px', lineHeight: '24px', height: '24px', display: 'block', textDecoration: 'none', margin: '0 0 4px 16px' }}>
                            <img src={icon('email')} width="20" height="20" style={{ verticalAlign: 'middle', marginRight: '2px' }} alt="email icon" />
                            {(secondContact as Record<string, unknown>).secondaryEmail === null ? '-----' : (secondContact as Record<string, unknown>).secondaryEmail as string}
                          </a>
                        </p>
                      ) : null}
                      {(user as Record<string, unknown>)?.showBadges && ((user as Record<string, unknown>).showBadges as Record<string, unknown>)?.emailSignature ? (
                        <p style={{ paddingLeft: '16px' }}>
                          {((user as Record<string, unknown>).badges as any[] || []).map((b: any) => {
                            const badgeUrl = b?.image?.url;
                            if (b.enabled) {
                              return (
                                <img
                                  key={b.id}
                                  src={badgeUrl}
                                  alt={b.title}
                                  style={{ width: '90px', height: '90px', display: 'inline-block', margin: '0 8px 0 0' }}
                                  width="90"
                                  height="90"
                                />
                              );
                            }
                            return null;
                          })}
                        </p>
                      ) : null}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td colSpan={3} style={{ paddingTop: '15px', height: '22px', paddingBottom: '0', backgroundColor: '#fff' }} />
          </tr>
          <tr>
            <td style={{ borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, marginBottom: '0', paddingBottom: '0', display: 'table-cell', paddingTop: '7px', backgroundColor: '#fff' }}>
              {(context.team as Record<string, unknown>)?.customSignatureLogo ? (
                <img src={((context.team as Record<string, unknown>).customSignatureLogo as Record<string, unknown>).url as string} alt={context.brokerage as string} style={{ width: '200px', height: 'auto' }} width="200" height="38" />
              ) : (
                <img src={iconsLinks.logo.standard} alt={context.brokerage as string} style={{ width: '200px', height: '38px' }} width="200" height="38" />
              )}
            </td>
            <td style={{ verticalAlign: 'top', borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, width: '160px', backgroundColor: '#fff' }}>
              {(user as Record<string, unknown>)?.team && ((user as Record<string, unknown>).team as Record<string, unknown>)?.showFSRA ? (
                <p style={{ margin: 0, paddingTop: '28px', paddingLeft: '8px', verticalAlign: 'middle', height: '40px', fontSize: '10px', fontWeight: 'bold' }}>
                  FSRA&nbsp;12403
                </p>
              ) : null}
              {(context.team as Record<string, unknown>)?.signatureAdditionalImages && ((context.team as Record<string, unknown>).signatureAdditionalImages as any[])?.length > 0 ? (
                <div style={{ display: 'flex', justifyContent: 'space-between', maxWidth: '160px', paddingTop: '5px' }}>
                  {((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).map((img: any, index: number) => {
                    const imgWidth = Math.floor((160 - 12 * (((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length - 1)) / ((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length);
                    return (
                      <img
                        key={index}
                        src={img.url}
                        alt={`Additional logo ${index + 1}`}
                        style={{ height: '45px', width: `${imgWidth}px`, marginRight: index < ((context.team as Record<string, unknown>).signatureAdditionalImages as any[]).length - 1 ? '12px' : '0' }}
                      />
                    );
                  })}
                </div>
              ) : null}
            </td>
            <td width="154" style={{ verticalAlign: 'top', borderTop: `1px solid ${colors ? colors.primary : '#2a7a94'}`, backgroundColor: '#fff' }}>
              <table width="154" height="32" border={0} cellPadding={0} cellSpacing={0} style={{ textAlign: 'right', margin: '0 0 0 10px' }}>
                <tbody>
                  <tr>
                    <td style={{ width: '100px' }} />
                    <SocialIcons isDark={dark as boolean} isBranded={!!(customStyle && (customStyle as Record<string, unknown>).styleEnabled)} />
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
        </tbody>
      </table>



    </>
  );
}

export default SignatureWeb;

