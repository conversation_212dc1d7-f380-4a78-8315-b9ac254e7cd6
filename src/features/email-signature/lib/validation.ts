import { z } from "zod";

export const emailSignatureSchema = z.object({
  firstname: z.string().min(1, "First name is required"),
  lastname: z.string().min(1, "Last name is required"),
  titles: z.string().optional().or(z.literal("")),
  position: z.string().optional().or(z.literal("")),
  license: z.string().optional().or(z.literal("")),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional().or(z.literal("")),
  ext: z.string().optional().or(z.literal("")),
  secondPhone: z.string().optional().or(z.literal("")),
  secondExt: z.string().optional().or(z.literal("")),
  tagline: z.string().optional().or(z.literal("")),
  website: z.string().optional().or(z.literal("")),
  instagram: z.string().optional().or(z.literal("")),
  facebook: z.string().optional().or(z.literal("")),
  linkedin: z.string().optional().or(z.literal("")),
  twitter: z.string().optional().or(z.literal("")),
  youtube: z.string().optional().or(z.literal("")),
  applicationLink: z.string().optional().or(z.literal("")),
  appointmentScheduleLink: z.string().optional().or(z.literal("")),
  googleReviewsLink: z.string().optional().or(z.literal("")),
  secondaryName: z.string().optional().or(z.literal("")),
  secondaryPosition: z.string().optional().or(z.literal("")),
  secondaryEmail: z.string().optional().or(z.literal("")),
  secondaryPhone: z.string().optional().or(z.literal("")),
});

export type EmailSignatureFormData = z.infer<typeof emailSignatureSchema>;

