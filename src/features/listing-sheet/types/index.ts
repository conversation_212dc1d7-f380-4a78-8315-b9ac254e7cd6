export interface FileUpload {
  url: string | null;
  ext?: string | null;
  formats?: {
    thumbnail?: { url: string };
    squared?: { url: string };
    medium?: { url: string };
  };
}

export interface UserInfo {
  id: string;
  firstname: string | null;
  lastname: string | null;
  position?: string | null;
  email?: string | null;
  workEmail?: string | null;
  phone?: string | null;
  workPhone?: string | null;
  cellPhone?: string | null;
  website?: string | null;
  brokerage?: string | null;
  photo?: FileUpload;
  team?: {
    id: string | null;
    logo?: { url: string | null };
  };
}

export interface RealtorInfo {
  id?: string;
  firstname: string;
  middlename?: string | null;
  lastname: string;
  position: string;
  company: string;
  email: string;
  phone: string;
  website?: string;
  photo?: FileUpload | null;
}

export interface CreateRealtorData {
  firstname: string;
  middlename?: string;
  lastname: string;
  position: string;
  email: string;
  phone: string;
  company: string;
  website?: string;
  photo?: File;
}

export interface PDFConfiguration {
  full: boolean;
  short: boolean;
  mlsCode?: string | null;
  address?: string | null;
  monthlyExpenses: boolean;
  cashNeeded: boolean;
  propertyPhoto?: FileUpload;
  realtorPhoto?: FileUpload;
  user: UserInfo;
}

export interface ChosenDownPayment {
  rate: string;
  percent: string;
  amount: number;
}

export interface RangeRate {
  type: "normalTier" | "middleTier" | "maxTier";
  rate: number | null;
}

export interface EffectiveRates {
  effectiveRate5: number;
  effectiveRate10: number;
  effectiveRate15: number;
  effectiveRate20: number;
  effectiveRateCustom: number;
  effectiveRateRange: number;
  effectiveRateFTHB: number;
}

export type PaymentFrequency =
  | "monthly"
  | "biweekly"
  | "weekly"
  | "accbiweekly"
  | "accweekly";

export interface MortgageCalculationData {
  // Loading and saving states
  loading: boolean;
  saving: boolean;
  uploading: { status: boolean; type: string | null };

  // Property Information
  amount: number;
  askingPrice: number;
  mlsCode?: string;
  address?: string;
  propertyPhoto?: FileUpload;

  // Calculation Parameters
  years: number;
  customYears?: number;
  frequency: PaymentFrequency;
  customPercentage: number;
  numberOfPayments: number;

  // Down Payment Calculations
  downPay5: number;
  downPay10: number;
  downPay15: number;
  downPay20: number;
  downPayCustom: number;
  downPayRange: number;
  downPayFTHB: number;
  chosenDownPay: ChosenDownPayment;

  // Principal Amounts
  principal5: number;
  principal10: number;
  principal15: number;
  principal20: number;
  principalCustom: number;
  principalRange: number;
  principalFTHB: number;
  chosenPrincipal: number;

  // Interest Rates
  rate: number;
  rate5: number;
  rate10: number;
  rate15: number;
  rate20: number;
  rateCustom: number;
  rateRange: number;
  rateFTHB: number;
  effectiveRates: EffectiveRates;

  // Insurance Calculations
  insurance5: number;
  insurance10: number;
  insurance15: number;
  insurance20: number;
  insuranceCustom: number;
  insuranceRange: number;
  insuranceFTHB: number;

  // Monthly Payment Calculations
  monthlyPay5: number;
  monthlyPay10: number;
  monthlyPay15: number;
  monthlyPay20: number;
  monthlyPayCustom: number;
  monthlyPayRange: number;
  monthlyPayFTHB: number;

  // Periodic Payment Calculations
  periodicPay5: number;
  periodicPay10: number;
  periodicPay15: number;
  periodicPay20: number;
  periodicPayCustom: number;
  periodicPayRange: number;
  periodicPayFTHB: number;
  chosenPeriodicPay: number;

  // Monthly Expenses
  propertyTax: number;
  propertyInsurance: number;
  utilities: number;
  condoFees: number;
  hoaFees: number;
  monthlyDebtPayments: number;
  phone: number;
  cable: number;
  internet: number;
  totalMonthlyPayments: number;
  totalYearlyPayments: number;

  // Closing Costs
  lawyerFee: number;
  homeInspection: number;
  appraisal: number;
  titleInsurance: number;
  estoppelFee: number;
  totalCashNeeded: number;

  // UI Configuration
  showMortgagePayment: boolean;
  showBodyCashNeeded: boolean;
  showBodyMonthlyExpenses: boolean;
  showBodyYearlyExpenses: boolean;
  showAdditionalInfo: boolean;
  showRealtorInfo: boolean;
  showImages: boolean;
  houseType: string;

  // Additional Fields
  chosenDownPayExpense: Partial<ChosenDownPayment>;
  amortizationBalance: number;
  rangeRate: RangeRate;

  // PDF Configuration
  pdf: PDFConfiguration;

  // Realtor Information
  realtor: RealtorInfo;
}

export interface ListingSheet {
  id?: string; // Keep for backward compatibility but make optional
  documentId: string; // Make this the primary identifier
  slug: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  user: string | UserInfo;
  sheet: MortgageCalculationData;
}

export interface ListingSheetCreatePayload {
  user: string;
  sheet: MortgageCalculationData;
  title: string;
  slug: string;
}

export interface ListingSheetUpdatePayload {
  user: string;
  sheet: MortgageCalculationData;
  title: string;
  slug: string;
}

// API Response Types
export interface ListingSheetsResponse {
  data: ListingSheet[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface ListingSheetResponse {
  data: ListingSheet;
}

// Form Types
export interface PropertyDetailsFormData {
  askingPrice: number;
  mlsCode?: string;
  address?: string;
  propertyPhoto?: File;
}

export interface RealtorInfoFormData {
  firstname: string;
  middlename?: string;
  lastname: string;
  position: string;
  company: string;
  email: string;
  phone: string;
  website?: string;
  photo?: File;
}

export interface MortgageCalculatorFormData {
  years: number;
  customYears?: number;
  frequency: PaymentFrequency;
  customPercentage: number;
  rate5: number;
  rate10: number;
  rate15: number;
  rate20: number;
  rateCustom: number;
  rateFTHB: number;
  chosenDownPay: ChosenDownPayment;
}

export interface ExpenseFormData {
  // Monthly Expenses
  propertyTax: number;
  propertyInsurance: number;
  utilities: number;
  condoFees: number;
  hoaFees: number;
  monthlyDebtPayments: number;
  phone: number;
  cable: number;
  internet: number;

  // Closing Costs
  lawyerFee: number;
  homeInspection: number;
  appraisal: number;
  titleInsurance: number;
  estoppelFee: number;
}

// Utility Types
export type DownPaymentScenario =
  | "5%"
  | "10%"
  | "15%"
  | "20%"
  | "custom"
  | "range"
  | "fthb";

export interface PaymentScenario {
  scenario: DownPaymentScenario;
  downPayment: number;
  principal: number;
  insurance: number;
  rate: number;
  monthlyPayment: number;
  periodicPayment: number;
  isRecommended?: boolean;
  isSelected?: boolean;
}

// Error Types
export interface ListingSheetError {
  message: string;
  field?: string;
  code?: string;
}

// Constants
export const PAYMENT_FREQUENCIES: Record<PaymentFrequency, string> = {
  monthly: "Monthly",
  biweekly: "Bi-Weekly",
  weekly: "Weekly",
  accbiweekly: "Accelerated Bi-Weekly",
  accweekly: "Accelerated Weekly",
};

export const DOWN_PAYMENT_SCENARIOS: Record<DownPaymentScenario, string> = {
  "5%": "5%",
  "10%": "10%",
  "15%": "15%",
  "20%": "20%",
  custom: "Custom",
  range: "Minimum Required",
  fthb: "First-Time Home Buyer",
};

export const HOUSE_TYPES = {
  select: "Select Property Type",
  newBuild: "New Construction",
  preOwned: "Pre-Owned",
  condo: "Condominium",
} as const;

export type HouseType = keyof typeof HOUSE_TYPES;
