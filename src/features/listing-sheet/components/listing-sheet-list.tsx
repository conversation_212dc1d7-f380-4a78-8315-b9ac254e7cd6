"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { Edit, Trash2, Plus, AlertCircle } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/shared/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/shared/ui/card";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Badge } from "@/shared/ui/badge";
import { Skeleton } from "@/shared/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";

import { useListingSheets } from "../hooks/use-listing-sheets";
import { formatCurrency } from "../lib/mortgage-calculations";
import type { ListingSheet } from "../types";

interface ListingSheetListProps {
  className?: string;
}

export default function ListingSheetList({ className }: ListingSheetListProps) {
  const { listingSheets, isLoading, error, deleteListingSheet, refetch } =
    useListingSheets();
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    sheet: ListingSheet | null;
  }>({ isOpen: false, sheet: null });
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (sheet: ListingSheet) => {
    setDeleteDialog({ isOpen: true, sheet });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.sheet) return;

    setIsDeleting(true);
    try {
      const result = await deleteListingSheet(deleteDialog.sheet.documentId);
      if (result.success) {
        setDeleteDialog({ isOpen: false, sheet: null });
        // Optionally show success message
      } else {
        // Handle error - could show toast notification
        console.error("Delete failed:", result.error);
      }
    } catch (error) {
      console.error("Delete error:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ isOpen: false, sheet: null });
  };

  const getPropertyImage = (sheet: ListingSheet): string => {
    return (
      sheet.sheet?.pdf?.propertyPhoto?.url ||
      "/images/property-image-default.jpg"
    );
  };

  const getFormattedPrice = (sheet: ListingSheet): string => {
    return formatCurrency(sheet.sheet?.askingPrice || 0);
  };

  const getMLSCode = (sheet: ListingSheet): string => {
    return sheet.sheet?.pdf?.mlsCode || sheet.sheet?.mlsCode || "";
  };

  const getAddress = (sheet: ListingSheet): string => {
    return sheet.sheet?.pdf?.address || sheet.sheet?.address || "";
  };

  if (isLoading) {
    return (
      <div className={className}>
        <ListingSheetListSkeleton />
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load listing sheets: {error}
            <Button
              variant="outline"
              size="sm"
              onClick={refetch}
              className="ml-2"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Listing Sheets</h1>
          <p className="text-muted-foreground">
            Manage your property listing sheets and mortgage calculations
          </p>
        </div>
        <Button asChild>
          <Link href="/listing-sheet/new">
            <Plus className="mr-2 h-4 w-4" />
            Create New
          </Link>
        </Button>
      </div>

      {/* Info Alert */}
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <p>
            The Listing Sheet feature keeps records of your generated sheets
            from the last <strong>90 days.</strong> Feel free to load an
            existing sheet and edit it as you wish, or create a new one from
            scratch.
          </p>
          
        </AlertDescription>
      </Alert>

      {/* Listing Sheets Grid */}
      {listingSheets.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {listingSheets.map((sheet) => (
            <ListingSheetCard
              key={sheet.documentId}
              sheet={sheet}
              onDelete={() => handleDeleteClick(sheet)}
            />
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.isOpen} onOpenChange={handleDeleteCancel}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Listing Sheet</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;{deleteDialog.sheet?.title}
              &quot;? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface ListingSheetCardProps {
  sheet: ListingSheet;
  onDelete: () => void;
}

function ListingSheetCard({ sheet, onDelete }: ListingSheetCardProps) {
  const propertyImage =
    sheet.sheet?.pdf?.propertyPhoto?.url ||
    "/images/property-image-default.jpg";
  const price = formatCurrency(sheet.sheet?.askingPrice || 0);
  const mlsCode = sheet.sheet?.pdf?.mlsCode || sheet.sheet?.mlsCode || "";
  const address = sheet.sheet?.pdf?.address || sheet.sheet?.address || "";
  const createdAt = format(new Date(sheet.createdAt), "MMM d, yyyy");

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow pt-0 overflow-hidden">
      <div className="aspect-video relative overflow-hidden">
        <img
          src={propertyImage}
          alt="Property"
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            // Prevent infinite loop by checking if we're already using the default image
            if (!target.src.includes("property-image-default.jpg")) {
              target.src = "/images/property-image-default.jpg";
            } else {
              // If default image also fails, use a data URL placeholder
              target.src =
                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzlmYTJhOCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==";
            }
          }}
        />
        {mlsCode && (
          <Badge className="absolute top-2 left-2 bg-black/70 text-white">
            MLS {mlsCode}
          </Badge>
        )}
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold truncate">
              {price}
            </CardTitle>
            {address && (
              <p className="text-sm text-muted-foreground truncate mt-1">
                {address}
              </p>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Created {createdAt}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/listing-sheet/listings/${sheet.documentId || sheet.id}`}>
                <Edit className="h-4 w-4 mr-1" />
                Edit
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onDelete}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function EmptyState() {
  return (
    <Card className="text-center py-12">
      <CardContent>
        <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
          <Plus className="h-12 w-12 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold mb-2">No listing sheets found</h3>
        <p className="text-muted-foreground mb-6">
          Get started by creating your first listing sheet with mortgage
          calculations.
        </p>
        <Button asChild>
          <Link href="/listing-sheet/new">
            <Plus className="mr-2 h-4 w-4" />
            Create Your First Listing Sheet
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}

function ListingSheetListSkeleton() {
  return (
    <div>
      {/* Header Skeleton */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Alert Skeleton */}
      <Skeleton className="h-16 w-full mb-6" />

      {/* Grid Skeleton */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <Skeleton className="aspect-video w-full" />
            <CardHeader className="pb-3">
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-24" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
