"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Upload, User, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Textarea } from "@/shared/ui/textarea";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { validateFile } from "../lib/file-upload";
import type { RealtorInfo, CreateRealtorData } from "../types";

// Validation schema for realtor form
const realtorFormSchema = z.object({
  firstname: z.string().min(1, "First name is required"),
  middlename: z.string().optional(),
  lastname: z.string().min(1, "Last name is required"),
  position: z.string().min(1, "Position is required"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(1, "Phone number is required"),
  company: z.string().min(1, "Company is required"),
  website: z.string().url("Please enter a valid website URL").optional().or(z.literal("")),
});

type RealtorFormData = CreateRealtorData;

interface RealtorFormProps {
  initialData?: Partial<RealtorInfo>;
  onSubmit: (data: RealtorFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  error?: string | null;
  submitLabel?: string;
  mode?: "create" | "edit";
}

export function RealtorForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
  submitLabel = "Save Realtor",
  mode = "create",
}: RealtorFormProps) {
  const [photoPreview, setPhotoPreview] = useState<string | null>(
    initialData?.photo?.url || null
  );
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm<Omit<RealtorFormData, "photo">>({
    resolver: zodResolver(realtorFormSchema),
    defaultValues: {
      firstname: initialData?.firstname || "",
      middlename: initialData?.middlename || "",
      lastname: initialData?.lastname || "",
      position: initialData?.position || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      company: initialData?.company || "",
      website: initialData?.website || "",
    },
    mode: "onChange",
  });

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file using the utility function
      const validation = validateFile(file);
      if (!validation.isValid) {
        alert(validation.error);
        return;
      }

      setSelectedPhoto(file);
      setPhotoPreview(URL.createObjectURL(file));
    }
  };

  const onFormSubmit = async (data: Omit<RealtorFormData, "photo">) => {
    const formData: RealtorFormData = {
      ...data,
      photo: selectedPhoto || undefined,
    };

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting realtor form:", error);
    }
  };

  const handleCancel = () => {
    reset();
    setPhotoPreview(initialData?.photo?.url || null);
    setSelectedPhoto(null);
    onCancel();
  };

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Photo Upload */}
      <div className="space-y-2">
        <Label>Profile Photo</Label>
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 bg-gray-100 rounded-full overflow-hidden flex items-center justify-center">
            {photoPreview ? (
              <img
                src={photoPreview}
                alt="Realtor photo"
                className="w-full h-full object-cover"
              />
            ) : (
              <User className="w-8 h-8 text-gray-400" />
            )}
          </div>
          <div>
            <Label htmlFor="photo" className="cursor-pointer">
              <div className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                <Upload className="w-4 h-4" />
                <span className="text-sm">Upload Photo</span>
              </div>
              <Input
                id="photo"
                type="file"
                accept="image/jpeg,image/jpg,image/png"
                onChange={handlePhotoChange}
                className="sr-only"
              />
            </Label>
            <p className="text-xs text-gray-500 mt-1">
              JPG, PNG up to 5MB
            </p>
          </div>
        </div>
      </div>

      {/* Personal Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="firstname">
            First Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="firstname"
            {...register("firstname")}
            placeholder="John"
            className={errors.firstname ? "border-red-500" : ""}
          />
          {errors.firstname && (
            <p className="text-sm text-red-500 mt-1">
              {errors.firstname.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="middlename">Middle Name</Label>
          <Input
            id="middlename"
            {...register("middlename")}
            placeholder="Michael"
          />
        </div>

        <div>
          <Label htmlFor="lastname">
            Last Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="lastname"
            {...register("lastname")}
            placeholder="Doe"
            className={errors.lastname ? "border-red-500" : ""}
          />
          {errors.lastname && (
            <p className="text-sm text-red-500 mt-1">
              {errors.lastname.message}
            </p>
          )}
        </div>
      </div>

      {/* Professional Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="position">
            Position <span className="text-red-500">*</span>
          </Label>
          <Input
            id="position"
            {...register("position")}
            placeholder="Real Estate Agent"
            className={errors.position ? "border-red-500" : ""}
          />
          {errors.position && (
            <p className="text-sm text-red-500 mt-1">
              {errors.position.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="company">
            Company <span className="text-red-500">*</span>
          </Label>
          <Input
            id="company"
            {...register("company")}
            placeholder="ABC Realty"
            className={errors.company ? "border-red-500" : ""}
          />
          {errors.company && (
            <p className="text-sm text-red-500 mt-1">
              {errors.company.message}
            </p>
          )}
        </div>
      </div>

      {/* Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="email">
            Email <span className="text-red-500">*</span>
          </Label>
          <Input
            id="email"
            type="email"
            {...register("email")}
            placeholder="<EMAIL>"
            className={errors.email ? "border-red-500" : ""}
          />
          {errors.email && (
            <p className="text-sm text-red-500 mt-1">
              {errors.email.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="phone">
            Phone <span className="text-red-500">*</span>
          </Label>
          <Input
            id="phone"
            type="tel"
            {...register("phone")}
            placeholder="************"
            className={errors.phone ? "border-red-500" : ""}
          />
          {errors.phone && (
            <p className="text-sm text-red-500 mt-1">
              {errors.phone.message}
            </p>
          )}
        </div>
      </div>

      {/* Website */}
      <div>
        <Label htmlFor="website">Website</Label>
        <Input
          id="website"
          type="url"
          {...register("website")}
          placeholder="https://johndoe.realtor"
          className={errors.website ? "border-red-500" : ""}
        />
        {errors.website && (
          <p className="text-sm text-red-500 mt-1">
            {errors.website.message}
          </p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={handleCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!isValid || isLoading}
          className="min-w-[120px]"
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {submitLabel}
        </Button>
      </div>
    </form>
  );
}
