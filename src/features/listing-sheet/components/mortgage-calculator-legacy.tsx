"use client";

import React, { useState, useEffect, useRef, useMemo } from "react";
import { useRouter } from "next/navigation";
import {  Upload, FileText, Loader2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/shared/ui/button";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Textarea } from "@/shared/ui/textarea";
import { CurrencyInputField } from "@/shared/ui/currency-input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/shared/ui/accordion";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Switch } from "@/shared/ui/switch";
import { useUserData } from "@/shared/hooks/use-auth";
import { formatCurrency } from "../lib/mortgage-calculations";
import { uploadFile, validateFile } from "../lib/file-upload";
import { useRealtors } from "../hooks/use-realtors";
import { RealtorModal } from "./realtor-modal";
import { GenerateListingSheet } from "../index";
import type { UserInfo, RealtorInfo } from "../types";

interface MortgageCalculatorLegacyProps {
  save?: (status: string, generator: any, calcInfo: any) => void;
  data?: any;
  saving?: boolean;
}

export default function MortgageCalculatorLegacy({
  save,
  data,
  saving: externalSaving,
}: MortgageCalculatorLegacyProps) {
  const calculator = useRef<HTMLDivElement>(null);

  // Get user data from the useUserData hook which has more complete information
  const { fetchUserData, isLoading: userDataLoading } = useUserData();
  const [userData, setUserData] = useState<any>(null);

  
  // Memoize userInfo to prevent infinite loops
  const userInfo = useMemo(() => ({
    id: userData?.id || "",
    documentId: userData?.documentId || "",
    photo: {
      url: userData?.photo?.url || userData?.avatar || null,
      formats: {
        thumbnail: {
          url: userData?.photo?.formats?.thumbnail?.url || null,
        },
        squared: {
          url: userData?.photo?.formats?.squared?.url || null,
        },
      },
    },
    firstname: userData?.firstname || userData?.name?.split(' ')[0] || null,
    lastname: userData?.lastname || userData?.name?.split(' ').slice(1).join(' ') || null,
    position: userData?.position || null,
    email: userData?.email || null,
    workEmail: userData?.workEmail || userData?.email || null,
    phone: userData?.phone || null,
    workPhone: userData?.workPhone || userData?.phone || null,
    cellPhone: userData?.cellPhone || userData?.phone || null,
    website: userData?.website || null,
    brokerage: userData?.brokerage || userData?.company || userData?.team?.name || null,
    team: {
      id: userData?.team?.id || null,
      logo: { url: userData?.team?.logo?.url || null },
      showFSRA: userData?.team?.showFSRA || false,
    },
  }), [userData]);

  // Fetch user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const { userInfo } = await fetchUserData();
        if (userInfo) {
          // Ensure all required fields are present for the PDF generator
          const enhancedUserData = {
            ...userInfo,
            // Map existing fields to expected structure
            firstname: userInfo.firstname || userInfo.name?.split(' ')[0] || '',
            lastname: userInfo.lastname || userInfo.name?.split(' ').slice(1).join(' ') || '',
            position: userInfo.position || '',
            workEmail: userInfo.workEmail || userInfo.email || '',
            email: userInfo.email || '',
            workPhone: userInfo.workPhone || userInfo.phone || '',
            phone: userInfo.phone || '',
            cellPhone: userInfo.cellPhone || userInfo.phone || '',
            website: userInfo.website || '',
            brokerage: userInfo.brokerage || userInfo.company || userInfo.team?.name || '',
            team: {
              id: userInfo.team?.id || '',
              name: userInfo.team?.name || userInfo.company || '',
              logo: userInfo.team?.logo || { url: null },
              showFSRA: userInfo.team?.showFSRA || false,
            },
            photo: userInfo.photo || { url: userInfo.avatar || null }
          };
          setUserData(enhancedUserData);
        }
      } catch (error) {
        console.error('Failed to fetch user data:', error);
      }
    };

    loadUserData();
  }, [fetchUserData]);



  // Fetch realtors for the current user and create realtor functionality
  const { 
    realtors, 
    isLoading: realtorsLoading, 
    error: realtorsError, 
    refetch: refetchRealtors,
    createRealtor,
    isCreating: isCreatingRealtor,
    createError: createRealtorError
  } = useRealtors(userData?.id);

  // Modal state
  const [isRealtorModalOpen, setIsRealtorModalOpen] = useState(false);

  // Initialize state exactly like legacy
  const [realtorInfo, setRealtorInfo] = useState<RealtorInfo>({
    firstname: "",
    middlename: null,
    lastname: "",
    position: "",
    company: "",
    email: "",
    phone: "",
    website: "",
    photo: null,
  });


  const initialState = data || {
    loading: false,
    saving: false,
    uploading: { status: false, type: null },
    amount: 0,
    askingPrice: 0,
    customPercentage: 0,
    downPay5: 0,
    downPay10: 0,
    downPay15: 0,
    downPay20: 0,
    downPayCustom: 0,
    downPayRange: 0,
    downPayFTHB: 0,
    chosenDownPay: { percent: "select", amount: 0, rate: "" },
    principal5: 0,
    principal10: 0,
    principal15: 0,
    principal20: 0,
    principalCustom: 0,
    principalRange: 0,
    principalFTHB: 0,
    chosenPrincipal: 0,
    rate5: 0,
    rate10: 0,
    rate15: 0,
    rate20: 0,
    rateCustom: 0,
    rateRange: 0,
    rateFTHB: 0,
    insurance5: 0,
    insurance10: 0,
    insurance15: 0,
    insurance20: 0,
    insuranceCustom: 0,
    insuranceRange: 0,
    insuranceFTHB: 0,
    monthlyPay5: 0,
    monthlyPay10: 0,
    monthlyPay15: 0,
    monthlyPay20: 0,
    monthlyPayCustom: 0,
    monthlyPayRange: 0,
    monthlyPayFTHB: 0,
    periodicPay5: 0,
    periodicPay10: 0,
    periodicPay15: 0,
    periodicPay20: 0,
    periodicPayCustom: 0,
    periodicPayRange: 0,
    periodicPayFTHB: 0,
    years: 0,
    customYears: 0,
    rate: 0,
    effectiveRates: {
      effectiveRate5: 0,
      effectiveRate10: 0,
      effectiveRate15: 0,
      effectiveRate20: 0,
      effectiveRateCustom: 0,
      effectiveRateRange: 0,
      effectiveRateFTHB: 0,
    },
    rangeRate: { type: "normalTier", rate: null },
    frequency: "monthly",
    numberOfPayments: 0,
    showMortgagePayment: true,
    showBodyCashNeeded: true,
    showBodyMonthlyExpenses: true,
    showBodyYearlyExpenses: true,
    showAdditionalInfo: true,
    showRealtorInfo: true,
    showImages: true,
    houseType: "select",
    estoppelFee: { raw: 0, currency: 0 },
    lawyerFee: { raw: 0, currency: 0 },
    titleInsurance: { raw: 0, currency: 0 },
    homeInspection: { raw: 0, currency: 0 },
    appraisal: { raw: 0, currency: 0 },
    totalCashNeeded: 0,
    condoFees: { raw: 0, currency: 0 },
    hoaFees: { raw: 0, currency: 0 },
    monthlyDebtPayments: { raw: 0, currency: 0 },
    utilities: { raw: 0, currency: 0 },
    propertyTax: { raw: 0, currency: 0 },
    propertyInsurance: { raw: 0, currency: 0 },
    phone: { raw: 0, currency: 0 },
    cable: { raw: 0, currency: 0 },
    internet: { raw: 0, currency: 0 },
    chosenPeriodicPay: 0,
    chosenDownPayExpense: {},
    totalMonthlyPayments: 0,
    totalYearlyPayments: 0,
    amortizationBalance: 0,
    pdf: {
      full: false,
      short: true,
      mlsCode: null,
      address: null,
      monthlyExpenses: true,
      cashNeeded: true,
      propertyPhoto: { url: null, ext: null },
      realtorPhoto: { url: null, ext: null },
      user: { ...userInfo },
    },
    realtor: {
      firstname: "",
      middlename: null,
      lastname: "",
      position: "",
      company: "",
      email: "",
      workPhone: "",
      website: "",
      photo: null,
    },
  };

  const [calcInfo, setCalcInfo] = useState(initialState);
  const [validation, setValidation] = useState<string[]>([]);
  const [scenarios, setScenarios] = useState({
    fthb: false,
    newBuild: true,
    preOwned: false,
  });

  // Track if down payment needs to be reselected after asking price change
  const [downPaymentNeedsReselection, setDownPaymentNeedsReselection] = useState(false);

  const [showUploader, setShowUploader] = useState(false);

  // Simple currency input handler using react-currency-input-field
  const handleCurrencyChange = (value: string | undefined, field: keyof typeof calcInfo) => {
    const numericValue = value ? parseFloat(value) : 0;
    setCalcInfo((prev: typeof calcInfo) => ({
      ...prev,
      [field]: { 
        raw: numericValue, 
        currency: numericValue 
      }
    }));
  };

  // Calculate the chosen periodic payment based on selected down payment
  const chosenPeriodicPayCalc = () => {
    const chosenDownPayPercent = calcInfo.chosenDownPay.percent;
    
    if (chosenDownPayPercent === "5%") {
      setCalcInfo({ 
        ...calcInfo, 
        chosenPeriodicPay: calcInfo.periodicPay5, 
        chosenPrincipal: calcInfo.principal5,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPay5,
          rate: "rate5"
        }
      });
    } else if (chosenDownPayPercent === "10%") {
      setCalcInfo({ 
        ...calcInfo, 
        chosenPeriodicPay: calcInfo.periodicPay10, 
        chosenPrincipal: calcInfo.principal10,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPay10,
          rate: "rate10"
        }
      });
    } else if (chosenDownPayPercent === "15%") {
      setCalcInfo({ 
        ...calcInfo, 
        chosenPeriodicPay: calcInfo.periodicPay15, 
        chosenPrincipal: calcInfo.principal15,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPay15,
          rate: "rate15"
        }
      });
    } else if (chosenDownPayPercent === "20%") {
      setCalcInfo({ 
        ...calcInfo, 
        chosenPeriodicPay: calcInfo.periodicPay20, 
        chosenPrincipal: calcInfo.principal20,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPay20,
          rate: "rate20"
        }
      });
    } else if (chosenDownPayPercent.includes("%") && calcInfo.rangeRate.type === "middleTier") {
      setCalcInfo({
        ...calcInfo,
        chosenPeriodicPay: calcInfo.periodicPayRange,
        chosenPrincipal: calcInfo.principalRange,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPayRange,
          rate: "rateRange"
        }
      });
    } else {
      setCalcInfo({
        ...calcInfo,
        chosenPeriodicPay: calcInfo.periodicPayCustom,
        chosenPrincipal: calcInfo.principalCustom,
        chosenDownPay: {
          ...calcInfo.chosenDownPay,
          amount: calcInfo.downPayCustom,
          rate: "rateCustom"
        }
      });
    }
  };

  const isNaNOrMoney = (n: number) => {
    if (isNaN(n)) {
      return "$0.00";
    }
    return formatCurrency(n)
  };

  // Validation function exactly like legacy
  const handleValidation = () => {
    const invalid: string[] = [];
    const { askingPrice, years, chosenDownPay } = calcInfo;

    if (askingPrice === 0 || !askingPrice) {
      invalid.push("Please add the asking price.");
    }
    if (chosenDownPay === 0 || !chosenDownPay) {
      invalid.push("Please select a down payment percentage.");
    }
    if (years === 0 || !years) {
      invalid.push("Please add an amortization period.");
    }
    
    // Add validation for down payment reselection after asking price change
    if (downPaymentNeedsReselection) {
      invalid.push("Please reselect your down payment percentage after changing the asking price.");
    }

    if (invalid.length > 0) {
      setValidation(invalid);
    } else {
      setValidation([]);
    }

    return invalid;
  };

  // Amount change handler exactly like legacy
  const onChangeAmount = (value: number) => {
    const numericAmount = value || 0;

    setCalcInfo({
      ...calcInfo,
      amount: numericAmount,
      askingPrice: numericAmount,
      pdf: { ...calcInfo.pdf, full: false, short: true },
      // Reset down payment selection when asking price changes
      chosenDownPay: { percent: "select", amount: 0, rate: "" },
      // Reset custom percentage when asking price changes
      customPercentage: 0,
    });

    // Set flag that down payment needs to be reselected
    setDownPaymentNeedsReselection(true);

    // Calculate range rate immediately when amount changes
    calcDownPaymentMinRateByAmount(numericAmount);
  };

  // Calculate Down Payment and Insurance Amounts exactly like legacy
  const calcDownPaymentMinRateByAmount = (amount?: number) => {
    const calcAmount = amount || parseFloat(calcInfo.amount.toString()) || 0;

    if (isNaN(calcAmount)) return;

    // Regular Buyer Calculations (Threshold updated from $1M to $1.5M)
    let regularRangeRate: { type: "normalTier" | "middleTier" | "maxTier"; rate: number | null } = { type: "normalTier", rate: null };
    if (calcAmount > 500000 && calcAmount < 1500000) {
      const regularRate = 500000 * 0.05;
      const remainingRate = (calcAmount - 500000) * 0.1;
      const rangeRate =
        (((100 * (regularRate + remainingRate)) / calcAmount) * 100) / 100;
      regularRangeRate = { type: "middleTier", rate: rangeRate };
    } else if (calcAmount >= 1500000) {
      regularRangeRate = { type: "maxTier", rate: 20 };
    } else if (calcAmount > 0) {
      regularRangeRate = { type: "normalTier", rate: null };
    }

    // FTHB Buyer Calculations
    let minDownPercentFTHB = 5;
    let minDownAmountFTHB = calcAmount * 0.05;
    if (calcAmount > 500000 && calcAmount <= 1500000) {
      const baseDown = 500000 * 0.05;
      const additionalDown = (calcAmount - 500000) * 0.1;
      minDownAmountFTHB = baseDown + additionalDown;
      minDownPercentFTHB = (minDownAmountFTHB / calcAmount) * 100;
    } else if (calcAmount > 1500000) {
      minDownPercentFTHB = 20;
      minDownAmountFTHB = calcAmount * 0.2;
    }

    // Insurance Calculation Helper
    const calculateInsurance = (principal: number, downPercent: number) => {
      if (downPercent >= 20) return 0;
      if (downPercent >= 15) return principal * 0.028; // 15% - 19.99%
      if (downPercent >= 10) return principal * 0.031; // 10% - 14.99%
      if (downPercent >= 5) return principal * 0.04; // 5% - 9.99%
      return 0;
    };

    // Calculate all tiers based on amount and regular threshold
    const isMiddleTier = regularRangeRate.type === "middleTier";

    const downPay5 = calcAmount * 0.05;
    const downPay10 = calcAmount * 0.1;
    const downPay15 = calcAmount * 0.15;
    const downPay20 = calcAmount * 0.2;
    const downPayRange =
      regularRangeRate.type === "middleTier"
        ? (calcAmount * (regularRangeRate.rate || 0)) / 100
        : 0;

    const insurance5 = calculateInsurance(calcAmount - downPay5, 5);
    const insurance10 = calculateInsurance(calcAmount - downPay10, 10);
    const insurance15 = calculateInsurance(calcAmount - downPay15, 15);
    const insurance20 = 0; // Always 0 for 20%
    const insuranceRange = isMiddleTier
      ? (calcAmount - (calcAmount * (regularRangeRate.rate || 0)) / 100) *
        ((regularRangeRate.rate || 0) >= 15
          ? 0.028
          : (regularRangeRate.rate || 0) >= 10
          ? 0.031
          : 0.04)
      : 0;

    const principal5 = downPay5 > 0 ? calcAmount - downPay5 + insurance5 : 0;
    const principal10 =
      downPay10 > 0 ? calcAmount - downPay10 + insurance10 : 0;
    const principal15 =
      downPay15 > 0 ? calcAmount - downPay15 + insurance15 : 0;
    const principal20 = calcAmount - downPay20 + insurance20;
    const principalRange = isMiddleTier
      ? calcAmount -
        (calcAmount * (regularRangeRate.rate || 0)) / 100 +
        (calcAmount - (calcAmount * (regularRangeRate.rate || 0)) / 100) *
          ((regularRangeRate.rate || 0) >= 15
            ? 0.028
            : (regularRangeRate.rate || 0) >= 10
            ? 0.031
            : 0.04)
      : 0;

    // Calculate FTHB Insurance and Principal
    const insuranceFTHB = calculateInsurance(
      calcAmount - minDownAmountFTHB,
      minDownPercentFTHB
    );
    const principalFTHB = calcAmount - minDownAmountFTHB + insuranceFTHB;

    // Update state with new range rate and all calculated payment info
    setCalcInfo((prevState: any) => ({
      ...prevState,
      rangeRate: regularRangeRate,
      downPay5,
      downPay10,
      downPay15,
      downPay20,
      downPayRange,
      downPayFTHB: minDownAmountFTHB,
      insurance5,
      insurance10,
      insurance15,
      insurance20,
      insuranceRange,
      insuranceFTHB,
      principal5,
      principal10,
      principal15,
      principal20,
      principalRange,
      principalFTHB,
    }));
  };

  // Handle scenario toggle exactly like legacy
  const handleScenarioToggle = (scenario: string) => {
    const newScenarios = {
      fthb: false,
      newBuild: false,
      preOwned: false,
    };

    newScenarios[scenario as keyof typeof newScenarios] = true;
    setScenarios(newScenarios);

    // Adjust the years based on the scenario
    if (scenario === "fthb" || scenario === "newBuild") {
      setCalcInfo((prev: any) => ({
        ...prev,
        years: Math.min(prev.years || 25, 30),
      }));
    } else if (scenario === "preOwned") {
      setCalcInfo((prev: any) => ({
        ...prev,
        years: Math.min(prev.years || 25, 25),
      }));
    }
  };

  // Toggle functions exactly like legacy
  const dropdownToggleAdditionalInfo = () => {
    setCalcInfo({
      ...calcInfo,
      showAdditionalInfo: !calcInfo.showAdditionalInfo,
    });
  };

  const dropdownRealtorInfo = () => {
    setCalcInfo({ ...calcInfo, showRealtorInfo: !calcInfo.showRealtorInfo });
  };

  const dropdownToggleMortgagePayment = () => {
    setCalcInfo({
      ...calcInfo,
      showMortgagePayment: !calcInfo.showMortgagePayment,
    });
  };

  const dropdownToggleCashNeeded = () => {
    setCalcInfo({
      ...calcInfo,
      showBodyCashNeeded: !calcInfo.showBodyCashNeeded,
    });
  };

  const dropdownToggleMonthlyExpenses = () => {
    setCalcInfo({
      ...calcInfo,
      showBodyMonthlyExpenses: !calcInfo.showBodyMonthlyExpenses,
    });
  };

  const dropdownToggleYearlyExpenses = () => {
    setCalcInfo({
      ...calcInfo,
      showBodyYearlyExpenses: !calcInfo.showBodyYearlyExpenses,
    });
  };

  // Get effective rate calculation exactly like legacy
  const getEffectiveRate = () => {
    const rateAmount = (r: string) => {
      if (parseFloat(calcInfo[r as keyof typeof calcInfo] as string) === 0) {
        return 0;
      } else {
        return parseFloat(calcInfo[r as keyof typeof calcInfo] as string);
      }
    };

    const effectiveRateCalc = (rate: number) => {
      const nominalRate = parseFloat(rate.toString()) / 100;
      const effectiveRate =
        (Math.pow(Math.pow(1 + nominalRate / 2, 2), 1 / 12) - 1) * 12;
      return effectiveRate;
    };

    const rates = {
      effectiveRate5: effectiveRateCalc(rateAmount("rate5")),
      effectiveRate10: effectiveRateCalc(rateAmount("rate10")),
      effectiveRate15: effectiveRateCalc(rateAmount("rate15")),
      effectiveRate20: effectiveRateCalc(rateAmount("rate20")),
      effectiveRateCustom: effectiveRateCalc(rateAmount("rateCustom")),
      effectiveRateRange: effectiveRateCalc(rateAmount("rateRange")),
      effectiveRateFTHB: effectiveRateCalc(rateAmount("rateFTHB")),
    };

    setCalcInfo((prevState: any) => ({
      ...prevState,
      effectiveRates: rates,
    }));
  };

  // Calculate monthly payment exactly like legacy
  const monthlyPay = () => {
    const { years, customYears } = calcInfo;
    const { effectiveRates } = calcInfo;

    const principalAmount = (p: string) => {
      const principalVal = calcInfo[p as keyof typeof calcInfo] as number;
      if (
        principalVal === undefined ||
        principalVal === null ||
        isNaN(parseFloat(principalVal.toString()))
      ) {
        return 0;
      }
      return parseFloat(principalVal.toString());
    };

    const {
      effectiveRate5,
      effectiveRate10,
      effectiveRate15,
      effectiveRate20,
      effectiveRateCustom,
      effectiveRateRange,
      effectiveRateFTHB,
    } = effectiveRates;

    const numberOfPayments = years !== 0 ? years * 12 : customYears * 12;

    const calculateMonthlyPayment = (rate: number, principal: number) => {
      if (rate <= 0 || principal <= 0 || numberOfPayments <= 0) return 0;
      const monthlyRate = rate / 12;
      return (
        (monthlyRate * principal) /
        (1 - Math.pow(1 + monthlyRate, -1 * numberOfPayments))
      );
    };

    setCalcInfo((prevState: any) => ({
      ...prevState,
      monthlyPay5: calculateMonthlyPayment(
        effectiveRate5,
        principalAmount("principal5")
      ),
      monthlyPay10: calculateMonthlyPayment(
        effectiveRate10,
        principalAmount("principal10")
      ),
      monthlyPay15: calculateMonthlyPayment(
        effectiveRate15,
        principalAmount("principal15")
      ),
      monthlyPay20: calculateMonthlyPayment(
        effectiveRate20,
        principalAmount("principal20")
      ),
      monthlyPayCustom: calculateMonthlyPayment(
        effectiveRateCustom,
        principalAmount("principalCustom")
      ),
      monthlyPayRange: calculateMonthlyPayment(
        effectiveRateRange,
        principalAmount("principalRange")
      ),
      monthlyPayFTHB: calculateMonthlyPayment(
        effectiveRateFTHB,
        principalAmount("principalFTHB")
      ),
    }));
  };

  // Calculate periodic payment exactly like legacy
  const periodicPayCalc = () => {
    const {
      frequency,
      monthlyPay5,
      monthlyPay10,
      monthlyPay15,
      monthlyPay20,
      monthlyPayCustom,
      monthlyPayRange,
      monthlyPayFTHB,
    } = calcInfo;

    let pay5 = 0,
      pay10 = 0,
      pay15 = 0,
      pay20 = 0,
      payCustom = 0,
      payRange = 0,
      payFTHB = 0,
      numPayments = 0;

    if (frequency === "monthly") {
      pay5 = monthlyPay5;
      pay10 = monthlyPay10;
      pay15 = monthlyPay15;
      pay20 = monthlyPay20;
      payCustom = monthlyPayCustom;
      payRange = monthlyPayRange;
      payFTHB = monthlyPayFTHB;
      numPayments = 12;
    } else if (frequency === "biweekly") {
      pay5 = (monthlyPay5 * 12) / 26;
      pay10 = (monthlyPay10 * 12) / 26;
      pay15 = (monthlyPay15 * 12) / 26;
      pay20 = (monthlyPay20 * 12) / 26;
      payCustom = (monthlyPayCustom * 12) / 26;
      payRange = (monthlyPayRange * 12) / 26;
      payFTHB = (monthlyPayFTHB * 12) / 26;
      numPayments = 26;
    } else if (frequency === "weekly") {
      pay5 = (monthlyPay5 * 12) / 52;
      pay10 = (monthlyPay10 * 12) / 52;
      pay15 = (monthlyPay15 * 12) / 52;
      pay20 = (monthlyPay20 * 12) / 52;
      payCustom = (monthlyPayCustom * 12) / 52;
      payRange = (monthlyPayRange * 12) / 52;
      payFTHB = (monthlyPayFTHB * 12) / 52;
      numPayments = 52;
    } else if (frequency === "accbiweekly") {
      pay5 = monthlyPay5 / 2;
      pay10 = monthlyPay10 / 2;
      pay15 = monthlyPay15 / 2;
      pay20 = monthlyPay20 / 2;
      payCustom = monthlyPayCustom / 2;
      payRange = monthlyPayRange / 2;
      payFTHB = monthlyPayFTHB / 2;
      numPayments = 26;
    } else if (frequency === "accweekly") {
      pay5 = monthlyPay5 / 4;
      pay10 = monthlyPay10 / 4;
      pay15 = monthlyPay15 / 4;
      pay20 = monthlyPay20 / 4;
      payCustom = monthlyPayCustom / 4;
      payRange = monthlyPayRange / 4;
      payFTHB = monthlyPayFTHB / 4;
      numPayments = 52;
    }


    setCalcInfo((prevState: any) => ({
      ...prevState,
      periodicPay5: pay5,
      periodicPay10: pay10,
      periodicPay15: pay15,
      periodicPay20: pay20,
      periodicPayCustom: payCustom,
      periodicPayRange: payRange,
      periodicPayFTHB: payFTHB,
      numberOfPayments: numPayments,
    }));
  };

  // Handle custom percentage calculation exactly like legacy
  const paymentCalc = (newAmount?: number) => {
    const percentage = calcInfo.customPercentage;
    const customPct = parseFloat(percentage.toString()) || 0;
    const { rangeRate } = calcInfo;
    const amount = newAmount || calcInfo.amount;

    const calcInsurance = (amountForInsurance: number, rate: number) => {
      if (rate >= 20) return 0;
      if (rate >= 15) return amountForInsurance * 0.028;
      if (rate >= 10) return amountForInsurance * 0.031;
      if (rate >= 5) return amountForInsurance * 0.04;
      return 0;
    };

    let downPayCustom = 0;
    let insuranceCustom = 0;
    let principalCustom = 0;
    const minRequiredPercent =
      rangeRate.type === "middleTier"
        ? rangeRate.rate || 0
        : rangeRate.type === "maxTier"
        ? 20
        : 5;

    if (customPct > 0 && customPct >= minRequiredPercent) {
      downPayCustom = amount * (customPct / 100);
      const amountForInsurance = amount - downPayCustom;
      insuranceCustom = calcInsurance(amountForInsurance, customPct);
      principalCustom = amount - downPayCustom + insuranceCustom;
    }

    setCalcInfo((prevState: any) => ({
      ...prevState,
      downPayCustom,
      insuranceCustom,
      principalCustom,
    }));
  };

  // PDF generation function
  const generatePdf = async () => {
    setCalcInfo({ ...calcInfo, loading: true });

    const isValid = handleValidation();

    if (isValid.length > 0) {
      setCalcInfo({ ...calcInfo, loading: false });
      return;
    }

    // Additional check for down payment reselection
    if (downPaymentNeedsReselection) {
      alert("Please reselect your down payment percentage after changing the asking price before generating the PDF.");
      setCalcInfo({ ...calcInfo, loading: false });
      return;
    }

    // Ensure user data is loaded
    if (!userData && userDataLoading) {
      alert("Please wait for user data to load before generating PDF.");
      setCalcInfo({ ...calcInfo, loading: false });
      return;
    }

    try {
      // Call the save function passed from parent if available
      if (save) {
        await save("generating", null, calcInfo);
      }

      // Debug log to see what data is being passed to PDF generator
      console.log('User data being passed to PDF generator:', userData);
      console.log('Realtor data being passed to PDF generator:', calcInfo.realtor);
      console.log('Complete calcInfo object:', calcInfo);
      console.log('Key fields for PDF:', {
        totalMonthlyPayments: calcInfo.totalMonthlyPayments,
        totalYearlyPayments: calcInfo.totalYearlyPayments,
        totalCashNeeded: calcInfo.totalCashNeeded,
        chosenDownPay: calcInfo.chosenDownPay,
        rate: calcInfo.rate
      });

      // Detect if browser is Safari
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      // Generate PDF using the actual PDF generator
      const result = await GenerateListingSheet(calcInfo, isSafari);

      if (result.status === 'error') {
        throw new Error(result.message);
      }

      setCalcInfo({ ...calcInfo, loading: false });
    } catch (error) {
      console.error("PDF generation failed:", error);
      setCalcInfo({ ...calcInfo, loading: false });
      alert("PDF generation failed. Please try again.");
    }
  };

  // useEffect hooks exactly like legacy to trigger calculations
  useEffect(() => {
    paymentCalc(calcInfo.amount);
  }, [
    calcInfo.rangeRate,
    calcInfo.years,
    calcInfo.chosenDownPay.percent,
    calcInfo.customPercentage,
  ]);

  // Handle loading saved data and ensure custom percentages are properly set
  useEffect(() => {
    if (data && calcInfo.customPercentage > 0 && calcInfo.chosenDownPay.percent === "select") {
      // If we have saved data with a custom percentage but no selected down payment,
      // set the chosenDownPay.percent to the custom percentage
      setCalcInfo((prev: typeof calcInfo) => ({
        ...prev,
        chosenDownPay: {
          ...prev.chosenDownPay,
          percent: `${prev.customPercentage}%`
        }
      }));
    }
  }, [data, calcInfo.customPercentage, calcInfo.chosenDownPay.percent]);

  // Comprehensive data loading handler for saved listing sheets
  useEffect(() => {
    if (data && data.chosenDownPay) {
      const savedDownPay = data.chosenDownPay;
      
      // Handle different formats of saved down payment data
      if (savedDownPay.percent && savedDownPay.percent !== "select") {
        // If we have a saved percentage, ensure it's in the correct format
        let formattedPercent = savedDownPay.percent;
        
        // Handle cases where the percentage might not have the % symbol
        if (!formattedPercent.includes('%')) {
          formattedPercent = `${formattedPercent}%`;
        }
        
        // Extract the numeric value from the percentage for customPercentage
        const numericPercent = parseFloat(formattedPercent.replace('%', ''));
        if (!isNaN(numericPercent) && numericPercent > 0) {
          setCalcInfo((prev: typeof calcInfo) => ({
            ...prev,
            customPercentage: numericPercent
          }));
        }
        
        // Update the chosenDownPay with the saved data
        setCalcInfo((prev: typeof calcInfo) => ({
          ...prev,
          chosenDownPay: {
            percent: formattedPercent,
            amount: savedDownPay.amount || prev.chosenDownPay.amount,
            rate: savedDownPay.rate || prev.chosenDownPay.rate
          }
        }));
        
        // Ensure the rangeRate.rate is calculated and consistent with the saved data
        if (data.amount && data.rangeRate) {
          // Recalculate to ensure consistency
          calcDownPaymentMinRateByAmount(data.amount);
        }
      }
      
      // If we have a custom percentage saved, ensure it's reflected in the UI
      if (data.customPercentage > 0) {
        setCalcInfo((prev: typeof calcInfo) => ({
          ...prev,
          customPercentage: data.customPercentage
        }));
      }
      
      // Clear the reselection flag when loading saved data (assuming it's consistent)
      setDownPaymentNeedsReselection(false);
    }
  }, [data]);

  // Update chosen periodic payment when down payment amounts change
  useEffect(() => {
    chosenPeriodicPayCalc();
  }, [
    calcInfo.downPay5,
    calcInfo.downPay10,
    calcInfo.downPay15,
    calcInfo.downPay20,
    calcInfo.downPayRange,
    calcInfo.downPayCustom,
    calcInfo.downPayFTHB
  ]);

  useEffect(() => {
    if (realtorInfo && realtorInfo !== null && realtorInfo !== undefined) {
      setCalcInfo({ ...calcInfo, realtor: { ...realtorInfo } });
    }
  }, [realtorInfo]);

  useEffect(() => {
    const timer = setTimeout(() => {
      handleValidation();
    }, 1000);
    return () => clearTimeout(timer);
  }, [
    calcInfo.askingPrice,
    calcInfo.years,
    calcInfo.rate5,
    calcInfo.rate10,
    calcInfo.rate15,
    calcInfo.rate20,
    calcInfo.rateCustom,
    calcInfo.chosenDownPay,
  ]);

  useEffect(() => {
    getEffectiveRate();
  }, [
    calcInfo.rate5,
    calcInfo.rate10,
    calcInfo.rate15,
    calcInfo.rate20,
    calcInfo.rateCustom,
    calcInfo.rateRange,
    calcInfo.rateFTHB,
  ]);

  useEffect(() => {
    monthlyPay();
  }, [
    calcInfo.effectiveRates,
    calcInfo.principal5,
    calcInfo.principal10,
    calcInfo.principal15,
    calcInfo.principal20,
    calcInfo.principalRange,
    calcInfo.principalCustom,
    calcInfo.principalFTHB,
    calcInfo.years,
    calcInfo.customYears,
  ]);

  useEffect(() => {
    periodicPayCalc();
  }, [
    calcInfo.monthlyPay5,
    calcInfo.monthlyPay10,
    calcInfo.monthlyPay15,
    calcInfo.monthlyPay20,
    calcInfo.monthlyPayRange,
    calcInfo.monthlyPayCustom,
    calcInfo.monthlyPayFTHB,
    calcInfo.frequency,
  ]);

  useEffect(() => {
    if (calcInfo.amount) {
      calcDownPaymentMinRateByAmount();
      paymentCalc(calcInfo.amount);
    }
  }, [calcInfo.amount]);

  // Update chosen periodic payment when down payment selection changes
  useEffect(() => {
    chosenPeriodicPayCalc();
  }, [calcInfo.chosenDownPay.percent]);

  // Update chosen periodic payment when periodic payments change
  useEffect(() => {
    chosenPeriodicPayCalc();
  }, [
    calcInfo.periodicPay5,
    calcInfo.periodicPay10,
    calcInfo.periodicPay15,
    calcInfo.periodicPay20,
    calcInfo.periodicPayRange,
    calcInfo.periodicPayCustom,
    calcInfo.periodicPayFTHB
  ]);

  // Handle creating a new realtor
  const handleCreateRealtor = async (realtorData: any) => {
    if (!userData?.id) {
      console.error("No user ID available");
      return;
    }

    try {
      const newRealtor = await createRealtor(realtorData, userData.id);
          
      
      setCalcInfo({
        ...calcInfo,
        realtor: {
          firstname: newRealtor.firstname,
          middlename: newRealtor.middlename,
          lastname: newRealtor.lastname,
          position: newRealtor.position,
          company: newRealtor.company,
          email: newRealtor.email,
          phone: newRealtor.phone,
          website: newRealtor.website,
          photo: newRealtor.photo,
        },
      });
      
      // Close the modal
      setIsRealtorModalOpen(false);
    } catch (error) {
      console.error("Failed to create realtor:", error);
      // Error is already handled by the hook
    }
  };

  // Log realtors data for debugging
  useEffect(() => {    
    if (realtorsError) {
      console.error('realtorsError', realtorsError);
    }
  }, [userData, realtors, realtorsLoading, realtorsError]);

  // Update calcInfo.pdf.user whenever userData changes
  useEffect(() => {
    if (userData) {
      console.log('Updating calcInfo.pdf.user with userInfo:', userInfo);
      setCalcInfo((prev: typeof calcInfo) => ({
        ...prev,
        pdf: {
          ...prev.pdf,
          user: { ...userInfo }
        }
      }));
    }
  }, [userData]); // userInfo is memoized, so we only need userData dependency

  // Sync saved realtor data with fresh realtor data from API
  useEffect(() => {
    console.log('Realtor sync effect running:', { 
      realtorsCount: realtors.length, 
      savedRealtor: calcInfo.realtor,
      hasSavedRealtor: !!calcInfo.realtor?.firstname 
    });
    
    if (realtors.length > 0 && calcInfo.realtor?.firstname) {
      // Find the current realtor data by matching the saved realtor info
      const savedRealtor = calcInfo.realtor;
      const currentRealtor = realtors.find(realtor => 
        realtor.firstname === savedRealtor.firstname && 
        realtor.lastname === savedRealtor.lastname &&
        realtor.email === savedRealtor.email
      );

      if (currentRealtor) {
        console.log('Syncing saved realtor with fresh data:', currentRealtor);
        // Update with fresh realtor data (including website, photo, etc.)
        setCalcInfo((prev: typeof calcInfo) => ({
          ...prev,
          realtor: {
            firstname: currentRealtor.firstname,
            middlename: currentRealtor.middlename,
            lastname: currentRealtor.lastname,
            position: currentRealtor.position,
            company: currentRealtor.company,
            email: currentRealtor.email,
            phone: currentRealtor.phone,
            website: currentRealtor.website,
            photo: currentRealtor.photo,
          }
        }));
      } else {
        console.log('Saved realtor no longer exists, clearing realtor selection');
        // Clear realtor selection if the saved realtor no longer exists
        setCalcInfo((prev: typeof calcInfo) => ({
          ...prev,
          realtor: {
            firstname: "",
            middlename: null,
            lastname: "",
            position: "",
            company: "",
            email: "",
            phone: "",
            website: "",
            photo: null,
          }
        }));
      }
    }
  }, [realtors, calcInfo.realtor?.firstname, calcInfo.realtor?.lastname, calcInfo.realtor?.email]);

  // Calculate totals for PDF generator
  useEffect(() => {
    // Calculate total monthly payments
    const totalMonthly = 
      (calcInfo.chosenPeriodicPay || 0) +
      (calcInfo.monthlyDebtPayments?.raw || 0) +
      (calcInfo.utilities?.raw || 0) +
      (calcInfo.condoFees?.raw || 0) +
      (calcInfo.phone?.raw || 0) +
      (calcInfo.cable?.raw || 0) +
      (calcInfo.internet?.raw || 0);

    // Calculate total yearly payments
    const totalYearly = 
      (calcInfo.propertyTax?.raw || 0) +
      (calcInfo.propertyInsurance?.raw || 0) +
      (calcInfo.hoaFees?.raw || 0);

    // Calculate total cash needed
    const totalCash = 
      (calcInfo.chosenDownPay?.amount || 0) +
      (calcInfo.lawyerFee?.raw || 0) +
      (calcInfo.homeInspection?.raw || 0) +
      (calcInfo.appraisal?.raw || 0) +
      (calcInfo.titleInsurance?.raw || 0) +
      (calcInfo.estoppelFee?.raw || 0);

    // Update calcInfo with calculated totals
    setCalcInfo((prev: typeof calcInfo) => ({
      ...prev,
      totalMonthlyPayments: totalMonthly,
      totalYearlyPayments: totalYearly,
      totalCashNeeded: totalCash,
    }));

    console.log('Calculated totals:', { totalMonthly, totalYearly, totalCash });
  }, [
    calcInfo.chosenPeriodicPay,
    calcInfo.monthlyDebtPayments?.raw,
    calcInfo.utilities?.raw,
    calcInfo.condoFees?.raw,
    calcInfo.phone?.raw,
    calcInfo.cable?.raw,
    calcInfo.internet?.raw,
    calcInfo.propertyTax?.raw,
    calcInfo.propertyInsurance?.raw,
    calcInfo.hoaFees?.raw,
    calcInfo.chosenDownPay?.amount,
    calcInfo.lawyerFee?.raw,
    calcInfo.homeInspection?.raw,
    calcInfo.appraisal?.raw,
    calcInfo.titleInsurance?.raw,
    calcInfo.estoppelFee?.raw,
  ]);

  // Set the chosenDownPay.rate field for PDF generator
  useEffect(() => {
    const { chosenDownPay, rangeRate } = calcInfo;
    
    if (chosenDownPay?.percent) {
      let rateField = 'rateCustom'; // default
      
      if (chosenDownPay.percent === '5%') {
        rateField = 'rate5';
      } else if (chosenDownPay.percent === '10%') {
        rateField = 'rate10';
      } else if (chosenDownPay.percent === '15%') {
        rateField = 'rate15';
      } else if (chosenDownPay.percent === '20%') {
        rateField = 'rate20';
      } else if (rangeRate?.type === 'middleTier' && chosenDownPay.percent.includes('%')) {
        rateField = 'rateRange';
      }
      
      setCalcInfo((prev: typeof calcInfo) => ({
        ...prev,
        chosenDownPay: {
          ...prev.chosenDownPay,
          rate: rateField
        }
      }));
      
      console.log('Set chosenDownPay.rate:', rateField);
    }
  }, [calcInfo.chosenDownPay?.percent, calcInfo.rangeRate?.type]);

  // Validation display
  const checkValidation = () => {
    if (validation.length > 0) {
      return (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>
            <div>
              <h3 className="font-semibold mb-2">
                The following fields are Required:
              </h3>
              <ul className="list-disc list-inside space-y-1">
                {validation.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      );
    }
    return null;
  };

  return (
    <div className="w-full mx-auto" ref={calculator}>
      {/* Loading overlay */}
      {(externalSaving || calcInfo.loading || calcInfo.uploading.status || userDataLoading) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>
              {calcInfo.uploading.status
                ? "Uploading Image..."
                : userDataLoading
                ? "Loading User Data..."
                : "Generating Listing Sheet PDF"}
            </span>
          </div>
        </div>
      )}

      {checkValidation()}

      <Accordion
        type="multiple"
        defaultValue={["property", "realtor", "mortgage"]}
        className="space-y-4"
      >
        {/* Property Details Section */}
        <AccordionItem value="property" className="border rounded-lg bg-white">
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownToggleAdditionalInfo}
          >
            <h2 className="text-lg font-semibold">Property Details</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="mlsCode">#MLS</Label>
                  <Input
                    id="mlsCode"
                    placeholder="#MLS"
                    value={calcInfo.pdf.mlsCode || ""}
                    onChange={(e) =>
                      setCalcInfo({
                        ...calcInfo,
                        pdf: { ...calcInfo.pdf, mlsCode: e.target.value },
                      })
                    }
                  />
                </div>
                <div>
                  <Label htmlFor="address">Property Address</Label>
                  <Textarea
                    id="address"
                    placeholder="Address"
                    value={calcInfo.pdf.address || ""}
                    onChange={(e) =>
                      setCalcInfo({
                        ...calcInfo,
                        pdf: { ...calcInfo.pdf, address: e.target.value },
                      })
                    }
                  />
                </div>
              </div>
              <div className="space-y-4">
                {!showUploader && calcInfo.pdf.propertyPhoto?.url ? (
                  <div className="space-y-2">
                    <div className="border rounded-lg p-4">
                      <img
                        src={calcInfo.pdf.propertyPhoto.url}
                        alt="Property Photo"
                        className="w-full h-32 object-cover rounded"
                      />
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowUploader(true)}
                      className="w-full"
                    >
                      Change Photo
                    </Button>
                  </div>
                ) : (
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-2">
                      <Label htmlFor="propertyPhoto" className="cursor-pointer">
                        <span className="text-sm font-medium text-blue-600 hover:text-blue-500">
                          Upload property photo
                        </span>
                        <Input
                          id="propertyPhoto"
                          type="file"
                          className="sr-only"
                          accept="image/jpeg,image/jpg,image/png"
                          onChange={async (e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              // Validate file
                              const validation = validateFile(file);
                              if (!validation.isValid) {
                                alert(validation.error);
                                return;
                              }

                              // Set uploading state to block other fields
                              setCalcInfo({
                                ...calcInfo,
                                uploading: {
                                  status: true,
                                  type: "propertyPhoto",
                                },
                              });

                              try {
                                // Upload the file using the utility function
                                const uploadedFile = await uploadFile(file);

                                setCalcInfo({
                                  ...calcInfo,
                                  pdf: {
                                    ...calcInfo.pdf,
                                    propertyPhoto: {
                                      url: uploadedFile.url,
                                      ext: uploadedFile.ext,
                                    },
                                  },
                                  uploading: { status: false, type: null },
                                });
                              } catch (error) {
                                console.error("Upload failed:", error);
                                setCalcInfo({
                                  ...calcInfo,
                                  uploading: { status: false, type: null },
                                });
                                alert(
                                  error instanceof Error 
                                    ? error.message 
                                    : "Upload failed. Please try again."
                                );
                              }
                            }
                          }}
                        />
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500">PNG, JPG up to 10MB</p>
                  </div>
                )}
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Realtor Info Section */}
        <AccordionItem value="realtor" className="border rounded-lg bg-white">
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownRealtorInfo}
          >
            <h2 className="text-lg font-semibold">Realtor Info</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                {realtors && realtors.length > 0 ? (
                  <>
                    <div>
                      <Label htmlFor="realtorSelect">
                        Select one of your Realtors
                      </Label>
                      <Select
                        value={
                          calcInfo.realtor?.firstname
                            ? realtors.find(
                                (realtor: any) =>
                                  realtor.firstname === calcInfo.realtor.firstname &&
                                  realtor.lastname === calcInfo.realtor.lastname
                              )?.id || ""
                            : ""
                        }
                        onValueChange={(selectedRealtorId) => {
                          const selectedRealtor = realtors.find(
                            (realtor: any) => realtor.id === selectedRealtorId
                          );
                          if (selectedRealtor) {
                            setCalcInfo({
                              ...calcInfo,
                              realtor: {
                                firstname: selectedRealtor.firstname,
                                middlename: selectedRealtor.middlename,
                                lastname: selectedRealtor.lastname,
                                position: selectedRealtor.position,
                                company: selectedRealtor.company,
                                email: selectedRealtor.email,
                                phone: selectedRealtor.phone,
                                website: selectedRealtor.website,
                                photo: selectedRealtor.photo,
                              },
                            });
                          }
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a Realtor" />
                        </SelectTrigger>
                        <SelectContent>
                          {realtors.map((realtor: any) => (
                            <SelectItem key={realtor.id} value={realtor.id}>
                              {realtor.firstname} {realtor.lastname}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-2">
                        Didn't find the Realtor on the list? Add a new Realtor.
                      </p>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsRealtorModalOpen(true)}
                      >
                        Add a Realtor
                      </Button>
                    </div>
                  </>
                ) : (
                  <div>
                    <p className="text-sm text-gray-600 mb-4">
                      We couldn't find any realtor in your account. Please add
                      your first realtor.
                    </p>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm"
                      onClick={() => setIsRealtorModalOpen(true)}
                    >
                      Add a Realtor
                    </Button>
                  </div>
                )}
              </div>
              <div>
                <div className="border rounded-lg p-4">
                  {calcInfo.realtor?.firstname ? (
                    <div className="flex gap-4">
                      <div className="w-16 h-16 bg-gray-200 rounded-full mb-2 overflow-hidden">
                        {(() => {                         
                          return calcInfo.realtor.photo?.url ? (
                            <img
                              src={calcInfo.realtor.photo.url}
                              alt={`${calcInfo.realtor.firstname} ${calcInfo.realtor.lastname}`}
                              className="w-full h-full object-cover"
                              onError={(e) => console.log("Image load error:", e)}
                              onLoad={() => console.log("Image loaded successfully")}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400">
                              <span className="text-lg font-medium">
                                {calcInfo.realtor.firstname.charAt(0)}
                                {calcInfo.realtor.lastname.charAt(0)}
                              </span>
                            </div>
                          );
                        })()}
                      </div>
                      <div>
                      <p className="font-medium">
                        {calcInfo.realtor.firstname} {calcInfo.realtor.lastname}
                      </p>
                      <p className="text-sm text-gray-600">
                        {calcInfo.realtor.position}
                      </p>
                      <p className="text-sm text-gray-600">
                        {calcInfo.realtor.company}
                      </p>
                      {calcInfo.realtor.email && (
                        <p className="text-xs text-gray-500 mt-1">
                          {calcInfo.realtor.email}
                        </p>
                      )}
                      {calcInfo.realtor.phone && (
                        <p className="text-xs text-gray-500">
                          {calcInfo.realtor.phone}
                        </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-500 py-8">No Realtor Selected.</p>
                  )}
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Mortgage Payment Section */}
        <AccordionItem value="mortgage" className="border rounded-lg bg-white">
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownToggleMortgagePayment}
          >
            <h2 className="text-lg font-semibold">Mortgage Payment</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            {/* Asking Price Input */}
            <div className="mb-6">
              <div className="flex items-center gap-4">
                <Label className="text-lg">Asking Price:</Label>
                <div className="flex items-center">
                  <span className="text-2xl text-teal-600 mr-2">$</span>
                  <Input
                    type="text"
                    className="text-xl font-semibold w-48"
                    value={calcInfo.amount.toLocaleString()}
                    onChange={(e) => {
                      const value =
                        parseFloat(e.target.value.replace(/,/g, "")) || 0;
                      onChangeAmount(value);
                    }}
                    onClick={(e) => (e.target as HTMLInputElement).select()}
                  />
                </div>
              </div>
            </div>

            {/* Calculation Table */}
            <div className="space-y-4">
              {/* Desktop Headers */}
              <div className="hidden md:block">
                <div className="grid grid-cols-6 gap-4 p-4 border-b-2 border-black rounded-t-lg text-center font-semibold">
                  <div></div>
                  <div>5% DOWN</div>
                  <div>
                    {Math.round((calcInfo.rangeRate.rate || 10) * 100) / 100}%
                    DOWN
                  </div>
                  <div>15% DOWN</div>
                  <div>20% DOWN</div>
                  <div className="flex items-center justify-center gap-1">
                    <Input
                      type="text"
                      className="w-12 text-center p-0 h-6"
                      value={calcInfo.customPercentage}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value) || 0;
                        setCalcInfo({ ...calcInfo, customPercentage: value });
                      }}
                    />
                    <span>% DOWN</span>
                  </div>
                </div>
              </div>

              {/* Down Payment Row */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border-b">
                <div className="font-medium md:text-right">Down Payment:</div>
                <div
                  className={`text-center ${
                    calcInfo.rangeRate.type === "middleTier" ? "opacity-50" : ""
                  }`}
                >
                  <div className="md:hidden font-medium mb-1">5%</div>
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.downPay5)}
                    className="text-center"
                    disabled={calcInfo.rangeRate.type === "middleTier"}
                  />
                </div>
                <div className="text-center">
                  <div className="md:hidden font-medium mb-1">
                    {Math.round((calcInfo.rangeRate.rate || 10) * 100) / 100}%
                  </div>
                  <Input
                    readOnly
                    value={isNaNOrMoney(
                      calcInfo.rangeRate.type === "middleTier"
                        ? calcInfo.downPayRange
                        : calcInfo.downPay10
                    )}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <div className="md:hidden font-medium mb-1">15%</div>
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.downPay15)}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <div className="md:hidden font-medium mb-1">20%</div>
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.downPay20)}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <div className="md:hidden font-medium mb-1 flex items-center justify-center gap-1">
                    <Input
                      type="text"
                      className="w-12 text-center p-1"
                      value={calcInfo.customPercentage}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value) || 0;
                        setCalcInfo({ ...calcInfo, customPercentage: value });
                      }}
                    />
                    <span>% Down</span>
                  </div>
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.downPayCustom)}
                    className="text-center mt-1"
                  />
                </div>
              </div>

              {/* Mortgage Default Insurance Row */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border-b">
                <div className="font-medium md:text-right">
                  Mortgage Default Insurance:
                </div>
                <div
                  className={`text-center ${
                    calcInfo.rangeRate.type === "middleTier" ? "opacity-50" : ""
                  }`}
                >
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.insurance5)}
                    className="text-center"
                    disabled={calcInfo.rangeRate.type === "middleTier"}
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(
                      calcInfo.rangeRate.type === "middleTier"
                        ? calcInfo.insuranceRange
                        : calcInfo.insurance10
                    )}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.insurance15)}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.insurance20)}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.insuranceCustom)}
                    className="text-center"
                  />
                </div>
              </div>

              {/* Total Mortgage Required Row */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border-b">
                <div className="font-semibold md:text-right">
                  Total Mortgage Required:
                </div>
                <div
                  className={`text-center ${
                    calcInfo.rangeRate.type === "middleTier" ? "opacity-50" : ""
                  }`}
                >
                  <Input
                    readOnly
                    value={
                      calcInfo.principal5 <= 0
                        ? "--"
                        : isNaNOrMoney(calcInfo.principal5)
                    }
                    className="text-center font-semibold"
                    disabled={calcInfo.rangeRate.type === "middleTier"}
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(
                      calcInfo.rangeRate.type === "middleTier"
                        ? calcInfo.principalRange
                        : calcInfo.principal10
                    )}
                    className="text-center font-semibold"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.principal15)}
                    className="text-center font-semibold"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.principal20)}
                    className="text-center font-semibold"
                  />
                </div>
                <div className="text-center">
                  <Input
                    readOnly
                    value={isNaNOrMoney(calcInfo.principalCustom)}
                    className="text-center font-semibold"
                  />
                </div>
              </div>

              {/* Interest Rate Row */}
              <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border-b">
                <div className="font-semibold md:text-right">
                  Insert The Interest Rate (%):
                </div>
                <div
                  className={`text-center ${
                    calcInfo.rangeRate.type === "middleTier" ||
                    calcInfo.rangeRate.type === "maxTier"
                      ? "opacity-50"
                      : ""
                  }`}
                >
                  <Input
                    type="number"
                    value={calcInfo.rate5}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 0;
                      setCalcInfo({ ...calcInfo, rate5: value });
                    }}
                    className="text-center"
                    disabled={
                      calcInfo.rangeRate.type === "middleTier" ||
                      calcInfo.rangeRate.type === "maxTier"
                    }
                  />
                </div>
                <div className="text-center">
                  <Input
                    type="number"
                    value={
                      calcInfo.rangeRate.type === "middleTier"
                        ? calcInfo.rateRange
                        : calcInfo.rate10
                    }
                    onChange={(e) => {

                      const value = parseFloat(e.target.value) || 0;
                      if (calcInfo.rangeRate.type === "middleTier") {

                        setCalcInfo({ ...calcInfo, rateRange: value });
                      } else {

                        setCalcInfo({ ...calcInfo, rate10: value });
                      }
                    }}
                    className="text-center"
                    disabled={calcInfo.rangeRate.type === "maxTier"}
                  />
                </div>
                <div className="text-center">
                  <Input
                    type="number"
                    value={calcInfo.rate15}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 0;
                      setCalcInfo({ ...calcInfo, rate15: value });
                    }}
                    className="text-center"
                    disabled={calcInfo.rangeRate.type === "maxTier"}
                  />
                </div>
                <div className="text-center">
                  <Input
                    type="number"
                    value={calcInfo.rate20}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 0;
                      setCalcInfo({ ...calcInfo, rate20: value });
                    }}
                    className="text-center"
                  />
                </div>
                <div className="text-center">
                  <Input
                    type="number"
                    value={calcInfo.rateCustom}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value) || 0;
                      setCalcInfo({ ...calcInfo, rateCustom: value });
                    }}
                    className="text-center"
                  />
                </div>
              </div>
            </div>

            {/* Purchase Type Selection */}
            <div className="mt-8 space-y-4">
              <h2 className="text-lg font-bold">SELECT PURCHASE TYPE</h2>
              <div className="flex gap-6">
                <div className="flex items-center">                  
                  <Switch
                    id="fthb"
                    checked={scenarios.fthb}
                    onCheckedChange={() => handleScenarioToggle("fthb")}
                  />
                  <Label htmlFor="fthb" className="font-medium ml-2">
                    First Time Home Buyer
                  </Label>
                </div>
                <div className="flex items-center">
                <Switch
                    id="newBuild"
                    checked={scenarios.newBuild}
                    onCheckedChange={() => handleScenarioToggle("newBuild")}
                  />
                  <Label htmlFor="newBuild" className="font-medium ml-2">
                    New Build
                  </Label>
                 
                </div>
                <div className="flex items-center">
                  
                  <Switch
                    id="preOwned"
                    checked={scenarios.preOwned}
                    onCheckedChange={() => handleScenarioToggle("preOwned")}
                  />
                  <Label htmlFor="preOwned" className="font-medium ml-2">
                    Resale Property
                  </Label>
                </div>
              </div>
            </div>

            {/* Form Controls */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="downPayment">Down Payment</Label>
                <Select
                  value={calcInfo.chosenDownPay.percent || "select"}
                  onValueChange={(value) => {
                    // Handle down payment selection
                    setCalcInfo({
                      ...calcInfo,
                      chosenDownPay: {
                        ...calcInfo.chosenDownPay,
                        percent: value,
                      },
                    });
                    // Clear the reselection flag when user makes a selection
                    setDownPaymentNeedsReselection(false);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    {calcInfo.rangeRate.type !== "middleTier" &&
                      calcInfo.rangeRate.type !== "maxTier" && (
                        <SelectItem value="5%">5% Down</SelectItem>
                      )}
                    {calcInfo.rangeRate.type === "middleTier" && (
                      <SelectItem
                        value={`${
                          Math.round((calcInfo.rangeRate.rate || 0) * 100) / 100
                        }%`}
                      >
                        {Math.round((calcInfo.rangeRate.rate || 0) * 100) / 100}
                        % Down (Minimum)
                      </SelectItem>
                    )}
                    <SelectItem value="10%">10% Down</SelectItem>
                    <SelectItem value="15%">15% Down</SelectItem>
                    <SelectItem value="20%">20% Down</SelectItem>
                    {/* Show custom percentage if it exists and is different from standard options */}
                    {calcInfo.customPercentage > 0 && 
                     calcInfo.customPercentage !== 5 && 
                     calcInfo.customPercentage !== 10 && 
                     calcInfo.customPercentage !== 15 && 
                     calcInfo.customPercentage !== 20 && (
                      <SelectItem value={`${calcInfo.customPercentage}%`}>
                        {calcInfo.customPercentage}% Down (Custom)
                      </SelectItem>
                    )}
                    {/* Only show saved custom percentage if asking price hasn't changed and it's different from current calculations */}
                    {!downPaymentNeedsReselection && 
                     calcInfo.chosenDownPay.percent && 
                     calcInfo.chosenDownPay.percent !== "select" &&
                     !calcInfo.chosenDownPay.percent.match(/^(5|10|15|20)%$/) &&
                     calcInfo.chosenDownPay.percent !== `${calcInfo.customPercentage}%` &&
                     calcInfo.chosenDownPay.percent !== `${Math.round((calcInfo.rangeRate.rate || 0) * 100) / 100}%` && (
                      <SelectItem value={calcInfo.chosenDownPay.percent}>
                        {calcInfo.chosenDownPay.percent} Down (Saved)
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                {downPaymentNeedsReselection && (
                  <p className="text-sm text-orange-600 mt-1">
                    ⚠️ Please reselect your down payment percentage after changing the asking price.
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="years">Amortization Period</Label>
                <Input
                  id="years"
                  type="text"
                  value={calcInfo.years}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    const maxYears = scenarios.preOwned ? 25 : 30;
                    const constrainedYears = Math.min(value, maxYears);
                    setCalcInfo({ ...calcInfo, years: constrainedYears });
                  }}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum {scenarios.preOwned ? "25" : "30"} years for{" "}
                  {scenarios.fthb
                    ? "First-Time Home Buyers"
                    : scenarios.newBuild
                    ? "New Build properties"
                    : "Resale properties"}
                </p>
              </div>
              <div>
                <Label htmlFor="frequency">Payment Frequency</Label>
                <Select
                  value={calcInfo.frequency}
                  onValueChange={(value) => {
                    setCalcInfo({ ...calcInfo, frequency: value });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="biweekly">Bi-Weekly</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="accbiweekly">
                      Accelerated Bi-Weekly
                    </SelectItem>
                    <SelectItem value="accweekly">
                      Accelerated Weekly
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Total Mortgage Payment */}
            <div className="mt-8 p-6 bg-black text-white rounded-lg">
              <h3 className="text-xl font-bold mb-4 text-center">
                Total Mortgage Payment:
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div
                  className={`text-center ${
                    calcInfo.rangeRate.type === "middleTier" ? "opacity-50" : ""
                  }`}
                >
                  <p className="font-medium mb-2">5% DOWN</p>
                  <Input
                    readOnly
                    value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay5))}
                    className="text-center bg-white text-black"
                    disabled={calcInfo.rangeRate.type === "middleTier"}
                  />
                </div>
                <div className="text-center">
                  <p className="font-medium mb-2">
                    {Math.round((calcInfo.rangeRate.rate || 10) * 100) / 100}%
                    DOWN
                  </p>
                  <Input
                    readOnly
                    value={isNaNOrMoney(
                      Math.trunc(
                        calcInfo.rangeRate.type === "middleTier"
                          ? calcInfo.periodicPayRange
                          : calcInfo.periodicPay10
                      )
                    )}
                    className="text-center bg-white text-black"
                  />
                </div>
                <div className="text-center">
                  <p className="font-medium mb-2">15% DOWN</p>
                  <Input
                    readOnly
                    value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay15))}
                    className="text-center bg-white text-black"
                  />
                </div>
                <div className="text-center">
                  <p className="font-medium mb-2">20% DOWN</p>
                  <Input
                    readOnly
                    value={isNaNOrMoney(Math.trunc(calcInfo.periodicPay20))}
                    className="text-center bg-white text-black"
                  />
                </div>
                <div className="text-center">
                  <p className="font-medium mb-2">
                    {calcInfo.customPercentage
                      ? `${calcInfo.customPercentage}%`
                      : "--"}{" "}
                    DOWN
                  </p>
                  <Input
                    readOnly
                    value={isNaNOrMoney(Math.trunc(calcInfo.periodicPayCustom))}
                    className="text-center bg-white text-black"
                  />
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Estimated Cash Needed Section */}
        <AccordionItem
          value="cash-needed"
          className="border rounded-lg bg-white"
        >
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownToggleCashNeeded}
          >
            <h2 className="text-lg font-semibold">Estimated Cash Needed</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                When you purchase a house, there are a number of costs you'll
                need to put cash aside for in addition to your down payment.
                These costs depend on a number of factors including things like
                what kind of home you are buying (i.e. house vs. condo) and
                where the home is located. Our tool will help you calculate
                these costs, so you know how much you'll need to save.
              </p>
              <p className="font-medium">
                Please enter all applicable expenses; sum will generate upon
                input.
              </p>

              <div className="mb-4">
                <Label htmlFor="houseType">Type of Home:</Label>
                <Select
                  value={calcInfo.houseType}
                  onValueChange={(value) => {
                    setCalcInfo({ ...calcInfo, houseType: value });
                  }}
                >
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="House">House</SelectItem>
                    <SelectItem value="Condominium">Condominium</SelectItem>
                    <SelectItem value="Townhome">Townhome</SelectItem>
                    <SelectItem value="Row Housing">Row Housing</SelectItem>
                    <SelectItem value="Duplex">Duplex</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="w-full flex gap-4">
                  <div className="space-y-4 w-full">
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Down Payment:</Label>
                    <Input
                      readOnly
                      value={isNaNOrMoney(
                        calcInfo.chosenDownPay.percent === "5%"
                          ? calcInfo.downPay5
                          : calcInfo.chosenDownPay.percent === "10%"
                          ? calcInfo.downPay10
                          : calcInfo.chosenDownPay.percent === "15%"
                          ? calcInfo.downPay15
                          : calcInfo.chosenDownPay.percent === "20%"
                          ? calcInfo.downPay20
                          : calcInfo.chosenDownPay.percent.includes("%") &&
                            calcInfo.rangeRate.type === "middleTier"
                          ? calcInfo.downPayRange
                          : calcInfo.downPayCustom || 0
                      )}
                      className="w-32 text-right bg-gray-50"
                    />
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Lawyer Fees:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.lawyerFee.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'lawyerFee')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Home Inspection:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.homeInspection.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'homeInspection')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Appraisal:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.appraisal.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'appraisal')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Title Insurance:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.titleInsurance.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'titleInsurance')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Estoppel certificate fee:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.estoppelFee.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'estoppelFee')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                </div>                
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <Label className="text-lg font-semibold">Total Cash Needed:</Label>
                  <div className="text-xl">
                    {isNaNOrMoney(
                      (calcInfo.chosenDownPay.percent === "5%"
                        ? calcInfo.downPay5
                        : calcInfo.chosenDownPay.percent === "10%"
                        ? calcInfo.downPay10
                        : calcInfo.chosenDownPay.percent === "15%"
                        ? calcInfo.downPay15
                        : calcInfo.chosenDownPay.percent === "20%"
                        ? calcInfo.downPay20
                        : calcInfo.chosenDownPay.percent.includes("%") &&
                          calcInfo.rangeRate.type === "middleTier"
                        ? calcInfo.downPayRange
                        : calcInfo.downPayCustom || 0) +
                        calcInfo.lawyerFee.raw +
                        calcInfo.homeInspection.raw +
                        calcInfo.appraisal.raw +
                        calcInfo.titleInsurance.raw +
                        calcInfo.estoppelFee.raw
                    )}
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Estimated Monthly Costs Section */}
        <AccordionItem
          value="monthly-costs"
          className="border rounded-lg bg-white"
        >
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownToggleMonthlyExpenses}
          >
            <h2 className="text-lg font-semibold">Estimated Monthly Costs</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <p className="font-medium">
                Please enter all applicable monthly expenses; sum will generate
                upon input.
              </p>

              <div className="flex gap-4">
                <div className="space-y-4 w-full">
                  <div className="flex justify-between items-center">
                    <Label>Mortgage Payment:</Label>
                    <Input
                      readOnly
                      value={isNaNOrMoney(
                        Math.trunc(
                          calcInfo.chosenDownPay.percent === "5%"
                            ? calcInfo.periodicPay5
                            : calcInfo.chosenDownPay.percent === "10%"
                            ? calcInfo.periodicPay10
                            : calcInfo.chosenDownPay.percent === "15%"
                            ? calcInfo.periodicPay15
                            : calcInfo.chosenDownPay.percent === "20%"
                            ? calcInfo.periodicPay20
                            : calcInfo.chosenDownPay.percent.includes("%") &&
                              calcInfo.rangeRate.type === "middleTier"
                            ? calcInfo.periodicPayRange
                            : calcInfo.periodicPayCustom || 0
                        )
                      )}
                      className="w-32 text-right bg-gray-50"
                    />
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Monthly Debt Payments:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.monthlyDebtPayments.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'monthlyDebtPayments')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Utilities:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.utilities.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'utilities')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Condo Fees:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.condoFees.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'condoFees')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>

                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Phone:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.phone.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'phone')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Cable:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.cable.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'cable')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-4">
                    <Label>Internet:</Label>
                    <div className="text-right">
                      <CurrencyInputField
                        value={calcInfo.internet.raw || undefined}
                        onValueChange={(value) => handleCurrencyChange(value, 'internet')}
                        className="w-32 text-right"
                        placeholder="$0.00"
                      />
                    </div>
                  </div>
                </div>
                
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <Label className="text-lg font-semibold">Total Monthly Expenses:</Label>
                  <div className="text-xl">
                    {isNaNOrMoney(
                      calcInfo.chosenPeriodicPay +
                        calcInfo.monthlyDebtPayments.raw +
                        calcInfo.utilities.raw +
                        calcInfo.condoFees.raw +
                        calcInfo.phone.raw +
                        calcInfo.cable.raw +
                        calcInfo.internet.raw
                    )}
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Estimated Yearly Costs Section */}
        <AccordionItem
          value="yearly-costs"
          className="border rounded-lg bg-white"
        >
          <AccordionTrigger
            className="px-6 py-4 hover:no-underline"
            onClick={dropdownToggleYearlyExpenses}
          >
            <h2 className="text-lg font-semibold">Estimated Yearly Costs</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <p className="font-medium">
                Please enter all applicable yearly expenses; sum will generate
                upon input.
              </p>

              <div className="space-y-4">
                <div className="flex justify-between items-center border-t pt-4">
                  <Label>Property Tax:</Label>
                  <div className="text-right">
                    <CurrencyInputField
                      value={calcInfo.propertyTax.raw || undefined}
                      onValueChange={(value) => handleCurrencyChange(value, 'propertyTax')}
                      className="w-32 text-right"
                      placeholder="$0.00"
                    />
                  </div>
                </div>
                <div className="flex justify-between items-center border-t pt-4">
                  <Label>Property Insurance:</Label>
                  <div className="text-right">
                    <CurrencyInputField
                      value={calcInfo.propertyInsurance.raw || undefined}
                      onValueChange={(value) => handleCurrencyChange(value, 'propertyInsurance')}
                      className="w-32 text-right"
                      placeholder="$0.00"
                    />
                  </div>
                </div>
                <div className="flex justify-between items-center border-t pt-4">
                  <Label>HOA Fees:</Label>
                  <div className="text-right">
                    <CurrencyInputField
                      value={calcInfo.hoaFees.raw || undefined}
                      onValueChange={(value) => handleCurrencyChange(value, 'hoaFees')}
                      className="w-32 text-right"
                      placeholder="$0.00"
                    />
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <Label className="text-lg font-semibold">Total Yearly Expenses:</Label>
                  <div className="text-xl">
                    {isNaNOrMoney(
                      calcInfo.propertyTax.raw +
                        calcInfo.propertyInsurance.raw +
                        calcInfo.hoaFees.raw
                    )}
                  </div>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* PDF Options Section */}
        <AccordionItem
          value="pdf-options"
          className="border rounded-lg bg-white"
        >
          <AccordionTrigger className="px-6 py-4 hover:no-underline">
            <h2 className="text-lg font-semibold">PDF Options</h2>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Select which sections you would like to include in your listing
                sheet PDF.
              </p>

              <div className="space-y-4">
                <div className="flex items-center">
                <Switch
                    id="show-yearly-monthly"
                    checked={calcInfo.pdf.monthlyExpenses}
                    onCheckedChange={(checked) =>
                      setCalcInfo({
                        ...calcInfo,
                        pdf: {
                          ...calcInfo.pdf,
                          monthlyExpenses: checked,
                        },
                      })
                    }
                  />
                  <Label htmlFor="show-yearly-monthly" className="font-medium ml-2">
                    Show Yearly and Monthly Expenses
                  </Label>
                  
                </div>
                <div className="flex items-center">
                <Switch
                    id="show-cash-needed"
                    checked={calcInfo.pdf.cashNeeded}
                    onCheckedChange={(checked) =>
                      setCalcInfo({
                        ...calcInfo,
                        pdf: {
                          ...calcInfo.pdf,
                          cashNeeded: checked,
                        },
                      })
                    }
                  />
                  <Label htmlFor="show-cash-needed" className="font-medium ml-2">
                    Show Estimated Cash Needed
                  </Label>
                  
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Generate PDF Button */}
      <div className="flex justify-center mt-8">
        <Button
          onClick={generatePdf}
          disabled={calcInfo.loading || externalSaving || userDataLoading}
          size="lg"
        >
          {(calcInfo.loading || externalSaving || userDataLoading) && (
            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
          )}
          <FileText className="mr-2 h-5 w-5" />
          Generate Listing Sheet PDF
        </Button>
      </div>

      {/* Realtor Modal */}
      <RealtorModal
        open={isRealtorModalOpen}
        onClose={() => setIsRealtorModalOpen(false)}
        onSave={handleCreateRealtor}
        isLoading={isCreatingRealtor}
        error={createRealtorError}
        mode="create"
      />
    </div>
  );
}
