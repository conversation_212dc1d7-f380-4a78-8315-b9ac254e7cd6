"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/shared/ui/dialog";
import { RealtorForm } from "./realtor-form";
import type { RealtorInfo } from "../types";

interface RealtorModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: any) => Promise<void>;
  initialData?: Partial<RealtorInfo>;
  isLoading?: boolean;
  error?: string | null;
  mode?: "create" | "edit";
  title?: string;
  description?: string;
}

export function RealtorModal({
  open,
  onClose,
  onSave,
  initialData,
  isLoading = false,
  error,
  mode = "create",
  title,
  description,
}: RealtorModalProps) {
  const defaultTitle = mode === "create" ? "Add New Realtor" : "Edit Realtor";
  const defaultDescription = mode === "create" 
    ? "Add a new realtor to your account. All fields marked with * are required."
    : "Update the realtor information. All fields marked with * are required.";

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title || defaultTitle}</DialogTitle>
          <DialogDescription>
            {description || defaultDescription}
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-4">
          <RealtorForm
            initialData={initialData}
            onSubmit={onSave}
            onCancel={onClose}
            isLoading={isLoading}
            error={error}
            mode={mode}
            submitLabel={mode === "create" ? "Add Realtor" : "Update Realtor"}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
