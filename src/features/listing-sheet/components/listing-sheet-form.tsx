"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm, Resolver } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2, Save, FileText } from "lucide-react";
import { But<PERSON> } from "@/shared/ui/button";
import MortgageCalculatorLegacy from "./mortgage-calculator-legacy";
import { useAuth } from "@/shared/hooks/use-auth";
import { useListingSheets } from "../hooks/use-listing-sheets";
import { useMortgageCalculator } from "../hooks/use-mortgage-calculator";
import { usePDFGenerator } from "../hooks/use-pdf-generator";
import { listingSheetSchema } from "../lib/validation-schemas";
import { showListingSheetCreated, showListingSheetUpdated, showListingSheetError, showListingSheetGenerated } from "@/shared/lib/toast";
import type {
  ListingSheet,
  MortgageCalculationD<PERSON>,
  ListingSheetCreatePayload,
  ListingSheetUpdatePayload,
  UserInfo,
  FileUpload,
} from "../types";

// Helper functions for file type conversion
const fileToFileUpload = (file: File): FileUpload => ({
  url: URL.createObjectURL(file),
  ext: file.name.split(".").pop() || null,
});

// Since we can't convert FileUpload back to File, we'll handle this differently
// The form components will work with File objects for new uploads
// and we'll convert them to FileUpload when updating the main form
const getFileFromFileUpload = (
  _fileUpload: FileUpload | null | undefined
): File | undefined => {
  // We can't convert FileUpload back to File since we don't have the original File object
  // This is used for initial data where we don't need the File object for display
  return undefined;
};

interface ListingSheetFormProps {
  initialData?: ListingSheet;
  mode: "create" | "edit";
  slug?: string;
  user?: UserInfo;
}

export function ListingSheetForm({
  initialData,
  mode,
  slug,
  user: propUser,
}: ListingSheetFormProps) {
  const router = useRouter();
  const [openSections, setOpenSections] = useState<string[]>([
    "property",
    "realtor",
    "mortgage",
  ]);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [currentListingSheet, setCurrentListingSheet] = useState<ListingSheet | null>(initialData || null);
  const [isLoading, setIsLoading] = useState(false);

  // Get user data from the global auth hook
  const { user: authUser } = useAuth();
  
  // Use propUser if available, otherwise fall back to authUser
  const user = propUser || authUser;

  const { createListingSheet, updateListingSheet, getListingSheetByDocumentId } = useListingSheets();
  const { generatePDF } = usePDFGenerator();

  // Extract save logic into a reusable function
  const handleSave = async (calcInfo: unknown) => {
    try {
      console.log("handleSave called with:", calcInfo);
      console.log("Current mode:", mode);
      console.log("Current listing sheet:", currentListingSheet);
      
      if (mode === "create") {
        const payload: ListingSheetCreatePayload = {
          title: `${
            (calcInfo as any)?.address || "New Property"
          } - ${new Date().toLocaleDateString()}`,
          sheet: calcInfo as MortgageCalculationData,
          user: user?.id || "",
          slug: slug || `listing-${Date.now()}`,
        };

        console.log("Create payload:", payload);
        const result = await createListingSheet(payload);

        if (result.data) {
          // Stay on the current page, show success toast
          console.log("Listing sheet created successfully");
          showListingSheetCreated();
        } else {
          console.error("Failed to create listing sheet:", result.error);
          showListingSheetError(result.error || "Failed to create listing sheet");
        }
      } else if (currentListingSheet) {
        console.log("Updating listing sheet with documentId:", currentListingSheet.documentId);
        console.log("Current listing sheet:", currentListingSheet);
        
        const payload: ListingSheetUpdatePayload = {
          title: `${
            (calcInfo as any)?.address || "Property"
          } - ${new Date().toLocaleDateString()}`,
          sheet: calcInfo as MortgageCalculationData,
          user: user?.id || "",
          slug: slug || currentListingSheet.slug,
        };

        console.log("Update payload:", payload);
        console.log("Using documentId for update:", currentListingSheet.documentId, "Type:", typeof currentListingSheet.documentId);
        const result = await updateListingSheet(currentListingSheet.documentId, payload);

        if (result.data) {
          // Stay on the current page, show success toast
          console.log("Listing sheet updated successfully");
          showListingSheetUpdated();
          // Update the local state to reflect the changes
          setCurrentListingSheet(result.data);
        } else {
          console.error("Failed to update listing sheet:", result.error);
          showListingSheetError(result.error || "Failed to update listing sheet");
        }
      } else {
        console.error("Cannot save: no current listing sheet and not in create mode");
        showListingSheetError("Cannot save: invalid state");
      }
    } catch (error: unknown) {
      console.error("Error saving listing sheet:", error);
      showListingSheetError("An unexpected error occurred while saving");
    }
  };

  const form = useForm<MortgageCalculationData>({
    // Note: Using resolver with type assertion to handle schema compatibility
    // The listingSheetSchema is a subset of MortgageCalculationData
    resolver: zodResolver(
      listingSheetSchema
    ) as unknown as Resolver<MortgageCalculationData>,
    defaultValues: currentListingSheet?.sheet || {
      // Loading and saving states
      loading: false,
      saving: false,
      uploading: { status: false, type: null },

      // Property Information
      amount: 0,
      askingPrice: 0,
      mlsCode: "",
      address: "",
      propertyPhoto: undefined,

      // Calculation Parameters
      years: 25,
      customYears: 0,
      frequency: "monthly",
      customPercentage: 0,
      numberOfPayments: 0,

      // Down Payment Calculations (will be calculated)
      downPay5: 0,
      downPay10: 0,
      downPay15: 0,
      downPay20: 0,
      downPayCustom: 0,
      downPayRange: 0,
      downPayFTHB: 0,
      chosenDownPay: { rate: "", percent: "select", amount: 0 },

      // Principal Amounts
      principal5: 0,
      principal10: 0,
      principal15: 0,
      principal20: 0,
      principalCustom: 0,
      principalRange: 0,
      principalFTHB: 0,
      chosenPrincipal: 0,

      // Interest Rates
      rate: 0,
      rate5: 0,
      rate10: 0,
      rate15: 0,
      rate20: 0,
      rateCustom: 0,
      rateRange: 0,
      rateFTHB: 0,
      effectiveRates: {
        effectiveRate5: 0,
        effectiveRate10: 0,
        effectiveRate15: 0,
        effectiveRate20: 0,
        effectiveRateCustom: 0,
        effectiveRateRange: 0,
        effectiveRateFTHB: 0,
      },

      // Insurance Calculations (will be calculated)
      insurance5: 0,
      insurance10: 0,
      insurance15: 0,
      insurance20: 0,
      insuranceCustom: 0,
      insuranceRange: 0,
      insuranceFTHB: 0,

      // Payment Calculations (will be calculated)
      monthlyPay5: 0,
      monthlyPay10: 0,
      monthlyPay15: 0,
      monthlyPay20: 0,
      monthlyPayCustom: 0,
      monthlyPayRange: 0,
      monthlyPayFTHB: 0,

      // Periodic Payments (will be calculated)
      periodicPay5: 0,
      periodicPay10: 0,
      periodicPay15: 0,
      periodicPay20: 0,
      periodicPayCustom: 0,
      periodicPayRange: 0,
      periodicPayFTHB: 0,
      chosenPeriodicPay: 0,

      // Monthly Expenses
      propertyTax: 0,
      propertyInsurance: 0,
      utilities: 0,
      condoFees: 0,
      hoaFees: 0,
      monthlyDebtPayments: 0,
      phone: 0,
      cable: 0,
      internet: 0,
      totalMonthlyPayments: 0,
      totalYearlyPayments: 0,

      // Closing Costs
      lawyerFee: 0,
      homeInspection: 0,
      appraisal: 0,
      titleInsurance: 0,
      estoppelFee: 0,
      totalCashNeeded: 0,

      // UI Configuration
      showMortgagePayment: true,
      showBodyCashNeeded: true,
      showBodyMonthlyExpenses: true,
      showBodyYearlyExpenses: true,
      showAdditionalInfo: true,
      showRealtorInfo: true,
      showImages: true,
      houseType: "select",

      // Additional Fields
      chosenDownPayExpense: {},
      amortizationBalance: 0,
      rangeRate: { type: "normalTier", rate: null },

      // PDF Configuration
      pdf: {
        full: false,
        short: true,
        mlsCode: null,
        address: null,
        monthlyExpenses: true,
        cashNeeded: true,
        propertyPhoto: { url: null, ext: null },
        realtorPhoto: { url: null, ext: null },
        user: user ? {
          id: user.id || "",
          firstname: user.firstname || null,
          lastname: user.lastname || null,
          position: (user as any)?.position || null,
          email: user.email || null,
          workEmail: (user as any)?.workEmail || null,
          phone: (user as any)?.phone || null,
          workPhone: (user as any)?.workPhone || null,
          cellPhone: (user as any)?.cellPhone || null,
          website: (user as any)?.website || null,
          brokerage: (user as any)?.brokerage || (user as any)?.company || null,
          photo: { url: (user as any)?.avatar || (user as any)?.photo?.url || null },
          team: { id: (user as any)?.team?.id || null, logo: { url: (user as any)?.team?.logo?.url || null } },
        } : {
          id: "",
          firstname: null,
          lastname: null,
          position: null,
          email: null,
          workEmail: null,
          phone: null,
          workPhone: null,
          cellPhone: null,
          website: null,
          brokerage: null,
          photo: { url: null },
          team: { id: null, logo: { url: null } },
        },
      },

      // Realtor Information
      realtor: {
        firstname: "",
        middlename: null,
        lastname: "",
        position: "",
        company: "",
        email: "",
        phone: "",
        website: "",
        photo: null,
      },
    },
  });

  // Fetch listing sheet data when in edit mode and slug (documentId) is provided
  useEffect(() => {
    const fetchListingSheet = async () => {
      if (mode === "edit" && slug && !currentListingSheet) {
        try {
          setIsLoading(true);
          console.log("Fetching listing sheet with documentId:", slug);
          const result = await getListingSheetByDocumentId(slug);
          
          if (result.data) {
            console.log("Fetched listing sheet data:", result.data);
            setCurrentListingSheet(result.data);
          } else {
            console.error("Failed to fetch listing sheet:", result.error);
          }
        } catch (error) {
          console.error("Error fetching listing sheet:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchListingSheet();
  }, [mode, slug, currentListingSheet, getListingSheetByDocumentId]);

  // Reset form when listing sheet data is loaded
  useEffect(() => {
    if (currentListingSheet?.sheet) {
      console.log("Resetting form with listing sheet data:", currentListingSheet.sheet);
      form.reset(currentListingSheet.sheet);
    }
  }, [currentListingSheet, form]);

  const { calcInfo, validation, isCalculating } = useMortgageCalculator({
    initialData: currentListingSheet?.sheet,
    autoCalculate: true,
  });

  // Update form when calculation data changes
  useEffect(() => {
    if (calcInfo) {
      // Update form with calculated values without triggering validation
      // Only update values that have actually changed to prevent infinite loops
      Object.entries(calcInfo).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          const currentValue = form.getValues(
            key as keyof MortgageCalculationData
          );
          // Only update if the value has actually changed
          if (currentValue !== value) {
            form.setValue(
              key as keyof MortgageCalculationData,
              value as MortgageCalculationData[keyof MortgageCalculationData],
              {
                shouldValidate: false,
                shouldDirty: false,
              }
            );
          }
        }
      });
    }
  }, [calcInfo, form]); // Removed 'form' from dependencies as it's stable



  const handleGeneratePDF = async () => {
    const formData = form.getValues();

    // Validate required fields for PDF generation
    if (
      !formData.askingPrice ||
      !formData.years ||
      formData.chosenDownPay.percent === "select"
    ) {
      form.trigger(); // Trigger validation to show errors
      return;
    }

    setIsGeneratingPDF(true);
    try {
      const result = await generatePDF(formData);
      if (result.success && result.pdfUrl) {
        // Open PDF in new window
        window.open(result.pdfUrl, "_blank");
        showListingSheetGenerated();
      } else {
        console.error("PDF generation failed:", result.error);
        showListingSheetError(result.error || "PDF generation failed");
      }
    } catch (error) {
      console.error("Error generating PDF:", error);
      showListingSheetError("Failed to generate PDF");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const isFormValid = form.formState.isValid && validation.length === 0;
  const canGeneratePDF = isFormValid && !isCalculating && !isGeneratingPDF;

  return (
    <div className="w-full space-y-6">
      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading listing sheet...</span>
          </div>
        </div>
      )}



      {/* Use the comprehensive MortgageCalculatorLegacy component */}
      {!isLoading && (
        <MortgageCalculatorLegacy          
          data={currentListingSheet?.sheet}
          saving={form.formState.isSubmitting || isGeneratingPDF}
          save={async (status: string, generator: any, calcInfo: unknown) => {
            // Use the extracted save function
            await handleSave(calcInfo);
          }}
        />
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-end pt-6 border-t">
        <div className="flex items-center gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/listing-sheet")}
          >
            Cancel
          </Button>

          <Button
            type="button"
            className="cursor-pointer"
            disabled={form.formState.isSubmitting || isLoading}
            onClick={async () => {
              // Get current form data and trigger manual save
              const formData = form.getValues();
              console.log("Manual save triggered with data:", formData);
              console.log("Form state:", form.formState);
              console.log("Current mode:", mode);
              console.log("Current listing sheet:", currentListingSheet);
              await handleSave(formData);
            }}
          >
            {form.formState.isSubmitting && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            <Save className="mr-2 h-4 w-4" />
            {mode === "create" ? "Save Listing Sheet" : "Update Listing Sheet"}
          </Button>
        </div>
      </div>
    </div>
  );
}
