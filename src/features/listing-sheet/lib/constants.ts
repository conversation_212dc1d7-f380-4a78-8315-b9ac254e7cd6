import type { PaymentFrequency, DownPaymentScenario } from "../types";

// Property Price Thresholds
export const PROPERTY_PRICE_THRESHOLDS = {
  MIDDLE_TIER_MIN: 500000,
  MIDDLE_TIER_MAX: 1500000,
  MAX_TIER_MIN: 1500000,
} as const;

// Down Payment Rates
export const DOWN_PAYMENT_RATES = {
  MIN_STANDARD: 0.05, // 5%
  MIN_MIDDLE_TIER: 0.1, // 10% for amount over $500K
  MIN_MAX_TIER: 0.2, // 20% for amount over $1.5M
} as const;

// Mortgage Insurance Rates
export const MORTGAGE_INSURANCE_RATES = {
  RATE_5_TO_9_99: 0.04, // 4.0% for 5% - 9.99% down
  RATE_10_TO_14_99: 0.031, // 3.1% for 10% - 14.99% down
  RATE_15_TO_19_99: 0.028, // 2.8% for 15% - 19.99% down
  RATE_20_PLUS: 0, // 0% for 20%+ down
} as const;

// Amortization Limits
export const AMORTIZATION_LIMITS = {
  NEW_BUILD_MAX: 30,
  PRE_OWNED_MAX: 25,
  DEFAULT_MAX: 30,
} as const;

// Payment Frequency Multipliers
export const PAYMENT_FREQUENCY_MULTIPLIERS: Record<PaymentFrequency, number> = {
  monthly: 12,
  biweekly: 26,
  weekly: 52,
  accbiweekly: 26,
  accweekly: 52,
};

// Payment Frequency Labels
export const PAYMENT_FREQUENCY_LABELS: Record<PaymentFrequency, string> = {
  monthly: "Monthly",
  biweekly: "Bi-Weekly",
  weekly: "Weekly",
  accbiweekly: "Accelerated Bi-Weekly",
  accweekly: "Accelerated Weekly",
};

// Down Payment Scenario Labels
export const DOWN_PAYMENT_SCENARIO_LABELS: Record<DownPaymentScenario, string> =
  {
    "5%": "5%",
    "10%": "10%",
    "15%": "15%",
    "20%": "20%",
    custom: "Custom",
    range: "Minimum Required",
    fthb: "First-Time Home Buyer",
  };

// File Upload Constants
export const FILE_UPLOAD_CONSTANTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ACCEPTED_IMAGE_TYPES: ["image/jpeg", "image/jpg", "image/png"],
  ACCEPTED_IMAGE_EXTENSIONS: [".jpg", ".jpeg", ".png"],
} as const;

// PDF Generation Constants
export const PDF_CONSTANTS = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:1337",
  DEFAULT_PROPERTY_IMAGE: "/images/property-image-default.jpg",
  DEFAULT_REALTOR_IMAGE: "/images/realtor-image-default.jpg",
  TEMPLATE_PATHS: {
    PAYMENT_ONLY:
      "/listing-models/{tier}/Listing_Sheets_Template_Only_Payment.pdf",
    PAYMENT_CASH:
      "/listing-models/{tier}/Listing_Sheets_Template_Payment_Cash.pdf",
    PAYMENT_EXPENSES:
      "/listing-models/{tier}/Listing_Sheets_Template_Payment_Expenses.pdf",
    PAYMENT_CASH_EXPENSES:
      "/listing-models/{tier}/Listing_Sheets_Template_Payment_Cash_Expenses.pdf",
  },
} as const;

// Validation Constants
export const VALIDATION_CONSTANTS = {
  MIN_ASKING_PRICE: 1000,
  MAX_ASKING_PRICE: 50000000,
  MIN_CUSTOM_PERCENTAGE: 5,
  MAX_CUSTOM_PERCENTAGE: 100,
  MIN_INTEREST_RATE: 0.1,
  MAX_INTEREST_RATE: 20,
  MIN_AMORTIZATION_YEARS: 5,
  MAX_AMORTIZATION_YEARS: 30,
  MLS_CODE_PATTERN: /^[A-Z0-9\-\s]+$/i,
  PHONE_PATTERN: /^[\+]?[1-9][\d]{0,15}$/,
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const;

// Currency Formatting
export const CURRENCY_CONFIG = {
  locale: "en-CA",
  currency: "CAD",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
} as const;

// Data Retention
export const DATA_RETENTION = {
  LISTING_SHEET_RETENTION_DAYS: 90,
} as const;

// Default Values
export const DEFAULT_VALUES = {
  MORTGAGE_CALCULATION: {
    loading: false,
    saving: false,
    uploading: { status: false, type: null },
    amount: 0,
    askingPrice: 0,
    customPercentage: 0,
    years: 25,
    frequency: "monthly" as PaymentFrequency,
    numberOfPayments: 0,
    showMortgagePayment: true,
    showBodyCashNeeded: true,
    showBodyMonthlyExpenses: true,
    showBodyYearlyExpenses: true,
    showAdditionalInfo: true,
    showRealtorInfo: true,
    showImages: true,
    houseType: "select",
    // All numeric fields default to 0
    downPay5: 0,
    downPay10: 0,
    downPay15: 0,
    downPay20: 0,
    downPayCustom: 0,
    downPayRange: 0,
    downPayFTHB: 0,
    principal5: 0,
    principal10: 0,
    principal15: 0,
    principal20: 0,
    principalCustom: 0,
    principalRange: 0,
    principalFTHB: 0,
    chosenPrincipal: 0,
    rate: 0,
    rate5: 0,
    rate10: 0,
    rate15: 0,
    rate20: 0,
    rateCustom: 0,
    rateRange: 0,
    rateFTHB: 0,
    insurance5: 0,
    insurance10: 0,
    insurance15: 0,
    insurance20: 0,
    insuranceCustom: 0,
    insuranceRange: 0,
    insuranceFTHB: 0,
    monthlyPay5: 0,
    monthlyPay10: 0,
    monthlyPay15: 0,
    monthlyPay20: 0,
    monthlyPayCustom: 0,
    monthlyPayRange: 0,
    monthlyPayFTHB: 0,
    periodicPay5: 0,
    periodicPay10: 0,
    periodicPay15: 0,
    periodicPay20: 0,
    periodicPayCustom: 0,
    periodicPayRange: 0,
    periodicPayFTHB: 0,
    chosenPeriodicPay: 0,
    propertyTax: 0,
    propertyInsurance: 0,
    utilities: 0,
    condoFees: 0,
    hoaFees: 0,
    monthlyDebtPayments: 0,
    phone: 0,
    cable: 0,
    internet: 0,
    totalMonthlyPayments: 0,
    totalYearlyPayments: 0,
    lawyerFee: 0,
    homeInspection: 0,
    appraisal: 0,
    titleInsurance: 0,
    estoppelFee: 0,
    totalCashNeeded: 0,
    chosenDownPayExpense: {},
    amortizationBalance: 0,
  },
  REALTOR_INFO: {
    firstname: "",
    lastname: "",
    position: "",
    company: "",
    email: "",
    phone: "",
  },
  PDF_CONFIG: {
    full: false,
    short: true,
    monthlyExpenses: true,
    cashNeeded: true,
  },
} as const;
