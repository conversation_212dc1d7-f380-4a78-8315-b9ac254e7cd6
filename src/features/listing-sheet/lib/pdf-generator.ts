import { PDFDocument, rgb } from 'pdf-lib';
import { saveAs } from 'file-saver';
import fontkit from '@pdf-lib/fontkit';
import { fetchImageWithProxy, type ProxyResult } from '@/shared/lib/s3-proxy';
import { getBaseUrl, getPdfTemplateBasePath, getDefaultImagePaths, getAssetUrl } from '@/shared/lib/environment';

interface PdfData {
  askingPrice?: number;
  years?: number | string;
  customYears?: number;
  frequency?: string;
  rangeRate?: {
    type: 'normalTier' | 'middleTier' | 'maxTier';
    rate?: number;
  };
  periodicPay5?: number;
  periodicPay10?: number;
  periodicPay15?: number;
  periodicPay20?: number;
  periodicPayCustom?: number;
  periodicPayRange?: number;
  downPay5?: number;
  downPay10?: number;
  downPay15?: number;
  downPay20?: number;
  downPayCustom?: number;
  downPayRange?: number;
  customPercentage?: number;
  insurance5?: number;
  insurance10?: number;
  insurance15?: number;
  insurance20?: number;
  insuranceCustom?: number;
  insuranceRange?: number;
  rate5?: number;
  rate10?: number;
  rate15?: number;
  rate20?: number;
  rateCustom?: number;
  rateRange?: number;
  chosenDownPay?: {
    percent?: string;
    amount?: number;
    rate?: string;
  };
  chosenPeriodicPay?: number;
  propertyTax?: { raw: number; currency: number } | number;
  propertyInsurance?: { raw: number; currency: number } | number;
  monthlyDebtPayments?: { raw: number; currency: number } | number;
  utilities?: { raw: number; currency: number } | number;
  condoFees?: { raw: number; currency: number } | number;
  hoaFees?: { raw: number; currency: number } | number;
  phone?: { raw: number; currency: number } | number;
  cable?: { raw: number; currency: number } | number;
  internet?: { raw: number; currency: number } | number;
  totalMonthlyPayments?: number;
  totalYearlyPayments?: number;
  lawyerFee?: { raw: number; currency: number } | number;
  homeInspection?: { raw: number; currency: number } | number;
  appraisal?: { raw: number; currency: number } | number;
  titleInsurance?: { raw: number; currency: number } | number;
  estoppelFee?: { raw: number; currency: number } | number;
  totalCashNeeded?: number;
  pdf?: {
    full?: boolean;
    short?: boolean;
    mlsCode?: string;
    address?: string;
    monthlyExpenses?: boolean;
    cashNeeded?: boolean;
    propertyPhoto?: {
      url?: string;
      ext?: string;
    };
    realtorPhoto?: {
      url?: string;
      ext?: string;
    };
    user?: any;
  };
  realtor?: {
    firstname?: string;
    lastname?: string;
    position?: string;
    email?: string;
    phone?: string;
    website?: string;
    company?: string;
    photo?: {
      url?: string;
    };
  };
}

interface User {
  photo?: {
    url?: string;
  };
  firstname?: string;
  lastname?: string;
  position?: string;
  workEmail?: string;
  email?: string;
  workPhone?: string;
  phone?: string;
  cellPhone?: string;
  website?: string;
  brokerage?: string;
  team?: {
    id?: string;
    showFSRA?: boolean;
  };
}

interface Realtor {
  firstname?: string;
  lastname?: string;
  position?: string;
  email?: string;
  phone?: string;
  website?: string;
  company?: string;
  photo?: {
    url?: string;
  };
}

const fetchImage = async (url: string, options?: { fallbackUrl?: string }): Promise<ProxyResult> => {
  return await fetchImageWithProxy(url, options);
};

const getFileExtension = (url: string): string => {
  const extension = url.split('.').pop()?.toLowerCase() || '';
  return extension;
};

// Format phone number to add spaces for better readability
const formatPhoneNumber = (phone: string | null | undefined): string => {
  if (!phone) return '--';
  
  // Remove all non-digit characters
  const digits = phone.toString().replace(/\D/g, '');
  
  // Handle empty or invalid phone numbers
  if (digits.length === 0) return '--';
  
  // Format based on length
  if (digits.length === 10) {
    // Format as XXX XXX XXXX (North American format)
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    // Format as 1 XXX XXX XXXX (North American with country code)
    return `1 ${digits.slice(1, 4)} ${digits.slice(4, 7)} ${digits.slice(7)}`;
  } else if (digits.length === 7) {
    // Format as XXX XXXX (7-digit format)
    return `${digits.slice(0, 3)} ${digits.slice(3)}`;
  } else if (digits.length <= 15) {
    // For other reasonable lengths, add spaces every 3-4 digits
    const chunks = [];
    for (let i = 0; i < digits.length; i += 3) {
      chunks.push(digits.slice(i, i + 3));
    }
    return chunks.join(' ');
  } else {
    // For very long numbers, just return as is
    return phone.toString();
  }
};

const GenerateListingSheet = async (pdfData: PdfData, isSafari?: boolean) => {
  const { pdf, realtor } = pdfData;
  const { user } = pdf || {};

  console.log('pdfData', pdfData);
  
  // Get environment-aware URLs
  const baseUrl = getBaseUrl();
  const { realtorDefault, propertyDefault } = getDefaultImagePaths();

  let askingPrice: any = null;
  let mlsCode: any = null;
  let years: any = null;
  let rate: any = null;
  let customDown: any = null;
  let frequencyTitle: any = null;
  let middleTierOr10DownPayment: any = null;
  let maxTierOr15DownPayment: any = null;
  let periodicPay5: any = null;
  let periodicPay10: any = null;
  let periodicPay15: any = null;
  let periodicPay20: any = null;
  let periodicPayCustom: any = null;
  let downPay5: any = null;
  let downPay10: any = null;
  let downPay15: any = null;
  let downPay20: any = null;
  let downPayCustom: any = null;
  let customPercentage: any = null;
  let insurance5: any = null;
  let insurance10: any = null;
  let insurance15: any = null;
  let insurance20: any = null;
  let insuranceCustom: any = null;
  let rate5: any = null;
  let rate10: any = null;
  let rate15: any = null;
  let rate20: any = null;
  let rateCustom: any = null;
  let brokerPhoto: any = null;
  let realtorPhoto: any = null;
  let propertyPhoto: any = null;
  let brokerName: any = null;
  let brokerPosition: any = null;
  let email: any = null;
  let phone: any = null;
  let website: any = null;
  let licensedWith: any = null;
  let realtorName: any = null;
  let realtorPosition: any = null;
  let realtorEmail: any = null;
  let realtorPhone: any = null;
  let realtorWebsite: any = null;
  let realtorLicensedWith: any = null;
  let fsra: any = null;
  let mortgagePayment: any = null;
  let propertyTax: any = null;
  let propertyInsurance: any = null;
  let monthlyDebtPayment: any = null;
  let utilities: any = null;
  let condoFee: any = null;
  let hoaFee: any = null;
  let phoneExpense: any = null;
  let cable: any = null;
  let internet: any = null;
  let totalExpenses: any = null;
  let totalYearly: any = null;
  let downPayment: any = null;
  let downPaymentRate: any = null;
  let lawyerFees: any = null;
  let homeInspection: any = null;
  let appraisal: any = null;
  let titleInsurance: any = null;
  let estoppelCertificate: any = null;
  let totalCashNeeded: any = null;
  let address: any = null;

  let pdfDoc: PDFDocument;
  let form: any;
  let arialBoldFont: any;
  let arialBlackFont: any;

  const brokerPhotoUrl = user?.photo?.url || realtorDefault;
  const realtorPhotoUrl = realtor?.photo?.url || realtorDefault;
  const propertyPhotoUrl = (pdf?.propertyPhoto && pdf.propertyPhoto.url) || propertyDefault;

  let brokerPhotoFinal: any, realtorPhotoFinal: any, propertyPhotoFinal: any;

  const toCurrencyString = (num: any, field?: string): string => {
    if (num === '0' || num === 0 || num === null || num === undefined || isNaN(num)) {
      return 'N/A';
    }
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currencyDisplay: 'symbol',
      currency: 'CAD'
    }).format(num);
  };

  const getPdfForm = async (url: string) => {
    const { arrayBuffer: sourceBuffer } = await fetchImage(url);
    pdfDoc = await PDFDocument.load(sourceBuffer);
    form = pdfDoc.getForm();

    pdfDoc.registerFontkit(fontkit);
    // Load Arial Bold font
    const fontUrl = getAssetUrl('fonts/ArialBold.ttf');
    const { arrayBuffer: fontBytes } = await fetchImage(fontUrl);
    arialBoldFont = await pdfDoc.embedFont(fontBytes);
    const fontUrlBlack = getAssetUrl('fonts/ArialBlack.ttf');
    const { arrayBuffer: fontBytesBlack } = await fetchImage(fontUrlBlack);
    arialBlackFont = await pdfDoc.embedFont(fontBytesBlack);
    return form;
  };

  const handleFrequency = (f: string): string => {
    const frequencies: Record<string, string> = {
      monthly: 'Monthly',
      biweekly: 'Bi-Weekly',
      weekly: 'Weekly',
      accbiweekly: 'Accelerated Bi-Weekly',
      accweekly: 'Accelerated Weekly'
    };
    return frequencies[f] || 'Monthly';
  };

  const handleChosenRate = (): number => {
    const { chosenDownPay } = pdfData;
    const rates: Record<string, number> = {
      rate5: pdfData.rate5 || 0,
      rate10: pdfData.rate10 || 0,
      rate15: pdfData.rate15 || 0,
      rate20: pdfData.rate20 || 0,
      rateRange: pdfData.rateRange || 0,
      rateCustom: pdfData.rateCustom || 0
    };
    return rates[chosenDownPay?.rate || 'rate5'] || pdfData.rate5 || 0;
  };

  // Helper to detect image type by magic number
  const detectImageType = (arrayBuffer: ArrayBuffer): string => {
    const bytes = new Uint8Array(arrayBuffer);
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    if (
      bytes[0] === 0x89 &&
      bytes[1] === 0x50 &&
      bytes[2] === 0x4e &&
      bytes[3] === 0x47 &&
      bytes[4] === 0x0d &&
      bytes[5] === 0x0a &&
      bytes[6] === 0x1a &&
      bytes[7] === 0x0a
    ) {
      return 'png';
    }
    // JPEG: FF D8 FF
    if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
      return 'jpg';
    }
    return 'unknown';
  };

  const fetchAndEmbedImages = async () => {
    try {
      const [
        { arrayBuffer: brokerPhotoBytes },
        { arrayBuffer: realtorPhotoBytes },
        { arrayBuffer: propertyPhotoBytes }
      ] = await Promise.all([
        fetchImage(brokerPhotoUrl),
        fetchImage(realtorPhotoUrl, { fallbackUrl: realtorDefault }),
        fetchImage(propertyPhotoUrl, { fallbackUrl: propertyDefault })
      ]);

      const brokerExt = getFileExtension(brokerPhotoUrl);
      const realtorExt = getFileExtension(realtorPhotoUrl);
      const propertyExt = getFileExtension(propertyPhotoUrl);

      // Detect actual image type by magic number
      const brokerType = detectImageType(brokerPhotoBytes);
      const realtorType = detectImageType(realtorPhotoBytes);
      const propertyType = detectImageType(propertyPhotoBytes);

      brokerPhotoFinal =
        brokerType === 'png'
          ? await pdfDoc!.embedPng(brokerPhotoBytes)
          : brokerType === 'jpg'
          ? await pdfDoc!.embedJpg(brokerPhotoBytes)
          : null;
      realtorPhotoFinal =
        realtorType === 'png'
          ? await pdfDoc!.embedPng(realtorPhotoBytes)
          : realtorType === 'jpg'
          ? await pdfDoc!.embedJpg(realtorPhotoBytes)
          : null;
      propertyPhotoFinal =
        propertyType === 'png'
          ? await pdfDoc!.embedPng(propertyPhotoBytes)
          : propertyType === 'jpg'
          ? await pdfDoc!.embedJpg(propertyPhotoBytes)
          : null;
    } catch (error) {
      console.error('Failed to fetch or embed one or more images:', error);
      // Handle errors, possibly by using placeholder images
    }
  };

  //Set Fields Short
  const setFields = () => {
    if (pdf?.address && pdf.address !== null && pdf.address !== undefined) {
      address.setText(pdf.address);
    }
    if (pdfData.askingPrice && pdfData.askingPrice !== null && pdfData.askingPrice !== undefined) {
      askingPrice.setText(toCurrencyString(pdfData.askingPrice));
      askingPrice.updateAppearances(arialBoldFont);
    }
    if (pdf?.mlsCode && pdf.mlsCode !== null && pdf.mlsCode !== undefined) {
      mlsCode.setText(`MLS ${pdf.mlsCode}`);
    }
    if (pdfData.years && pdfData.years !== null && pdfData.years !== undefined && pdfData.years !== 'custom') {
      years.setText(`${pdfData.years.toString()} years`);
      years.updateAppearances(arialBoldFont);
    }
    if (
      pdfData.customYears &&
      pdfData.customYears !== null &&
      pdfData.customYears !== undefined &&
      pdfData.years === 'custom'
    ) {
      years.setText(`${pdfData.customYears.toString()} years`);
      years.updateAppearances(arialBoldFont);
    }

    if (pdfData.frequency && pdfData.frequency !== null && pdfData.frequency !== undefined) {
      frequencyTitle.setText(`${handleFrequency(pdfData.frequency)} Payment`, {
        lineHeight: 1.2
      });
      frequencyTitle.updateAppearances(arialBlackFont);
    }

    if (pdfData.rangeRate && pdfData.rangeRate.type === 'normalTier') {
      middleTierOr10DownPayment.setText('10');
      maxTierOr15DownPayment.setText('15');
      middleTierOr10DownPayment.updateAppearances(arialBlackFont);
      maxTierOr15DownPayment.updateAppearances(arialBlackFont);
    }

    if (pdfData.rangeRate && pdfData.rangeRate.type === 'middleTier') {
      middleTierOr10DownPayment.setText((Math.round((pdfData.rangeRate.rate || 0) * 100) / 100).toString());
      maxTierOr15DownPayment.setText('15');
      middleTierOr10DownPayment.updateAppearances(arialBlackFont);
      maxTierOr15DownPayment.updateAppearances(arialBlackFont);
    }

    // if (pdfData.rangeRate && pdfData.rangeRate.type === 'maxTier') {
    //   middleTierOr10DownPayment.setText('10')
    //   maxTierOr15DownPayment.setText('15')
    //   middleTierOr10DownPayment.updateAppearances(arialBoldFont)
    //   maxTierOr15DownPayment.updateAppearances(arialBoldFont)
    // }

    if (
      pdfData.periodicPay5 &&
      pdfData.periodicPay5 !== null &&
      pdfData.periodicPay5 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      periodicPay5?.setText(toCurrencyString(pdfData.periodicPay5));
    } else {
      // Set N/A for unavailable 5% scenarios
      periodicPay5?.setText('N/A');
    }

    if (
      pdfData.periodicPay10 &&
      pdfData.periodicPay10 !== null &&
      pdfData.periodicPay10 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      periodicPay10?.setText(toCurrencyString(pdfData.periodicPay10));
    } else if (
      pdfData.periodicPayRange &&
      pdfData.periodicPayRange !== null &&
      pdfData.periodicPayRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      periodicPay10?.setText(toCurrencyString(pdfData.periodicPayRange));
    } else {
      // Set N/A for unavailable 10% scenarios
      periodicPay10?.setText('N/A');
    }

    if (
      pdfData.periodicPay15 &&
      pdfData.periodicPay15 !== null &&
      pdfData.periodicPay15 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      periodicPay15?.setText(toCurrencyString(pdfData.periodicPay15));
    } else if (
      pdfData.periodicPayRange &&
      pdfData.periodicPayRange !== null &&
      pdfData.periodicPayRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      periodicPay15?.setText(toCurrencyString(pdfData.periodicPayRange));
    } else {
      // Set N/A for unavailable 15% scenarios
      periodicPay15?.setText('N/A');
    }

    if (pdfData.periodicPay20 && pdfData.periodicPay20 !== null && pdfData.periodicPay20 !== undefined) {
      periodicPay20?.setText(toCurrencyString(pdfData.periodicPay20));
    } else {
      // Set N/A for unavailable 20% scenarios
      periodicPay20?.setText('N/A');
    }

    if (pdfData.periodicPayCustom && pdfData.periodicPayCustom !== null && pdfData.periodicPayCustom !== undefined) {
      periodicPayCustom?.setText(toCurrencyString(pdfData.periodicPayCustom));
    } else if (isNaN(pdfData.periodicPayCustom || 0)) {
      periodicPayCustom?.setText(toCurrencyString(pdfData.periodicPayCustom));
    } else {
      periodicPayCustom?.setText('N/A');
    }

    if (
      pdfData.downPay5 &&
      pdfData.downPay5 !== null &&
      pdfData.downPay5 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      downPay5?.setText(toCurrencyString(pdfData.downPay5));
    } else {
      // Set N/A for unavailable 5% scenarios
      downPay5?.setText('N/A');
    }

    if (
      pdfData.downPay10 &&
      pdfData.downPay10 !== null &&
      pdfData.downPay10 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      downPay10?.setText(toCurrencyString(pdfData.downPay10));
    } else if (
      pdfData.downPayRange &&
      pdfData.downPayRange !== null &&
      pdfData.downPayRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      downPay10?.setText(toCurrencyString(pdfData.downPayRange));
    } else {
      // Set N/A for unavailable 10% scenarios
      downPay10?.setText('N/A');
    }

    if (
      pdfData.downPay15 &&
      pdfData.downPay15 !== null &&
      pdfData.downPay15 !== undefined &&
      pdfData.rangeRate?.type === 'normalTier'
    ) {
      downPay15?.setText(toCurrencyString(pdfData.downPay15));
    } else if (
      pdfData.downPayRange &&
      pdfData.downPayRange !== null &&
      pdfData.downPayRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      downPay15?.setText(toCurrencyString(pdfData.downPayRange));
    } else {
      // Set N/A for unavailable 15% scenarios
      downPay15?.setText('N/A');
    }

    if (pdfData.downPay20 && pdfData.downPay20 !== null && pdfData.downPay20 !== undefined) {
      downPay20?.setText(toCurrencyString(pdfData.downPay20));
    } else {
      // Set N/A for unavailable 20% scenarios
      downPay20?.setText('N/A');
    }
    
    if (pdfData.downPayCustom && pdfData.downPayCustom !== null && pdfData.downPayCustom !== undefined && pdfData.downPayCustom > 0) {
      downPayCustom?.setText(toCurrencyString(pdfData.downPayCustom));
    } else {
      // Set N/A for unavailable custom down payment scenarios
      downPayCustom?.setText('N/A');
    }

    if (
      pdfData.insurance5 &&
      pdfData.insurance5 !== null &&
      pdfData.insurance5 !== undefined &&
      pdfData.rangeRate?.type !== 'maxTier' &&
      pdfData.rangeRate?.type !== 'middleTier'
    ) {
      insurance5?.setText(toCurrencyString(pdfData.insurance5));
    } else {
      // Set N/A for unavailable 5% insurance scenarios
      insurance5?.setText('N/A');
    }

    if (
      pdfData.insurance10 &&
      pdfData.insurance10 !== null &&
      pdfData.insurance10 !== undefined &&
      pdfData.rangeRate?.type !== 'maxTier'
    ) {
      insurance10?.setText(toCurrencyString(pdfData.insurance10));
    } else if (
      pdfData.insuranceRange &&
      pdfData.insuranceRange !== null &&
      pdfData.insuranceRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      insurance10?.setText(toCurrencyString(pdfData.insuranceRange));
    } else {
      // Set N/A for unavailable 10% insurance scenarios
      insurance10?.setText('N/A');
    }

    if (
      pdfData.insurance15 &&
      pdfData.insurance15 !== null &&
      pdfData.insurance15 !== undefined &&
      pdfData.rangeRate?.type !== 'maxTier'
    ) {
      insurance15?.setText(toCurrencyString(pdfData.insurance15));
    } else {
      // Set N/A for unavailable 15% insurance scenarios
      insurance15?.setText('N/A');
    }

    insurance20?.setText('N/A');

    if (pdfData.insuranceCustom !== null && pdfData.insuranceCustom !== undefined) {
      if (pdfData.rangeRate?.type === 'maxTier') {
        if (parseInt((pdfData.customPercentage || 0).toString()) >= 20) {
          insuranceCustom?.setText('N/A');
        }
      } else {
        insuranceCustom?.setText(toCurrencyString(pdfData.insuranceCustom));
      }
    }

    if (
      pdfData.insuranceRange &&
      pdfData.insuranceRange !== null &&
      pdfData.insuranceRange !== undefined &&
      pdfData.rangeRate?.type === 'middleTier'
    ) {
      if (parseInt((pdfData.customPercentage || 0).toString()) >= 20) {
        insuranceCustom?.setText('N/A');
      }
      insurance10?.setText(toCurrencyString(pdfData.insuranceRange));
    }

    if (
      pdfData.rate5 &&
      pdfData.rate5 !== null &&
      pdfData.rate5 !== undefined &&
      pdfData.rangeRate?.type !== 'middleTier' &&
      pdfData.rangeRate?.type !== 'maxTier'
    ) {
      rate5?.setText(`${pdfData.rate5}%`);
    } else {
      // Set N/A for unavailable 5% rate scenarios
      rate5?.setText('N/A');
    }

    if (
      pdfData.rate10 &&
      pdfData.rate10 !== null &&
      pdfData.rate10 !== undefined &&
      pdfData.rangeRate?.type !== 'maxTier'
    ) {
      rate10?.setText(`${pdfData.rate10}%`);
    } else if (
      pdfData.rateRange &&
      pdfData.rateRange !== null &&
      pdfData.rateRange !== undefined &&
      (pdfData.rangeRate?.type === 'normalTier' || pdfData.rangeRate?.type === 'middleTier')
    ) {
      rate10?.setText(`${pdfData.rateRange}%`);
    } else {
      // Set N/A for unavailable 10% rate scenarios
      rate10?.setText('N/A');
    }

    if (
      pdfData.rate15 &&
      pdfData.rate15 !== null &&
      pdfData.rate15 !== undefined &&
      pdfData.rangeRate?.type !== 'maxTier'
    ) {
      rate15?.setText(`${pdfData.rate15}%`);
    } else {
      // Set N/A for unavailable 15% rate scenarios
      rate15?.setText('N/A');
    }

    if (pdfData.rate20 && pdfData.rate20 !== null && pdfData.rate20 !== undefined) {
      rate20?.setText(`${pdfData.rate20}%`);
    } else {
      // Set N/A for unavailable 20% rate scenarios
      rate20?.setText('N/A');
    }

    if (pdfData.rateCustom) {
      if (pdfData.rateCustom === 0 || isNaN(pdfData.rateCustom) || pdfData.rateCustom.toString() === '0') {
        rateCustom?.setText('N/A');
      } else {
        rateCustom?.setText(`${pdfData.rateCustom}%`);
      }
    } else {
      rateCustom?.setText('N/A');
    }

    if (pdfData.periodicPay20 && pdfData.periodicPay20 !== null && pdfData.periodicPay20 !== undefined) {
      periodicPay20?.setText(toCurrencyString(pdfData.periodicPay20));
    } else {
      // Set N/A for unavailable 20% scenarios
      periodicPay20?.setText('N/A');
    }

    if (pdfData.periodicPayCustom && pdfData.periodicPayCustom !== null && pdfData.periodicPayCustom !== undefined) {
      periodicPayCustom?.setText(toCurrencyString(pdfData.periodicPayCustom));
    } else if (isNaN(pdfData.periodicPayCustom || 0)) {
      periodicPayCustom?.setText(toCurrencyString(pdfData.periodicPayCustom));
    } else {
      periodicPayCustom?.setText('N/A');
    }

    if (brokerPhotoFinal && brokerPhotoFinal !== null && brokerPhotoFinal !== undefined) {
      brokerPhoto.setImage(brokerPhotoFinal);
    }
    if (realtorPhotoFinal && realtorPhotoFinal !== null && realtorPhotoFinal !== undefined) {
      realtorPhoto.setImage(realtorPhotoFinal);
    } else {
      realtorPhoto.setImage(realtorDefault);
    }
    if (propertyPhotoFinal && propertyPhotoFinal !== null && propertyPhotoFinal !== undefined) {
      propertyPhoto.setImage(propertyPhotoFinal);
    } else {
      propertyPhoto.setImage(propertyDefault);
    }
    if (user && user.firstname && user.firstname !== null && user.firstname !== undefined) {
      brokerName.setText(`${user.firstname} ${user.lastname}`);
      brokerName.updateAppearances(arialBoldFont);
    } else {
      brokerName.setText('--');
    }
    if (user && user.position && user.position !== null && user.position !== undefined) {
      brokerPosition.setText(user.position);
    } else {
      brokerPosition.setText('--');
    }
    if (user && user.workEmail && user.workEmail !== null && user.workEmail !== undefined && user.workEmail !== '') {
      email.setText(user.workEmail);
    } else if (user && user.email && user.email !== null && user.email !== undefined) {
      email.setText(user.email);
    } else {
      email.setText('--');
    }

    if (user && user.workPhone && user.workPhone !== null && user.workPhone !== undefined) {
      const formattedPhone = formatPhoneNumber(user.workPhone);
      console.log('Formatting user workPhone:', { original: user.workPhone, formatted: formattedPhone });
      phone.setText(formattedPhone);
    } else if (user && user.phone && user.phone !== null && user.phone !== undefined) {
      const formattedPhone = formatPhoneNumber(user.phone);
      console.log('Formatting user phone:', { original: user.phone, formatted: formattedPhone });
      phone.setText(formattedPhone);
    } else if (user && user.cellPhone && user.cellPhone !== null && user.cellPhone !== undefined) {
      const formattedPhone = formatPhoneNumber(user.cellPhone);
      console.log('Formatting user cellPhone:', { original: user.cellPhone, formatted: formattedPhone });
      phone.setText(formattedPhone);
    } else {
      phone.setText('--');
    }

    if (user && user.website && user.website !== null && user.website !== undefined) {
      website.setText(user.website);
    } else {
      website.setText('--');
    }
    if (user && user.brokerage && user.brokerage !== null && user.brokerage !== undefined) {
      licensedWith.setText(user.brokerage);
    } else {
      licensedWith.setText('--');
    }
    
    // Set FSRA field based on user.team.showFSRA condition
    if (user && user.team && user.team.showFSRA === true) {
      fsra.setText('FSRA 12403');
    } else {
      fsra.setText('');
    }
    if (realtor && realtor.firstname && realtor.firstname !== null && realtor.firstname !== undefined) {
      realtorName.setText(`${realtor.firstname} ${realtor.lastname}`);
      realtorName.updateAppearances(arialBoldFont);
    } else {
      realtorName.setText('--');
    }
    if (realtor && realtor.position && realtor.position !== null && realtor.position !== undefined) {
      realtorPosition.setText(realtor.position);
    } else {
      realtorPosition.setText('--');
    }
    if (realtor && realtor.email && realtor.email !== null && realtor.email !== undefined) {
      realtorEmail.setText(realtor.email);
    } else {
      realtorEmail.setText('--');
    }
    if (realtor && realtor.phone && realtor.phone !== null && realtor.phone !== undefined) {
      const formattedPhone = formatPhoneNumber(realtor.phone);
      console.log('Formatting realtor phone:', { original: realtor.phone, formatted: formattedPhone });
      realtorPhone.setText(formattedPhone);
    } else {
      realtorPhone.setText('--');
    }
    if (realtor && realtor.website && realtor.website !== null && realtor.website !== undefined) {
      realtorWebsite.setText(realtor.website);
    } else {
      realtorWebsite.setText('--');
    }
    if (realtor && realtor.company && realtor.company !== null && realtor.company !== undefined) {
      realtorLicensedWith.setText(realtor.company);
    } else {
      realtorLicensedWith.setText('--');
    }

    if (pdfData.customPercentage) {
      if (pdfData.customPercentage === 0 || isNaN(pdfData.customPercentage) || pdfData.customPercentage.toString() === '0') {
        customDown.setText('N/A');
      } else {
        customDown.setText(`${pdfData.customPercentage}`);
      }
      customDown.updateAppearances(arialBlackFont);
    } else {
      customDown.setText('N/A');
      customDown.updateAppearances(arialBlackFont);
    }

    if (pdf?.monthlyExpenses || pdf?.cashNeeded) {
      if (rate && pdfData.chosenDownPay && pdfData.chosenDownPay.rate !== null && pdfData.chosenDownPay.rate !== undefined) {
        rate.setText(`${handleChosenRate().toString()}%`);
        rate.updateAppearances(arialBoldFont);
      }
    }
    
    if (pdf?.monthlyExpenses || pdf?.cashNeeded) {
      if (
        downPaymentRate &&
        pdfData.chosenDownPay &&
        pdfData.chosenDownPay.percent !== null &&
        pdfData.chosenDownPay.percent !== undefined
      ) {
        downPaymentRate.setText(pdfData.chosenDownPay.percent);
        downPaymentRate.updateAppearances(arialBoldFont);
      }
    }
  };

  const getExpensesFields = (form: any) => {
    mortgagePayment = form.getTextField('mortgagePayment');
    propertyTax = form.getTextField('propertyTax');
    propertyInsurance = form.getTextField('propertyInsurance');
    monthlyDebtPayment = form.getTextField('monthlyDebtPayment');
    utilities = form.getTextField('utilities');
    condoFee = form.getTextField('condoFee');
    hoaFee = form.getTextField('hoaFee');
    phoneExpense = form.getTextField('phoneExpense');
    cable = form.getTextField('cable');
    internet = form.getTextField('internet');
    totalExpenses = form.getTextField('totalExpenses');
    totalYearly = form.getTextField('totalYearly');
    mortgagePayment.setText(toCurrencyString(pdfData.chosenPeriodicPay));
    
    // Handle both new dual value structure and legacy single value structure
    const getCurrencyValue = (field: any) => {
      if (field && typeof field === 'object' && 'currency' in field) {
        return field.currency;
      }
      return field || 0;
    };
    
    propertyTax.setText(toCurrencyString(getCurrencyValue(pdfData.propertyTax)));
    propertyInsurance.setText(toCurrencyString(getCurrencyValue(pdfData.propertyInsurance)));
    monthlyDebtPayment.setText(toCurrencyString(getCurrencyValue(pdfData.monthlyDebtPayments)));
    utilities.setText(toCurrencyString(getCurrencyValue(pdfData.utilities)));
    condoFee.setText(toCurrencyString(getCurrencyValue(pdfData.condoFees)));
    hoaFee.setText(toCurrencyString(getCurrencyValue(pdfData.hoaFees)));
    phoneExpense.setText(toCurrencyString(getCurrencyValue(pdfData.phone)));
    cable.setText(toCurrencyString(getCurrencyValue(pdfData.cable)));
    internet.setText(toCurrencyString(getCurrencyValue(pdfData.internet)));
    totalExpenses.setText(toCurrencyString(pdfData.totalMonthlyPayments));
    totalExpenses.updateAppearances(arialBoldFont);
    totalYearly.setText(toCurrencyString(pdfData.totalYearlyPayments));
    totalYearly.updateAppearances(arialBoldFont);
  };

  const getCashFields = (form: any) => {
    downPayment = form.getTextField('downPayment');
    lawyerFees = form.getTextField('lawyerFees');
    homeInspection = form.getTextField('homeInspection');
    appraisal = form.getTextField('appraisal');
    titleInsurance = form.getTextField('titleInsurance');
    estoppelCertificate = form.getTextField('estoppelCertificate');
    totalCashNeeded = form.getTextField('totalCashNeeded');
    downPayment.setText(toCurrencyString(pdfData.chosenDownPay?.amount));
    
    // Handle both new dual value structure and legacy single value structure
    const getCurrencyValue = (field: any) => {
      if (field && typeof field === 'object' && 'currency' in field) {
        return field.currency;
      }
      return field || 0;
    };
    
    lawyerFees.setText(toCurrencyString(getCurrencyValue(pdfData.lawyerFee)));
    homeInspection.setText(toCurrencyString(getCurrencyValue(pdfData.homeInspection)));
    appraisal.setText(toCurrencyString(getCurrencyValue(pdfData.appraisal)));
    titleInsurance.setText(toCurrencyString(getCurrencyValue(pdfData.titleInsurance)));
    estoppelCertificate.setText(toCurrencyString(getCurrencyValue(pdfData.estoppelFee)));
    totalCashNeeded.setText(toCurrencyString(pdfData.totalCashNeeded));
    totalCashNeeded.updateAppearances(arialBoldFont);
  };

  const getCommonFields = async (form: any) => {
    if (pdf?.monthlyExpenses || pdf?.cashNeeded) {
      downPaymentRate = form.getTextField('downPaymentRate');
      rate = form.getTextField('rate');
    }
    address = form.getTextField('address');
    address.enableMultiline();
    mlsCode = form.getTextField('mlsCode');
    askingPrice = form.getTextField('askingPrice');
    years = form.getTextField('years');
    customDown = form.getTextField('customDownPayment');
    frequencyTitle = form.getTextField('frequencyTitle');
    frequencyTitle.updateAppearances(arialBoldFont);
    frequencyTitle.enableMultiline();

    // Always retrieve all form fields to prevent null reference errors
    middleTierOr10DownPayment = form.getTextField('middleTierOr10DownPayment');
    maxTierOr15DownPayment = form.getTextField('maxTierOr15DownPayment');
    periodicPay5 = form.getTextField('periodicPay5');
    periodicPay10 = form.getTextField('periodicPay10');
    periodicPay15 = form.getTextField('periodicPay15');
    periodicPay20 = form.getTextField('periodicPay20');
    downPay5 = form.getTextField('downPay5');
    downPay10 = form.getTextField('downPay10');
    downPay15 = form.getTextField('downPay15');
    downPay20 = form.getTextField('downPay20');
    downPayCustom = form.getTextField('downPayCustom');
    insurance5 = form.getTextField('insurance5');
    insurance10 = form.getTextField('insurance10');
    insurance15 = form.getTextField('insurance15');
    insurance20 = form.getTextField('insurance20');
    rate5 = form.getTextField('rate5');
    rate10 = form.getTextField('rate10');
    rate15 = form.getTextField('rate15');
    rate20 = form.getTextField('rate20');

    periodicPayCustom = form.getTextField('periodicPayCustom');
    insuranceCustom = form.getTextField('insuranceCustom');
    rateCustom = form.getTextField('rateCustom');
    brokerPhoto = form.getButton('brokerPhoto_af_image');
    realtorPhoto = form.getButton('realtorPhoto_af_image');
    propertyPhoto = form.getButton('banner_af_image');
    brokerName = form.getTextField('brokerName');
    brokerPosition = form.getTextField('brokerPosition');
    email = form.getTextField('email');
    phone = form.getTextField('phone');
    website = form.getTextField('website');
    licensedWith = form.getTextField('licensedWith');
    realtorName = form.getTextField('realtorName');
    realtorPosition = form.getTextField('realtorPosition');
    realtorEmail = form.getTextField('realtorEmail');
    realtorPhone = form.getTextField('realtorPhone');
    realtorWebsite = form.getTextField('realtorWebsite');
    realtorLicensedWith = form.getTextField('realtorLicensedWith');
    fsra = form.getTextField('fsra');
  };

  const handleBrokerageLogo = async () => {
    let logo: string;

    const pages = pdfDoc!.getPages();
    const firstPage = pages[0];
    const lastPage = pages[pages.length - 1];

    if (user && user.team && user.team.id === '108') {
      logo = getAssetUrl('images/Ideal_Mortgage_Solutions_Logo.png');
      const { arrayBuffer: brokerageLogo } = await fetchImage(logo);
      const brokerageLogoFinal = await pdfDoc!.embedPng(brokerageLogo);

      const company = getAssetUrl('images/indi-logo-horizontal-tagline-fsra.png');
      const { arrayBuffer: companyLogo } = await fetchImage(company);
      const companyLogoFinal = await pdfDoc!.embedPng(companyLogo);

      const { width, height } = firstPage.getSize();

      //Adding Logo
      firstPage.drawImage(brokerageLogoFinal, {
        width: 103,
        height: 45,
        x: 15,
        y: height - 58
      });

      lastPage.drawImage(brokerageLogoFinal, {
        width: !pdf?.monthlyExpenses && !pdf?.cashNeeded ? 80 : 103,
        height: !pdf?.monthlyExpenses && !pdf?.cashNeeded ? 35 : 45,
        x: !pdf?.monthlyExpenses && !pdf?.cashNeeded ? 20 : 24,
        y: !pdf?.monthlyExpenses && !pdf?.cashNeeded ? 8 : 12
      });

      firstPage.drawImage(companyLogoFinal, {
        width: 115,
        height: 31,
        x: width - 146,
        y: height - 50
      });
    } else {
      logo = getAssetUrl('images/indi-logo-horizontal-tagline.png');
      const { arrayBuffer: brokerageLogo } = await fetchImage(logo);
      const brokerageLogoFinal = await pdfDoc!.embedPng(brokerageLogo);

      const pages = pdfDoc!.getPages();
      const firstPage = pages[0];
      const { width, height } = firstPage.getSize();

      //Adding Logo
      firstPage.drawImage(brokerageLogoFinal, {
        width: 111,
        height: 30,
        x: 26,
        y: height - 58
      });

      lastPage.drawImage(brokerageLogoFinal, {
        width: 111,
        height: 30,
        x: 26,
        y: 10
      });
    }
  };

  // Determine which PDF template to use
  let sourcePdfUrl: string;
  const templateBasePath = getPdfTemplateBasePath();
    
  if (pdf?.monthlyExpenses && pdf?.cashNeeded) {
    sourcePdfUrl = `${templateBasePath}/Listing_Sheets_Template_Payment_Cash_Expenses.pdf`;
  } else if (pdf?.monthlyExpenses && !pdf?.cashNeeded) {
    sourcePdfUrl = `${templateBasePath}/Listing_Sheets_Template_Payment_Expenses.pdf`;
  } else if (!pdf?.monthlyExpenses && pdf?.cashNeeded) {
    sourcePdfUrl = `${templateBasePath}/Listing_Sheets_Template_Payment_Cash.pdf`;
  } else {
    sourcePdfUrl = `${templateBasePath}/Listing_Sheets_Template_Only_Payment.pdf`;
  }

  // Main execution
  try {
    const pdfForm = await getPdfForm(sourcePdfUrl);
    await fetchAndEmbedImages();
    await getCommonFields(pdfForm);
    
    // Dev-only: list all form fields for verification
    if (process.env.NODE_ENV !== 'production') {
      try {
        const fields = form.getFields();
        const fieldNames = fields.map((f: any) => f.getName());
        console.log('[PDF] Available form fields:', fieldNames);
      } catch (err) {
        console.warn('[PDF] Could not enumerate form fields:', err);
      }
    }
    
    if (pdf?.monthlyExpenses) getExpensesFields(pdfForm);
    if (pdf?.cashNeeded) getCashFields(pdfForm);
    setFields();
    await handleBrokerageLogo();

    // Ensure all fields render correctly with the embedded font
    try {
      form.updateFieldAppearances(arialBoldFont);
    } catch (e) {
      // Non-fatal; continue
      console.warn("Failed to update field appearances:", e);
    }

    const pdfBytes = await pdfDoc!.save();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const pdfFile = new File([blob], 'listing-sheet.pdf');
    const docUrl = URL.createObjectURL(blob);

    if (isSafari) {
      saveAs(docUrl, 'listing-sheet.pdf');
      window.open(docUrl);
      return { pdfFile, docUrl, status: 'finished' };
    }
    window.open(docUrl);
    return { pdfFile, docUrl, status: 'finished' };
  } catch (error) {
    console.error('Error generating listing sheet:', error);
    return { status: 'error', message: (error as Error).message };
  }
};

export default GenerateListingSheet;


