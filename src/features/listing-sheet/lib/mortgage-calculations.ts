import type { PaymentFrequency, RangeRate } from "../types";

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Down Payment and Insurance Amounts
 * This replicates the exact logic from legacy/components/MortgageCalculator/MortgageCalculator.js
 */
export function calcDownPaymentMinRateByAmount(amount: number) {
  if (isNaN(amount)) return null;

  // --- Regular Buyer Calculations --- (Threshold updated from $1M to $1.5M)
  let regularRangeRate: RangeRate = {
    type: "normalTier",
    rate: null,
  };
  if (amount > 500000 && amount < 1500000) {
    const regularRate = 500000 * 0.05;
    const remainingRate = (amount - 500000) * 0.1;
    const rangeRate =
      (((100 * (regularRate + remainingRate)) / amount) * 100) / 100;
    regularRangeRate = { type: "middleTier", rate: rangeRate };
  } else if (amount >= 1500000) {
    regularRangeRate = { type: "maxTier", rate: 20 };
  } else if (amount > 0) {
    regularRangeRate = { type: "normalTier", rate: null };
  }

  // --- FTHB Buyer Calculations --- (Already using $1.5M threshold)
  let minDownPercentFTHB = 5;
  let minDownAmountFTHB = amount * 0.05;
  if (amount > 500000 && amount <= 1500000) {
    const baseDown = 500000 * 0.05;
    const additionalDown = (amount - 500000) * 0.1;
    minDownAmountFTHB = baseDown + additionalDown;
    minDownPercentFTHB = (minDownAmountFTHB / amount) * 100;
  } else if (amount > 1500000) {
    minDownPercentFTHB = 20;
    minDownAmountFTHB = amount * 0.2;
  }

  // --- Insurance Calculation Helper ---
  const calculateInsurance = (principal: number, downPercent: number) => {
    if (downPercent >= 20) return 0;
    if (downPercent >= 15) return principal * 0.028; // 15% - 19.99%
    if (downPercent >= 10) return principal * 0.031; // 10% - 14.99%
    if (downPercent >= 5) return principal * 0.04; // 5% - 9.99%
    return 0; // Should not happen if down% >= 5
  };

  // --- Calculate all tiers based on amount and regular threshold ---
  const isMiddleTier = regularRangeRate.type === "middleTier";
  const isMaxTier = regularRangeRate.type === "maxTier";

  const downPay5 = amount * 0.05;
  const downPay10 = amount * 0.1;
  const downPay15 = amount * 0.15;
  const downPay20 = amount * 0.2;
  const downPayRange =
    regularRangeRate.type === "middleTier"
      ? (amount * regularRangeRate.rate!) / 100
      : 0;

  const insurance5 = calculateInsurance(amount - downPay5, 5);
  const insurance10 = calculateInsurance(amount - downPay10, 10);
  const insurance15 = calculateInsurance(amount - downPay15, 15);
  const insurance20 = 0; // Always 0 for 20%
  const insuranceRange = isMiddleTier
    ? (amount - (amount * regularRangeRate.rate!) / 100) *
      (regularRangeRate.rate! >= 15
        ? 0.028
        : regularRangeRate.rate! >= 10
        ? 0.031
        : 0.04)
    : 0;

  const principal5 = downPay5 > 0 ? amount - downPay5 + insurance5 : 0;
  const principal10 = downPay10 > 0 ? amount - downPay10 + insurance10 : 0;
  const principal15 = downPay15 > 0 ? amount - downPay15 + insurance15 : 0;
  const principal20 = amount - downPay20 + insurance20;
  const principalRange = isMiddleTier
    ? amount -
      (amount * regularRangeRate.rate!) / 100 +
      (amount - (amount * regularRangeRate.rate!) / 100) *
        (regularRangeRate.rate! >= 15
          ? 0.028
          : regularRangeRate.rate! >= 10
          ? 0.031
          : 0.04)
    : 0;

  // --- Calculate FTHB Insurance and Principal ---
  const insuranceFTHB = calculateInsurance(
    amount - minDownAmountFTHB,
    minDownPercentFTHB
  );
  const principalFTHB = amount - minDownAmountFTHB + insuranceFTHB;

  return {
    rangeRate: regularRangeRate,
    downPay5,
    downPay10,
    downPay15,
    downPay20,
    downPayRange,
    downPayFTHB: minDownAmountFTHB,
    insurance5,
    insurance10,
    insurance15,
    insurance20,
    insuranceRange,
    insuranceFTHB,
    principal5,
    principal10,
    principal15,
    principal20,
    principalRange,
    principalFTHB,
  };
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Custom Down Payment
 * This replicates the paymentCalc function from legacy
 */
export function paymentCalc(
  amount: number,
  customPercentage: number,
  rangeRate: RangeRate
) {
  const customPct = parseFloat(customPercentage.toString()) || 0; // Ensure it's a number, default to 0

  // --- Insurance Calculation Helper (repeated for custom/range) ---
  const calcInsurance = (amountForInsurance: number, rate: number) => {
    if (rate >= 20) return 0;
    if (rate >= 15) return amountForInsurance * 0.028;
    if (rate >= 10) return amountForInsurance * 0.031;
    if (rate >= 5) return amountForInsurance * 0.04;
    return 0;
  };

  // --- Calculate Custom Down Payment, Insurance, Principal ---
  let downPayCustom = 0;
  let insuranceCustom = 0;
  let principalCustom = 0;
  const minRequiredPercent =
    rangeRate.type === "middleTier"
      ? rangeRate.rate!
      : rangeRate.type === "maxTier"
      ? 20
      : 5;

  if (customPct > 0 && customPct >= minRequiredPercent) {
    downPayCustom = amount * (customPct / 100);
    const amountForInsurance = amount - downPayCustom;
    insuranceCustom = calcInsurance(amountForInsurance, customPct);
    principalCustom = amount - downPayCustom + insuranceCustom;
  }

  return {
    downPayCustom,
    insuranceCustom,
    principalCustom,
  };
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Effective Rate
 * This replicates the getEffectiveRate function from legacy
 */
export function getEffectiveRate(rate: number): number {
  if (parseFloat(rate.toString()) === 0) {
    return 0;
  } else {
    const nominalRate = parseFloat(rate.toString()) / 100;
    const effectiveRate =
      (Math.pow(Math.pow(1 + nominalRate / 2, 2), 1 / 12) - 1) * 12;
    return effectiveRate;
  }
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Monthly Payment
 * This replicates the monthlyPay function from legacy
 */
export function calculateMonthlyPaymentLegacy(
  rate: number,
  principal: number,
  years: number,
  customYears?: number
): number {
  const principalVal = principal;
  if (
    principalVal === undefined ||
    principalVal === null ||
    isNaN(parseFloat(principalVal.toString())) ||
    principalVal === 0
  ) {
    return 0;
  }

  const principalAmount = parseFloat(principalVal.toString());
  const effectiveRate = getEffectiveRate(rate);
  const numberOfPayments = years !== 0 ? years * 12 : (customYears || 0) * 12;

  if (effectiveRate <= 0 || principalAmount <= 0 || numberOfPayments <= 0)
    return 0;
  const monthlyRate = effectiveRate / 12;
  return (
    (monthlyRate * principalAmount) /
    (1 - Math.pow(1 + monthlyRate, -1 * numberOfPayments))
  );
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Periodic Payment
 * This replicates the periodicPayCalc function from legacy
 */
export function periodicPayCalc(
  frequency: PaymentFrequency,
  monthlyPayments: {
    monthlyPay5: number;
    monthlyPay10: number;
    monthlyPay15: number;
    monthlyPay20: number;
    monthlyPayCustom: number;
    monthlyPayRange: number;
    monthlyPayFTHB: number;
  }
) {
  const {
    monthlyPay5,
    monthlyPay10,
    monthlyPay15,
    monthlyPay20,
    monthlyPayCustom,
    monthlyPayRange,
    monthlyPayFTHB,
  } = monthlyPayments;

  let pay5 = 0,
    pay10 = 0,
    pay15 = 0,
    pay20 = 0,
    payCustom = 0,
    payRange = 0,
    payFTHB = 0,
    numPayments = 0;

  if (frequency === "monthly") {
    pay5 = monthlyPay5;
    pay10 = monthlyPay10;
    pay15 = monthlyPay15;
    pay20 = monthlyPay20;
    payCustom = monthlyPayCustom;
    payRange = monthlyPayRange;
    payFTHB = monthlyPayFTHB;
    numPayments = 12;
  } else if (frequency === "biweekly") {
    pay5 = (monthlyPay5 * 12) / 26;
    pay10 = (monthlyPay10 * 12) / 26;
    pay15 = (monthlyPay15 * 12) / 26;
    pay20 = (monthlyPay20 * 12) / 26;
    payCustom = (monthlyPayCustom * 12) / 26;
    payRange = (monthlyPayRange * 12) / 26;
    payFTHB = (monthlyPayFTHB * 12) / 26;
    numPayments = 26;
  } else if (frequency === "weekly") {
    pay5 = (monthlyPay5 * 12) / 52;
    pay10 = (monthlyPay10 * 12) / 52;
    pay15 = (monthlyPay15 * 12) / 52;
    pay20 = (monthlyPay20 * 12) / 52;
    payCustom = (monthlyPayCustom * 12) / 52;
    payRange = (monthlyPayRange * 12) / 52;
    payFTHB = (monthlyPayFTHB * 12) / 52;
    numPayments = 52;
  } else if (frequency === "accbiweekly") {
    pay5 = monthlyPay5 / 2;
    pay10 = monthlyPay10 / 2;
    pay15 = monthlyPay15 / 2;
    pay20 = monthlyPay20 / 2;
    payCustom = monthlyPayCustom / 2;
    payRange = monthlyPayRange / 2;
    payFTHB = monthlyPayFTHB / 2;
    numPayments = 26;
  } else if (frequency === "accweekly") {
    pay5 = monthlyPay5 / 4;
    pay10 = monthlyPay10 / 4;
    pay15 = monthlyPay15 / 4;
    pay20 = monthlyPay20 / 4;
    payCustom = monthlyPayCustom / 4;
    payRange = monthlyPayRange / 4;
    payFTHB = monthlyPayFTHB / 4;
    numPayments = 52;
  }

  return {
    periodicPay5: pay5,
    periodicPay10: pay10,
    periodicPay15: pay15,
    periodicPay20: pay20,
    periodicPayCustom: payCustom,
    periodicPayRange: payRange,
    periodicPayFTHB: payFTHB,
    numberOfPayments: numPayments,
  };
}

/**
 * EXACT LEGACY IMPLEMENTATION - Get Down Payment Amount by Type
 */
export function getDownTypeAmount(value: string, calcInfo: any): number {
  let downValue;

  if (
    value === "5%" ||
    value === "10%" ||
    value === "15%" ||
    value === "20%"
  ) {
    switch (value) {
      case "5%":
        downValue = calcInfo.downPay5;
        return downValue;
      case "10%":
        downValue = calcInfo.downPay10;
        return downValue;
      case "15%":
        downValue = calcInfo.downPay15;
        return downValue;
      case "20%":
        downValue = calcInfo.downPay20;
        return downValue;
      default:
        return 0;
    }
  } else if (calcInfo.rangeRate.type === "normalTier") {
    downValue = calcInfo.downPayCustom;
  } else {
    downValue = calcInfo.downPayRange;
  }

  return downValue;
}

/**
 * EXACT LEGACY IMPLEMENTATION - Get Chosen Rate
 */
export function handleChosenRate(calcInfo: any): number {
  const { chosenDownPay } = calcInfo;
  let chosenRate;

  if (chosenDownPay && chosenDownPay.rate) {
    switch (chosenDownPay.rate) {
      case "rate5":
        chosenRate = calcInfo.rate5;
        break;
      case "rate10":
        chosenRate = calcInfo.rate10;
        break;
      case "rate15":
        chosenRate = calcInfo.rate15;
        break;
      case "rate20":
        chosenRate = calcInfo.rate20;
        break;
      case "rateRange":
        chosenRate = calcInfo.rangeRate.rate;
        break;
      case "rateCustom":
        chosenRate = calcInfo.rateCustom;
        break;
      case "rateFTHB":
        chosenRate = calcInfo.rateFTHB;
        break;
      default:
        chosenRate = calcInfo.rate5;
    }
  }

  return chosenRate;
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Total Cash Needed
 */
export function totalCashNeededCalc(calcInfo: any): number {
  const titleInsurance = calcInfo.titleInsurance;

  return (
    parseFloat(calcInfo.chosenDownPay.amount) +
    parseFloat(calcInfo.estoppelFee) +
    parseFloat(titleInsurance) +
    parseFloat(calcInfo.homeInspection) +
    parseFloat(calcInfo.lawyerFee) +
    parseFloat(calcInfo.appraisal)
  );
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Monthly Expenses
 */
export function monthlyExpensesCalc(calcInfo: any): number {
  const condoFeesVal = calcInfo.condoFees;
  const utilitiesVal = calcInfo.utilities;
  const phoneVal = calcInfo.phone;
  const cableVal = calcInfo.cable;
  const internetVal = calcInfo.internet;
  const MonthlyDebtVal = calcInfo.monthlyDebtPayments;

  const totalExpenses =
    parseFloat(utilitiesVal) +
    parseFloat(condoFeesVal) +
    parseFloat(phoneVal) +
    parseFloat(cableVal) +
    parseFloat(internetVal) +
    parseFloat(MonthlyDebtVal) +
    parseFloat(calcInfo.chosenPeriodicPay);

  return totalExpenses;
}

/**
 * EXACT LEGACY IMPLEMENTATION - Calculate Yearly Expenses
 */
export function yearlyExpensesCalc(calcInfo: any): number {
  const hoaFeesVal = calcInfo.hoaFees;
  const propertyTaxVal = calcInfo.propertyTax;
  const propertyInsurance = calcInfo.propertyInsurance;

  const totalYearlyExpenses =
    parseFloat(propertyTaxVal) +
    parseFloat(propertyInsurance) +
    parseFloat(hoaFeesVal);

  return totalYearlyExpenses;
}

/**
 * EXACT LEGACY IMPLEMENTATION - Chosen Periodic Pay Calculation
 */
export function chosenPeriodicPayCalc(calcInfo: any): {
  chosenPeriodicPay: number;
  chosenPrincipal: number;
} {
  const chosenDownPayExpense = parseFloat(
    calcInfo.chosenDownPayExpense.percent
  );

  if (chosenDownPayExpense === 5) {
    return {
      chosenPeriodicPay: calcInfo.periodicPay5,
      chosenPrincipal: calcInfo.principal5,
    };
  } else if (chosenDownPayExpense === 10) {
    return {
      chosenPeriodicPay: calcInfo.periodicPay10,
      chosenPrincipal: calcInfo.principal10,
    };
  } else if (chosenDownPayExpense === 15) {
    return {
      chosenPeriodicPay: calcInfo.periodicPay15,
      chosenPrincipal: calcInfo.principal15,
    };
  } else if (chosenDownPayExpense === 20) {
    return {
      chosenPeriodicPay: calcInfo.periodicPay20,
      chosenPrincipal: calcInfo.principal20,
    };
  } else if (chosenDownPayExpense > 5 && chosenDownPayExpense < 10) {
    return {
      chosenPeriodicPay: calcInfo.periodicPayRange,
      chosenPrincipal: calcInfo.principalRange,
    };
  } else if (
    chosenDownPayExpense !== 5 &&
    chosenDownPayExpense !== 10 &&
    chosenDownPayExpense !== 15 &&
    chosenDownPayExpense !== 20 &&
    chosenDownPayExpense !== 0
  ) {
    return {
      chosenPeriodicPay: calcInfo.periodicPayCustom,
      chosenPrincipal: calcInfo.principalCustom,
    };
  } else {
    return { chosenPeriodicPay: 0, chosenPrincipal: 0 };
  }
}

/**
 * Utility function to format currency (keeping this for compatibility)
 */
export function formatCurrency(amount: number | undefined | null): string {
  if (
    amount === 0 ||
    amount === null ||
    amount === undefined ||
    isNaN(amount)
  ) {
    return "$0.00";
  }

  const cleanAmount = amount.toString().replace(/[^0-9.]/g, "");

  return new Intl.NumberFormat("en-CA", {
    style: "currency",
    currency: "CAD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(parseFloat(cleanAmount));
}

/**
 * Utility function to format percentage (keeping this for compatibility)
 */
export function formatPercentage(rate: number, decimals: number = 2): string {
  if (rate === 0 || isNaN(rate)) {
    return "N/A";
  }

  return `${rate.toFixed(decimals)}%`;
}
// Additional function exports for test compatibility
export function calculateDownPayment(
  amount: number,
  percentage: number
): number {
  return amount * (percentage / 100);
}

export function calculateRangeRate(amount: number): RangeRate {
  if (amount <= 500000) {
    return { type: "normalTier", rate: null };
  } else if (amount < 1500000) {
    const regularRate = 500000 * 0.05;
    const remainingRate = (amount - 500000) * 0.1;
    const rangeRate =
      (((100 * (regularRate + remainingRate)) / amount) * 100) / 100;
    return { type: "middleTier", rate: rangeRate };
  } else {
    return { type: "maxTier", rate: 20 };
  }
}

export function calculateMortgageInsurance(
  principal: number,
  downPaymentPercent: number
): number {
  if (downPaymentPercent >= 20) return 0;
  if (downPaymentPercent >= 15) return principal * 0.028;
  if (downPaymentPercent >= 10) return principal * 0.031;
  if (downPaymentPercent >= 5) return principal * 0.04;
  return 0;
}

export function calculateFTHBDownPayment(amount: number): {
  amount: number;
  percentage: number;
} {
  if (amount <= 500000) {
    return { amount: amount * 0.05, percentage: 5 };
  } else if (amount <= 1500000) {
    const baseDown = 500000 * 0.05;
    const additionalDown = (amount - 500000) * 0.1;
    const totalDown = baseDown + additionalDown;
    return { amount: totalDown, percentage: (totalDown / amount) * 100 };
  } else {
    return { amount: amount * 0.2, percentage: 20 };
  }
}

export function calculateEffectiveRate(nominalRate: number): number {
  if (nominalRate <= 0) return 0;
  return getEffectiveRate(nominalRate);
}

export function calculateMonthlyPaymentSimple(
  principal: number,
  annualRate: number,
  years: number
): number {
  if (principal <= 0 || years <= 0) return 0;
  if (annualRate === 0) return principal / (years * 12);

  const monthlyRate = annualRate / 12;
  const numberOfPayments = years * 12;

  return (
    (monthlyRate * principal) /
    (1 - Math.pow(1 + monthlyRate, -numberOfPayments))
  );
}

export function calculatePeriodicPayment(
  monthlyPayment: number,
  frequency: PaymentFrequency
): number {
  switch (frequency) {
    case "monthly":
      return monthlyPayment;
    case "biweekly":
      return (monthlyPayment * 12) / 26;
    case "weekly":
      return (monthlyPayment * 12) / 52;
    case "accbiweekly":
      return monthlyPayment / 2;
    case "accweekly":
      return monthlyPayment / 4;
    default:
      return monthlyPayment;
  }
}

export function calculateAllDownPaymentScenarios(
  amount: number,
  customPercentage: number
) {
  const result = calcDownPaymentMinRateByAmount(amount);
  if (!result) return null;

  const customResult = paymentCalc(amount, customPercentage, result.rangeRate);

  return {
    ...result,
    ...customResult,
  };
}
