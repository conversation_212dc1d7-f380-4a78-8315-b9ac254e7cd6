import {
  calculateDownPayment,
  calculateRangeRate,
  calculateMortgageInsurance,
  calculateFTHBDownPayment,
  calculateEffectiveRate,
  calculateMonthlyPaymentSimple,
  calculatePeriodicPayment,
  calculateAllDownPaymentScenarios,
  formatCurrency,
  formatPercentage,
} from "../mortgage-calculations";

describe("Mortgage Calculations", () => {
  describe("calculateDownPayment", () => {
    it("should calculate correct down payment amount", () => {
      expect(calculateDownPayment(500000, 5)).toBe(25000);
      expect(calculateDownPayment(500000, 10)).toBe(50000);
      expect(calculateDownPayment(500000, 20)).toBe(100000);
    });

    it("should handle zero values", () => {
      expect(calculateDownPayment(0, 5)).toBe(0);
      expect(calculateDownPayment(500000, 0)).toBe(0);
    });
  });

  describe("calculateRangeRate", () => {
    it("should return normalTier for properties under $500K", () => {
      const result = calculateRangeRate(400000);
      expect(result.type).toBe("normalTier");
      expect(result.rate).toBeNull();
    });

    it("should return middleTier for properties between $500K-$1.5M", () => {
      const result = calculateRangeRate(800000);
      expect(result.type).toBe("middleTier");
      expect(result.rate).toBeGreaterThan(5);
      expect(result.rate).toBeLessThan(10);
    });

    it("should return maxTier for properties over $1.5M", () => {
      const result = calculateRangeRate(2000000);
      expect(result.type).toBe("maxTier");
      expect(result.rate).toBe(20);
    });

    it("should handle edge cases", () => {
      expect(calculateRangeRate(0).type).toBe("normalTier");
      expect(calculateRangeRate(500000).type).toBe("normalTier");
      expect(calculateRangeRate(1500000).type).toBe("maxTier");
    });
  });

  describe("calculateMortgageInsurance", () => {
    const principal = 400000;

    it("should return 0 for down payments >= 20%", () => {
      expect(calculateMortgageInsurance(principal, 20)).toBe(0);
      expect(calculateMortgageInsurance(principal, 25)).toBe(0);
    });

    it("should calculate correct insurance for 15-19.99% down", () => {
      const expected = principal * 0.028;
      expect(calculateMortgageInsurance(principal, 15)).toBe(expected);
      expect(calculateMortgageInsurance(principal, 18)).toBe(expected);
    });

    it("should calculate correct insurance for 10-14.99% down", () => {
      const expected = principal * 0.031;
      expect(calculateMortgageInsurance(principal, 10)).toBe(expected);
      expect(calculateMortgageInsurance(principal, 12)).toBe(expected);
    });

    it("should calculate correct insurance for 5-9.99% down", () => {
      const expected = principal * 0.04;
      expect(calculateMortgageInsurance(principal, 5)).toBe(expected);
      expect(calculateMortgageInsurance(principal, 8)).toBe(expected);
    });
  });

  describe("calculateFTHBDownPayment", () => {
    it("should calculate 5% for properties under $500K", () => {
      const result = calculateFTHBDownPayment(400000);
      expect(result.amount).toBe(20000);
      expect(result.percentage).toBe(5);
    });

    it("should calculate blended rate for properties $500K-$1.5M", () => {
      const result = calculateFTHBDownPayment(800000);
      expect(result.amount).toBe(55000); // $25K + $30K
      expect(result.percentage).toBeCloseTo(6.875, 2);
    });

    it("should calculate 20% for properties over $1.5M", () => {
      const result = calculateFTHBDownPayment(2000000);
      expect(result.amount).toBe(400000);
      expect(result.percentage).toBe(20);
    });
  });

  describe("calculateEffectiveRate", () => {
    it("should convert nominal rate to effective rate", () => {
      const result = calculateEffectiveRate(5);
      expect(result).toBeGreaterThan(0.04);
      expect(result).toBeLessThan(0.06);
    });

    it("should handle zero rate", () => {
      expect(calculateEffectiveRate(0)).toBe(0);
    });

    it("should handle negative rates", () => {
      expect(calculateEffectiveRate(-1)).toBe(0);
    });
  });

  describe("calculateMonthlyPaymentSimple", () => {
    it("should calculate correct monthly payment", () => {
      const payment = calculateMonthlyPaymentSimple(400000, 0.05, 25);
      expect(payment).toBeGreaterThan(2000);
      expect(payment).toBeLessThan(3000);
    });

    it("should handle zero values", () => {
      expect(calculateMonthlyPaymentSimple(0, 0.05, 25)).toBe(0);
      expect(calculateMonthlyPaymentSimple(400000, 0, 25)).toBe(
        400000 / (25 * 12)
      );
      expect(calculateMonthlyPaymentSimple(400000, 0.05, 0)).toBe(0);
    });
  });

  describe("calculatePeriodicPayment", () => {
    const monthlyPayment = 2000;

    it("should return same amount for monthly frequency", () => {
      expect(calculatePeriodicPayment(monthlyPayment, "monthly")).toBe(
        monthlyPayment
      );
    });

    it("should calculate bi-weekly payment", () => {
      const result = calculatePeriodicPayment(monthlyPayment, "biweekly");
      expect(result).toBeCloseTo((monthlyPayment * 12) / 26, 2);
    });

    it("should calculate accelerated bi-weekly payment", () => {
      const result = calculatePeriodicPayment(monthlyPayment, "accbiweekly");
      expect(result).toBe(monthlyPayment / 2);
    });

    it("should handle zero payment", () => {
      expect(calculatePeriodicPayment(0, "monthly")).toBe(0);
    });
  });

  describe("calculateAllDownPaymentScenarios", () => {
    it("should calculate all scenarios for normal tier property", () => {
      const scenarios = calculateAllDownPaymentScenarios(400000, 15);

      expect(scenarios).not.toBeNull();
      expect(scenarios!.downPay5).toBe(20000);
      expect(scenarios!.downPay10).toBe(40000);
      expect(scenarios!.downPay15).toBe(60000);
      expect(scenarios!.downPay20).toBe(80000);
      expect(scenarios!.downPayCustom).toBe(60000);
      expect(scenarios!.downPayRange).toBe(0);

      expect(scenarios!.insurance20).toBe(0);
      expect(scenarios!.insurance15).toBeGreaterThan(0);
      expect(scenarios!.principal20).toBe(320000);
    });

    it("should calculate scenarios for middle tier property", () => {
      const scenarios = calculateAllDownPaymentScenarios(800000, 8);

      expect(scenarios).not.toBeNull();
      expect(scenarios!.downPayRange).toBeGreaterThan(0);
      expect(scenarios!.downPayCustom).toBeGreaterThan(0); // 8% is above minimum required for middle tier
      expect(scenarios!.principalRange).toBeGreaterThan(0);
    });

    it("should return null for invalid amount", () => {
      const scenarios = calculateAllDownPaymentScenarios(NaN, 15);
      expect(scenarios).toBeNull();
    });
  });

  describe("formatCurrency", () => {
    it("should format currency correctly", () => {
      expect(formatCurrency(1000)).toBe("$1,000.00");
      expect(formatCurrency(1000000)).toBe("$1,000,000.00");
    });

    it("should handle zero and NaN", () => {
      expect(formatCurrency(0)).toBe("$0.00");
      expect(formatCurrency(NaN)).toBe("$0.00");
      expect(formatCurrency(null)).toBe("$0.00");
      expect(formatCurrency(undefined)).toBe("$0.00");
    });
  });

  describe("formatPercentage", () => {
    it("should format percentage correctly", () => {
      expect(formatPercentage(5.5)).toBe("5.50%");
      expect(formatPercentage(10, 1)).toBe("10.0%");
    });

    it("should handle zero and NaN", () => {
      expect(formatPercentage(0)).toBe("N/A");
      expect(formatPercentage(NaN)).toBe("N/A");
    });
  });
});
