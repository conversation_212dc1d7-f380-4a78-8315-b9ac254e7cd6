import { ListingSheetApiClient } from "../api-client";
import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";

// Mock dependencies
jest.mock("@/shared/lib/api");
jest.mock("@/shared/lib/auth");

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;
const mockGetCookie = getCookie as jest.MockedFunction<typeof getCookie>;

// Mock fetch for file uploads
global.fetch = jest.fn();

describe("ListingSheetApiClient", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetCookie.mockReturnValue("mock-user-id");
  });

  describe("getListingSheets", () => {
    it("should fetch listing sheets successfully", async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [
            {
              id: "1",
              slug: "test-listing",
              title: "Test Listing",
              createdAt: "2024-01-01T00:00:00Z",
              updatedAt: "2024-01-01T00:00:00Z",
              user: "user-1",
              sheet: {},
            },
          ],
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await ListingSheetApiClient.getListingSheets();

      expect(result.data).toHaveLength(1);
      expect(result.error).toBeUndefined();
      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringContaining("/listing-sheets?")
      );
    });

    it("should handle API errors", async () => {
      mockApiClient.get.mockResolvedValue({
        success: false,
        error: "API Error",
      });

      const result = await ListingSheetApiClient.getListingSheets();

      expect(result.data).toEqual([]);
      expect(result.error).toBe("API Error");
    });

    it("should handle network errors", async () => {
      mockApiClient.get.mockRejectedValue(new Error("Network Error"));

      const result = await ListingSheetApiClient.getListingSheets();

      expect(result.data).toEqual([]);
      expect(result.error).toBe("Network Error");
    });

    it("should include pagination parameters", async () => {
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: { data: [] },
      });

      await ListingSheetApiClient.getListingSheets({
        page: 2,
        pageSize: 10,
        sortBy: "createdAt",
        sortOrder: "desc",
      });

      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringContaining("pagination%5Bpage%5D=2")
      );
      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringContaining("pagination%5BpageSize%5D=10")
      );
      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringContaining("sort=createdAt%3Adesc")
      );
    });
  });

  describe("getListingSheet", () => {
    it("should fetch a single listing sheet", async () => {
      const mockResponse = {
        success: true,
        data: {
          data: {
            id: "1",
            slug: "test-listing",
            title: "Test Listing",
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z",
            user: "user-1",
            sheet: {},
          },
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await ListingSheetApiClient.getListingSheet("1");

      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(mockApiClient.get).toHaveBeenCalledWith(
        "/listing-sheets/1?populate=user"
      );
    });

    it("should handle not found errors", async () => {
      mockApiClient.get.mockResolvedValue({
        success: false,
        error: "Not found",
      });

      const result = await ListingSheetApiClient.getListingSheet("999");

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Not found");
    });
  });

  describe("getListingSheetBySlug", () => {
    it("should fetch listing sheet by slug", async () => {
      const mockResponse = {
        success: true,
        data: {
          data: [
            {
              id: "1",
              slug: "test-listing",
              title: "Test Listing",
              createdAt: "2024-01-01T00:00:00Z",
              updatedAt: "2024-01-01T00:00:00Z",
              user: "user-1",
              sheet: {},
            },
          ],
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await ListingSheetApiClient.getListingSheetBySlug(
        "test-listing"
      );

      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(mockApiClient.get).toHaveBeenCalledWith(
        expect.stringContaining("filters%5Bslug%5D%5B%24eq%5D=test-listing")
      );
    });

    it("should handle slug not found", async () => {
      mockApiClient.get.mockResolvedValue({
        success: true,
        data: { data: [] },
      });

      const result = await ListingSheetApiClient.getListingSheetBySlug(
        "non-existent"
      );

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Listing sheet not found");
    });
  });

  describe("createListingSheet", () => {
    it("should create a new listing sheet", async () => {
      const mockPayload = {
        user: "user-1",
        sheet: {
          askingPrice: 500000,
          years: 25,
          frequency: "monthly" as const,
        },
        title: "Test Listing",
        slug: "test-listing",
      };

      const mockResponse = {
        success: true,
        data: {
          data: {
            id: "1",
            ...mockPayload,
            createdAt: "2024-01-01T00:00:00Z",
            updatedAt: "2024-01-01T00:00:00Z",
          },
        },
      };

      mockApiClient.post.mockResolvedValue(mockResponse);

      const result = await ListingSheetApiClient.createListingSheet(
        mockPayload
      );

      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(mockApiClient.post).toHaveBeenCalledWith("/listing-sheets", {
        data: mockPayload,
      });
    });

    it("should handle creation errors", async () => {
      const mockPayload = {
        user: "user-1",
        sheet: {},
        title: "Test Listing",
        slug: "test-listing",
      };

      mockApiClient.post.mockResolvedValue({
        success: false,
        error: "Validation error",
      });

      const result = await ListingSheetApiClient.createListingSheet(
        mockPayload
      );

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Validation error");
    });
  });

  describe("updateListingSheet", () => {
    it("should update an existing listing sheet", async () => {
      const mockPayload = {
        user: "user-1",
        sheet: {
          askingPrice: 600000,
          years: 30,
          frequency: "biweekly" as const,
        },
        title: "Updated Listing",
        slug: "updated-listing",
      };

      const mockResponse = {
        success: true,
        data: {
          data: {
            id: "1",
            ...mockPayload,
            updatedAt: "2024-01-02T00:00:00Z",
          },
        },
      };

      mockApiClient.put.mockResolvedValue(mockResponse);

      const result = await ListingSheetApiClient.updateListingSheet(
        "1",
        mockPayload
      );

      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(mockApiClient.put).toHaveBeenCalledWith("/listing-sheets/1", {
        data: mockPayload,
      });
    });
  });

  describe("deleteListingSheet", () => {
    it("should delete a listing sheet", async () => {
      mockApiClient.delete.mockResolvedValue({ success: true });

      const result = await ListingSheetApiClient.deleteListingSheet("1");

      expect(result.success).toBe(true);
      expect(result.error).toBeUndefined();
      expect(mockApiClient.delete).toHaveBeenCalledWith("/listing-sheets/1");
    });

    it("should handle deletion errors", async () => {
      mockApiClient.delete.mockResolvedValue({
        success: false,
        error: "Not found",
      });

      const result = await ListingSheetApiClient.deleteListingSheet("999");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Not found");
    });
  });

  describe("uploadFile", () => {
    it("should upload a file successfully", async () => {
      const mockFile = new File(["test"], "test.jpg", { type: "image/jpeg" });
      const mockResponse = {
        ok: true,
        json: () =>
          Promise.resolve({ id: 1, url: "http://example.com/test.jpg" }),
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
      mockGetCookie.mockReturnValue("mock-jwt-token");

      const result = await ListingSheetApiClient.uploadFile(
        mockFile,
        "propertyPhoto"
      );

      expect(result.data).toBeDefined();
      expect(result.error).toBeUndefined();
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining("/api/upload"),
        expect.objectContaining({
          method: "POST",
          headers: {
            Authorization: "Bearer mock-jwt-token",
          },
        })
      );
    });

    it("should handle upload errors", async () => {
      const mockFile = new File(["test"], "test.jpg", { type: "image/jpeg" });

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 400,
      });
      mockGetCookie.mockReturnValue("mock-jwt-token");

      const result = await ListingSheetApiClient.uploadFile(mockFile);

      expect(result.data).toBeUndefined();
      expect(result.error).toContain("HTTP error");
    });

    it("should handle missing authentication", async () => {
      const mockFile = new File(["test"], "test.jpg", { type: "image/jpeg" });
      mockGetCookie.mockReturnValue(null);

      const result = await ListingSheetApiClient.uploadFile(mockFile);

      expect(result.data).toBeUndefined();
      expect(result.error).toBe("Authentication required");
    });
  });

  describe("utility functions", () => {
    describe("generateSlug", () => {
      it("should generate slug with MLS code", () => {
        const slug = ListingSheetApiClient.generateSlug("MLS123", 500000);
        expect(slug).toMatch(/^mls-mls123-/);
      });

      it("should generate slug with asking price", () => {
        const slug = ListingSheetApiClient.generateSlug(undefined, 500000);
        expect(slug).toMatch(/^price-500000-/);
      });

      it("should generate default slug", () => {
        const slug = ListingSheetApiClient.generateSlug();
        expect(slug).toMatch(/^listing-/);
      });
    });

    describe("generateTitle", () => {
      it("should generate title with MLS code", () => {
        const title = ListingSheetApiClient.generateTitle("MLS123");
        expect(title).toBe("MLS: MLS123");
      });

      it("should generate title with asking price", () => {
        const title = ListingSheetApiClient.generateTitle(undefined, 500000);
        expect(title).toBe("Price: $500,000");
      });

      it("should generate default title", () => {
        const title = ListingSheetApiClient.generateTitle();
        expect(title).toBe("Listing Sheet");
      });
    });

    describe("validateListingSheetData", () => {
      it("should validate complete data", () => {
        const validData = {
          sheet: {
            askingPrice: 500000,
            years: 25,
            chosenDownPay: { percent: "10%", amount: 50000, rate: "rate10" },
            realtor: {
              firstname: "John",
              lastname: "Doe",
              email: "<EMAIL>",
              position: "Agent",
              company: "ABC Realty",
              phone: "************",
            },
          },
        };

        const result =
          ListingSheetApiClient.validateListingSheetData(validData);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it("should identify missing required fields", () => {
        const invalidData = {
          sheet: {
            // Missing askingPrice, years, chosenDownPay
          },
        };

        const result =
          ListingSheetApiClient.validateListingSheetData(invalidData);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
        expect(result.errors.some((e) => e.field === "askingPrice")).toBe(true);
      });

      it("should validate realtor information", () => {
        const dataWithIncompleteRealtor = {
          sheet: {
            askingPrice: 500000,
            years: 25,
            chosenDownPay: { percent: "10%", amount: 50000, rate: "rate10" },
            realtor: {
              firstname: "John",
              // Missing lastname and email
            },
          },
        };

        const result = ListingSheetApiClient.validateListingSheetData(
          dataWithIncompleteRealtor
        );
        expect(result.isValid).toBe(false);
        expect(result.errors.some((e) => e.field === "realtor.lastname")).toBe(
          true
        );
        expect(result.errors.some((e) => e.field === "realtor.email")).toBe(
          true
        );
      });
    });
  });
});
