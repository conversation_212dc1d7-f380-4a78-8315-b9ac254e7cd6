import {
  getPaymentFrequencyMultiplier,
  getPaymentFrequencyLabel,
  convertPaymentFrequency,
  calculateAllFrequencyPayments,
  validateAmortizationPeriod,
  calculateTotalPayments,
  calculateAcceleratedSavings,
  getRecommendedFrequency,
  calculatePaymentSchedule,
  getAllPaymentFrequencies,
} from "../payment-frequency-utils";

describe("Payment Frequency Utils", () => {
  describe("getPaymentFrequencyMultiplier", () => {
    it("should return correct multipliers", () => {
      expect(getPaymentFrequencyMultiplier("monthly")).toBe(12);
      expect(getPaymentFrequencyMultiplier("biweekly")).toBe(26);
      expect(getPaymentFrequencyMultiplier("weekly")).toBe(52);
      expect(getPaymentFrequencyMultiplier("accbiweekly")).toBe(26);
      expect(getPaymentFrequencyMultiplier("accweekly")).toBe(52);
    });
  });

  describe("getPaymentFrequencyLabel", () => {
    it("should return correct labels", () => {
      expect(getPaymentFrequencyLabel("monthly")).toBe("Monthly");
      expect(getPaymentFrequencyLabel("biweekly")).toBe("Bi-Weekly");
      expect(getPaymentFrequencyLabel("accbiweekly")).toBe(
        "Accelerated Bi-Weekly"
      );
    });
  });

  describe("convertPaymentFrequency", () => {
    it("should convert monthly to biweekly correctly", () => {
      const result = convertPaymentFrequency(2000, "monthly", "biweekly");
      expect(result).toBeCloseTo((2000 * 12) / 26, 2);
    });

    it("should convert biweekly to monthly correctly", () => {
      const biweeklyAmount = 923.08; // Approximately $2000 monthly
      const result = convertPaymentFrequency(
        biweeklyAmount,
        "biweekly",
        "monthly"
      );
      expect(result).toBeCloseTo(2000, 0);
    });

    it("should handle zero amounts", () => {
      expect(convertPaymentFrequency(0, "monthly", "biweekly")).toBe(0);
    });

    it("should handle same frequency conversion", () => {
      expect(convertPaymentFrequency(2000, "monthly", "monthly")).toBe(2000);
    });
  });

  describe("calculateAllFrequencyPayments", () => {
    it("should calculate all frequency payments from monthly", () => {
      const payments = calculateAllFrequencyPayments(2000);

      expect(payments.monthly).toBe(2000);
      expect(payments.biweekly).toBeCloseTo((2000 * 12) / 26, 2);
      expect(payments.weekly).toBeCloseTo((2000 * 12) / 52, 2);
      expect(payments.accbiweekly).toBe(1000);
      expect(payments.accweekly).toBe(500);
    });

    it("should handle zero monthly payment", () => {
      const payments = calculateAllFrequencyPayments(0);

      expect(payments.monthly).toBe(0);
      expect(payments.biweekly).toBe(0);
      expect(payments.weekly).toBe(0);
      expect(payments.accbiweekly).toBe(0);
      expect(payments.accweekly).toBe(0);
    });
  });

  describe("validateAmortizationPeriod", () => {
    it("should validate pre-owned property limits", () => {
      const result = validateAmortizationPeriod(30, "preOwned");
      expect(result.isValid).toBe(false);
      expect(result.maxYears).toBe(25);
      expect(result.constrainedYears).toBe(25);
    });

    it("should validate new build property limits", () => {
      const result = validateAmortizationPeriod(25, "newBuild");
      expect(result.isValid).toBe(true);
      expect(result.maxYears).toBe(30);
      expect(result.constrainedYears).toBe(25);
    });

    it("should handle valid periods", () => {
      const result = validateAmortizationPeriod(20, "preOwned");
      expect(result.isValid).toBe(true);
      expect(result.constrainedYears).toBe(20);
    });
  });

  describe("calculateTotalPayments", () => {
    it("should calculate total payments correctly", () => {
      expect(calculateTotalPayments(25, "monthly")).toBe(300);
      expect(calculateTotalPayments(25, "biweekly")).toBe(650);
      expect(calculateTotalPayments(25, "weekly")).toBe(1300);
    });
  });

  describe("calculateAcceleratedSavings", () => {
    it("should calculate savings with accelerated payments", () => {
      const savings = calculateAcceleratedSavings(400000, 0.05, 25, 2500);

      expect(savings.timeReduction).toBeGreaterThan(0);
      expect(savings.interestSavings).toBeGreaterThan(0);
      expect(savings.totalPayments).toBeGreaterThan(0);
    });

    it("should handle zero values", () => {
      const savings = calculateAcceleratedSavings(0, 0.05, 25, 1200);

      expect(savings.timeReduction).toBe(0);
      expect(savings.interestSavings).toBe(0);
      expect(savings.totalPayments).toBe(0);
    });
  });

  describe("getRecommendedFrequency", () => {
    it("should recommend monthly for low income ratio", () => {
      const frequency = getRecommendedFrequency(5000, 4500, "low");
      expect(frequency).toBe("monthly");
    });

    it("should recommend biweekly for medium income ratio", () => {
      const frequency = getRecommendedFrequency(5000, 3000, "medium");
      expect(frequency).toBe("biweekly");
    });

    it("should recommend accelerated for high income ratio", () => {
      const frequency = getRecommendedFrequency(5000, 2000, "high");
      expect(frequency).toBe("accbiweekly");
    });
  });

  describe("calculatePaymentSchedule", () => {
    it("should generate payment schedule", () => {
      const schedule = calculatePaymentSchedule(400000, 0.05, 25, "monthly", 6);

      expect(schedule).toHaveLength(6);
      expect(schedule[0].paymentNumber).toBe(1);
      expect(schedule[0].remainingBalance).toBeLessThan(400000);
      expect(schedule[0].principalPayment).toBeGreaterThan(0);
      expect(schedule[0].interestPayment).toBeGreaterThan(0);
    });

    it("should handle zero values", () => {
      const schedule = calculatePaymentSchedule(0, 0.05, 25, "monthly", 6);
      expect(schedule).toHaveLength(0);
    });
  });

  describe("getAllPaymentFrequencies", () => {
    it("should return all frequency options", () => {
      const frequencies = getAllPaymentFrequencies();

      expect(frequencies).toHaveLength(5);
      expect(frequencies[0]).toEqual({ value: "monthly", label: "Monthly" });
      expect(frequencies[4]).toEqual({
        value: "accweekly",
        label: "Accelerated Weekly",
      });
    });
  });
});
