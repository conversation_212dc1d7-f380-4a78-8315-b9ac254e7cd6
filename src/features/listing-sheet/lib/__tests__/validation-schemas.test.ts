import {
  propertyDetailsSchema,
  realtorInfoSchema,
  mortgageCalculatorSchema,
  expenseCalculationSchema,
  validateFileUpload,
  validateCustomDownPayment,
  validateAmortizationForPropertyType,
  getValidationErrorMessage,
  getFieldErrors,
} from "../validation-schemas";

describe("Validation Schemas", () => {
  describe("propertyDetailsSchema", () => {
    it("should validate valid property details", () => {
      const validData = {
        askingPrice: 500000,
        mlsCode: "MLS123",
        address: "123 Main Street, Toronto, ON",
      };

      const result = propertyDetailsSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject invalid asking price", () => {
      const invalidData = {
        askingPrice: 500, // Too low
        mlsCode: "MLS123",
      };

      const result = propertyDetailsSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should reject invalid MLS code", () => {
      const invalidData = {
        askingPrice: 500000,
        mlsCode: "MLS@123!", // Invalid characters
      };

      const result = propertyDetailsSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should accept optional fields", () => {
      const minimalData = {
        askingPrice: 500000,
      };

      const result = propertyDetailsSchema.safeParse(minimalData);
      expect(result.success).toBe(true);
    });
  });

  describe("realtorInfoSchema", () => {
    it("should validate complete realtor info", () => {
      const validData = {
        firstname: "John",
        lastname: "Doe",
        position: "Real Estate Agent",
        company: "ABC Realty",
        email: "<EMAIL>",
        phone: "+1234567890",
        website: "https://johndoe.com",
      };

      const result = realtorInfoSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should require mandatory fields", () => {
      const incompleteData = {
        firstname: "John",
        // Missing lastname, position, company, email, phone
      };

      const result = realtorInfoSchema.safeParse(incompleteData);
      expect(result.success).toBe(false);
    });

    it("should validate email format", () => {
      const invalidEmailData = {
        firstname: "John",
        lastname: "Doe",
        position: "Agent",
        company: "ABC Realty",
        email: "invalid-email",
        phone: "+1234567890",
      };

      const result = realtorInfoSchema.safeParse(invalidEmailData);
      expect(result.success).toBe(false);
    });

    it("should validate phone format", () => {
      const invalidPhoneData = {
        firstname: "John",
        lastname: "Doe",
        position: "Agent",
        company: "ABC Realty",
        email: "<EMAIL>",
        phone: "invalid-phone",
      };

      const result = realtorInfoSchema.safeParse(invalidPhoneData);
      expect(result.success).toBe(false);
    });
  });

  describe("mortgageCalculatorSchema", () => {
    it("should validate complete mortgage data", () => {
      const validData = {
        years: 25,
        frequency: "monthly" as const,
        customPercentage: 10,
        rate5: 5.5,
        rate10: 5.3,
        rate15: 5.1,
        rate20: 4.9,
        rateCustom: 5.2,
        rateFTHB: 5.4,
      };

      const result = mortgageCalculatorSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject invalid amortization years", () => {
      const invalidData = {
        years: 35, // Too high
        frequency: "monthly" as const,
        customPercentage: 10,
        rate5: 5.5,
        rate10: 5.3,
        rate15: 5.1,
        rate20: 4.9,
        rateCustom: 5.2,
        rateFTHB: 5.4,
      };

      const result = mortgageCalculatorSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it("should reject invalid interest rates", () => {
      const invalidData = {
        years: 25,
        frequency: "monthly" as const,
        customPercentage: 10,
        rate5: 25, // Too high
        rate10: 5.3,
        rate15: 5.1,
        rate20: 4.9,
        rateCustom: 5.2,
        rateFTHB: 5.4,
      };

      const result = mortgageCalculatorSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe("expenseCalculationSchema", () => {
    it("should validate all expense fields", () => {
      const validData = {
        propertyTax: 500,
        propertyInsurance: 200,
        utilities: 150,
        condoFees: 300,
        hoaFees: 100,
        monthlyDebtPayments: 800,
        phone: 80,
        cable: 60,
        internet: 70,
        lawyerFee: 1500,
        homeInspection: 600,
        appraisal: 400,
        titleInsurance: 300,
        estoppelFee: 200,
      };

      const result = expenseCalculationSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it("should reject negative values", () => {
      const invalidData = {
        propertyTax: -100, // Negative value
        propertyInsurance: 200,
        utilities: 150,
        condoFees: 300,
        hoaFees: 100,
        monthlyDebtPayments: 800,
        phone: 80,
        cable: 60,
        internet: 70,
        lawyerFee: 1500,
        homeInspection: 600,
        appraisal: 400,
        titleInsurance: 300,
        estoppelFee: 200,
      };

      const result = expenseCalculationSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe("validateFileUpload", () => {
    it("should validate correct file type and size", () => {
      const mockFile = new File(["test"], "test.jpg", {
        type: "image/jpeg",
        lastModified: Date.now(),
      });

      // Mock file size to be within limits
      Object.defineProperty(mockFile, "size", { value: 1024 * 1024 }); // 1MB

      const result = validateFileUpload(mockFile);
      expect(result.isValid).toBe(true);
    });

    it("should reject files that are too large", () => {
      const mockFile = new File(["test"], "test.jpg", {
        type: "image/jpeg",
        lastModified: Date.now(),
      });

      // Mock file size to exceed limits
      Object.defineProperty(mockFile, "size", { value: 10 * 1024 * 1024 }); // 10MB

      const result = validateFileUpload(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("File size must be less than");
    });

    it("should reject invalid file types", () => {
      const mockFile = new File(["test"], "test.pdf", {
        type: "application/pdf",
        lastModified: Date.now(),
      });

      const result = validateFileUpload(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Only JPEG and PNG files are allowed");
    });
  });

  describe("validateCustomDownPayment", () => {
    it("should validate sufficient down payment for normal tier", () => {
      const result = validateCustomDownPayment(10, 400000);
      expect(result.isValid).toBe(true);
    });

    it("should reject insufficient down payment for middle tier", () => {
      const result = validateCustomDownPayment(5, 800000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Minimum down payment required");
    });

    it("should reject insufficient down payment for max tier", () => {
      const result = validateCustomDownPayment(15, 2000000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Minimum down payment required");
    });

    it("should handle zero property price", () => {
      const result = validateCustomDownPayment(10, 0);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain("Property price is required");
    });
  });

  describe("validateAmortizationForPropertyType", () => {
    it("should validate correct amortization for new build", () => {
      const result = validateAmortizationForPropertyType(30, "newBuild");
      expect(result.isValid).toBe(true);
    });

    it("should reject excessive amortization for pre-owned", () => {
      const result = validateAmortizationForPropertyType(30, "preOwned");
      expect(result.isValid).toBe(false);
      expect(result.error).toContain(
        "Maximum amortization for preOwned properties is 25 years"
      );
    });

    it("should validate correct amortization for pre-owned", () => {
      const result = validateAmortizationForPropertyType(25, "preOwned");
      expect(result.isValid).toBe(true);
    });
  });

  describe("error handling utilities", () => {
    it("should extract validation error message", () => {
      const invalidData = { askingPrice: 500 };
      const result = propertyDetailsSchema.safeParse(invalidData);

      if (!result.success) {
        const message = getValidationErrorMessage(result.error);
        expect(message).toContain("Asking price must be at least");
      }
    });

    it("should extract field errors", () => {
      const invalidData = {
        askingPrice: 500,
        mlsCode: "INVALID@CODE",
      };
      const result = propertyDetailsSchema.safeParse(invalidData);

      if (!result.success) {
        const fieldErrors = getFieldErrors(result.error);
        expect(fieldErrors.askingPrice).toBeDefined();
        expect(fieldErrors.mlsCode).toBeDefined();
      }
    });
  });
});
