import { env } from "@/shared/lib/env";
import { getCookie } from "@/shared/lib/auth";

export interface UploadResult {
  id: string;
  url: string;
  ext: string;
  name: string;
  size: number;
  mime: string;
  formats?: any;
}

export interface UploadError {
  message: string;
  status?: number;
}

/**
 * Upload a file to Strapi V5
 * Returns the uploaded file data or throws an error
 */
export async function uploadFile(file: File): Promise<UploadResult> {
  try {
    const formData = new FormData();
    formData.append("files", file);

    // For FormData uploads, don't set Content-Type header - let browser set it with boundary
    const jwt = getCookie("jwt");
    if (!jwt) {
      throw new Error("No authentication token found");
    }

    const headers: { [key: string]: string } = {
      Authorization: `Bearer ${jwt}`,
    };

    const response = await fetch(
      `${env.API_URL}/api/upload`,
      {
        method: "POST",
        headers,
        body: formData,
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.error?.message || `Upload failed with status ${response.status}`
      );
    }

    const uploadResult = await response.json();
    const uploadedFile = uploadResult[0]; // Strapi returns array of uploaded files

    if (!uploadedFile || !uploadedFile.url) {
      throw new Error("No file data returned from upload");
    }

    return {
      id: uploadedFile.id,
      url: uploadedFile.url,
      ext: uploadedFile.ext,
      name: uploadedFile.name,
      size: uploadedFile.size,
      mime: uploadedFile.mime,
      formats: uploadedFile.formats,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Unknown upload error occurred");
  }
}

/**
 * Upload file and associate it with a Strapi entity using direct upload
 * This approach uploads and links the file in one request (Strapi V5 method)
 */
export async function uploadFileDirectly(
  file: File,
  entityType: string,
  entityId: string,
  fieldName: string
): Promise<UploadResult> {
  try {
    const formData = new FormData();
    formData.append("files", file);
    formData.append("ref", `api::${entityType.replace(/s$/, '')}.${entityType.replace(/s$/, '')}`); // api::realtor.realtor
    formData.append("refId", entityId);
    formData.append("field", fieldName);

    const jwt = getCookie("jwt");
    const headers: { [key: string]: string } = {};
    if (jwt) {
      headers.Authorization = `Bearer ${jwt}`;
    }

    const response = await fetch(
      `${env.API_URL}/api/upload`,
      {
        method: "POST",
        headers,
        body: formData,
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.error?.message || `Upload failed with status ${response.status}`
      );
    }

    const uploadResult = await response.json();
    const uploadedFile = uploadResult[0];

    if (!uploadedFile || !uploadedFile.url) {
      throw new Error("No file data returned from upload");
    }

    return {
      id: uploadedFile.id,
      url: uploadedFile.url,
      ext: uploadedFile.ext,
      name: uploadedFile.name,
      size: uploadedFile.size,
      mime: uploadedFile.mime,
      formats: uploadedFile.formats,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Unknown upload error occurred");
  }
}

/**
 * Upload file and associate it with a Strapi entity (2-step process)
 * This is an alternative approach: upload file first, then update entity
 */
export async function uploadFileAndAssociate(
  file: File,
  entityType: string,
  entityId: string,
  fieldName: string
): Promise<UploadResult> {
  try {
    // Step 1: Upload the file
    const uploadedFile = await uploadFile(file);

    // Step 2: Associate the file with the entity
    const updateData = {
      data: {
        [fieldName]: uploadedFile.id,
      },
    };

    const jwt = getCookie("jwt");
    const updateHeaders: { [key: string]: string } = {
      "Content-Type": "application/json",
    };
    if (jwt) {
      updateHeaders.Authorization = `Bearer ${jwt}`;
    }

    const updateResponse = await fetch(
      `${env.API_URL}/api/${entityType}/${entityId}`,
      {
        method: "PUT",
        headers: updateHeaders,
        body: JSON.stringify(updateData),
      }
    );

    if (!updateResponse.ok) {
      // File was uploaded but association failed
      console.warn("File uploaded but failed to associate with entity");
    }

    return uploadedFile;
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("Unknown upload error occurred");
  }
}

/**
 * Validate file before upload
 */
export function validateFile(file: File): { isValid: boolean; error?: string } {
  const MAX_SIZE = 5 * 1024 * 1024; // 5MB
  const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png"];

  if (file.size > MAX_SIZE) {
    return {
      isValid: false,
      error: "File size must be less than 5MB",
    };
  }

  if (!ALLOWED_TYPES.includes(file.type)) {
    return {
      isValid: false,
      error: "Only JPEG and PNG files are allowed",
    };
  }

  return { isValid: true };
}
