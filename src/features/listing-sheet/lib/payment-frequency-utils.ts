import {
  PAYMENT_FREQUENCY_MULTIPLIERS,
  AMORTIZATION_LIMITS,
} from "./constants";
import type { PaymentFrequency, HouseType } from "../types";

/**
 * Get payment frequency multiplier (payments per year)
 */
export function getPaymentFrequencyMultiplier(
  frequency: PaymentFrequency
): number {
  return PAYMENT_FREQUENCY_MULTIPLIERS[frequency];
}

/**
 * Get payment frequency display label
 */
export function getPaymentFrequencyLabel(frequency: PaymentFrequency): string {
  const labels: Record<PaymentFrequency, string> = {
    monthly: "Monthly",
    biweekly: "Bi-Weekly",
    weekly: "Weekly",
    accbiweekly: "Accelerated Bi-Weekly",
    accweekly: "Accelerated Weekly",
  };

  return labels[frequency];
}

/**
 * Convert between payment frequencies
 */
export function convertPaymentFrequency(
  amount: number,
  fromFrequency: PaymentFrequency,
  toFrequency: PaymentFrequency
): number {
  if (amount <= 0) return 0;

  // Convert to annual amount first
  const fromMultiplier = getPaymentFrequencyMultiplier(fromFrequency);
  const toMultiplier = getPaymentFrequencyMultiplier(toFrequency);

  const annualAmount = amount * fromMultiplier;
  return annualAmount / toMultiplier;
}

/**
 * Calculate payment amount for different frequencies
 */
export interface FrequencyPayments {
  monthly: number;
  biweekly: number;
  weekly: number;
  accbiweekly: number;
  accweekly: number;
}

export function calculateAllFrequencyPayments(
  monthlyPayment: number
): FrequencyPayments {
  if (monthlyPayment <= 0) {
    return {
      monthly: 0,
      biweekly: 0,
      weekly: 0,
      accbiweekly: 0,
      accweekly: 0,
    };
  }

  return {
    monthly: monthlyPayment,
    biweekly: convertPaymentFrequency(monthlyPayment, "monthly", "biweekly"),
    weekly: convertPaymentFrequency(monthlyPayment, "monthly", "weekly"),
    accbiweekly: monthlyPayment / 2, // Accelerated is half monthly
    accweekly: monthlyPayment / 4, // Accelerated is quarter monthly
  };
}

/**
 * Validate amortization period based on property type
 */
export function validateAmortizationPeriod(
  years: number,
  houseType: HouseType
): {
  isValid: boolean;
  maxYears: number;
  constrainedYears: number;
} {
  let maxYears: number;

  switch (houseType) {
    case "preOwned":
      maxYears = AMORTIZATION_LIMITS.PRE_OWNED_MAX;
      break;
    case "newBuild":
      maxYears = AMORTIZATION_LIMITS.NEW_BUILD_MAX;
      break;
    default:
      maxYears = AMORTIZATION_LIMITS.DEFAULT_MAX;
  }

  const constrainedYears = Math.min(years, maxYears);
  const isValid = years <= maxYears;

  return {
    isValid,
    maxYears,
    constrainedYears,
  };
}

/**
 * Calculate total number of payments for amortization
 */
export function calculateTotalPayments(
  years: number,
  frequency: PaymentFrequency
): number {
  const multiplier = getPaymentFrequencyMultiplier(frequency);
  return years * multiplier;
}

/**
 * Calculate payment savings with accelerated payments
 */
export interface AcceleratedSavings {
  timeReduction: number; // Years saved
  interestSavings: number; // Total interest saved
  totalPayments: number; // Total payments with accelerated schedule
}

export function calculateAcceleratedSavings(
  principal: number,
  rate: number,
  amortizationYears: number,
  acceleratedPayment: number
): AcceleratedSavings {
  if (principal <= 0 || rate <= 0 || acceleratedPayment <= 0) {
    return {
      timeReduction: 0,
      interestSavings: 0,
      totalPayments: 0,
    };
  }

  const monthlyRate = rate / 12;
  let balance = principal;
  let totalPayments = 0;
  let months = 0;
  const maxMonths = amortizationYears * 12;

  // Calculate accelerated payoff
  while (balance > 0.01 && months < maxMonths) {
    const interestPayment = balance * monthlyRate;
    const principalPayment = Math.min(
      acceleratedPayment - interestPayment,
      balance
    );

    if (principalPayment <= 0) break;

    balance -= principalPayment;
    totalPayments += acceleratedPayment;
    months++;
  }

  // Calculate regular payoff for comparison
  const regularMonthlyPayment = calculateRegularMonthlyPayment(
    principal,
    rate,
    amortizationYears
  );
  const regularTotalInterest =
    regularMonthlyPayment * amortizationYears * 12 - principal;
  const acceleratedTotalInterest = totalPayments - principal;

  return {
    timeReduction: Math.max(0, amortizationYears - months / 12),
    interestSavings: Math.max(
      0,
      regularTotalInterest - acceleratedTotalInterest
    ),
    totalPayments: months,
  };
}

/**
 * Helper function to calculate regular monthly payment
 */
function calculateRegularMonthlyPayment(
  principal: number,
  rate: number,
  years: number
): number {
  const monthlyRate = rate / 12;
  const numberOfPayments = years * 12;

  if (monthlyRate === 0) {
    return principal / numberOfPayments;
  }

  return (
    (monthlyRate * principal) /
    (1 - Math.pow(1 + monthlyRate, -numberOfPayments))
  );
}

/**
 * Get recommended payment frequency based on user profile
 */
export function getRecommendedFrequency(
  monthlyIncome: number,
  monthlyExpenses: number,
  riskTolerance: "low" | "medium" | "high" = "medium"
): PaymentFrequency {
  const disposableIncome = monthlyIncome - monthlyExpenses;
  const incomeRatio = disposableIncome / monthlyIncome;

  // Conservative approach for low income ratio
  if (incomeRatio < 0.2 || riskTolerance === "low") {
    return "monthly";
  }

  // Moderate acceleration for medium income ratio
  if (incomeRatio < 0.4 || riskTolerance === "medium") {
    return "biweekly";
  }

  // Aggressive acceleration for high income ratio
  return "accbiweekly";
}

/**
 * Calculate payment schedule breakdown
 */
export interface PaymentBreakdown {
  paymentNumber: number;
  paymentAmount: number;
  principalPayment: number;
  interestPayment: number;
  remainingBalance: number;
  cumulativeInterest: number;
}

export function calculatePaymentSchedule(
  principal: number,
  rate: number,
  years: number,
  frequency: PaymentFrequency,
  maxPayments: number = 12 // Limit for performance
): PaymentBreakdown[] {
  if (principal <= 0 || rate <= 0 || years <= 0) {
    return [];
  }

  const schedule: PaymentBreakdown[] = [];
  const periodicRate = rate / getPaymentFrequencyMultiplier(frequency);
  const totalPayments = calculateTotalPayments(years, frequency);
  const paymentAmount = calculateRegularMonthlyPayment(principal, rate, years);

  let balance = principal;
  let cumulativeInterest = 0;
  const paymentsToShow = Math.min(maxPayments, totalPayments);

  for (let i = 1; i <= paymentsToShow && balance > 0.01; i++) {
    const interestPayment = balance * periodicRate;
    const principalPayment = Math.min(paymentAmount - interestPayment, balance);

    balance -= principalPayment;
    cumulativeInterest += interestPayment;

    schedule.push({
      paymentNumber: i,
      paymentAmount,
      principalPayment,
      interestPayment,
      remainingBalance: balance,
      cumulativeInterest,
    });
  }

  return schedule;
}

/**
 * Format payment frequency for display
 */
export function formatPaymentFrequency(frequency: PaymentFrequency): string {
  return getPaymentFrequencyLabel(frequency);
}

/**
 * Get all available payment frequencies
 */
export function getAllPaymentFrequencies(): Array<{
  value: PaymentFrequency;
  label: string;
}> {
  return [
    { value: "monthly", label: "Monthly" },
    { value: "biweekly", label: "Bi-Weekly" },
    { value: "weekly", label: "Weekly" },
    { value: "accbiweekly", label: "Accelerated Bi-Weekly" },
    { value: "accweekly", label: "Accelerated Weekly" },
  ];
}
