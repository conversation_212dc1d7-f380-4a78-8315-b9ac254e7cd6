import { apiClient } from "@/shared/lib/api";
import { getCookie } from "@/shared/lib/auth";
import type {
  ListingSheet,
  ListingSheetCreatePayload,
  ListingSheetUpdatePayload,
  ListingSheetsResponse,
  ListingSheetResponse,
  ListingSheetError,
} from "../types";

/**
 * API client for listing sheet operations
 */
export class ListingSheetApiClient {
  private static readonly BASE_PATH = "/listing-sheets";

  /**
   * Get all listing sheets for the current user
   */
  static async getListingSheets(params?: {
    page?: number;
    pageSize?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  }): Promise<{ data: ListingSheet[]; error?: string }> {
    try {
      const queryParams = new URLSearchParams();

      if (params?.page)
        queryParams.append("pagination[page]", params.page.toString());
      if (params?.pageSize)
        queryParams.append("pagination[pageSize]", params.pageSize.toString());
      if (params?.sortBy)
        queryParams.append(
          "sort",
          `${params.sortBy}:${params.sortOrder || "desc"}`
        );

      // Filter by current user and recent sheets (90 days)
      const userId = getCookie("userId");
      if (userId) {
        queryParams.append("filters[user][id][$eq]", userId);
      }

      // Filter by creation date (last 90 days)
      const ninetyDaysAgo = new Date();
      ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
      queryParams.append(
        "filters[createdAt][$gte]",
        ninetyDaysAgo.toISOString()
      );

      // Populate user data
      queryParams.append("populate", "user");

      const url = `${this.BASE_PATH}?${queryParams.toString()}`;
      const response = await apiClient.get<ListingSheetsResponse>(url);

      if (response.success && response.data) {
        return { data: response.data.data || [] };
      }

      return {
        data: [],
        error: response.error || "Failed to fetch listing sheets",
      };
    } catch (error) {
      console.error("Error fetching listing sheets:", error);
      return {
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch listing sheets",
      };
    }
  }

  /**
   * Get a specific listing sheet by documentId
   */
  static async getListingSheet(
    documentId: string
  ): Promise<{ data?: ListingSheet; error?: string }> {
    try {
      const response = await apiClient.get<ListingSheetResponse>(
        `${this.BASE_PATH}/${documentId}?populate=user`
      );

      if (response.success && response.data) {
        return { data: response.data.data };
      }

      return { error: response.error || "Failed to fetch listing sheet" };
    } catch (error) {
      console.error("Error fetching listing sheet:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch listing sheet",
      };
    }
  }

  /**
   * Get a specific listing sheet by slug
   */
  static async getListingSheetBySlug(
    slug: string
  ): Promise<{ data?: ListingSheet; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("filters[slug][$eq]", slug);
      queryParams.append("populate", "user");

      const response = await apiClient.get<ListingSheetsResponse>(
        `${this.BASE_PATH}?${queryParams.toString()}`
      );

      if (
        response.success &&
        response.data?.data &&
        response.data.data.length > 0
      ) {
        return { data: response.data.data[0] };
      }

      return { error: "Listing sheet not found" };
    } catch (error) {
      console.error("Error fetching listing sheet by slug:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch listing sheet",
      };
    }
  }

  /**
   * Get a specific listing sheet by documentId
   */
  static async getListingSheetByDocumentId(
    documentId: string
  ): Promise<{ data?: ListingSheet; error?: string }> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append("populate", "user");

      const response = await apiClient.get<ListingSheetResponse>(
        `${this.BASE_PATH}/${documentId}?${queryParams.toString()}`
      );

      if (response.success && response.data) {
        return { data: response.data.data };
      }

      return { error: response.error || "Failed to fetch listing sheet" };
    } catch (error) {
      console.error("Error fetching listing sheet by documentId:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch listing sheet",
      };
    }
  }

  /**
   * Create a new listing sheet
   */
  static async createListingSheet(
    payload: ListingSheetCreatePayload
  ): Promise<{ data?: ListingSheet; error?: string }> {
    try {
      const response = await apiClient.post<ListingSheetResponse>(
        this.BASE_PATH,
        {
          data: payload,
        }
      );

      if (response.success && response.data) {
        return { data: response.data.data };
      }

      return { error: response.error || "Failed to create listing sheet" };
    } catch (error) {
      console.error("Error creating listing sheet:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to create listing sheet",
      };
    }
  }

  /**
   * Update an existing listing sheet
   */
  static async updateListingSheet(
    documentId: string,
    payload: ListingSheetUpdatePayload
  ): Promise<{ data?: ListingSheet; error?: string }> {
    try {
      const url = `${this.BASE_PATH}/${documentId}`;
      console.log("Update URL:", url);
      console.log("Update payload:", payload);
      
      const response = await apiClient.put<ListingSheetResponse>(
        url,
        {
          data: payload,
        }
      );

      if (response.success && response.data) {
        return { data: response.data.data };
      }

      return { error: response.error || "Failed to update listing sheet" };
    } catch (error) {
      console.error("Error updating listing sheet:", error);
      return {
        error:
          error instanceof Error
            ? error.message
            : "Failed to update listing sheet",
      };
    }
  }

  /**
   * Delete a listing sheet
   */
  static async deleteListingSheet(
    documentId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await apiClient.delete(`${this.BASE_PATH}/${documentId}`);

      if (response.success) {
        return { success: true };
      }

      return {
        success: false,
        error: response.error || "Failed to delete listing sheet",
      };
    } catch (error) {
      console.error("Error deleting listing sheet:", error);
      return {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete listing sheet",
      };
    }
  }

  /**
   * Upload file for listing sheet (property photo, realtor photo)
   */
  static async uploadFile(
    file: File,
    identifier?: string
  ): Promise<{ data?: any; error?: string }> {
    try {
      const formData = new FormData();
      formData.append("files", file);

      if (identifier) {
        formData.append("identifier", identifier);
      }

      const jwt = getCookie("jwt");
      if (!jwt) {
        return { error: "Authentication required" };
      }

      const response = await fetch(
        `${
          process.env.NEXT_PUBLIC_STRAPI_URL || "http://localhost:1337"
        }/api/upload`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { data };
    } catch (error) {
      console.error("Error uploading file:", error);
      return {
        error: error instanceof Error ? error.message : "Failed to upload file",
      };
    }
  }

  /**
   * Generate unique slug for listing sheet
   */
  static generateSlug(mlsCode?: string, askingPrice?: number): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);

    if (mlsCode) {
      const cleanMlsCode = mlsCode.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase();
      return `mls-${cleanMlsCode}-${timestamp}${random}`;
    }

    if (askingPrice) {
      return `price-${askingPrice}-${timestamp}${random}`;
    }

    return `listing-${timestamp}${random}`;
  }

  /**
   * Generate title for listing sheet
   */
  static generateTitle(mlsCode?: string, askingPrice?: number): string {
    if (mlsCode) {
      return `MLS: ${mlsCode}`;
    }

    if (askingPrice) {
      return `Price: $${askingPrice.toLocaleString()}`;
    }

    return "Listing Sheet";
  }

  /**
   * Validate listing sheet data before submission
   */
  static validateListingSheetData(data: Partial<ListingSheetCreatePayload>): {
    isValid: boolean;
    errors: ListingSheetError[];
  } {
    const errors: ListingSheetError[] = [];

    // Validate required fields
    if (!data.sheet?.askingPrice || data.sheet.askingPrice <= 0) {
      errors.push({
        message: "Asking price is required and must be greater than 0",
        field: "askingPrice",
        code: "REQUIRED_FIELD",
      });
    }

    if (!data.sheet?.years || data.sheet.years <= 0) {
      errors.push({
        message: "Amortization period is required",
        field: "years",
        code: "REQUIRED_FIELD",
      });
    }

    if (!data.sheet?.chosenDownPay || !data.sheet.chosenDownPay.percent) {
      errors.push({
        message: "Down payment selection is required",
        field: "chosenDownPay",
        code: "REQUIRED_FIELD",
      });
    }

    // Validate realtor information if provided
    if (data.sheet?.realtor) {
      const realtor = data.sheet.realtor;

      if (!realtor.firstname?.trim()) {
        errors.push({
          message: "Realtor first name is required",
          field: "realtor.firstname",
          code: "REQUIRED_FIELD",
        });
      }

      if (!realtor.lastname?.trim()) {
        errors.push({
          message: "Realtor last name is required",
          field: "realtor.lastname",
          code: "REQUIRED_FIELD",
        });
      }

      if (!realtor.email?.trim()) {
        errors.push({
          message: "Realtor email is required",
          field: "realtor.email",
          code: "REQUIRED_FIELD",
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export convenience functions
export const {
  getListingSheets,
  getListingSheet,
  getListingSheetBySlug,
  getListingSheetByDocumentId,
  createListingSheet,
  updateListingSheet,
  deleteListingSheet,
  uploadFile,
  generateSlug,
  generateTitle,
  validateListingSheetData,
} = ListingSheetApiClient;
