import { useState, useCallback } from "react";
import { ListingSheetApiClient } from "../lib/api-client";
import { validateFileUpload } from "../lib/validation-schemas";
import type { FileUpload } from "../types";

interface UseFileUploadOptions {
  maxFileSize?: number;
  acceptedTypes?: string[];
  onUploadComplete?: (file: FileUpload, identifier?: string) => void;
  onUploadError?: (error: string) => void;
}

interface UseFileUploadResult {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  uploadedFiles: Record<string, FileUpload>;

  uploadFile: (
    file: File,
    identifier?: string
  ) => Promise<{ data?: FileUpload; error?: string }>;
  removeFile: (identifier: string) => void;
  clearError: () => void;
  resetUpload: () => void;

  // Preview utilities
  getFilePreview: (file: File) => Promise<string>;
  validateFile: (file: File) => { isValid: boolean; error?: string };
}

export function useFileUpload(
  options: UseFileUploadOptions = {}
): UseFileUploadResult {
  const { onUploadComplete, onUploadError } = options;

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<
    Record<string, FileUpload>
  >({});

  const uploadFile = useCallback(
    async (file: File, identifier?: string) => {
      try {
        setIsUploading(true);
        setError(null);
        setUploadProgress(0);

        // Validate file before upload
        const validation = validateFileUpload(file);
        if (!validation.isValid) {
          const errorMessage = validation.error || "Invalid file";
          setError(errorMessage);
          onUploadError?.(errorMessage);
          return { error: errorMessage };
        }

        // Simulate upload progress (since we can't track real progress with fetch)
        const progressInterval = setInterval(() => {
          setUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 100);

        const result = await ListingSheetApiClient.uploadFile(file, identifier);

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (result.error) {
          setError(result.error);
          onUploadError?.(result.error);
          return { error: result.error };
        }

        if (result.data && result.data.length > 0) {
          const uploadedFile: FileUpload = {
            url: result.data[0].url,
            ext: result.data[0].ext,
            formats: result.data[0].formats,
          };

          // Store uploaded file
          if (identifier) {
            setUploadedFiles((prev) => ({
              ...prev,
              [identifier]: uploadedFile,
            }));
          }

          onUploadComplete?.(uploadedFile, identifier);

          return { data: uploadedFile };
        }

        const errorMessage = "Upload failed - no file data returned";
        setError(errorMessage);
        onUploadError?.(errorMessage);
        return { error: errorMessage };
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Upload failed";
        setError(errorMessage);
        onUploadError?.(errorMessage);
        return { error: errorMessage };
      } finally {
        setIsUploading(false);
        setTimeout(() => setUploadProgress(0), 1000); // Reset progress after delay
      }
    },
    [onUploadComplete, onUploadError]
  );

  const removeFile = useCallback((identifier: string) => {
    setUploadedFiles((prev) => {
      const updated = { ...prev };
      delete updated[identifier];
      return updated;
    });
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const resetUpload = useCallback(() => {
    setIsUploading(false);
    setUploadProgress(0);
    setError(null);
    setUploadedFiles({});
  }, []);

  const getFilePreview = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith("image/")) {
        reject(new Error("File is not an image"));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error("Failed to read file"));
        }
      };
      reader.onerror = () => reject(new Error("Failed to read file"));
      reader.readAsDataURL(file);
    });
  }, []);

  const validateFile = useCallback((file: File) => {
    return validateFileUpload(file);
  }, []);

  return {
    isUploading,
    uploadProgress,
    error,
    uploadedFiles,
    uploadFile,
    removeFile,
    clearError,
    resetUpload,
    getFilePreview,
    validateFile,
  };
}
