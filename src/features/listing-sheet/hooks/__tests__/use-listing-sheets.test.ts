import { renderHook, act } from "@testing-library/react";
import { useListingSheets } from "../use-listing-sheets";
import { ListingSheetApiClient } from "../../lib/api-client";

// Mock the API client
jest.mock("../../lib/api-client");

const mockApiClient = ListingSheetApiClient as jest.Mocked<
  typeof ListingSheetApiClient
>;

describe("useListingSheets", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should initialize with empty state", () => {
    mockApiClient.getListingSheets.mockResolvedValue({ data: [] });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    expect(result.current.listingSheets).toEqual([]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("should fetch listing sheets on mount when autoFetch is true", async () => {
    const mockSheets = [
      {
        id: "1",
        slug: "test-sheet",
        title: "Test Sheet",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        user: "user-1",
        sheet: {},
      },
    ];

    mockApiClient.getListingSheets.mockResolvedValue({ data: mockSheets });

    const { result } = renderHook(() => useListingSheets());

    // Wait for the effect to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.listingSheets).toEqual(mockSheets);
    expect(result.current.isLoading).toBe(false);
    expect(mockApiClient.getListingSheets).toHaveBeenCalledWith({
      page: 1,
      pageSize: 20,
      sortBy: "createdAt",
      sortOrder: "desc",
    });
  });

  it("should handle fetch errors", async () => {
    mockApiClient.getListingSheets.mockResolvedValue({
      data: [],
      error: "Failed to fetch",
    });

    const { result } = renderHook(() => useListingSheets());

    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    expect(result.current.listingSheets).toEqual([]);
    expect(result.current.error).toBe("Failed to fetch");
  });

  it("should refetch data when refetch is called", async () => {
    const mockSheets = [
      {
        id: "1",
        slug: "test-sheet",
        title: "Test Sheet",
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
        user: "user-1",
        sheet: {},
      },
    ];

    mockApiClient.getListingSheets.mockResolvedValue({ data: mockSheets });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    await act(async () => {
      await result.current.refetch();
    });

    expect(result.current.listingSheets).toEqual(mockSheets);
    expect(mockApiClient.getListingSheets).toHaveBeenCalledTimes(1);
  });

  it("should create a new listing sheet", async () => {
    const newSheet = {
      id: "2",
      slug: "new-sheet",
      title: "New Sheet",
      createdAt: "2024-01-02T00:00:00Z",
      updatedAt: "2024-01-02T00:00:00Z",
      user: "user-1",
      sheet: {},
    };

    const createPayload = {
      user: "user-1",
      sheet: {},
      title: "New Sheet",
      slug: "new-sheet",
    };

    mockApiClient.createListingSheet.mockResolvedValue({ data: newSheet });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    let createResult;
    await act(async () => {
      createResult = await result.current.createListingSheet(createPayload);
    });

    expect(createResult).toEqual({ data: newSheet });
    expect(result.current.listingSheets).toContain(newSheet);
    expect(mockApiClient.createListingSheet).toHaveBeenCalledWith(
      createPayload
    );
  });

  it("should update an existing listing sheet", async () => {
    const existingSheet = {
      id: "1",
      slug: "test-sheet",
      title: "Test Sheet",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
      user: "user-1",
      sheet: {},
    };

    const updatedSheet = {
      ...existingSheet,
      title: "Updated Sheet",
      updatedAt: "2024-01-02T00:00:00Z",
    };

    const updatePayload = {
      user: "user-1",
      sheet: {},
      title: "Updated Sheet",
      slug: "test-sheet",
    };

    mockApiClient.getListingSheets.mockResolvedValue({ data: [existingSheet] });
    mockApiClient.updateListingSheet.mockResolvedValue({ data: updatedSheet });

    const { result } = renderHook(() => useListingSheets());

    // Wait for initial fetch
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    let updateResult;
    await act(async () => {
      updateResult = await result.current.updateListingSheet(
        "1",
        updatePayload
      );
    });

    expect(updateResult).toEqual({ data: updatedSheet });
    expect(result.current.listingSheets[0].title).toBe("Updated Sheet");
    expect(mockApiClient.updateListingSheet).toHaveBeenCalledWith(
      "1",
      updatePayload
    );
  });

  it("should delete a listing sheet", async () => {
    const existingSheet = {
      id: "1",
      slug: "test-sheet",
      title: "Test Sheet",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
      user: "user-1",
      sheet: {},
    };

    mockApiClient.getListingSheets.mockResolvedValue({ data: [existingSheet] });
    mockApiClient.deleteListingSheet.mockResolvedValue({ success: true });

    const { result } = renderHook(() => useListingSheets());

    // Wait for initial fetch
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 0));
    });

    let deleteResult;
    await act(async () => {
      deleteResult = await result.current.deleteListingSheet("1");
    });

    expect(deleteResult).toEqual({ success: true });
    expect(result.current.listingSheets).toHaveLength(0);
    expect(mockApiClient.deleteListingSheet).toHaveBeenCalledWith("1");
  });

  it("should get a single listing sheet", async () => {
    const sheet = {
      id: "1",
      slug: "test-sheet",
      title: "Test Sheet",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
      user: "user-1",
      sheet: {},
    };

    mockApiClient.getListingSheet.mockResolvedValue({ data: sheet });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    let getResult;
    await act(async () => {
      getResult = await result.current.getListingSheet("1");
    });

    expect(getResult).toEqual({ data: sheet });
    expect(mockApiClient.getListingSheet).toHaveBeenCalledWith("1");
  });

  it("should get a listing sheet by slug", async () => {
    const sheet = {
      id: "1",
      slug: "test-sheet",
      title: "Test Sheet",
      createdAt: "2024-01-01T00:00:00Z",
      updatedAt: "2024-01-01T00:00:00Z",
      user: "user-1",
      sheet: {},
    };

    mockApiClient.getListingSheetBySlug.mockResolvedValue({ data: sheet });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    let getResult;
    await act(async () => {
      getResult = await result.current.getListingSheetBySlug("test-sheet");
    });

    expect(getResult).toEqual({ data: sheet });
    expect(mockApiClient.getListingSheetBySlug).toHaveBeenCalledWith(
      "test-sheet"
    );
  });

  it("should handle API errors gracefully", async () => {
    mockApiClient.createListingSheet.mockResolvedValue({
      error: "Creation failed",
    });

    const { result } = renderHook(() => useListingSheets({ autoFetch: false }));

    let createResult;
    await act(async () => {
      createResult = await result.current.createListingSheet({
        user: "user-1",
        sheet: {},
        title: "Test",
        slug: "test",
      });
    });

    expect(createResult).toEqual({ error: "Creation failed" });
    expect(result.current.error).toBe("Creation failed");
  });
});
