import { renderHook, act } from "@testing-library/react";
import { usePDFGenerator } from "../use-pdf-generator";

describe("usePDFGenerator", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should initialize with correct default state", () => {
    const { result } = renderHook(() => usePDFGenerator());

    expect(result.current.isGenerating).toBe(false);
    expect(typeof result.current.generatePDF).toBe("function");
  });

  it("should generate PDF successfully", async () => {
    const { result } = renderHook(() => usePDFGenerator());

    const mockData = {
      askingPrice: 500000,
      years: 25,
      frequency: "monthly" as const,
      chosenDownPay: { rate: "rate20", percent: "20%", amount: 100000 },
    };

    let pdfResult;
    await act(async () => {
      pdfResult = await result.current.generatePDF(mockData as any);
    });

    expect(pdfResult).toEqual({
      success: true,
      pdfUrl: "/api/mock-pdf-url",
    });
  });

  it("should handle PDF generation errors", async () => {
    const { result } = renderHook(() => usePDFGenerator());

    // Mock console.error to avoid noise in tests
    const consoleSpy = jest.spyOn(console, "error").mockImplementation();

    // Force an error by passing invalid data
    const mockData = null;

    let pdfResult;
    await act(async () => {
      pdfResult = await result.current.generatePDF(mockData as any);
    });

    expect(pdfResult).toEqual({
      success: false,
      error: expect.any(String),
    });

    consoleSpy.mockRestore();
  });

  it("should set isGenerating to true during generation", async () => {
    const { result } = renderHook(() => usePDFGenerator());

    const mockData = {
      askingPrice: 500000,
      years: 25,
      frequency: "monthly" as const,
    };

    act(() => {
      result.current.generatePDF(mockData as any);
    });

    expect(result.current.isGenerating).toBe(true);

    // Wait for the generation to complete
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 2100)); // Wait longer than the mock delay
    });

    expect(result.current.isGenerating).toBe(false);
  });
});
