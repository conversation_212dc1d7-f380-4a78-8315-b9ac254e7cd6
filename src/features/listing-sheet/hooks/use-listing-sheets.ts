import { useState, useCallback, useEffect } from "react";
import { ListingSheetApiClient } from "../lib/api-client";
import type {
  ListingSheet,
  ListingSheetCreatePayload,
  ListingSheetUpdatePayload,
} from "../types";

interface UseListingSheetsOptions {
  autoFetch?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

interface UseListingSheetsResult {
  listingSheets: ListingSheet[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createListingSheet: (
    payload: ListingSheetCreatePayload
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  updateListingSheet: (
    documentId: string,
    payload: ListingSheetUpdatePayload
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  deleteListingSheet: (
    documentId: string
  ) => Promise<{ success: boolean; error?: string }>;
  getListingSheet: (
    documentId: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  getListingSheetBySlug: (
    slug: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
  getListingSheetByDocumentId: (
    documentId: string
  ) => Promise<{ data?: ListingSheet; error?: string }>;
}

export function useListingSheets(
  options: UseListingSheetsOptions = {}
): UseListingSheetsResult {
  const {
    autoFetch = true,
    page = 1,
    pageSize = 20,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = options;

  const [listingSheets, setListingSheets] = useState<ListingSheet[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refetch = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await ListingSheetApiClient.getListingSheets({
        page,
        pageSize,
        sortBy,
        sortOrder,
      });

      if (result.error) {
        setError(result.error);
        setListingSheets([]);
      } else {
        setListingSheets(result.data);
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheets";
      setError(errorMessage);
      setListingSheets([]);
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, sortBy, sortOrder]);

  const createListingSheet = useCallback(
    async (payload: ListingSheetCreatePayload) => {
      try {
        setError(null);

        const result = await ListingSheetApiClient.createListingSheet(payload);

        if (result.data) {
          // Add the new listing sheet to the beginning of the list
          setListingSheets((prev) => [result.data!, ...prev]);
        }

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to create listing sheet";
        setError(errorMessage);
        return { error: errorMessage };
      }
    },
    []
  );

  const updateListingSheet = useCallback(
    async (documentId: string, payload: ListingSheetUpdatePayload) => {
      try {
        setError(null);

        const result = await ListingSheetApiClient.updateListingSheet(
          documentId,
          payload
        );

        if (result.data) {
          // Update the listing sheet in the list
          setListingSheets((prev) =>
            prev.map((sheet) => (sheet.documentId === documentId ? result.data! : sheet))
          );
        }

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to update listing sheet";
        setError(errorMessage);
        return { error: errorMessage };
      }
    },
    []
  );

  const deleteListingSheet = useCallback(async (documentId: string) => {
    try {
      setError(null);

      const result = await ListingSheetApiClient.deleteListingSheet(documentId);

      if (result.success) {
        // Remove the listing sheet from the list
        setListingSheets((prev) => prev.filter((sheet) => sheet.documentId !== documentId));
      }

      return result;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to delete listing sheet";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, []);

  const getListingSheet = useCallback(async (documentId: string) => {
    try {
      setError(null);
      return await ListingSheetApiClient.getListingSheet(documentId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setError(errorMessage);
      return { error: errorMessage };
    }
  }, []);

  const getListingSheetBySlug = useCallback(async (slug: string) => {
    try {
      setError(null);
      return await ListingSheetApiClient.getListingSheetBySlug(slug);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setError(errorMessage);
      return { error: errorMessage };
    }
  }, []);

  const getListingSheetByDocumentId = useCallback(async (documentId: string) => {
    try {
      setError(null);
      return await ListingSheetApiClient.getListingSheetByDocumentId(documentId);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to fetch listing sheet";
      setError(errorMessage);
      return { error: errorMessage };
    }
  }, []);

  // Auto-fetch on mount if enabled
  useEffect(() => {
    if (autoFetch) {
      refetch();
    }
  }, [autoFetch, refetch]);

  return {
    listingSheets,
    isLoading,
    error,
    refetch,
    createListingSheet,
    updateListingSheet,
    deleteListingSheet,
    getListingSheet,
    getListingSheetBySlug,
    getListingSheetByDocumentId,
  };
}
