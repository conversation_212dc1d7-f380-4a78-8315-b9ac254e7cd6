import { useState, useCallback, useEffect } from "react";
import {
  calcDownPaymentMinRateByAmount,
  paymentCalc,
  getEffectiveRate,
  calculateMonthlyPaymentLegacy,
  periodicPayCalc,
  getDownTypeAmount,
  handleChosenRate,
  totalCashNeededCalc,
  monthlyExpensesCalc,
  yearlyExpensesCalc,
  chosenPeriodicPayCalc,
  formatCurrency,
} from "../lib/mortgage-calculations";
import { DEFAULT_VALUES } from "../lib/constants";
import type {
  MortgageCalculationData,
  PaymentFrequency,
  ChosenDownPayment,
  RangeRate,
  EffectiveRates,
  HouseType,
  UserInfo,
} from "../types";

interface UseMortgageCalculatorOptions {
  initialData?: Partial<MortgageCalculationData>;
  user?: UserInfo;
  autoCalculate?: boolean;
}

interface UseMortgageCalculatorResult {
  calcInfo: MortgageCalculationData;
  validation: string[];
  isCalculating: boolean;

  // Core calculation functions - EXACT LEGACY IMPLEMENTATION
  onChangeAmount: (value: number | undefined) => void;
  handleInitialCalc: () => void;
  calcDownPaymentMinRateByAmountHandler: () => void;
  paymentCalcHandler: (newAmount: number) => void;
  getEffectiveRateHandler: () => void;
  monthlyPayHandler: () => void;
  periodicPayCalcHandler: () => void;
  chosenPeriodicPayCalcHandler: () => void;
  totalCashNeededCalcHandler: () => void;
  monthlyExpensesCalcHandler: () => void;
  yearlyExpensesCalcHandler: () => void;

  // Form handlers
  onChangeRate: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onChangeFrequency: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  onChangeYears: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCustomPercentage: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleDownType: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  handleHouseType: (event: React.ChangeEvent<HTMLSelectElement>) => void;

  // Expense handlers
  handleChangePropertyTax: (value: number) => void;
  handleChangeTitleInsurance: (value: number) => void;
  handleChangeLawyerFee: (value: number) => void;
  handleChangeEstoppelFee: (value: number) => void;
  handleChangeHomeInspection: (value: number) => void;
  handleChangeAppraisal: (value: number) => void;
  handleChangeMonthlyDebt: (value: number) => void;
  handleChangeUtilities: (value: number) => void;
  handleChangeCondoFees: (value: number) => void;
  handleChangeHoaFees: (value: number) => void;
  handleChangePropertyInsurance: (value: number) => void;
  handleChangePhone: (value: number) => void;
  handleChangeCable: (value: number) => void;
  handleChangeInternet: (value: number) => void;

  // UI toggles
  dropdownToggleMortgagePayment: () => void;
  dropdownToggleAdditionalInfo: () => void;
  dropdownRealtorInfo: () => void;
  dropdownToggleImages: () => void;
  dropdownToggleMonthlyExpenses: () => void;
  dropdownToggleYearlyExpenses: () => void;

  // Validation
  handleValidation: () => string[];

  // File upload
  handleUploadComplete: (uploadedFile: any) => void;

  // Utility
  setCalcInfo: React.Dispatch<React.SetStateAction<MortgageCalculationData>>;
}

export function useMortgageCalculator(
  options: UseMortgageCalculatorOptions = {}
): UseMortgageCalculatorResult {
  const { initialData, user, autoCalculate = false } = options;

  // EXACT LEGACY INITIAL STATE
  const createInitialState = (): MortgageCalculationData => {
    const userInfo = user
      ? {
          id: user.id,
          photo: user.photo || { url: null },
          firstname: user.firstname || null,
          lastname: user.lastname || null,
          position: user.position || null,
          email: user.email || null,
          workEmail: user.workEmail || null,
          phone: user.phone || null,
          workPhone: user.workPhone || null,
          cellPhone: user.cellPhone || null,
          website: user.website || null,
          brokerage: user.brokerage || null,
          team: user.team || { id: null, logo: { url: null } },
        }
      : {
          id: "",
          photo: { url: null },
          firstname: null,
          lastname: null,
          position: null,
          email: null,
          workEmail: null,
          phone: null,
          workPhone: null,
          cellPhone: null,
          website: null,
          brokerage: null,
          team: { id: null, logo: { url: null } },
        };

    if (!initialData) {
      return {
        loading: false,
        saving: false,
        uploading: { status: false, type: null },
        amount: 0,
        askingPrice: 0,
        customPercentage: 0,
        downPay5: 0,
        downPay10: 0,
        downPay15: 0,
        downPay20: 0,
        downPayCustom: 0,
        downPayRange: 0,
        downPayFTHB: 0,
        chosenDownPay: { percent: "select", amount: 0, rate: "" },
        principal5: 0,
        principal10: 0,
        principal15: 0,
        principal20: 0,
        principalCustom: 0,
        principalRange: 0,
        principalFTHB: 0,
        chosenPrincipal: 0,
        rate5: 0,
        rate10: 0,
        rate15: 0,
        rate20: 0,
        rateCustom: 0,
        rateRange: 0,
        rateFTHB: 0,
        insurance5: 0,
        insurance10: 0,
        insurance15: 0,
        insurance20: 0,
        insuranceCustom: 0,
        insuranceRange: 0,
        insuranceFTHB: 0,
        monthlyPay5: 0,
        monthlyPay10: 0,
        monthlyPay15: 0,
        monthlyPay20: 0,
        monthlyPayCustom: 0,
        monthlyPayRange: 0,
        monthlyPayFTHB: 0,
        periodicPay5: 0,
        periodicPay10: 0,
        periodicPay15: 0,
        periodicPay20: 0,
        periodicPayCustom: 0,
        periodicPayRange: 0,
        periodicPayFTHB: 0,
        years: 0,
        customYears: 0,
        rate: 0,
        effectiveRates: {
          effectiveRate5: 0,
          effectiveRate10: 0,
          effectiveRate15: 0,
          effectiveRate20: 0,
          effectiveRateCustom: 0,
          effectiveRateRange: 0,
          effectiveRateFTHB: 0,
        },
        rangeRate: { type: "normalTier", rate: null },
        frequency: "monthly",
        numberOfPayments: 0,
        showMortgagePayment: true,
        showBodyCashNeeded: true,
        showBodyMonthlyExpenses: true,
        showBodyYearlyExpenses: true,
        showAdditionalInfo: true,
        showRealtorInfo: true,
        showImages: true,
        houseType: "select",
        estoppelFee: 0,
        lawyerFee: 0,
        titleInsurance: 0,
        homeInspection: 0,
        appraisal: 0,
        totalCashNeeded: 0,
        condoFees: 0,
        hoaFees: 0,
        monthlyDebtPayments: 0,
        utilities: 0,
        propertyInsurance: 0,
        propertyTax: 0,
        phone: 0,
        cable: 0,
        internet: 0,
        chosenPeriodicPay: 0,
        chosenDownPayExpense: {},
        totalMonthlyPayments: 0,
        totalYearlyPayments: 0,
        amortizationBalance: 0,
        pdf: {
          full: false,
          short: true,
          mlsCode: null,
          address: null,
          monthlyExpenses: true,
          cashNeeded: true,
          propertyPhoto: { url: null, ext: null },
          realtorPhoto: { url: null, ext: null },
          user: userInfo,
        },
        realtor: {
          firstname: "",
          middlename: null,
          lastname: "",
          position: "",
          company: "",
          email: "",
          phone: "",
          website: "",
          photo: null,
        },
      };
    } else {
      return {
        ...initialData,
        // Ensure rangeRate is always initialized
        rangeRate: initialData.rangeRate || { type: "normalTier", rate: null },
        pdf: {
          ...initialData.pdf,
          user: userInfo,
          realtor: { ...initialData.realtor },
        },
      } as MortgageCalculationData;
    }
  };

  const [calcInfo, setCalcInfo] =
    useState<MortgageCalculationData>(createInitialState);
  const [validation, setValidation] = useState<string[]>([]);
  const [isCalculating, setIsCalculating] = useState(false);

  // EXACT LEGACY IMPLEMENTATION - onChangeAmount
  const onChangeAmount = useCallback((e: number | undefined) => {
    const numericAmount = e || 0;

    setCalcInfo((prev) => ({
      ...prev,
      amount: numericAmount,
      askingPrice: numericAmount,
      pdf: { ...prev.pdf, full: false, short: true },
    }));

    // Calculate range rate immediately when amount changes
    setTimeout(() => calcDownPaymentMinRateByAmountHandler(), 0);
  }, []);

  const handleInitialCalc = useCallback(() => {
    calcDownPaymentMinRateByAmountHandler();
  }, []);

  // EXACT LEGACY IMPLEMENTATION - calcDownPaymentMinRateByAmount
  const calcDownPaymentMinRateByAmountHandler = useCallback(() => {
    const amount = parseFloat(calcInfo.amount.toString()) || 0;
    const result = calcDownPaymentMinRateByAmount(amount);

    if (result) {
      setCalcInfo((prevState) => ({
        ...prevState,
        ...result,
      }));
    }
  }, [calcInfo.amount]);

  // EXACT LEGACY IMPLEMENTATION - paymentCalc
  const paymentCalcHandler = useCallback(
    (newAmount: number) => {
      const result = paymentCalc(
        newAmount,
        calcInfo.customPercentage,
        calcInfo.rangeRate
      );

      setCalcInfo((prev) => ({
        ...prev,
        ...result,
      }));
    },
    [calcInfo.customPercentage, calcInfo.rangeRate]
  );

  // EXACT LEGACY IMPLEMENTATION - getEffectiveRate
  const getEffectiveRateHandler = useCallback(() => {
    const rates = {
      effectiveRate5: getEffectiveRate(calcInfo.rate5),
      effectiveRate10: getEffectiveRate(calcInfo.rate10),
      effectiveRate15: getEffectiveRate(calcInfo.rate15),
      effectiveRate20: getEffectiveRate(calcInfo.rate20),
      effectiveRateCustom: getEffectiveRate(calcInfo.rateCustom),
      effectiveRateRange: getEffectiveRate(calcInfo.rateRange),
      effectiveRateFTHB: getEffectiveRate(calcInfo.rateFTHB),
    };

    setCalcInfo((prevState) => ({
      ...prevState,
      effectiveRates: rates,
    }));
  }, [
    calcInfo.rate5,
    calcInfo.rate10,
    calcInfo.rate15,
    calcInfo.rate20,
    calcInfo.rateCustom,
    calcInfo.rateRange,
    calcInfo.rateFTHB,
  ]);

  // EXACT LEGACY IMPLEMENTATION - monthlyPay
  const monthlyPayHandler = useCallback(() => {
    const { years, customYears, effectiveRates } = calcInfo;

    setCalcInfo((prev) => ({
      ...prev,
      monthlyPay5: calculateMonthlyPaymentLegacy(
        calcInfo.rate5,
        calcInfo.principal5,
        years,
        customYears
      ),
      monthlyPay10: calculateMonthlyPaymentLegacy(
        calcInfo.rate10,
        calcInfo.principal10,
        years,
        customYears
      ),
      monthlyPay15: calculateMonthlyPaymentLegacy(
        calcInfo.rate15,
        calcInfo.principal15,
        years,
        customYears
      ),
      monthlyPay20: calculateMonthlyPaymentLegacy(
        calcInfo.rate20,
        calcInfo.principal20,
        years,
        customYears
      ),
      monthlyPayCustom: calculateMonthlyPaymentLegacy(
        calcInfo.rateCustom,
        calcInfo.principalCustom,
        years,
        customYears
      ),
      monthlyPayRange: calculateMonthlyPaymentLegacy(
        calcInfo.rateRange,
        calcInfo.principalRange,
        years,
        customYears
      ),
      monthlyPayFTHB: calculateMonthlyPaymentLegacy(
        calcInfo.rateFTHB,
        calcInfo.principalFTHB,
        years,
        customYears
      ),
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - periodicPayCalc
  const periodicPayCalcHandler = useCallback(() => {
    const result = periodicPayCalc(calcInfo.frequency, {
      monthlyPay5: calcInfo.monthlyPay5,
      monthlyPay10: calcInfo.monthlyPay10,
      monthlyPay15: calcInfo.monthlyPay15,
      monthlyPay20: calcInfo.monthlyPay20,
      monthlyPayCustom: calcInfo.monthlyPayCustom,
      monthlyPayRange: calcInfo.monthlyPayRange,
      monthlyPayFTHB: calcInfo.monthlyPayFTHB,
    });

    setCalcInfo((prev) => ({
      ...prev,
      ...result,
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - chosenPeriodicPayCalc
  const chosenPeriodicPayCalcHandler = useCallback(() => {
    const result = chosenPeriodicPayCalc(calcInfo);

    setCalcInfo((prev) => ({
      ...prev,
      ...result,
      amortizationBalance: result.chosenPrincipal,
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - totalCashNeededCalc
  const totalCashNeededCalcHandler = useCallback(() => {
    const total = totalCashNeededCalc(calcInfo);

    setCalcInfo((prev) => ({
      ...prev,
      totalCashNeeded: total,
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - monthlyExpensesCalc
  const monthlyExpensesCalcHandler = useCallback(() => {
    const total = monthlyExpensesCalc(calcInfo);

    setCalcInfo((prev) => ({
      ...prev,
      totalMonthlyPayments: total,
      amortizationBalance: calcInfo.chosenPrincipal,
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - yearlyExpensesCalc
  const yearlyExpensesCalcHandler = useCallback(() => {
    const total = yearlyExpensesCalc(calcInfo);

    setCalcInfo((prev) => ({
      ...prev,
      totalYearlyPayments: total,
      amortizationBalance: calcInfo.chosenPrincipal,
    }));
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - onChangeRate
  const onChangeRate = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { rangeRate } = calcInfo;
      const rateType = rangeRate.type;
      const rateTypeLimiter = () => {
        if (
          (rateType && rateType === "middleTier") ||
          (rateType && rateType === "maxTier")
        ) {
          return true;
        }
        return false;
      };

      if (e && e.target && e.target.value.length >= 0) {
        const value = e.target.value;
        switch (e.target.id) {
          case "rate5":
            setCalcInfo((prev) => ({
              ...prev,
              rate5: rateTypeLimiter() ? 0 : parseFloat(value) || 0,
            }));
            break;
          case "rate10":
            setCalcInfo((prev) => ({
              ...prev,
              rate10: rateType === "maxTier" ? 0 : parseFloat(value) || 0,
            }));
            break;
          case "rate15":
            setCalcInfo((prev) => ({
              ...prev,
              rate15: rateType === "maxTier" ? 0 : parseFloat(value) || 0,
            }));
            break;
          case "rate20":
            setCalcInfo((prev) => ({
              ...prev,
              rate20: parseFloat(value) || 0,
            }));
            break;
          case "rateCustom":
            setCalcInfo((prev) => ({
              ...prev,
              rateCustom: parseFloat(value) || 0,
            }));
            break;
          case "rateRange":
            setCalcInfo((prev) => ({
              ...prev,
              rateRange: rateType === "middleTier" ? parseFloat(value) || 0 : 0,
            }));
            break;
          case "rateFTHB":
            setCalcInfo((prev) => ({
              ...prev,
              rateFTHB: parseFloat(value) || 0,
            }));
            break;
          default:
            setCalcInfo((prev) => ({ ...prev, rate: parseFloat(value) || 0 }));
        }
      }
    },
    [calcInfo.rangeRate]
  );

  // EXACT LEGACY IMPLEMENTATION - onChangeFrequency
  const onChangeFrequency = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      setCalcInfo((prev) => ({
        ...prev,
        frequency: event.target.value as PaymentFrequency,
      }));
    },
    []
  );

  // EXACT LEGACY IMPLEMENTATION - onChangeYears
  const onChangeYears = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event && event.target && event.target.value.length > 0) {
        const inputYears = parseFloat(event.target.value);

        // Apply scenario-specific constraints (assuming scenarios state exists)
        const maxYears = 30; // Default, would need scenarios state for exact logic
        const constrainedYears = Math.min(inputYears, maxYears);

        if (inputYears > maxYears) {
          // Show some visual feedback that the value was constrained
          event.target.style.borderColor = "#e53935";
          setTimeout(() => {
            event.target.style.borderColor = "";
          }, 1500);
        }

        setCalcInfo((prev) => ({ ...prev, years: constrainedYears }));
      }
    },
    []
  );

  // EXACT LEGACY IMPLEMENTATION - handleCustomPercentage
  const handleCustomPercentage = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e && e.target && e.target.value.length > 0) {
        setCalcInfo((prev) => ({
          ...prev,
          customPercentage: parseFloat(e.target.value) || 0,
        }));
      }
    },
    []
  );

  // EXACT LEGACY IMPLEMENTATION - handleDownType
  const handleDownType = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      const amount = getDownTypeAmount(event.target.value, calcInfo);
      const index = event.target.selectedIndex;

      setCalcInfo((prev) => ({
        ...prev,
        chosenDownPay: {
          rate: event.target.options[index].id,
          percent: event.target.value,
          amount: parseFloat(amount.toString()),
        },
        chosenDownPayExpense: {
          percent: event.target.value,
          amount: parseFloat(amount.toString()),
        },
        amortizationBalance: calcInfo.chosenPrincipal,
      }));
    },
    [calcInfo]
  );

  // EXACT LEGACY IMPLEMENTATION - handleHouseType
  const handleHouseType = useCallback(
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      setCalcInfo((prev) => ({ ...prev, houseType: event.target.value }));
    },
    []
  );

  // EXACT LEGACY IMPLEMENTATION - Expense handlers
  const handleChangePropertyTax = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, propertyTax: value }));
  }, []);

  const handleChangeTitleInsurance = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, titleInsurance: value }));
  }, []);

  const handleChangeLawyerFee = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, lawyerFee: value }));
  }, []);

  const handleChangeEstoppelFee = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, estoppelFee: value }));
  }, []);

  const handleChangeHomeInspection = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, homeInspection: value }));
  }, []);

  const handleChangeAppraisal = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, appraisal: value }));
  }, []);

  const handleChangeMonthlyDebt = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, monthlyDebtPayments: value }));
  }, []);

  const handleChangeUtilities = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, utilities: value }));
  }, []);

  const handleChangeCondoFees = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, condoFees: value }));
  }, []);

  const handleChangeHoaFees = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, hoaFees: value }));
  }, []);

  const handleChangePropertyInsurance = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, propertyInsurance: value }));
  }, []);

  const handleChangePhone = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, phone: value }));
  }, []);

  const handleChangeCable = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, cable: value }));
  }, []);

  const handleChangeInternet = useCallback((value: number) => {
    setCalcInfo((prev) => ({ ...prev, internet: value }));
  }, []);

  // EXACT LEGACY IMPLEMENTATION - UI toggles
  const dropdownToggleMortgagePayment = useCallback(() => {
    setCalcInfo((prev) => ({
      ...prev,
      showMortgagePayment: !prev.showMortgagePayment,
    }));
  }, []);

  const dropdownToggleAdditionalInfo = useCallback(() => {
    setCalcInfo((prev) => ({
      ...prev,
      showAdditionalInfo: !prev.showAdditionalInfo,
    }));
  }, []);

  const dropdownRealtorInfo = useCallback(() => {
    setCalcInfo((prev) => ({
      ...prev,
      showRealtorInfo: !prev.showRealtorInfo,
    }));
  }, []);

  const dropdownToggleImages = useCallback(() => {
    setCalcInfo((prev) => ({ ...prev, showImages: !prev.showImages }));
  }, []);

  const dropdownToggleMonthlyExpenses = useCallback(() => {
    setCalcInfo((prev) => ({
      ...prev,
      showBodyMonthlyExpenses: !prev.showBodyMonthlyExpenses,
    }));
  }, []);

  const dropdownToggleYearlyExpenses = useCallback(() => {
    setCalcInfo((prev) => ({
      ...prev,
      showBodyYearlyExpenses: !prev.showBodyYearlyExpenses,
    }));
  }, []);

  // EXACT LEGACY IMPLEMENTATION - handleValidation
  const handleValidation = useCallback(() => {
    const invalid: string[] = [];
    const { askingPrice, years, chosenDownPay } = calcInfo;

    if (askingPrice === 0 || !askingPrice) {
      invalid.push("Please add the asking price.");
    }
    if (chosenDownPay.percent === "select" || !chosenDownPay) {
      invalid.push("Please select a down payment percentage.");
    }
    if (years === 0 || !years) {
      invalid.push("Please add an amortization period.");
    }

    setValidation(invalid);
    return invalid;
  }, [calcInfo]);

  // EXACT LEGACY IMPLEMENTATION - handleUploadComplete
  const handleUploadComplete = useCallback((uploadedFile: unknown) => {
    const file = uploadedFile as Record<string, unknown>;
    if (file && file.identifier === "propertyPhoto") {
      if ((file.url as string)?.length > 0) {
        setCalcInfo((prev) => ({
          ...prev,
          pdf: {
            ...prev.pdf,
            propertyPhoto: { url: file.url as string, ext: file.ext as string },
          },
        }));
      }
    }

    if (file && file.identifier === "realtorPhoto") {
      if ((file.url as string)?.length > 0) {
        setCalcInfo((prev) => ({
          ...prev,
          pdf: {
            ...prev.pdf,
            realtorPhoto: { url: file.url as string, ext: file.ext as string },
          },
        }));
      }
    }
  }, []);

  return {
    calcInfo,
    validation,
    isCalculating,
    onChangeAmount,
    handleInitialCalc,
    calcDownPaymentMinRateByAmountHandler,
    paymentCalcHandler,
    getEffectiveRateHandler,
    monthlyPayHandler,
    periodicPayCalcHandler,
    chosenPeriodicPayCalcHandler,
    totalCashNeededCalcHandler,
    monthlyExpensesCalcHandler,
    yearlyExpensesCalcHandler,
    onChangeRate,
    onChangeFrequency,
    onChangeYears,
    handleCustomPercentage,
    handleDownType,
    handleHouseType,
    handleChangePropertyTax,
    handleChangeTitleInsurance,
    handleChangeLawyerFee,
    handleChangeEstoppelFee,
    handleChangeHomeInspection,
    handleChangeAppraisal,
    handleChangeMonthlyDebt,
    handleChangeUtilities,
    handleChangeCondoFees,
    handleChangeHoaFees,
    handleChangePropertyInsurance,
    handleChangePhone,
    handleChangeCable,
    handleChangeInternet,
    dropdownToggleMortgagePayment,
    dropdownToggleAdditionalInfo,
    dropdownRealtorInfo,
    dropdownToggleImages,
    dropdownToggleMonthlyExpenses,
    dropdownToggleYearlyExpenses,
    handleValidation,
    handleUploadComplete,
    setCalcInfo,
  };
}
