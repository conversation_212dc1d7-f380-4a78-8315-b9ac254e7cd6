"use client";

import { useState, useEffect, useCallback } from "react";
import { env } from "@/shared/lib/env";
import { getAuthHeaders, getCookie } from "@/shared/lib/auth";
import { uploadFile } from "../lib/file-upload";
import type { RealtorInfo } from "../types";

interface CreateRealtorData {
  firstname: string;
  middlename?: string;
  lastname: string;
  position: string;
  email: string;
  phone: string;
  company: string;
  website?: string;
  photo?: File;
}

interface UseRealtorsResult {
  realtors: RealtorInfo[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createRealtor: (data: CreateRealtorData, userId: string) => Promise<RealtorInfo>;
  isCreating: boolean;
  createError: string | null;
}

/**
 * Hook for fetching user's realtors from the API
 */
export function useRealtors(userId?: string): UseRealtorsResult {
  const [realtors, setRealtors] = useState<RealtorInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<string | null>(null);

  const fetchRealtors = useCallback(async () => {
    if (!userId) {
      setRealtors([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const headers = getAuthHeaders();
      const response = await fetch(
        `${env.API_URL}/api/realtors?filters[user][$eq]=${userId}&populate[photo][fields][1]=url`,
        { headers }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch realtors");
      }

      const data = await response.json();
      
      // Normalize the response data
      const realtorsData = Array.isArray(data.data) ? data.data : data || [];

      console.log('realtorsData', realtorsData);
      
      const normalizedRealtors: RealtorInfo[] = realtorsData.map((realtor: any) => ({
        id: realtor.id || realtor.documentId,
        firstname: realtor.firstname || "",
        middlename: realtor.middlename || null,
        lastname: realtor.lastname || "",
        position: realtor.position || "",
        company: realtor.company || "",
        email: realtor.email || "",
        phone: realtor.phone || realtor.workPhone || "",
        website: realtor.website || "",
        photo: realtor.photo || null,
      }));

      setRealtors(normalizedRealtors);
    } catch (error) {
      const errorMessage = 
        error instanceof Error ? error.message : "Failed to fetch realtors";
      setError(errorMessage);
      console.error("Error fetching realtors:", error);
      setRealtors([]);
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Create a new realtor
  const createRealtor = useCallback(async (
    data: CreateRealtorData, 
    userIdForRealtor: string
  ): Promise<RealtorInfo> => {
    setIsCreating(true);
    setCreateError(null);

    try {
      const headers = getAuthHeaders();
      let uploadedFileId: string | null = null;
      
      // Step 1: Upload the photo first (if provided)
      if (data.photo) {
        try {
          const formData = new FormData();
          formData.append("files", data.photo);

          const jwt = getCookie("jwt");
          if (!jwt) {
            throw new Error("No authentication token found");
          }

          console.log("Uploading photo...");
          const photoResponse = await fetch(
            `${env.API_URL}/api/upload`,
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${jwt}`,
              },
              body: formData,
            }
          );

          if (photoResponse.ok) {
            const uploadResult = await photoResponse.json();
            uploadedFileId = uploadResult[0]?.id;
            console.log("Photo uploaded successfully, ID:", uploadedFileId);
          } else {
            const errorData = await photoResponse.json().catch(() => null);
            console.warn("Photo upload failed:", errorData);
          }
        } catch (photoError) {
          console.warn("Photo upload failed:", photoError);
        }
      }
      
      // Step 2: Create the realtor with photo ID (if available)
      const realtorData = {
        data: {
          firstname: data.firstname,
          middlename: data.middlename || null,
          lastname: data.lastname,
          position: data.position,
          email: data.email,
          phone: data.phone,
          company: data.company,
          website: data.website || null,
          user: userIdForRealtor,
          ...(uploadedFileId && { photo: uploadedFileId }),
        },
      };

      console.log("Creating realtor with data:", realtorData);

      let response = await fetch(
        `${env.API_URL}/api/realtors?populate[photo][fields][0]=url&populate[photo][fields][1]=formats`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...headers,
          },
          body: JSON.stringify(realtorData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error?.message || "Failed to create realtor"
        );
      }

      let responseData = await response.json();
      let createdRealtor = responseData.data;

      console.log("Realtor created successfully:", createdRealtor);

      // Normalize the response to match RealtorInfo interface
      const normalizedRealtor: RealtorInfo = {
        id: createdRealtor.id || createdRealtor.documentId,
        firstname: createdRealtor.firstname,
        middlename: createdRealtor.middlename,
        lastname: createdRealtor.lastname,
        position: createdRealtor.position,
        company: createdRealtor.company,
        email: createdRealtor.email,
        phone: createdRealtor.phone,
        website: createdRealtor.website,
        photo: createdRealtor.photo || null,
      };

      // Refresh the realtors list
      await fetchRealtors();

      console.log('normalizedRealtor', normalizedRealtor);

      return normalizedRealtor;
    } catch (error) {
      const errorMessage = 
        error instanceof Error ? error.message : "Failed to create realtor";
      setCreateError(errorMessage);
      console.error("Error creating realtor:", error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [fetchRealtors]);

  // Fetch realtors when userId changes
  useEffect(() => {
    fetchRealtors();
  }, [fetchRealtors]);

  return {
    realtors,
    isLoading,
    error,
    refetch: fetchRealtors,
    createRealtor,
    isCreating,
    createError,
  };
}