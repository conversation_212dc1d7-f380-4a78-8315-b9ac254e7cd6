/**
 * Company Directory Feature Types
 */

export interface DirectoryUser {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  phone?: string;
  position?: string;
  photo?: {
    url?: string;
    formats?: {
      thumbnail?: {
        url?: string;
      };
    };
  };
  province?: string;
  isStaffMember?: boolean;
}

export interface DirectoryFilters {
  province?: string;
  search?: string;
  isStaffMember?: boolean;
  page?: number;
  limit?: number;
}

export interface DirectoryResult {
  users: DirectoryUser[];
  count: number;
  error?: string;
}

export interface DirectoryPageProps {
  initialUsers: DirectoryUser[];
  initialCount: number;
  initialPage: number;
  initialFilters?: DirectoryFilters;
}

export type ViewMode = 'cards' | 'table';

export interface DirectoryState {
  users: DirectoryUser[];
  count: number;
  currentPage: number;
  isLoading: boolean;
  error: string | null;
  filters: DirectoryFilters;
  viewMode: ViewMode;
}

// Canadian provinces for the filter dropdown
export const CANADIAN_PROVINCES = [
  { value: 'alberta', label: 'Alberta' },
  { value: 'britishColumbia', label: 'British Columbia' },
  { value: 'manitoba', label: 'Manitoba' },
  { value: 'newBrunswick', label: 'New Brunswick' },
  { value: 'newFoundlandAndLabrador', label: 'Newfoundland and Labrador' },
  { value: 'northwestTerritories', label: 'Northwest Territories' },
  { value: 'novaScotia', label: 'Nova Scotia' },
  { value: 'nunavut', label: 'Nunavut' },
  { value: 'ontario', label: 'Ontario' },
  { value: 'princeEdwardIsland', label: 'Prince Edward Island' },
  { value: 'quebec', label: 'Quebec' },
  { value: 'saskatchewan', label: 'Saskatchewan' },
  { value: 'yukon', label: 'Yukon' }
] as const;

export type ProvinceValue = typeof CANADIAN_PROVINCES[number]['value'] | 'all';
