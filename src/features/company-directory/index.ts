/**
 * Company Directory Feature - Public API
 *
 * This file exports all public components, hooks, and utilities
 * from the company directory feature following the feature-sliced pattern.
 */

// Components
export { default as CompanyDirectoryPage } from "./components/company-directory-page";
export { default as UserCard } from "./components/user-card";
export { default as UserTable } from "./components/user-table";
export { default as DirectoryFilters } from "./components/directory-filters";
export { default as DirectoryPagination } from "./components/directory-pagination";
export { default as ViewSwitcher } from "./components/view-switcher";

// Hooks
export { useDirectory } from "./hooks/use-directory";

// Types
export type * from "./types";

// API Client
export * from "./lib/api-client";
