"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { DirectoryUser, DirectoryFilters, DirectoryState, ViewMode } from "../types";
import { fetchDirectoryData } from "../lib/api-client";

interface UseDirectoryOptions {
  initialUsers: DirectoryUser[];
  initialCount: number;
  initialPage: number;
  initialFilters?: DirectoryFilters;
}

interface UseDirectoryResult extends DirectoryState {
  setUsers: (users: DirectoryUser[]) => void;
  setFilters: (filters: DirectoryFilters) => void;
  setCurrentPage: (page: number) => void;
  setViewMode: (mode: ViewMode) => void;
  handleProvinceFilter: (province: string) => Promise<void>;
  handleStaffFilter: (isStaffMember: boolean) => Promise<void>;
  handleSearch: (options: { resetPagination?: boolean }) => void;
  handlePagination: (page: number) => Promise<void>;
  refetch: () => Promise<void>;
}

const ITEMS_PER_PAGE = 12;

export function useDirectory({
  initialUsers,
  initialCount,
  initialPage,
  initialFilters = {}
}: UseDirectoryOptions): UseDirectoryResult {
  const [state, setState] = useState<DirectoryState>({
    users: initialUsers,
    count: initialCount,
    currentPage: initialPage,
    isLoading: false,
    error: null,
    filters: initialFilters,
    viewMode: 'cards' // Default to cards as specified
  });

  const currentPageRef = useRef(initialPage);
  const [filteredPages, setFilteredPages] = useState<number[]>([]);
  const filteredPagesSet = useRef(false);

  // Calculate filtered pages when count changes
  useEffect(() => {
    if (state.count !== null && !filteredPagesSet.current) {
      const totalPages = Math.ceil(state.count / ITEMS_PER_PAGE);
      setFilteredPages(Array.from({ length: totalPages }, (_, i) => i + 1));
      filteredPagesSet.current = true;
    }
  }, [state.count]);

  // Fetch data when filters change
  useEffect(() => {
    const fetchData = async () => {
      setState(prev => ({ ...prev, isLoading: true }));

      try {
        const { users, count } = await fetchDirectoryData({
          start: (currentPageRef.current - 1) * ITEMS_PER_PAGE,
          province: state.filters.province,
          isStaffMember: state.filters.isStaffMember,
          search: state.filters.search
        });

        setState(prev => ({
          ...prev,
          users,
          count,
          error: null,
          isLoading: false
        }));
      } catch (err) {
        console.error('Error in fetchData:', err);
        setState(prev => ({
          ...prev,
          error: err instanceof Error ? err.message : 'Unknown error occurred',
          isLoading: false
        }));
      }
    };

    fetchData();
  }, [state.filters, currentPageRef.current]);

  const setUsers = useCallback((users: DirectoryUser[]) => {
    setState(prev => ({ ...prev, users }));
  }, []);

  const setFilters = useCallback((filters: DirectoryFilters) => {
    setState(prev => ({ ...prev, filters }));
    filteredPagesSet.current = false;
  }, []);

  const setCurrentPage = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
    currentPageRef.current = page;
  }, []);

  const setViewMode = useCallback((mode: ViewMode) => {
    setState(prev => ({ ...prev, viewMode: mode }));
  }, []);

  const handleProvinceFilter = useCallback(async (province: string) => {
    setCurrentPage(1);
    currentPageRef.current = 1;
    setFilters({ ...state.filters, province });
    filteredPagesSet.current = false;
  }, [state.filters, setCurrentPage, setFilters]);

  const handleStaffFilter = useCallback(async (isStaffMember: boolean) => {
    setCurrentPage(1);
    currentPageRef.current = 1;
    setFilters({ ...state.filters, isStaffMember });
    filteredPagesSet.current = false;
  }, [state.filters, setCurrentPage, setFilters]);

  const handleSearch = useCallback((options: { resetPagination?: boolean }) => {
    if (options.resetPagination) {
      setCurrentPage(1);
      currentPageRef.current = 1;
      filteredPagesSet.current = false;
    }
  }, [setCurrentPage]);

  const handlePagination = useCallback(async (newPage: number) => {
    if (!filteredPages.includes(newPage)) {
      return;
    }

    setCurrentPage(newPage);
    currentPageRef.current = newPage;
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const { users } = await fetchDirectoryData({
        start: (newPage - 1) * ITEMS_PER_PAGE,
        province: state.filters.province,
        isStaffMember: state.filters.isStaffMember,
        search: state.filters.search
      });
      
      setState(prev => ({
        ...prev,
        users,
        error: null,
        isLoading: false
      }));
    } catch (error) {
      console.error('Error in pagination:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        isLoading: false
      }));
    }
  }, [filteredPages, state.filters, setCurrentPage]);

  const refetch = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const { users, count } = await fetchDirectoryData({
        start: (state.currentPage - 1) * ITEMS_PER_PAGE,
        province: state.filters.province,
        isStaffMember: state.filters.isStaffMember,
        search: state.filters.search
      });

      setState(prev => ({
        ...prev,
        users,
        count,
        error: null,
        isLoading: false
      }));
    } catch (err) {
      console.error('Error in refetch:', err);
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'Unknown error occurred',
        isLoading: false
      }));
    }
  }, [state.currentPage, state.filters]);

  return {
    ...state,
    setUsers,
    setFilters,
    setCurrentPage,
    setViewMode,
    handleProvinceFilter,
    handleStaffFilter,
    handleSearch,
    handlePagination,
    refetch
  };
}
