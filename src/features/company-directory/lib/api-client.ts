"use client";

import { DirectoryUser, DirectoryFilters, DirectoryResult } from "../types";

/**
 * Client-side API functions for company directory
 * These are used for client-side filtering and pagination
 */

/**
 * Build query parameters for directory API calls
 */
function buildDirectoryQueryParams({
  start = 0,
  limit = 12,
  sort = 'firstname:ASC',
  province,
  search,
  isStaffMember
}: {
  start?: number;
  limit?: number;
  sort?: string;
  province?: string;
  search?: string;
  isStaffMember?: boolean;
} = {}): URLSearchParams {
  const queryParams = new URLSearchParams({
    _start: start.toString(),
    _limit: limit.toString(),
    _sort: sort
  });

  if (province && province !== 'all') {
    queryParams.append('province', province);
  }

  if (search) {
    queryParams.append('search', search);
  }

  // Add staffFilter parameter if isStaffMember is true
  if (isStaffMember === true) {
    queryParams.append('staffFilter', 'only');
  }

  return queryParams;
}

/**
 * Get JWT token from cookies (client-side)
 */
function getJwtToken(): string | null {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const jwtCookie = cookies.find(cookie => cookie.trim().startsWith('jwt='));
  return jwtCookie ? jwtCookie.split('=')[1] : null;
}

/**
 * Fetch directory data from client-side
 */
export async function fetchDirectoryData({
  start = 0,
  limit = 12,
  sort = 'firstname:ASC',
  province,
  search,
  isStaffMember
}: {
  start?: number;
  limit?: number;
  sort?: string;
  province?: string;
  search?: string;
  isStaffMember?: boolean;
} = {}): Promise<DirectoryResult> {
  try {
    const jwt = getJwtToken();

    if (!jwt) {
      throw new Error('Authentication required');
    }

    const headers = {
      'Authorization': `Bearer ${jwt}`,
      'Content-Type': 'application/json',
    };

    const queryParams = buildDirectoryQueryParams({ start, limit, sort, province, search, isStaffMember });

    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:1339';

    try {
      // Try to fetch from the real API first
      const [usersResponse, countResponse] = await Promise.all([
        fetch(`${apiUrl}/users-permissions/directory?${queryParams}`, { headers }),
        fetch(`${apiUrl}/users-permissions/directory/count?${queryParams}`, { headers })
      ]);

      if (usersResponse.ok && countResponse.ok) {
        const users = await usersResponse.json();
        const count = await countResponse.json();

        return {
          users: Array.isArray(users) ? users : [],
          count: typeof count === 'number' ? count : 0,
        };
      }
    } catch (apiError) {
      console.log("API not available, falling back to mock data");
    }

    // Fallback to mock data if API is not available
    const { getMockDirectoryData } = await import("./mock-data");
    const mockResult = getMockDirectoryData({ start, limit, province, search, isStaffMember });

    return {
      users: mockResult.users,
      count: mockResult.count,
    };
  } catch (error) {
    console.error("Error in fetchDirectoryData:", error);
    throw error;
  }
}

/**
 * Search users by name (client-side)
 */
export async function searchDirectoryUsers(searchTerm: string): Promise<DirectoryUser[]> {
  try {
    if (!searchTerm || searchTerm.length < 3) {
      // Return default results if search term is too short
      const result = await fetchDirectoryData({ start: 0, limit: 12 });
      return result.users;
    }

    const result = await fetchDirectoryData({ search: searchTerm });
    return result.users;
  } catch (error) {
    console.error("Error in searchDirectoryUsers:", error);
    throw error;
  }
}

/**
 * Reset search to default state (client-side)
 */
export async function resetDirectorySearch(): Promise<DirectoryUser[]> {
  try {
    const result = await fetchDirectoryData({ start: 0, limit: 12 });
    return result.users;
  } catch (error) {
    console.error("Error in resetDirectorySearch:", error);
    throw error;
  }
}
