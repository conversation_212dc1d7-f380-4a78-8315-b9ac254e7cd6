import { DirectoryUser } from "../types";

export const mockDirectoryUsers: DirectoryUser[] = [
  {
    id: "1",
    firstname: "<PERSON>",
    lastname: "<PERSON> <PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "3688871667",
    position: "IT Director",
    photo: {
      url: "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1752775980069_2t6w6-<PERSON>_Portrait-1_low.jpg",
      formats: {
        thumbnail: {
          url: "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1752775980069_2t6w6-<PERSON>_Portrait-1_low.jpg"
        }
      }
    },
    province: "alberta",
    isStaffMember: true
  },
  {
    id: "2",
    firstname: "<PERSON>",
    lastname: "<PERSON>",
    email: "<EMAIL>",
    phone: "4031234567",
    position: "Senior Mortgage Broker",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "alberta",
    isStaffMember: false
  },
  {
    id: "3",
    firstname: "<PERSON>",
    lastname: "Chen",
    email: "<EMAIL>",
    phone: "6041234567",
    position: "Mortgage Specialist",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "britishColumbia",
    isStaffMember: false
  },
  {
    id: "4",
    firstname: "Emily",
    lastname: "Rodriguez",
    email: "<EMAIL>",
    phone: "4161234567",
    position: "Branch Manager",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "ontario",
    isStaffMember: true
  },
  {
    id: "5",
    firstname: "David",
    lastname: "Thompson",
    email: "<EMAIL>",
    phone: "5141234567",
    position: "Mortgage Advisor",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "quebec",
    isStaffMember: false
  },
  {
    id: "6",
    firstname: "Lisa",
    lastname: "Anderson",
    email: "<EMAIL>",
    phone: "2041234567",
    position: "Operations Manager",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "manitoba",
    isStaffMember: true
  },
  {
    id: "7",
    firstname: "James",
    lastname: "Wilson",
    email: "<EMAIL>",
    phone: "9021234567",
    position: "Mortgage Broker",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "novaScotia",
    isStaffMember: false
  },
  {
    id: "8",
    firstname: "Jennifer",
    lastname: "Brown",
    email: "<EMAIL>",
    phone: "3061234567",
    position: "Senior Advisor",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "saskatchewan",
    isStaffMember: false
  },
  {
    id: "9",
    firstname: "Robert",
    lastname: "Davis",
    email: "<EMAIL>",
    phone: "5061234567",
    position: "Regional Director",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "newBrunswick",
    isStaffMember: true
  },
  {
    id: "10",
    firstname: "Amanda",
    lastname: "Miller",
    email: "<EMAIL>",
    phone: "7091234567",
    position: "Mortgage Specialist",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "newFoundlandAndLabrador",
    isStaffMember: false
  },
  {
    id: "11",
    firstname: "Christopher",
    lastname: "Garcia",
    email: "<EMAIL>",
    phone: "7821234567",
    position: "Mortgage Broker",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "northwestTerritories",
    isStaffMember: false
  },
  {
    id: "12",
    firstname: "Michelle",
    lastname: "Martinez",
    email: "<EMAIL>",
    phone: "9021234568",
    position: "Senior Mortgage Advisor",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "princeEdwardIsland",
    isStaffMember: false
  },
  {
    id: "13",
    firstname: "Kevin",
    lastname: "Lee",
    email: "<EMAIL>",
    phone: "8671234567",
    position: "Mortgage Specialist",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "yukon",
    isStaffMember: false
  },
  {
    id: "14",
    firstname: "Rachel",
    lastname: "Taylor",
    email: "<EMAIL>",
    phone: "8671234568",
    position: "Branch Coordinator",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "nunavut",
    isStaffMember: true
  },
  {
    id: "15",
    firstname: "Daniel",
    lastname: "White",
    email: "<EMAIL>",
    phone: "4031234568",
    position: "Mortgage Broker",
    photo: {
      url: "/images/indi-a-logo.svg"
    },
    province: "alberta",
    isStaffMember: false
  }
];

export function getMockDirectoryData({
  start = 0,
  limit = 12,
  province,
  search,
  isStaffMember
}: {
  start?: number;
  limit?: number;
  province?: string;
  search?: string;
  isStaffMember?: boolean;
} = {}) {
  let filteredUsers = [...mockDirectoryUsers];

  // Filter by province
  if (province && province !== 'all') {
    filteredUsers = filteredUsers.filter(user => user.province === province);
  }

  // Filter by staff member status
  if (isStaffMember === true) {
    filteredUsers = filteredUsers.filter(user => user.isStaffMember === true);
  }

  // Filter by search term
  if (search) {
    const searchLower = search.toLowerCase();
    filteredUsers = filteredUsers.filter(user => 
      user.firstname.toLowerCase().includes(searchLower) ||
      user.lastname.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      (user.position && user.position.toLowerCase().includes(searchLower))
    );
  }

  // Apply pagination
  const paginatedUsers = filteredUsers.slice(start, start + limit);

  return {
    users: paginatedUsers,
    count: filteredUsers.length
  };
}
