"use client";

import React, { useMemo } from "react";
import { But<PERSON> } from "@/shared/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/shared/lib/utils";

interface DirectoryPaginationProps {
  currentPage: number;
  totalCount: number;
  itemsPerPage?: number;
  onPageChange: (page: number) => void;
  className?: string;
  disabled?: boolean;
}

export default function DirectoryPagination({
  currentPage,
  totalCount,
  itemsPerPage = 12,
  onPageChange,
  className,
  disabled = false
}: DirectoryPaginationProps) {
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  
  // Don't render pagination if there's only one page or no items
  if (totalPages <= 1) {
    return null;
  }

  const filteredPages = Array.from({ length: totalPages }, (_, i) => i + 1);

  const calculatePagination = useMemo(() => {
    if (filteredPages.length <= 5) return filteredPages;

    const pagesToShow: (number | string)[] = [];
    const currentPageIndex = filteredPages.indexOf(currentPage);

    if (currentPageIndex <= 2) {
      pagesToShow.push(...filteredPages.slice(0, 5));
    } else if (currentPageIndex >= filteredPages.length - 3) {
      pagesToShow.push(...filteredPages.slice(-5));
    } else {
      pagesToShow.push(...filteredPages.slice(currentPageIndex - 2, currentPageIndex + 3));
    }

    // Add ellipsis and first/last pages if needed
    if (pagesToShow[0] as number > 2) pagesToShow.unshift('...');
    if (pagesToShow[0] !== 1) pagesToShow.unshift(1);
    if ((pagesToShow[pagesToShow.length - 1] as number) < filteredPages.length - 1) pagesToShow.push('...');
    if (pagesToShow[pagesToShow.length - 1] !== filteredPages.length) pagesToShow.push(filteredPages.length);

    return pagesToShow;
  }, [currentPage, filteredPages]);

  const handlePageChange = (page: number) => {
    if (disabled || !filteredPages.includes(page) || page === currentPage) {
      return;
    }
    onPageChange(page);
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  };

  return (
    <div className={cn("flex justify-center items-center gap-2 mt-8", className)}>
      {/* Previous Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={handlePrevious}
        disabled={disabled || currentPage <= 1}
        className="flex items-center gap-1"
      >
        <ChevronLeft className="h-4 w-4" />
        Previous
      </Button>

      {/* Page Numbers */}
      <div className="flex items-center gap-1">
        {calculatePagination.map((pageNum, index) => {
          if (pageNum === '...') {
            return (
              <div key={`ellipsis-${index}`} className="px-2">
                <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
              </div>
            );
          }

          const page = pageNum as number;
          const isCurrentPage = currentPage === page;
          const isValidPage = filteredPages.includes(page);

          return (
            <Button
              key={page}
              variant={isCurrentPage ? "default" : "outline"}
              size="sm"
              onClick={() => handlePageChange(page)}
              disabled={disabled || !isValidPage}
              className={cn(
                "min-w-[40px]",
                isCurrentPage && "bg-primary text-primary-foreground hover:bg-primary/90"
              )}
            >
              {page}
            </Button>
          );
        })}
      </div>

      {/* Next Button */}
      <Button
        variant="outline"
        size="sm"
        onClick={handleNext}
        disabled={disabled || currentPage >= totalPages}
        className="flex items-center gap-1"
      >
        Next
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
