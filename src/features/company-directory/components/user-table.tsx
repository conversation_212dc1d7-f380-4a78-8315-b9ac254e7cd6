"use client";

import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/shared/ui/avatar";
import { Badge } from "@/shared/ui/badge";
import { Mail, Phone, MapPin, User } from "lucide-react";
import { DirectoryUser } from "../types";
import { cn } from "@/shared/lib/utils";

interface UserTableProps {
  users: DirectoryUser[];
  className?: string;
  onUserClick?: (user: DirectoryUser) => void;
}

export default function UserTable({ users, className, onUserClick }: UserTableProps) {
  if (!users || users.length === 0) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold text-muted-foreground">No matches found.</h2>
      </div>
    );
  }

  const formatProvince = (province?: string) => {
    if (!province) return '';
    return province.replace(/([A-Z])/g, ' $1').trim();
  };

  return (
    <div className={cn("rounded-md border", className)}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[60px]">Photo</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Position</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Province</TableHead>
            <TableHead className="w-[100px]">Type</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => {
            const photoUrl = user.photo?.formats?.thumbnail?.url || user.photo?.url || '/images/indi-a-logo.svg';
            const initials = `${user.firstname?.charAt(0) || ''}${user.lastname?.charAt(0) || ''}`.toUpperCase();
            const fullName = `${user.firstname || ''} ${user.lastname || ''}`.trim();

            return (
              <TableRow 
                key={user.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-muted/50",
                  onUserClick && "hover:bg-accent/50"
                )}
                onClick={() => onUserClick?.(user)}
              >
                <TableCell>
                  <Avatar className="h-10 w-10">
                    <AvatarImage 
                      src={photoUrl} 
                      alt={`${fullName} photo`}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold text-sm">
                      {initials || <User className="h-4 w-4" />}
                    </AvatarFallback>
                  </Avatar>
                </TableCell>
                
                <TableCell>
                  <div className="font-medium">{fullName}</div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-muted-foreground">
                    {user.position || '-'}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center text-sm">
                    {user.email ? (
                      <>
                        <Mail className="h-3 w-3 mr-1 text-muted-foreground" />
                        <span className="truncate max-w-[200px]">{user.email}</span>
                      </>
                    ) : (
                      '-'
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center text-sm">
                    {user.phone ? (
                      <>
                        <Phone className="h-3 w-3 mr-1 text-muted-foreground" />
                        <span>{user.phone}</span>
                      </>
                    ) : (
                      '-'
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center text-sm">
                    {user.province ? (
                      <>
                        <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
                        <span className="capitalize">{formatProvince(user.province)}</span>
                      </>
                    ) : (
                      '-'
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  {user.isStaffMember ? (
                    <Badge variant="secondary" className="text-xs">
                      Head Office
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">
                      Broker
                    </Badge>
                  )}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
