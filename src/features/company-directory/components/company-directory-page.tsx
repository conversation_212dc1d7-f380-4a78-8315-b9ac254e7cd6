"use client";

import React from "react";
import { Alert, AlertDescription } from "@/shared/ui/alert";
import { Skeleton } from "@/shared/ui/skeleton";
import { AlertCircle, Users } from "lucide-react";
import { DirectoryPageProps, DirectoryUser } from "../types";
import { useDirectory } from "../hooks/use-directory";
import UserCard from "./user-card";
import UserTable from "./user-table";
import DirectoryFilters from "./directory-filters";
import DirectoryPagination from "./directory-pagination";
import ViewSwitcher from "./view-switcher";
import { cn } from "@/shared/lib/utils";

interface CompanyDirectoryPageProps extends DirectoryPageProps {
  className?: string;
}

export default function CompanyDirectoryPage({
  initialUsers,
  initialCount,
  initialPage,
  initialFilters,
  className
}: CompanyDirectoryPageProps) {
  const {
    users,
    count,
    currentPage,
    isLoading,
    error,
    viewMode,
    setUsers,
    setViewMode,
    handleProvinceFilter,
    handleStaffFilter,
    handleSearch,
    handlePagination,
    refetch
  } = useDirectory({
    initialUsers,
    initialCount,
    initialPage,
    initialFilters
  });

  const handleUserClick = (user: DirectoryUser) => {
    // Could be extended to show user details modal or navigate to profile
    console.log('User clicked:', user);
  };

  const handleSearchResults = (searchUsers: DirectoryUser[]) => {
    setUsers(searchUsers);
  };

  const renderUsers = () => {
    if (isLoading) {
      return (
        <div className="space-y-4">
          {viewMode === 'cards' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <Skeleton key={index} className="h-48 w-full" />
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {Array.from({ length: 6 }).map((_, index) => (
                <Skeleton key={index} className="h-16 w-full" />
              ))}
            </div>
          )}
        </div>
      );
    }

    if (!users || users.length === 0) {
      return (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-muted-foreground mb-2">No matches found</h2>
          <p className="text-muted-foreground">
            Try adjusting your search criteria or filters to find more results.
          </p>
        </div>
      );
    }

    if (viewMode === 'cards') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {users.map((user) => (
            <UserCard
              key={user.id}
              user={user}
              wide
              onClick={() => handleUserClick(user)}
            />
          ))}
        </div>
      );
    }

    return (
      <UserTable
        users={users}
        onUserClick={handleUserClick}
      />
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Company Directory</h1>
        <p className="text-muted-foreground">
          Find and connect with team members across the organization.
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Error loading directory: {error}
            <button
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <DirectoryFilters
        onProvinceChange={handleProvinceFilter}
        onStaffFilterChange={handleStaffFilter}
        onSearchResults={handleSearchResults}
        onSearchStateChange={handleSearch}
        initialProvince={initialFilters?.province}
        initialStaffFilter={initialFilters?.isStaffMember}
      />

      {/* View Switcher */}
      <ViewSwitcher
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        disabled={isLoading}
      />

      {/* Results Count */}
      {!isLoading && (
        <div className="text-sm text-muted-foreground">
          Showing {users.length} of {count} results
        </div>
      )}

      {/* Users Display */}
      {renderUsers()}

      {/* Pagination */}
      {users && users.length > 0 && (
        <DirectoryPagination
          currentPage={currentPage}
          totalCount={count}
          itemsPerPage={25}
          onPageChange={handlePagination}
          disabled={isLoading}
        />
      )}
    </div>
  );
}
