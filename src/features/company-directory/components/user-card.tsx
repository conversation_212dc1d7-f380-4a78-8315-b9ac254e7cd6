"use client";

import React from "react";
import { Card, CardContent } from "@/shared/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/shared/ui/avatar";
import { Badge } from "@/shared/ui/badge";
import { Mail, Phone, MapPin, User } from "lucide-react";
import { DirectoryUser } from "../types";
import { cn } from "@/shared/lib/utils";

interface UserCardProps {
  user: DirectoryUser;
  className?: string;
  wide?: boolean;
  shadow?: boolean;
  onClick?: () => void;
}

export default function UserCard({ 
  user, 
  className, 
  wide = false, 
  shadow = false, 
  onClick 
}: UserCardProps) {
  const {
    firstname,
    lastname,
    position,
    email,
    phone,
    photo,
    province,
    isStaffMember
  } = user;

  // Get photo URL with fallback
  const photoUrl = photo?.formats?.thumbnail?.url || photo?.url || '/images/indi-a-logo.svg';
  
  // Generate initials for avatar fallback
  const initials = `${firstname?.charAt(0) || ''}${lastname?.charAt(0) || ''}`.toUpperCase();
  
  // Format full name
  const fullName = `${firstname || ''} ${lastname || ''}`.trim();

  return (
    <Card 
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        wide && "w-full",
        shadow && "shadow-lg",
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-6">
        <div className="flex items-start space-x-4">
          {/* Avatar */}
          <Avatar className="h-16 w-16 flex-shrink-0">
            <AvatarImage 
              src={photoUrl} 
              alt={`${fullName} photo`}
              className="object-cover"
            />
            <AvatarFallback className="bg-primary/10 text-primary font-semibold">
              {initials || <User className="h-6 w-6" />}
            </AvatarFallback>
          </Avatar>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {/* Name and Staff Badge */}
            <div className="flex items-center gap-2 mb-2">
              {fullName && (
                <h3 className="text-lg font-semibold text-foreground truncate">
                  {fullName}
                </h3>
              )}
              {isStaffMember && (
                <Badge variant="secondary" className="text-xs">
                  Head Office
                </Badge>
              )}
            </div>

            {/* Position */}
            {position && (
              <p className="text-sm font-medium text-muted-foreground mb-3">
                {position}
              </p>
            )}

            {/* Contact Information */}
            <div className="space-y-2">
              {email && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{email}</span>
                </div>
              )}
              
              {phone && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>{phone}</span>
                </div>
              )}
              
              {province && (
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="capitalize">
                    {province.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
