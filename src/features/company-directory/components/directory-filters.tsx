"use client";

import React, { useState, useCallback, useEffect } from "react";
import { Input } from "@/shared/ui/input";
import { Label } from "@/shared/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/ui/select";
import { Switch } from "@/shared/ui/switch";
import { Card, CardContent } from "@/shared/ui/card";
import { Search, Loader2 } from "lucide-react";
import { CANADIAN_PROVINCES, ProvinceValue, DirectoryUser } from "../types";
import { searchDirectoryUsers, resetDirectorySearch } from "../lib/api-client";

interface DirectoryFiltersProps {
  onProvinceChange: (province: string) => void;
  onStaffFilterChange: (isStaffMember: boolean) => void;
  onSearchResults: (users: DirectoryUser[]) => void;
  onSearchStateChange: (options: { resetPagination?: boolean }) => void;
  initialProvince?: string;
  initialStaffFilter?: boolean;
  className?: string;
}

export default function DirectoryFilters({
  onProvinceChange,
  onStaffFilterChange,
  onSearchResults,
  onSearchStateChange,
  initialProvince = 'all',
  initialStaffFilter = false,
  className
}: DirectoryFiltersProps) {
  const [selectedProvince, setSelectedProvince] = useState<ProvinceValue>(initialProvince as ProvinceValue);
  const [isStaffMember, setIsStaffMember] = useState(initialStaffFilter);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isProvinceLoading, setIsProvinceLoading] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // Sync with initial values when they change
  useEffect(() => {
    if (initialProvince !== selectedProvince) {
      setSelectedProvince(initialProvince as ProvinceValue);
    }
  }, [initialProvince]);

  useEffect(() => {
    if (initialStaffFilter !== isStaffMember) {
      setIsStaffMember(initialStaffFilter);
    }
  }, [initialStaffFilter]);

  // Debounced search function
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  };

  const performSearch = async (term: string) => {
    if (!term || term.length < 3) {
      // Reset to default state if search term is cleared
      if (term === '') {
        try {
          const defaultResults = await resetDirectorySearch();
          onSearchResults(defaultResults);
          onSearchStateChange({ resetPagination: true });
        } catch (err) {
          console.error('Error resetting search:', err);
          setSearchError('Failed to reset search');
        }
      }
      setSearchError(null);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const results = await searchDirectoryUsers(term);
      onSearchResults(results);
      onSearchStateChange({ resetPagination: true });
    } catch (err) {
      console.error('Search error:', err);
      setSearchError('An error occurred while searching. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  // Create memoized debounced function
  const debouncedSearch = useCallback(
    debounce((term: string) => performSearch(term), 300),
    [onSearchResults, onSearchStateChange]
  );

  const handleProvinceChange = async (province: string) => {
    setIsProvinceLoading(true);
    setSelectedProvince(province as ProvinceValue);

    try {
      await onProvinceChange(province);
    } finally {
      setIsProvinceLoading(false);
    }
  };

  const handleStaffFilterChange = (checked: boolean) => {
    setIsStaffMember(checked);
    onStaffFilterChange(checked);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    debouncedSearch(value);
  };

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Province Filter */}
          <div className="flex-1">
            <Label htmlFor="province-select" className="text-sm font-medium">
              Province:
            </Label>
            <Select
              value={selectedProvince}
              onValueChange={handleProvinceChange}
              disabled={isProvinceLoading}
            >
              <SelectTrigger id="province-select" className="mt-1">
                <SelectValue placeholder="Select province" />
                {isProvinceLoading && <Loader2 className="h-4 w-4 animate-spin" />}
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Provinces</SelectItem>
                {CANADIAN_PROVINCES.map(({ value, label }) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Search Filter */}
          <div className="flex-1">
            <Label htmlFor="search-input" className="text-sm font-medium">
              Search By Name:
            </Label>
            <div className="relative mt-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search-input"
                type="text"
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Type to search..."
                className="pl-10 pr-10"
                aria-label="Search by name"
              />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>
            {searchError && (
              <p className="text-sm text-destructive mt-1">{searchError}</p>
            )}
            {searchTerm && searchTerm.length > 0 && searchTerm.length < 3 && (
              <p className="text-sm text-muted-foreground mt-1">
                Please enter at least 3 characters to search
              </p>
            )}
          </div>

          {/* Staff Filter */}
          <div className="flex items-center space-x-2 lg:pt-6">
            <Switch
              id="staff-filter"
              checked={isStaffMember}
              onCheckedChange={handleStaffFilterChange}
            />
            <Label htmlFor="staff-filter" className="text-sm font-medium">
              Head Office Only
            </Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
