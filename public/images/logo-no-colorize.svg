<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 27.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="488.8px" height="351.1px" viewBox="0 0 488.8 351.1" style="enable-background:new 0 0 488.8 351.1;" xml:space="preserve"
	>
<style type="text/css">
	.st0{fill:#C6A143;}
	.st1{fill:none;stroke:#FF0000;stroke-width:4;stroke-miterlimit:10;}
	.st2{clip-path:url(#SVGID_00000013903829893934505910000005854095080356552120_);fill:#C6A143;}
	.st3{fill:#415A71;}
</style>
<g>
	<g>
		<g>
			<g>
				<g>
					<g>
						<path class="st0" d="M127.5,134.2v60c0,3.4,2.8,6.2,6.2,6.2h12.7h10.6c0.4,0,0.8-0.4,0.8-0.8V134c0-0.6-0.5-0.9-1-0.7
							c-3.5,1.4-7.8,2.7-13.7,2.7c-6.2,0-10.7-1.1-14.4-2.5C128,133.3,127.5,133.6,127.5,134.2z"/>
					</g>
					<circle class="st0" cx="142.5" cy="111.8" r="15.1"/>
					<g>
						<circle class="st0" cx="334" cy="111.8" r="15.1"/>
						<g>
							<path class="st0" d="M319.7,200.3c-0.4,0-0.8-0.4-0.8-0.8v-61.1c0-0.1,0.1-0.4,0.2-0.5c1.7-1.7,8.8-4.9,14.8-4.9l0,0
								c6.2,0,13.2,3.3,14.8,4.9c0.1,0.1,0.2,0.4,0.2,0.5v55.7c0,3.4-2.8,6.2-6.2,6.2L319.7,200.3z"/>
						</g>
					</g>
					<path class="st0" d="M221.9,161.9"/>
					<g>
						<path class="st0" d="M207,133.1c-1.7,0-3.2-0.1-4.6-0.1c-0.4,0-0.8,0.4-0.8,0.8l0.1,60.3c0,3.4,2.8,6.2,6.2,6.2h12.7h10.6
							c0.4,0,0.8-0.4,0.8-0.8v-42.1C231.9,144,220.7,133.1,207,133.1z"/>
						<path class="st0" d="M195.5,133.7c0-0.4-0.4-0.8-0.8-0.8c-7.1-0.2-11.1-3.2-17.1-3.3c-5.1,0-10.7,1.8-10.7,6.1v58.4
							c0,3.4,2.8,6.2,6.2,6.2h11.2h10.6c0.4,0,0.8-0.4,0.8-0.8v-51.9L195.5,133.7z"/>
					</g>
					<g>
						<path class="st0" d="M273.6,138.3L273.6,138.3v-4.7c0-0.4-0.4-0.8-0.8-0.8c-0.3,0-0.5-0.1-0.7,0c-18.8,0-34.1,15-34.1,33.8
							s15.3,33.7,34.1,33.7c0.1,0,0.4,0,0.7,0c0.4,0,0.8-0.4,0.8-0.8v-0.4l0,0l-0.1-54.2L273.6,138.3z"/>
						<path class="st0" d="M309.8,199.5v-100c0-2-1.9-3.5-3.8-3c-6.5,1.7-13,3.5-22.8,6.3c-2,0.6-3.3,2.3-3.3,4.3v23.2l0,0v18l0,0
							v0.6v2.4l0,0V194c0,3.4,2.8,6.2,6.2,6.2l23.1,0C309.4,200.3,309.8,199.9,309.8,199.5z"/>
					</g>
				</g>
			</g>
			<g>
				<path class="st0" d="M131.2,218.5l7.3,11.4l7.2-11.5c0.4-0.7,1.2-1.1,1.8-1c1,0,2,0.7,2,2V238c0,1.3-1.1,1.8-2.1,1.8
					c-1,0-2-0.6-2-1.8l0-11.7l-5.2,8.1c-0.4,0.7-1.1,1-1.8,1s-1.6-0.4-1.8-1.1l-5.2-8l0,11.7c0,1.3-1.1,1.8-2,1.8
					c-1,0-2.1-0.6-2-1.8v-18.4c0-1.3,1-2,2-2C130.1,217.4,130.8,217.8,131.2,218.5z"/>
				<path class="st0" d="M170.4,240c-6.8,0-11.5-4.5-11.5-11.5c0-6.9,4.6-11.5,11.5-11.5c6.8,0,11.4,4.6,11.3,11.5
					C181.8,235.4,177.2,240,170.4,240z M177.6,228.6c0-4.8-2.8-7.9-7.2-7.9s-7.3,3.2-7.4,7.9c0,4.7,3,7.9,7.4,7.9
					C174.8,236.5,177.7,233.3,177.6,228.6z"/>
				<path class="st0" d="M206.7,239l-6.2-6.6h-5.2v5.5c0,1.1-1.1,1.8-2,1.8c-1,0-2.1-0.6-2.1-1.8v-18.7c0-1.1,0.8-1.8,2-1.8h7.8
					c6.2,0,9.4,3.3,9.4,7.6c0,3.3-1.8,5.9-5.3,6.9l4.2,4.4C211.3,238.5,208.4,240.9,206.7,239z M200.9,229c3.7,0,5.4-1.8,5.3-3.9
					c-0.1-2.3-2.1-4-5.3-4h-5.8v8.1L200.9,229z"/>
				<path class="st0" d="M237.7,219.2c0,0.8-0.8,1.8-2.1,1.8h-6.5l0.1,16.8c0,1.3-1.1,1.8-2.1,1.9c-1,0-2.1-0.6-2-1.8V221h-6.5
					c-1.4,0-1.9-0.9-1.9-1.8c0-0.8,0.6-1.8,2-1.8h17C237,217.4,237.8,218.3,237.7,219.2z"/>
				<path class="st0" d="M262.5,235.7c-2.2,2.5-5.8,4.2-9.1,4.2c-6.9,0-11.4-4.7-11.2-11.7c0-6.8,4.5-11.3,11.3-11.3
					c3.1,0,6.3,1.1,8.3,3.3c2,2.1-0.9,4.5-3,2.4c-1.6-1.6-3.5-2.2-5.4-2.3c-4.4,0-7.3,3.2-7.4,7.9c0.1,4.7,2.9,8,7.2,8.1
					c2,0,4.2-1,5.5-2.5v-3.5h-5.1c-1.3,0-1.9-0.8-1.9-1.6s0.6-1.8,1.9-1.8h6.9c1.6,0,2.1,0.8,2.1,1.9v5.5
					C263,234.8,262.9,235.3,262.5,235.7z"/>
				<path class="st0" d="M284.3,218l9.9,19.4c0.9,1.5-0.5,2.6-1.8,2.5c-0.7,0-1.5-0.4-1.8-1.1l-2.5-4.9h-11.2l-2.5,4.9
					c-0.4,0.8-1.1,1.1-1.7,1c-1.3,0-2.5-1.1-1.9-2.5l10-19.4c0.4-0.8,0.9-0.9,1.8-1C283.2,217.1,283.9,217.4,284.3,218z
					 M278.6,230.8h7.8l-4-7.8L278.6,230.8z"/>
				<path class="st0" d="M321.2,235.7c-2.2,2.5-5.8,4.2-9.1,4.2c-6.9,0-11.4-4.7-11.2-11.7c0-6.8,4.5-11.3,11.3-11.3
					c3.1,0,6.3,1.1,8.3,3.3c2,2.1-0.9,4.5-3,2.4c-1.6-1.6-3.5-2.2-5.4-2.3c-4.4,0-7.3,3.2-7.4,7.9c0.1,4.7,2.9,8,7.2,8.1
					c2,0,4.2-1,5.5-2.5v-3.5h-5.1c-1.3,0-1.9-0.8-1.9-1.6s0.6-1.8,1.9-1.8h6.9c1.6,0,2.1,0.8,2.1,1.9v5.5
					C321.7,234.8,321.6,235.3,321.2,235.7z"/>
				<path class="st0" d="M349.1,238c0,0.8-0.6,1.6-1.9,1.6l-13.9,0c-1.4,0-2.3-0.9-2.3-2.1l0-18.1c0-1.1,0.9-2.1,2.3-2.1h13.9
					c1.3,0,1.8,0.8,1.8,1.7s-0.6,1.8-1.8,1.7h-12.2v6.1l11.6,0c1.1,0,1.8,0.8,1.8,1.8c0,0.8-0.6,1.7-1.8,1.7h-11.6v5.8h12.2
					C348.4,236.2,349.2,237.1,349.1,238z"/>
			</g>
			<g>
				<path class="st0" d="M128.7,253.7l-0.4,1.1c-0.2,0.5-1,0.1-0.8-0.4l0.5-1.1C128.2,252.9,128.9,253.2,128.7,253.7z M130.1,253.7
					l-0.4,1.1c-0.2,0.5-1,0.1-0.8-0.4l0.5-1.1C129.6,253,130.3,253.2,130.1,253.7z"/>
				<path class="st0" d="M136.5,254.1H134l0.1,6.2c0,0.3-0.3,0.4-0.4,0.4s-0.4-0.1-0.4-0.4l0-6.1h-2.5c-0.6,0-0.6-0.8,0-0.8l5.9,0
					C137.1,253.3,137.1,254.2,136.5,254.1z"/>
				<path class="st0" d="M141,255.5c-1.1,0-2.2,0.8-2.1,2v2.8c0,0.3-0.1,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4l-0.1-7
					c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4l0,2.5c0.4-0.8,1.3-1.1,2.2-1.2c1.4,0,2.6,0.9,2.6,2.6v3c0,0.3-0.1,0.4-0.4,0.4
					s-0.4-0.1-0.4-0.4v-3C142.9,256.2,142,255.5,141,255.5z"/>
				<path class="st0" d="M150.7,258.3l-4.5,0c0.1,1,0.9,1.8,2.1,1.8c0.6,0,1.1-0.1,1.5-0.5c0.4-0.4,1,0.1,0.6,0.6
					c-0.5,0.5-1.3,0.7-2.1,0.6c-1.8,0-3-1.3-3-3c0-2,1.5-3,3-3c1.6,0,3,0.9,3,3C151.3,258,151.2,258.2,150.7,258.3z M150.5,257.5
					c-0.1-1.4-1.2-2.1-2.2-2.1c-1.1,0-2.1,0.6-2.2,2.1H150.5z"/>
				<path class="st0" d="M156.7,253.3c0.3,0,0.4,0.1,0.4,0.4v6.5c0,0.3-0.2,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-6.5
					C156.2,253.5,156.4,253.3,156.7,253.3z"/>
				<path class="st0" d="M161.9,255.4c-1.1,0-2.2,0.9-2.2,2.2l0,2.5c0,0.3-0.1,0.4-0.4,0.4s-0.4-0.1-0.4-0.4v-5.1
					c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v0.7c0.4-0.8,1.3-1.2,2.2-1.2c1.4,0,2.6,0.9,2.6,2.6v3c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-3C163.7,256.2,162.8,255.5,161.9,255.4z"/>
				<path class="st0" d="M170.9,255.7v-2.3c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v6.9c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4l0-0.4c-0.4,0.7-1.2,1.1-1.8,1c-1.6,0-3-1.1-3-3.1c0-1.1,0.7-3,3-3C169.8,254.7,170.6,255.1,170.9,255.7
					z M170.9,258.2v-0.8c0-1-0.8-1.8-1.8-1.8c-1.7,0-2.1,1.4-2.1,2.1c0,1.8,1.3,2.3,2.2,2.3C170.1,260,170.9,259.3,170.9,258.2z"/>
				<path class="st0" d="M178.8,258.2l-4.5,0c0.1,1,0.9,1.8,2.1,1.8c0.6,0,1.1-0.1,1.5-0.5c0.4-0.4,1,0.1,0.6,0.6
					c-0.5,0.5-1.3,0.7-2.1,0.6c-1.8,0-3-1.3-3-3c0-2,1.5-3,3-3c1.6,0,3,0.9,3,3C179.4,257.9,179.3,258.3,178.8,258.2z M178.6,257.6
					c-0.1-1.4-1.2-2.1-2.2-2.1c-1.1,0-2.1,0.6-2.2,2.1H178.6z"/>
				<path class="st0" d="M183.7,260.8c-0.7,0-1.6-0.3-1.9-0.9v2.5c0,0.6-0.8,0.6-0.8,0v-7.2c0-0.3,0.1-0.4,0.4-0.4s0.4,0.1,0.4,0.4
					v0.6c0.4-0.6,1.2-1.1,1.9-1.1c1.6,0,3,1.2,3,3.3C186.6,259.5,185.4,260.7,183.7,260.8z M181.8,257.3v0.8c0,1,0.8,1.8,1.8,1.8
					c1.4,0,2.1-1,2.1-2.1c0.1-1.3-0.8-2.4-2.1-2.5C182.8,255.5,181.8,256.1,181.8,257.3z"/>
				<path class="st0" d="M193.4,258.3h-4.5c0.1,1,0.9,1.8,2.1,1.8c0.6,0,1.1-0.1,1.5-0.5c0.4-0.4,1,0.1,0.6,0.6
					c-0.5,0.5-1.3,0.7-2.1,0.6c-1.8,0-3-1.3-3-3c0-2,1.5-3,3-3c1.6,0,3,0.9,3,3C194,258,193.9,258.3,193.4,258.3z M193.1,257.6
					c-0.1-1.4-1.2-2.1-2.2-2.1c-1.1,0-2.1,0.6-2.2,2.1H193.1z"/>
				<path class="st0" d="M198.6,255.5c-1.1,0-2.2,0.9-2.2,2.2v2.5c0,0.3-0.1,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-5.1
					c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v0.7c0.4-0.8,1.3-1.2,2.2-1.2c1.4,0,2.6,0.9,2.6,2.6v3c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-3C200.4,256.2,199.5,255.4,198.6,255.5z"/>
				<path class="st0" d="M207.6,255.7v-2.3c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v6.9c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-0.4c-0.4,0.7-1.2,1.1-1.8,1c-1.6,0-3-1.1-3-3.1c0-1.1,0.7-3,3-3C206.6,254.7,207.3,255,207.6,255.7z
					 M207.6,258.2v-0.8c0-1-0.8-1.8-1.8-1.8c-1.7,0-2.1,1.4-2.1,2.1c0,1.8,1.3,2.3,2.2,2.3C206.9,260,207.6,259.3,207.6,258.2z"/>
				<path class="st0" d="M215.6,258.2H211c0.1,1,0.9,1.8,2.1,1.8c0.6,0,1.1-0.1,1.5-0.5c0.4-0.4,1,0.1,0.6,0.6
					c-0.5,0.5-1.3,0.7-2.1,0.7c-1.8,0-3-1.3-3-3c0-2,1.5-3,3-3c1.6,0,3,0.9,3,3C216.1,257.9,216,258.2,215.6,258.2z M215.3,257.5
					c-0.1-1.4-1.2-2.1-2.2-2.1c-1.1,0-2.1,0.6-2.2,2.1H215.3z"/>
				<path class="st0" d="M220.7,255.4c-1.1,0-2.2,0.9-2.2,2.2l0,2.5c0,0.3-0.1,0.4-0.4,0.4s-0.4-0.1-0.4-0.4v-5.1
					c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v0.7c0.4-0.8,1.3-1.2,2.2-1.2c1.4,0,2.6,0.9,2.6,2.6v3c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-3C222.5,256.2,221.6,255.5,220.7,255.4z"/>
				<path class="st0" d="M227.7,255.5h-1.3v3.1c0,0.7,0.1,1.3,0.9,1.3c0.1,0,0.4,0,0.6-0.1c0.4-0.3,0.8,0.4,0.4,0.6
					c-0.3,0.1-0.6,0.1-0.9,0.2c-1.4,0-1.7-1-1.7-2.1l0-3.1H225c-0.6,0-0.6-0.7,0-0.7h0.7v-1.4c0-0.6,0.8-0.6,0.8,0v1.4h1.3
					C228.3,254.8,228.3,255.5,227.7,255.5z"/>
				<path class="st0" d="M240,253.3c0.1,0,0.4,0.1,0.4,0.4v6.5c0,0.3-0.2,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-5.1l-2.4,3.7
					c-0.2,0.4-0.6,0.3-0.8,0l-2.4-3.7v5.1c0,0.3-0.2,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-6.5c0-0.3,0.2-0.5,0.4-0.4
					c0.1,0,0.3,0.1,0.4,0.3l2.8,4.4l2.8-4.4C239.7,253.3,239.8,253.3,240,253.3z"/>
				<path class="st0" d="M242.1,257.7c0-1.7,1.3-3,3-3c1.7,0,3,1.3,3,3c0,1.7-1.3,3.1-3,3S242.1,259.4,242.1,257.7z M242.9,257.7
					c0,1.3,0.8,2.3,2.2,2.2c1.3-0.1,2.2-1.1,2.2-2.2c0-1.6-1.1-2.3-2.2-2.3C244,255.5,242.9,256.3,242.9,257.7z"/>
				<path class="st0" d="M252.1,254.7c0.3,0,0.4,0.1,0.4,0.4c0,0.3-0.1,0.4-0.4,0.4c-1.1,0-1.7,1-1.7,2.1v2.7c0,0.3-0.2,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-5.1c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4l0,0.4C250.8,254.9,251.5,254.7,252.1,254.7z"/>
				<path class="st0" d="M256.7,255.5h-1.3v3.1c0,0.7,0.1,1.3,0.9,1.3c0.1,0,0.4,0,0.6-0.1c0.4-0.3,0.8,0.4,0.4,0.6
					c-0.3,0.1-0.6,0.1-0.9,0.2c-1.4,0-1.7-1-1.7-2.1l0-3.1H254c-0.6,0-0.6-0.7,0-0.7h0.7v-1.4c0-0.6,0.8-0.6,0.8,0v1.4h1.3
					C257.1,254.8,257.1,255.5,256.7,255.5z"/>
				<path class="st0" d="M261.2,262.9c-0.8,0-1.6-0.2-2.2-0.8c-0.5-0.5,0.1-1,0.6-0.6c0.6,0.4,1,0.6,1.7,0.6c1,0,2.1-0.5,2.1-1.8
					v-0.6c-0.4,0.7-1.2,1.1-2,1c-0.4,0-0.8-0.1-1.2-0.2c-1.1-0.4-1.8-1.6-1.8-2.8c0-2,1.4-3.1,3-3.1c0.7,0,1.6,0.3,1.9,0.9l0-0.4
					c0-0.3,0.1-0.4,0.4-0.4s0.4,0.1,0.4,0.4v5.2C264,262.1,262.7,262.9,261.2,262.9z M259.2,257.8c0,1.1,0.8,2.2,2.2,2.2
					c1.1-0.1,1.9-0.8,1.8-1.8v-0.8c0-1.3-0.9-1.8-1.9-1.8C260.1,255.5,259.2,256.4,259.2,257.8z"/>
				<path class="st0" d="M270.4,255.7l0-0.4c0-0.3,0.1-0.4,0.4-0.4c0.3,0,0.4,0.1,0.4,0.4v5.1c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-0.6c-0.4,0.6-1.2,1.1-1.9,1.1c-1.7,0-3-1.1-3-3.1c0-2,1.3-3,3-3C269.3,254.7,270,254.9,270.4,255.7z
					 M270.4,258.2v-0.8c0-1.1-0.8-1.8-1.8-1.8c-1.4,0-2.2,1.1-2.2,2.2c0,1.7,1.4,2.3,2.1,2.3C269.6,259.9,270.4,259.3,270.4,258.2z"
					/>
				<path class="st0" d="M275.7,262.9c-0.8,0-1.6-0.2-2.2-0.8c-0.5-0.5,0.1-1,0.6-0.6c0.6,0.4,1,0.6,1.7,0.6c1,0,2.1-0.5,2.1-1.8
					v-0.6c-0.4,0.7-1.2,1.1-2,1c-0.4,0-0.8-0.1-1.2-0.2c-1.1-0.4-1.8-1.6-1.8-2.8c0-2,1.4-3.1,3-3.1c0.7,0,1.6,0.3,1.9,0.9v-0.4
					c0-0.3,0.1-0.4,0.4-0.4c0.3,0,0.4,0.1,0.4,0.4v5.2C278.6,262.1,277.3,262.9,275.7,262.9z M273.7,257.8c0,1.1,0.8,2.2,2.2,2.2
					c1.1-0.1,1.9-0.8,1.8-1.8v-0.8c0-1.3-0.9-1.8-1.9-1.8C274.6,255.4,273.7,256.5,273.7,257.8z"/>
				<path class="st0" d="M285.6,258.3h-4.5c0.1,1,0.9,1.8,2.1,1.8c0.6,0,1.1-0.1,1.5-0.5c0.4-0.4,1,0.1,0.6,0.6
					c-0.5,0.5-1.3,0.7-2.1,0.6c-1.8,0-3-1.3-3-3c0-2,1.5-3,3-3c1.6,0,3,0.9,3,3C286.2,258,286.1,258.3,285.6,258.3z M285.4,257.6
					c-0.1-1.4-1.2-2.1-2.2-2.1c-1.1,0-2.1,0.6-2.2,2.1H285.4z"/>
				<path class="st0" d="M297.3,254.4c0.4,0.4-0.3,0.8-0.6,0.6c-0.6-0.6-1.3-0.9-2.1-0.8c-1.3,0-3,0.6-3,2.9c0,1.7,1.3,3,3,3.1
					c0.8,0,1.7-0.4,2.3-1.1c0.3-0.4,1,0,0.6,0.5c-0.6,0.9-1.8,1.4-3,1.4c-2,0-3.7-1.6-3.7-3.9c0-2.7,2.3-3.7,3.8-3.7
					C295.7,253.2,296.6,253.5,297.3,254.4z"/>
				<path class="st0" d="M298.6,257.6c0-1.7,1.3-3,3-3c1.7,0,3,1.3,3,3s-1.3,3.1-3,3C299.7,260.7,298.5,259.4,298.6,257.6z
					 M299.4,257.8c0,1.3,0.8,2.3,2.2,2.2c1.3-0.1,2.2-1.1,2.2-2.2c0-1.6-1.1-2.3-2.2-2.3C300.4,255.5,299.4,256.2,299.4,257.8z"/>
				<path class="st0" d="M314,255.5c-1.3,0-2.2,0.9-2.2,2.2v2.5c0,0.3-0.1,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-2.8
					c0-0.7-0.3-1.3-0.8-1.6c-0.3-0.1-0.6-0.2-1-0.3c-1.1,0-2.1,0.8-2.2,2.2v2.7c0,0.3-0.2,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-5.1
					c0-0.3,0.1-0.4,0.4-0.4c0.3,0,0.4,0.1,0.4,0.4v0.7c0.4-0.8,1.3-1.2,2.2-1.2c1,0,2.1,0.5,2.3,1.5c0.4-1,1.4-1.6,2.5-1.5
					c1.3,0,2.7,0.8,2.6,2.5v3.1c0,0.3-0.1,0.4-0.4,0.4c-0.3,0-0.4-0.1-0.4-0.4v-3.1C315.8,256.2,315,255.5,314,255.5z"/>
				<path class="st0" d="M321.3,260.8c-0.7,0-1.6-0.3-1.9-0.9l0,2.5c0,0.6-0.8,0.6-0.8,0v-7.2c0-0.3,0.1-0.4,0.4-0.4
					c0.3,0,0.4,0.1,0.4,0.4v0.6c0.4-0.6,1.2-1.1,1.9-1.1c1.6,0,3,1.2,3,3.3C324.2,259.5,323,260.7,321.3,260.8z M319.4,257.3v0.8
					c0,1,0.8,1.8,1.8,1.8c1.4,0,2.1-1,2.1-2.1c0.1-1.3-0.8-2.4-2.1-2.5C320.3,255.4,319.4,256.1,319.4,257.3z"/>
				<path class="st0" d="M330.3,255.6v-0.4c0-0.3,0.1-0.4,0.4-0.4c0.3,0,0.4,0.1,0.4,0.4v5.1c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-0.6c-0.4,0.6-1.2,1.1-1.9,1.1c-1.7,0-3-1.1-3-3.1s1.3-3,3-3C329.2,254.7,329.9,254.9,330.3,255.6z
					 M330.4,258.2v-0.8c0-1.1-0.8-1.8-1.8-1.8c-1.4,0-2.2,1.1-2.2,2.2c0,1.7,1.4,2.3,2.1,2.3C329.5,260,330.4,259.3,330.4,258.2z"/>
				<path class="st0" d="M336.1,255.4c-1.1,0-2.2,0.9-2.2,2.2l0,2.5c0,0.3-0.1,0.4-0.4,0.4s-0.4-0.1-0.4-0.4v-5.1
					c0-0.3,0.2-0.4,0.4-0.4c0.1,0,0.4,0.1,0.4,0.4v0.7c0.4-0.8,1.3-1.2,2.2-1.2c1.4,0,2.6,0.9,2.6,2.6v3c0,0.3-0.1,0.4-0.4,0.4
					c-0.3,0-0.4-0.1-0.4-0.4v-3C337.9,256.2,337,255.5,336.1,255.4z"/>
				<path class="st0" d="M340.3,254.8c0.1,0,0.2,0.1,0.4,0.2l2.2,4.3l2.1-4.4c0.1-0.2,0.2-0.2,0.4-0.2c0.3,0,0.5,0.2,0.4,0.6
					l-3.6,7.3c-0.3,0.6-1,0.3-0.8-0.3l1.1-2.1l-2.5-4.9C339.7,255.1,340,254.8,340.3,254.8z"/>
				<path class="st0" d="M346.4,254.5l0.4-1.1c0.2-0.5,1-0.1,0.8,0.4l-0.5,1.1C346.8,255.4,346.2,255,346.4,254.5z M347.7,254.6
					l0.4-1.1c0.2-0.5,1-0.1,0.8,0.4l-0.5,1.1C348.2,255.4,347.5,255.1,347.7,254.6z"/>
			</g>
		</g>
	</g>
</g>
<line class="st1" x1="77.5" y1="28.7" x2="367.8" y2="319"/>
<g>
	<defs>
		<rect id="SVGID_1_" x="591.8" y="-75.9" width="61.4" height="81.6"/>
	</defs>
	<clipPath id="SVGID_00000098916382561876276740000010849998523834870924_">
		<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
	</clipPath>
	
		<rect x="591.8" y="-75.9" style="clip-path:url(#SVGID_00000098916382561876276740000010849998523834870924_);fill:#C6A143;" width="61.4" height="81.6"/>
</g>
<rect x="591.8" y="37.6" class="st3" width="61.4" height="81.6"/>
</svg>
