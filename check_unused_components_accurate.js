const fs = require('fs');
const path = require('path');

// Configuration
const LISTING_SHEET_FEATURE_PATH = './src/features/listing-sheet';
const LISTING_SHEET_PAGES_PATH = './src/app/(inapp)/listing-sheet';
const SEARCH_PATHS = [
  LISTING_SHEET_FEATURE_PATH,
  LISTING_SHEET_PAGES_PATH
];

// Get all component files
function getComponentFiles() {
  const componentsDir = path.join(LISTING_SHEET_FEATURE_PATH, 'components');
  const files = fs.readdirSync(componentsDir);
  return files
    .filter(file => file.endsWith('.tsx') || file.endsWith('.ts'))
    .map(file => file.replace('.tsx', '').replace('.ts', ''));
}

// Get all files to search in
function getAllFilesToSearch() {
  const files = [];
  
  function traverseDir(dirPath) {
    const items = fs.readdirSync(dirPath);
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverseDir(fullPath);
      } else if (item.endsWith('.tsx') || item.endsWith('.ts') || item.endsWith('.js') || item.endsWith('.jsx')) {
        files.push(fullPath);
      }
    }
  }
  
  SEARCH_PATHS.forEach(searchPath => {
    if (fs.existsSync(searchPath)) {
      traverseDir(searchPath);
    }
  });
  
  return files;
}

// Check if a component is actually imported and used in a file
function checkComponentInFile(componentName, filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if this is the component file itself
    if (filePath.includes(`/${componentName}.tsx`) || filePath.includes(`/${componentName}.ts`)) {
      return false;
    }
    
    // Check for actual import statements (more specific patterns)
    const importPatterns = [
      // Direct import from components directory
      new RegExp(`import\\s+\\{[^}]*\\}\\s+from\\s+["']\\.\\./components/${componentName}["']`),
      new RegExp(`import\\s+\\{[^}]*\\}\\s+from\\s+["']\\.\\./\\.\\./components/${componentName}["']`),
      new RegExp(`import\\s+\\{[^}]*\\}\\s+from\\s+["']@/features/listing-sheet/components/${componentName}["']`),
      
      // Default imports
      new RegExp(`import\\s+${componentName}\\s+from\\s+["']\\.\\./components/${componentName}["']`),
      new RegExp(`import\\s+${componentName}\\s+from\\s+["']\\.\\./\\.\\./components/${componentName}["']`),
      new RegExp(`import\\s+${componentName}\\s+from\\s+["']@/features/listing-sheet/components/${componentName}["']`),
      
      // Mixed imports
      new RegExp(`import\\s+${componentName}\\s*,\\s*\\{[^}]*\\}\\s+from\\s+["']\\.\\./components/${componentName}["']`),
      new RegExp(`import\\s+\\{[^}]*\\},\\s*${componentName}\\s+from\\s+["']\\.\\./components/${componentName}["']`),
    ];
    
    // Check for JSX usage (actual component tags)
    const jsxPatterns = [
      new RegExp(`<${componentName}[\\s/>]`),
      new RegExp(`<${componentName.replace(/([A-Z])/g, '-$1').toLowerCase()}[\\s/>]`), // kebab-case
    ];
    
    // Check for import statements
    const hasImport = importPatterns.some(pattern => pattern.test(content));
    
    // Check for JSX usage
    const hasJSX = jsxPatterns.some(pattern => pattern.test(content));
    
    return hasImport || hasJSX;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return false;
  }
}

// Main analysis function
function analyzeComponentUsage() {
  console.log('🔍 Analyzing component usage in listing-sheet feature (ACCURATE VERSION)...\n');
  
  const components = getComponentFiles();
  const filesToSearch = getAllFilesToSearch();
  
  console.log(`Found ${components.length} components:`);
  components.forEach(comp => console.log(`  - ${comp}`));
  
  console.log(`\nSearching in ${filesToSearch.length} files...\n`);
  
  const usageResults = {};
  
  components.forEach(component => {
    usageResults[component] = {
      isUsed: false,
      usedIn: [],
      importPatterns: []
    };
    
    filesToSearch.forEach(filePath => {
      if (checkComponentInFile(component, filePath)) {
        usageResults[component].isUsed = true;
        usageResults[component].usedIn.push(filePath);
      }
    });
  });
  
  // Display results
  console.log('📊 COMPONENT USAGE ANALYSIS:\n');
  
  const usedComponents = [];
  const unusedComponents = [];
  
  Object.entries(usageResults).forEach(([component, result]) => {
    if (result.isUsed) {
      usedComponents.push(component);
      console.log(`✅ ${component} - USED`);
      console.log(`   Used in: ${result.usedIn.length} file(s)`);
      result.usedIn.forEach(file => {
        console.log(`     - ${path.relative('.', file)}`);
      });
      console.log('');
    } else {
      unusedComponents.push(component);
      console.log(`❌ ${component} - UNUSED`);
      console.log('');
    }
  });
  
  console.log('📋 SUMMARY:');
  console.log(`   Total components: ${components.length}`);
  console.log(`   Used components: ${usedComponents.length}`);
  console.log(`   Unused components: ${unusedComponents.length}`);
  
  if (unusedComponents.length > 0) {
    console.log('\n🗑️  UNUSED COMPONENTS (can be safely deleted):');
    unusedComponents.forEach(comp => console.log(`   - ${comp}`));
  }
  
  if (usedComponents.length > 0) {
    console.log('\n✅ USED COMPONENTS:');
    usedComponents.forEach(comp => console.log(`   - ${comp}`));
  }
  
  return { usedComponents, unusedComponents, usageResults };
}

// Run the analysis
if (require.main === module) {
  analyzeComponentUsage();
}

module.exports = { analyzeComponentUsage };
