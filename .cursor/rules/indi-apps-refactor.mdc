---
description: 
globs: 
alwaysApply: false
---
We must redo an existing project that has deprecated code. The existing project has concepts from NextJS v10, it uses old methods and patterns and do not take advantage of most recent advantages and ways of building frontend projects with nextjs.

The existing project has UI components built from scratch that uses sass module stylesheets to customize them. We are not doing the basic components and css/sass from scratch anymore. Instead, we are going to use a more recent approach by using Tailwind and Shadcn components for that.

The old project do not use the recent app router from NextJs, and we are going to use it now. 

We are starting from scratch, where we are going to rebuilt every single section of the project.

The backend is built with Strapi V3 for now. The old project fetches data using 'getServerSideProps' function from NextJS. In our new project we are going to fetch data also in the server, but now we are going to create custom hooks for that, and centralize all of our fetching requests in hooks and actions.

You can find all features from the old project on the /legacy folder.

Our new project structure follows a feature-sliced structure. Always check the project tree and make sure to create new files in the correct folders. For example, type definitions files must always go into the src/features/feature-name/types folder and imported into the component file. Type definitions must never be declared within the component file. The same rule applies for hooks, helper functions, etc.

While shadcn is going to provide us with the base UI components, each compounded component must have its own unitary tests by using Jest + React Testing Library, and we must have component tests, utility function tests and API integration tests.

The way we are going to work is, I'm gonna provide you the existing code from the old project here on chat, then you are going to use it as reference and re-implement it in our new current project following our new approach described above.