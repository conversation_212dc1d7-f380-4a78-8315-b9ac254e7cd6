# Common Layout Implementation for Category-Based Features

## Overview

This document outlines the complete implementation of a common layout system for the five category-based features from the legacy project:
- **Printables** - Province-based printable documents
- **Compliance** - Province-based compliance documents  
- **Resources** - Province-based resource documents
- **Brand Materials** - Category-based media assets
- **Custom Shop** - Category-based design templates

## Problem Statement

The legacy project had five features with very similar layouts and functionality:
1. **Landing Page**: Display categories (provinces or media types) in a grid
2. **Category Items Page**: Show items within a selected category
3. **Navigation**: Click category → view items → view individual item

Each feature had duplicated code for:
- Layout components
- Data fetching logic
- Error handling
- Loading states
- Search and filtering

## Solution Architecture

### 1. Common Components (`src/shared/components/`)

#### CategoryLandingPage
- **Purpose**: Landing page with category grid
- **Features**: 
  - Responsive grid layout
  - Loading skeletons
  - Error states with retry
  - Empty states
  - Customizable button labels and messages
  - Hover effects and animations

#### CategoryItemsList  
- **Purpose**: Display items within a category
- **Features**:
  - Grid/list layout options
  - Search and filter capabilities
  - Item metadata display
  - File type badges
  - Responsive design

### 2. Common Hooks (`src/shared/hooks/`)

#### useCategoryData
- **Purpose**: Generic data management for categories
- **Features**:
  - Category extraction and transformation
  - Search functionality
  - Filtering capabilities
  - State management

#### Specialized Hooks
- `useProvinceBasedCategories`: For printables, compliance, resources
- `useMediaBasedCategories`: For brand-materials, custom-shop

### 3. Common API Client (`src/shared/lib/`)

#### CategoryApiClient
- **Purpose**: Unified API interface for all features
- **Features**:
  - Document fetching by type
  - Media item fetching
  - Data transformation utilities
  - Error handling
  - Authentication support

## Implementation Details

### Data Flow

```
API Request → CategoryApiClient → useCategoryData → Component Rendering
     ↓              ↓                ↓              ↓
Fetch Data → Transform Data → Manage State → Display UI
```

### Component Props

#### CategoryLandingPage
```tsx
interface CategoryLandingPageProps {
  title: string;                    // Main page title
  subtitle?: string;                // Optional subtitle
  description?: string;             // Page description
  categories: CategoryItem[];       // Category data
  isLoading?: boolean;              // Loading state
  error?: string | null;            // Error message
  onCategorySelect: (category: CategoryItem) => void;
  buttonLabel?: string;             // Action button text
  layout?: "grid" | "list";        // Layout option
  emptyStateMessage?: string;       // Empty state message
  retryAction?: () => void;         // Retry function
}
```

#### CategoryItemsList
```tsx
interface CategoryItemsListProps {
  title: string;                    // Page title
  subtitle?: string;                // Subtitle
  items: CategoryItem[];            // Items to display
  isLoading?: boolean;              // Loading state
  error?: string | null;            // Error message
  onItemSelect?: (item: CategoryItem) => void;
  buttonLabel?: string;             // Action button text
  layout?: "grid" | "list";        // Layout option
  showSearchAndFilter?: boolean;    // Enable search/filter
  onSearch?: (query: string) => void;
  onFilterChange?: (filter: string) => void;
  availableFilters?: string[];      // Filter options
}
```

### Data Types

#### CategoryItem
```tsx
interface CategoryItem {
  id: string;                       // Unique identifier
  title: string;                    // Display title
  description?: string;             // Description text
  thumbnail?: {                     // Image/icon data
    url: string;
    alternativeText?: string;
    formats?: {
      thumbnail?: { url: string };
    };
  };
  icon?: React.ReactNode;           // Custom icon component
  metadata?: Record<string, any>;   // Additional data
}
```

#### DocumentItem (for printables, compliance, resources)
```tsx
interface DocumentItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: ThumbnailData;
  file?: {                          // File information
    url: string;
    ext: string;
    size?: number;
  };
  provinceFile?: Array<{            // Province-specific files
    province: string;
    file: FileData;
    thumbnail?: ThumbnailData;
  }>;
  isHidden?: boolean;
  metadata?: Record<string, any>;
}
```

#### MediaItem (for brand-materials, custom-shop)
```tsx
interface MediaItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: ThumbnailData;
  category?: string;                // Media category
  tags?: string[];                  // Tag array
  mediaItems?: MediaItem[];         // Nested media items
  metadata?: Record<string, any>;
}
```

## Feature Implementation Examples

### 1. Printables Feature (Complete Implementation)

#### Landing Page Component
```tsx
const PrintablesLandingPage: React.FC = () => {
  const { categories, isLoading, error, retry } = usePrintables();
  
  return (
    <CategoryLandingPage
      title="Printables"
      subtitle="Printable documents applicable for all provinces"
      description="Access printable documents organized by province."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Documents"
      retryAction={retry}
    />
  );
};
```

#### Hook Implementation
```tsx
export function usePrintables() {
  const [documents, setDocuments] = useState<DocumentItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const apiClient = new CategoryApiClient({
    baseUrl: process.env.NEXT_PUBLIC_API_URL || "",
  });

  const fetchPrintables = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiClient.fetchPrintables();
      setDocuments(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch printables");
    } finally {
      setIsLoading(false);
    }
  };

  // Transform documents to categories using common hook
  const { categories } = useCategoryData({
    data: documents,
    categoryExtractor: (doc) => {
      if (doc.provinceFile && doc.provinceFile.length > 0) {
        return doc.provinceFile[0].province;
      }
      return "General";
    },
    itemTransformer: (doc) => ({
      id: doc.provinceFile?.[0]?.province || "General",
      title: doc.provinceFile?.[0]?.province || "General",
      description: `${documents.length} documents available`,
      metadata: { province: doc.provinceFile?.[0]?.province },
    }),
    searchFields: ["title", "description"],
  });

  return { categories, documents, isLoading, error, retry: fetchPrintables };
}
```

### 2. Brand Materials Feature (Media-Based)

#### Landing Page Component
```tsx
const BrandMaterialsLandingPage: React.FC = () => {
  const { categories, isLoading, error, retry } = useBrandMaterials();
  
  return (
    <CategoryLandingPage
      title="Brand Materials"
      subtitle="Brand materials and assets organized by category"
      description="Access brand materials including banners, logos, and marketing assets."
      categories={categories}
      isLoading={isLoading}
      error={error}
      onCategorySelect={handleCategorySelect}
      buttonLabel="View Items"
      retryAction={retry}
    />
  );
};
```

#### Hook Implementation
```tsx
export function useBrandMaterials() {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const apiClient = new CategoryApiClient({
    baseUrl: process.env.NEXT_PUBLIC_API_URL || "",
  });

  const fetchBrandMaterials = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiClient.fetchBrandMaterials();
      setMediaItems(response.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch brand materials");
    } finally {
      setIsLoading(false);
    }
  };

  // Transform media items to categories
  const { categories } = useCategoryData({
    data: mediaItems,
    categoryExtractor: (item) => item.category || "General",
    itemTransformer: (item) => ({
      id: item.category || "General",
      title: item.category || "General",
      description: `${mediaItems.length} items available`,
      metadata: { category: item.category },
    }),
    searchFields: ["title", "description", "category"],
  });

  return { categories, mediaItems, isLoading, error, retry: fetchBrandMaterials };
}
```

## Benefits of This Approach

### 1. **Code Reusability**
- Single source of truth for layout components
- Shared hooks for data management
- Common API client for data fetching

### 2. **Consistency**
- Uniform UI across all features
- Consistent error handling and loading states
- Standardized user experience

### 3. **Maintainability**
- Changes to layout only need to be made in one place
- Centralized styling and behavior
- Easier to implement new features

### 4. **Performance**
- Optimized rendering with React hooks
- Efficient data transformation
- Lazy loading and error boundaries

### 5. **Developer Experience**
- TypeScript support throughout
- Clear component interfaces
- Comprehensive documentation
- Easy to understand and extend

### 6. **Accessibility**
- Keyboard navigation support
- Screen reader compatibility
- ARIA labels and roles
- Focus management

## File Structure

```
src/
├── shared/
│   ├── components/
│   │   ├── category-landing-page.tsx      # Main landing page component
│   │   ├── category-items-list.tsx        # Items list component
│   │   ├── index.ts                       # Component exports
│   │   └── README.md                      # Component documentation
│   ├── hooks/
│   │   └── use-category-data.ts           # Common data management hook
│   └── lib/
│       └── category-api-client.ts         # Unified API client
├── features/
│   ├── printables/                        # Complete implementation
│   ├── compliance/                        # To be implemented
│   ├── resources/                         # To be implemented
│   ├── brand-materials/                   # To be implemented
│   └── custom-shop/                       # To be implemented
└── app/
    └── (inapp)/
        ├── printables/                     # Page components
        ├── compliance/                     # Page components
        ├── resources/                      # Page components
        ├── brand-materials/                # Page components
        └── custom-shop/                    # Page components
```

## Next Steps

### 1. **Immediate Implementation**
- ✅ Printables feature (complete)
- 🔄 Compliance feature (template provided)
- 🔄 Resources feature (template provided)
- 🔄 Brand Materials feature (template provided)
- 🔄 Custom Shop feature (template provided)

### 2. **Testing**
- Unit tests for common components
- Integration tests for feature workflows
- E2E tests for user journeys

### 3. **Enhancements**
- Advanced search and filtering
- Pagination for large datasets
- Offline support
- Performance optimizations

### 4. **Documentation**
- API documentation
- Component storybook
- Usage examples
- Best practices guide

## Conclusion

This common layout implementation provides a robust, scalable foundation for all category-based features. By leveraging shared components, hooks, and API clients, we've eliminated code duplication while maintaining flexibility for feature-specific requirements.

The solution follows modern React patterns, provides excellent TypeScript support, and maintains the high-quality user experience established in the legacy project. Each feature can now be implemented quickly and consistently, with minimal custom code required.

The architecture is designed to be extensible, allowing for future enhancements and new features while maintaining the established patterns and conventions.
