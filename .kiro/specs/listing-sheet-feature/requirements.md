# Requirements Document

## Introduction

The Listing Sheet feature is a comprehensive mortgage calculation and PDF generation tool that allows mortgage brokers to create professional listing sheets for properties. The feature replicates the exact functionality and UI layout of the legacy MortgageCalculator component, maintaining all sections in a single-page accordion layout. The feature enables users to input property details, calculate mortgage payments with various down payment scenarios, and generate branded PDF documents that can be shared with clients and realtors. The system maintains a 90-day history of generated sheets for easy access and editing.

**Key Design Principle**: The implementation must exactly replicate the legacy MortgageCalculator.js component structure and UI layout, using a single comprehensive component with accordion-style sections, not separate components or tabs.

## Requirements

### Requirement 1: Listing Sheet Management

**User Story:** As a mortgage broker, I want to view and manage my existing listing sheets, so that I can easily access and modify previously created documents.

#### Acceptance Criteria

1. WHEN a user navigates to the listing sheet page THEN the system SHALL display a list of existing listing sheets from the last 90 days
2. WHEN displaying existing sheets THEN the system SHALL show property photo, MLS code, price, address, and action buttons for each sheet
3. WHEN a user clicks the edit button THEN the system SHALL navigate to the edit page with pre-populated data
4. WHEN a user clicks the delete button THEN the system SHALL prompt for confirmation before deletion
5. WHEN a user confirms deletion THEN the system SHALL remove the sheet from the database and refresh the list
6. IF no existing sheets are found THEN the system SHALL display a "No sheets found" message

### Requirement 2: Listing Sheet Creation

**User Story:** As a mortgage broker, I want to create new listing sheets with property and mortgage details, so that I can generate professional documents for my clients.

#### Acceptance Criteria

1. WHEN a user clicks "Create New" THEN the system SHALL navigate to the creation page with a blank mortgage calculator form
2. WHEN creating a new sheet THEN the system SHALL require asking price, down payment selection, and amortization period
3. WHEN a user inputs property details THEN the system SHALL validate required fields before allowing save
4. WHEN a user saves a new sheet THEN the system SHALL generate a unique slug and store the data
5. WHEN save is successful THEN the system SHALL redirect to the listing sheet index page
6. IF save fails THEN the system SHALL display an error message and allow retry

### Requirement 3: Mortgage Calculation Engine

**User Story:** As a mortgage broker, I want to calculate mortgage payments with different down payment scenarios, so that I can provide accurate financial information to clients.

#### Acceptance Criteria

1. WHEN a user enters an asking price THEN the system SHALL automatically calculate down payment options (5%, 10%, 15%, 20%, custom)
2. WHEN the property price is between $500K-$1.5M THEN the system SHALL calculate the blended minimum down payment rate
3. WHEN the property price exceeds $1.5M THEN the system SHALL require minimum 20% down payment
4. WHEN calculating payments THEN the system SHALL include mortgage insurance for down payments under 20%
5. WHEN a user selects payment frequency THEN the system SHALL calculate monthly, bi-weekly, weekly, or accelerated payments
6. WHEN amortization period is selected THEN the system SHALL validate maximum years based on property type (25 for pre-owned, 30 for new)
7. WHEN all required fields are complete THEN the system SHALL display calculated payment amounts for all scenarios

### Requirement 4: Property and Contact Information Management

**User Story:** As a mortgage broker, I want to add property details and realtor information, so that the generated PDF includes complete property and contact information.

#### Acceptance Criteria

1. WHEN creating a listing sheet THEN the system SHALL allow input of MLS code, property address, and property photo upload
2. WHEN adding realtor information THEN the system SHALL allow input of name, position, company, email, phone, website, and photo
3. WHEN uploading images THEN the system SHALL support JPEG and PNG formats with appropriate validation
4. WHEN no property photo is provided THEN the system SHALL use a default property image
5. WHEN no realtor photo is provided THEN the system SHALL use a default realtor image
6. WHEN saving THEN the system SHALL store all property and contact information with the listing sheet

### Requirement 5: Expense and Cash Calculation

**User Story:** As a mortgage broker, I want to calculate monthly expenses and cash needed for purchase, so that clients understand the total cost of homeownership.

#### Acceptance Criteria

1. WHEN a user enables monthly expenses THEN the system SHALL allow input of property tax, insurance, utilities, condo fees, and other monthly costs
2. WHEN a user enables cash needed calculation THEN the system SHALL allow input of lawyer fees, home inspection, appraisal, title insurance, and estoppel fees
3. WHEN calculating total monthly expenses THEN the system SHALL include mortgage payment plus all selected monthly costs
4. WHEN calculating total cash needed THEN the system SHALL include down payment plus all closing costs
5. WHEN values are updated THEN the system SHALL automatically recalculate totals in real-time
6. WHEN generating PDF THEN the system SHALL include or exclude expense sections based on user selection

### Requirement 6: PDF Generation and Branding

**User Story:** As a mortgage broker, I want to generate professional branded PDF listing sheets, so that I can provide polished documents to clients and realtors.

#### Acceptance Criteria

1. WHEN a user generates a PDF THEN the system SHALL create a document with mortgage broker and realtor information
2. WHEN generating PDF THEN the system SHALL include company branding and logos based on user's team affiliation
3. WHEN including payment scenarios THEN the system SHALL display only applicable down payment options based on property price
4. WHEN property price requires blended rates THEN the system SHALL show calculated minimum down payment percentages
5. WHEN monthly expenses are enabled THEN the system SHALL include expense breakdown in the PDF
6. WHEN cash needed is enabled THEN the system SHALL include closing costs breakdown in the PDF
7. WHEN PDF is generated THEN the system SHALL open the document in a new window and provide download option

### Requirement 7: Data Persistence and History

**User Story:** As a mortgage broker, I want my listing sheets to be saved and accessible for 90 days, so that I can reference and modify them as needed.

#### Acceptance Criteria

1. WHEN a listing sheet is created THEN the system SHALL store it with a unique identifier and timestamp
2. WHEN storing sheets THEN the system SHALL include all calculation data, property information, and user selections
3. WHEN retrieving sheets THEN the system SHALL only show sheets created within the last 90 days
4. WHEN editing an existing sheet THEN the system SHALL preserve the original creation date and update the modification timestamp
5. WHEN a sheet is older than 90 days THEN the system SHALL automatically exclude it from the listing
6. WHEN updating an existing sheet THEN the system SHALL maintain the same slug and ID for consistency

### Requirement 8: Form Validation and Error Handling

**User Story:** As a mortgage broker, I want clear validation and error messages, so that I can quickly identify and correct any issues with my listing sheet data.

#### Acceptance Criteria

1. WHEN required fields are missing THEN the system SHALL display specific validation messages
2. WHEN invalid data is entered THEN the system SHALL prevent form submission and highlight errors
3. WHEN API calls fail THEN the system SHALL display user-friendly error messages
4. WHEN network errors occur THEN the system SHALL provide retry options
5. WHEN file uploads fail THEN the system SHALL show upload-specific error messages
6. WHEN validation passes THEN the system SHALL allow form submission and PDF generation

### Requirement 9: Responsive Design and Accessibility

**User Story:** As a mortgage broker, I want the listing sheet feature to work on all devices, so that I can create and manage sheets from anywhere.

#### Acceptance Criteria

1. WHEN accessing on mobile devices THEN the system SHALL provide a responsive layout with touch-friendly controls
2. WHEN using keyboard navigation THEN the system SHALL support tab order and keyboard shortcuts
3. WHEN using screen readers THEN the system SHALL provide appropriate ARIA labels and descriptions
4. WHEN displaying data tables THEN the system SHALL provide horizontal scrolling on smaller screens
5. WHEN uploading files on mobile THEN the system SHALL support camera capture and file selection
6. WHEN generating PDFs on mobile THEN the system SHALL handle browser-specific download behaviors
