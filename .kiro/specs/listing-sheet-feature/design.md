# Design Document

## Overview

The Listing Sheet feature will be implemented as a feature-sliced module in the modern Next.js application, following the established patterns. The feature will consist of a single comprehensive MortgageCalculator component that exactly replicates the legacy implementation, along with PDF generation system and data management layer. The design emphasizes exact functional parity with the legacy system while using modern React patterns and shadcn/ui components.

**Critical Design Decision**: Instead of splitting functionality into multiple components (which causes reactivity issues), we will create a single comprehensive MortgageCalculator component that manages all state internally, exactly like the legacy implementation. This ensures proper reactivity and eliminates useEffect dependency issues.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Next.js App Router] --> B[Listing Sheet Pages]
    B --> C[Feature Components]
    C --> D[Shared UI Components]
    C --> E[Business Logic Hooks]
    E --> F[API Layer]
    F --> G[Strapi Backend]

    H[PDF Generation] --> I[PDF-lib]
    H --> J[File Storage]

    K[State Management] --> L[React Hook Form]
    K --> M[Context API]
```

### Feature Structure

```
src/features/listing-sheet/
├── components/
│   ├── listing-sheet-list.tsx
│   ├── listing-sheet-form.tsx
│   ├── mortgage-calculator.tsx
│   ├── property-details-form.tsx
│   ├── realtor-info-form.tsx
│   ├── expense-calculator.tsx
│   ├── payment-scenarios.tsx
│   └── pdf-preview.tsx
├── hooks/
│   ├── use-listing-sheets.ts
│   ├── use-mortgage-calculator.ts
│   ├── use-pdf-generator.ts
│   └── use-file-upload.ts
├── lib/
│   ├── mortgage-calculations.ts
│   ├── pdf-generator.ts
│   ├── validation-schemas.ts
│   └── constants.ts
├── types/
│   └── index.ts
└── contexts/
    └── listing-sheet-context.tsx
```

## Components and Interfaces

### Core Types

```typescript
interface ListingSheet {
  id: string;
  slug: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  user: string;
  sheet: MortgageCalculationData;
}

interface MortgageCalculationData {
  // Property Information
  askingPrice: number;
  mlsCode?: string;
  address?: string;
  propertyPhoto?: FileUpload;

  // Calculation Parameters
  years: number;
  customYears?: number;
  frequency: PaymentFrequency;
  customPercentage: number;

  // Down Payment Calculations
  downPay5: number;
  downPay10: number;
  downPay15: number;
  downPay20: number;
  downPayCustom: number;
  downPayRange: number;
  downPayFTHB: number;
  chosenDownPay: ChosenDownPayment;

  // Interest Rates
  rate5: number;
  rate10: number;
  rate15: number;
  rate20: number;
  rateCustom: number;
  rateRange: number;
  rateFTHB: number;

  // Insurance Calculations
  insurance5: number;
  insurance10: number;
  insurance15: number;
  insurance20: number;
  insuranceCustom: number;
  insuranceRange: number;
  insuranceFTHB: number;

  // Payment Calculations
  monthlyPay5: number;
  monthlyPay10: number;
  monthlyPay15: number;
  monthlyPay20: number;
  monthlyPayCustom: number;
  monthlyPayRange: number;
  monthlyPayFTHB: number;

  // Periodic Payments
  periodicPay5: number;
  periodicPay10: number;
  periodicPay15: number;
  periodicPay20: number;
  periodicPayCustom: number;
  periodicPayRange: number;
  periodicPayFTHB: number;

  // Expenses
  propertyTax: number;
  propertyInsurance: number;
  utilities: number;
  condoFees: number;
  hoaFees: number;
  monthlyDebtPayments: number;
  phone: number;
  cable: number;
  internet: number;
  totalMonthlyPayments: number;
  totalYearlyPayments: number;

  // Closing Costs
  lawyerFee: number;
  homeInspection: number;
  appraisal: number;
  titleInsurance: number;
  estoppelFee: number;
  totalCashNeeded: number;

  // PDF Configuration
  pdf: PDFConfiguration;

  // Realtor Information
  realtor: RealtorInfo;

  // Rate Information
  rangeRate: RangeRate;
  effectiveRates: EffectiveRates;
}

interface PDFConfiguration {
  full: boolean;
  short: boolean;
  mlsCode?: string;
  address?: string;
  monthlyExpenses: boolean;
  cashNeeded: boolean;
  propertyPhoto?: FileUpload;
  realtorPhoto?: FileUpload;
  user: UserInfo;
}

interface RealtorInfo {
  id?: string;
  firstname: string;
  middlename?: string;
  lastname: string;
  position: string;
  company: string;
  email: string;
  phone: string;
  website?: string;
  photo?: FileUpload;
}

type PaymentFrequency =
  | "monthly"
  | "biweekly"
  | "weekly"
  | "accbiweekly"
  | "accweekly";

interface ChosenDownPayment {
  rate: string;
  percent: string;
  amount: number;
}

interface RangeRate {
  type: "normalTier" | "middleTier" | "maxTier";
  rate?: number;
}
```

### Component Architecture

#### 1. ListingSheetList Component

- **Purpose**: Display and manage existing listing sheets
- **Features**:
  - Data table with sorting and filtering
  - Edit/Delete actions
  - Responsive design
  - Loading states
- **Dependencies**: `use-listing-sheets` hook, shadcn/ui Table components

#### 2. ListingSheetForm Component

- **Purpose**: Main form container for creating/editing listing sheets
- **Features**:
  - Multi-step form with validation
  - Real-time calculations
  - Auto-save functionality
  - Progress indication
- **Dependencies**: React Hook Form, Zod validation

#### 3. MortgageCalculator Component

- **Purpose**: Core calculation engine with interactive UI
- **Features**:
  - Dynamic down payment scenarios
  - Real-time payment calculations
  - Interest rate management
  - Payment frequency selection
- **Dependencies**: `use-mortgage-calculator` hook

#### 4. PropertyDetailsForm Component

- **Purpose**: Property information input
- **Features**:
  - File upload for property photos
  - Address validation
  - MLS code input
- **Dependencies**: `use-file-upload` hook

#### 5. RealtorInfoForm Component

- **Purpose**: Realtor contact information
- **Features**:
  - Contact form with validation
  - Photo upload
  - Auto-complete suggestions
- **Dependencies**: Form validation, file upload

#### 6. ExpenseCalculator Component

- **Purpose**: Monthly expenses and closing costs calculation
- **Features**:
  - Toggleable expense categories
  - Real-time total calculations
  - Currency formatting
- **Dependencies**: Currency utilities

#### 7. PaymentScenarios Component

- **Purpose**: Display calculated payment options
- **Features**:
  - Scenario comparison table
  - Interactive selection
  - Visual indicators for recommendations
- **Dependencies**: Calculation utilities

#### 8. PDFPreview Component

- **Purpose**: PDF generation and preview
- **Features**:
  - Live preview
  - Download functionality
  - Print options
- **Dependencies**: `use-pdf-generator` hook

## Data Models

### Database Schema (Strapi)

```typescript
// listing-sheets collection
interface ListingSheetEntity {
  id: number;
  documentId: string;
  slug: string;
  title: string;
  sheet: JSON; // MortgageCalculationData
  user: UserEntity;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
}
```

### API Endpoints

```typescript
// GET /api/listing-sheets - Get user's listing sheets
// POST /api/listing-sheets - Create new listing sheet
// PUT /api/listing-sheets/:id - Update existing listing sheet
// DELETE /api/listing-sheets/:id - Delete listing sheet
// GET /api/listing-sheets/:slug - Get specific listing sheet by slug
```

## Error Handling

### Error Boundaries

- Feature-level error boundary for graceful degradation
- Component-specific error handling for form validation
- API error handling with user-friendly messages

### Validation Strategy

- Zod schemas for type-safe validation
- Real-time validation feedback
- Server-side validation backup

### Loading States

- Skeleton components for data loading
- Progressive loading for large forms
- Optimistic updates for better UX

## Testing Strategy

### Unit Tests

- Mortgage calculation functions
- Validation schemas
- Utility functions
- Component logic

### Integration Tests

- Form submission flows
- API integration
- PDF generation
- File upload functionality

### E2E Tests

- Complete listing sheet creation flow
- Edit existing sheet workflow
- PDF generation and download
- Responsive behavior

## Performance Considerations

### Optimization Strategies

- Lazy loading for PDF generation components
- Memoization of expensive calculations
- Debounced input handling for real-time calculations
- Image optimization for uploaded photos

### Caching Strategy

- React Query for API data caching
- Local storage for form draft persistence
- Service worker for offline PDF templates

### Bundle Optimization

- Code splitting by route
- Dynamic imports for PDF generation
- Tree shaking for unused utilities

## Security Considerations

### Data Protection

- Input sanitization for all form fields
- File upload validation and scanning
- JWT token validation for API access
- CSRF protection for form submissions

### Access Control

- User-specific data isolation
- Role-based feature access
- Secure file storage with signed URLs

## Accessibility Features

### WCAG Compliance

- Semantic HTML structure
- ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility

### User Experience

- High contrast mode support
- Scalable text and UI elements
- Touch-friendly mobile interface
- Clear error messaging

## Migration Strategy

### Data Migration

- Legacy data transformation utilities
- Batch processing for existing sheets
- Data validation and cleanup
- Rollback procedures

### Feature Parity

- Side-by-side comparison testing
- Gradual feature rollout
- User feedback collection
- Performance monitoring

## Deployment Considerations

### Environment Configuration

- PDF template storage configuration
- File upload service setup
- API endpoint configuration
- Feature flags for gradual rollout

### Monitoring

- Error tracking and reporting
- Performance metrics collection
- User interaction analytics
- PDF generation success rates
