# Implementation Plan

- [x] 1. Set up feature structure and core types

  - Create the feature directory structure following the established pattern
  - Define TypeScript interfaces for all data models and API responses
  - Set up barrel exports for clean imports
  - _Requirements: 1.1, 2.1, 3.1_

- [x] 2. Implement core mortgage calculation utilities

  - [x] 2.1 Create mortgage calculation functions

    - Write functions for down payment calculations based on property price tiers
    - Implement mortgage insurance calculation logic
    - Create effective interest rate calculation utilities
    - Write unit tests for all calculation functions
    - _Requirements: 3.1, 3.2, 3.3, 3.4_

  - [x] 2.2 Implement payment frequency calculations

    - Create functions for monthly, bi-weekly, weekly, and accelerated payment calculations
    - Write utilities for converting between payment frequencies
    - Add validation for amortization period limits based on property type
    - Write unit tests for payment calculations
    - _Requirements: 3.5, 3.6_

  - [x] 2.3 Create validation schemas
    - Define Zod schemas for all form inputs and data structures
    - Implement custom validation rules for mortgage-specific logic
    - Create error message utilities for user-friendly feedback
    - Write tests for validation schemas
    - _Requirements: 8.1, 8.2_

- [x] 3. Build data layer and API integration

  - [x] 3.1 Create listing sheets API client

    - Implement CRUD operations for listing sheets using the shared API client
    - Add proper error handling and response typing
    - Create API utilities for file uploads
    - Write integration tests for API functions
    - _Requirements: 1.1, 2.4, 7.1, 7.2_

  - [x] 3.2 Implement custom hooks for data management
    - Create `use-listing-sheets` hook for fetching and managing listing sheet data
    - Implement `use-mortgage-calculator` hook for calculation state management
    - Create `use-file-upload` hook for property and realtor photo uploads
    - Add proper loading states and error handling to all hooks
    - _Requirements: 1.1, 4.3, 7.3_

- [x] 4. Create core UI components

  - [x] 4.1 Build ListingSheetList component

    - Create responsive data table for displaying existing listing sheets
    - Implement edit and delete actions with confirmation dialogs
    - Add loading states and empty state handling
    - Integrate with the listing sheets hook for data fetching
    - Write component tests for user interactions
    - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

  - [x] 4.2 Create PropertyDetailsForm component

    - Build form for property information input (MLS code, address, price)
    - Implement file upload component for property photos with drag-and-drop
    - Add image preview and validation for supported formats
    - Integrate with file upload hook and form validation
    - Write tests for form submission and file upload
    - _Requirements: 4.1, 4.3, 4.4_

  - [x] 4.3 Build RealtorInfoForm component
    - Create form for realtor contact information
    - Implement photo upload with preview functionality
    - Add form validation for email and phone number formats
    - Integrate with validation schemas and file upload utilities
    - Write tests for form validation and submission
    - _Requirements: 4.2, 4.3, 4.5_

- [ ] 5. Implement comprehensive MortgageCalculator component (Legacy Replica)

  - [ ] 5.1 Create single comprehensive MortgageCalculator component

    - **CRITICAL**: Replicate the exact structure and functionality of legacy/components/MortgageCalculator/MortgageCalculator.js
    - Build a single component that manages all state internally (no separate sub-components)
    - Implement all accordion sections: Property Details, Realtor Info, Mortgage Payment, Cash Needed, Monthly Costs, Yearly Costs
    - Use shadcn/ui Accordion component to replicate the dropdown sections from legacy
    - Maintain exact calculation logic and state management patterns from legacy
    - Implement all useEffect dependencies and state updates exactly as in legacy
    - Write tests for calculation accuracy and user interactions
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.7, 4.1, 4.2, 5.1, 5.2, 5.3_

  - [ ] 5.2 Implement legacy calculation functions within component

    - Port all calculation functions from legacy (calcDownPaymentMinRateByAmount, paymentCalc, etc.)
    - Maintain exact calculation logic for down payments, insurance, and mortgage payments
    - Implement scenario toggles (FTHB, New Build, Resale Property) exactly as in legacy
    - Add all currency formatting and validation as in legacy
    - Ensure real-time calculations work exactly as in legacy
    - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

  - [ ] 5.3 Implement all form sections within single component
    - Property Details section with file upload (exact replica of legacy)
    - Realtor Info section with realtor selection and photo upload
    - Mortgage Payment section with all calculation tables and inputs
    - Cash Needed section with all closing cost inputs
    - Monthly Costs section with all expense inputs
    - Yearly Costs section with property tax and insurance
    - PDF Options section with generation controls
    - _Requirements: 4.1, 4.2, 4.3, 5.1, 5.2, 5.3, 5.4, 5.5, 6.1, 6.7_

- [ ] 6. Implement PDF generation system

  - [ ] 6.1 Create PDF generation utilities

    - Implement PDF template loading and form field population
    - Create image embedding utilities for property and realtor photos
    - Add branding logic based on user's team affiliation
    - Implement conditional content inclusion based on user selections
    - Write tests for PDF generation accuracy
    - _Requirements: 6.1, 6.2, 6.5, 6.6_

  - [ ] 6.2 Build PDFPreview component

    - Create PDF generation trigger with loading states
    - Implement download and print functionality
    - Add error handling for PDF generation failures
    - Integrate with PDF generation utilities
    - Write tests for PDF generation workflow
    - _Requirements: 6.7, 8.3, 8.4_

  - [ ] 6.3 Implement PDF template management
    - Create utilities for selecting appropriate PDF templates based on property price tiers
    - Implement dynamic field mapping for different template types
    - Add support for conditional sections (expenses, cash needed)
    - Write tests for template selection logic
    - _Requirements: 6.3, 6.4, 6.5, 6.6_

- [ ] 7. Create main form container and routing

  - [x] 7.1 Build ListingSheetForm component

    - Create multi-step form container with progress indication
    - Implement form state management using React Hook Form
    - Add auto-save functionality for draft persistence
    - Integrate all sub-components (property, realtor, calculator, expenses)
    - Write tests for form flow and state management
    - _Requirements: 2.2, 2.3, 8.1, 8.2_

  - [x] 7.2 Create page components and routing
    - Build listing sheet index page with list and create button
    - Create new listing sheet page with form container
    - Implement edit listing sheet page with pre-populated data
    - Add proper navigation and breadcrumbs
    - Write tests for page navigation and data loading
    - _Requirements: 1.1, 2.1, 2.5_

- [ ] 8. Implement data persistence and management

  - [ ] 8.1 Create listing sheet CRUD operations

    - Implement create functionality with unique slug generation
    - Add update functionality preserving creation timestamps
    - Implement delete functionality with confirmation
    - Add proper error handling and user feedback
    - Write tests for all CRUD operations
    - _Requirements: 2.4, 2.5, 7.1, 7.4, 7.6_

  - [ ] 8.2 Implement data filtering and history management
    - Add 90-day filtering for listing sheet display
    - Implement search and sorting functionality
    - Create data cleanup utilities for expired sheets
    - Add pagination for large datasets
    - Write tests for filtering and pagination
    - _Requirements: 7.3, 7.5_

- [ ] 9. Add responsive design and accessibility features

  - [ ] 9.1 Implement responsive layouts

    - Create mobile-friendly layouts for all components
    - Implement touch-friendly controls and interactions
    - Add responsive data table with horizontal scrolling
    - Optimize file upload for mobile devices
    - Write tests for responsive behavior
    - _Requirements: 9.1, 9.5_

  - [ ] 9.2 Add accessibility features
    - Implement proper ARIA labels and descriptions
    - Add keyboard navigation support throughout the feature
    - Create screen reader compatible components
    - Implement high contrast mode support
    - Write accessibility tests and manual testing procedures
    - _Requirements: 9.2, 9.3, 9.4_

- [ ] 10. Implement error handling and validation

  - [ ] 10.1 Create comprehensive error handling

    - Implement feature-level error boundary
    - Add API error handling with retry mechanisms
    - Create user-friendly error messages and recovery options
    - Add network error handling and offline support
    - Write tests for error scenarios
    - _Requirements: 8.3, 8.4, 8.5, 8.6_

  - [ ] 10.2 Add form validation and feedback
    - Implement real-time validation with clear error messages
    - Add field-level validation feedback
    - Create validation summary for form submission
    - Add success feedback for completed actions
    - Write tests for validation scenarios
    - _Requirements: 8.1, 8.2, 8.6_

- [ ] 11. Performance optimization and testing

  - [ ] 11.1 Implement performance optimizations

    - Add lazy loading for PDF generation components
    - Implement calculation memoization for expensive operations
    - Add debounced input handling for real-time calculations
    - Optimize image handling and upload processes
    - Write performance tests and benchmarks
    - _Requirements: Performance considerations from design_

  - [ ] 11.2 Create comprehensive test suite
    - Write unit tests for all utility functions and hooks
    - Create integration tests for form workflows
    - Implement E2E tests for complete user journeys
    - Add visual regression tests for UI components
    - Set up test coverage reporting and CI integration
    - _Requirements: All requirements validation_

- [ ] 12. Final integration and deployment preparation

  - [ ] 12.1 Integrate with existing application

    - Connect with authentication system and user context
    - Integrate with shared UI components and styling
    - Add feature to main navigation and routing
    - Ensure consistent styling with application theme
    - Write integration tests with existing features
    - _Requirements: Integration with existing system_

  - [ ] 12.2 Prepare for deployment
    - Add environment configuration for PDF templates and file storage
    - Implement feature flags for gradual rollout
    - Add monitoring and analytics tracking
    - Create deployment documentation and runbooks
    - Perform final testing and quality assurance
    - _Requirements: Deployment readiness_
