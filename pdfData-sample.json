{"pdf": {"full": false, "user": {"id": "16", "documentId": "oq8x21rbsfaqaz3m2y822ex0", "photo": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/origin/1752775980069_2t6w6-<PERSON>_Portrait-1_low.jpg", "formats": {"thumbnail": {"url": null}, "squared": {"url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/public/images/squared/1752775980069_2t6w6-<PERSON>_Portrait-1_low.jpg"}}}, "firstname": "<PERSON>", "lastname": "<PERSON>", "position": "IT Director", "email": "<EMAIL>", "workEmail": "<EMAIL>", "phone": "3688871667", "workPhone": "3688871667", "cellPhone": "3688871667", "website": "https://brunosousa.cc", "brokerage": "Indi Calgary", "team": {"id": 94, "logo": {"url": null}}}, "short": true, "address": "Some\nCool \nAddress", "mlsCode": "ABBSA", "cashNeeded": true, "realtorPhoto": {"ext": null, "url": null}, "propertyPhoto": {"ext": ".jpg", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/house_property_18c422bdd4.jpg"}, "monthlyExpenses": true}, "rate": 0, "cable": {"raw": 90, "currency": 90}, "phone": {"raw": 90, "currency": 90}, "rate5": 0, "years": 25, "amount": 989000, "rate10": 0, "rate15": 5.6, "rate20": 5.8, "saving": false, "hoaFees": {"raw": 88, "currency": 88}, "loading": false, "realtor": {"firstname": "Brunoco", "middlename": null, "lastname": "Test", "position": "test", "company": "test", "email": "<EMAIL>", "phone": "9998887777", "website": "brunoco.ca", "photo": {"id": 15503, "documentId": "epuz8s6poeum87fz6fw4h8t8", "url": "https://indi-strapi-v2.s3.us-east-1.amazonaws.com/pexels_anjana_c_169994_674010_236be7bcc4.jpg"}}, "downPay5": 49450, "internet": {"raw": 90, "currency": 90}, "rateFTHB": 0, "appraisal": {"raw": 77, "currency": 77}, "condoFees": {"raw": 90, "currency": 90}, "downPay10": 98900, "downPay15": 148350, "downPay20": 197800, "frequency": "monthly", "houseType": "select", "lawyerFee": {"raw": 98, "currency": 98}, "rangeRate": {"rate": 7.472194135490394, "type": "middleTier"}, "rateRange": 5.4, "uploading": {"type": null, "status": false}, "utilities": {"raw": 90, "currency": 90}, "insurance5": 37582, "principal5": 977132, "rateCustom": 6.4, "showImages": true, "askingPrice": 989000, "customYears": 0, "downPayFTHB": 73900, "estoppelFee": {"raw": 25, "currency": 25}, "insurance10": 27593.1, "insurance15": 23538.2, "insurance20": 0, "monthlyPay5": 0, "principal10": 917693.1, "principal15": 864188.2, "principal20": 791200, "propertyTax": {"raw": 90, "currency": 90}, "downPayRange": 73900, "monthlyPay10": 0, "monthlyPay15": 5325.344263646007, "monthlyPay20": 4968.473467250895, "periodicPay5": 0, "chosenDownPay": {"amount": 0, "percent": "7.39%"}, "downPayCustom": 178020, "insuranceFTHB": 36604, "periodicPay10": 0, "periodicPay15": 5325.344263646007, "periodicPay20": 4968.473467250895, "principalFTHB": 951704, "effectiveRates": {"effectiveRate5": 0, "effectiveRate10": 0, "effectiveRate15": 0.05535762885867168, "effectiveRate20": 0.057311337399674755, "effectiveRateFTHB": 0, "effectiveRateRange": 0.053402335929647826, "effectiveRateCustom": 0.06316298487285366}, "homeInspection": {"raw": 98, "currency": 98}, "insuranceRange": 36604, "monthlyPayFTHB": 0, "principalRange": 951704, "titleInsurance": {"raw": 99, "currency": 99}, "chosenPrincipal": 951704, "insuranceCustom": 22707.44, "monthlyPayRange": 5753.841093929893, "periodicPayFTHB": 0, "principalCustom": 833687.44, "showRealtorInfo": true, "totalCashNeeded": 0, "customPercentage": 18, "monthlyPayCustom": 5533.796226575959, "numberOfPayments": 12, "periodicPayRange": 5753.841093929893, "chosenPeriodicPay": 5753.841093929893, "periodicPayCustom": 5533.796226575959, "propertyInsurance": {"raw": 90, "currency": 90}, "showAdditionalInfo": true, "showBodyCashNeeded": false, "amortizationBalance": 0, "monthlyDebtPayments": {"raw": 90, "currency": 90}, "showMortgagePayment": true, "totalYearlyPayments": 0, "chosenDownPayExpense": {}, "totalMonthlyPayments": 0, "showBodyYearlyExpenses": false, "showBodyMonthlyExpenses": false}